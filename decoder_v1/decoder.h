#ifndef _DECODER_H
#define _DECODER_H

//#include "function.h"
#include "mdtypes.h"
#include "decoder_config.h"

#if (MACR_PROJ_SYS_V1 == 1)
u32 decoder_shell(void* deco_sys_api_pt1, void* code_info, void* code_addition_info, void* max_buff_len);
s32 decoder_init(void* deco_sys_api_pt1);
void Decoder_OutputStateInfo(s32(*fun_printf_pt)(const s8*,...));
void decoder_destroy_callback(void);
#endif

#if (MACR_PROJ_SYS_V2 == 1)
u32 Decoder_Shell(void* deco_sys_api_pt1, void* deco_sys_api_pt2, void* code_info,
					void* code_addition_info, void* max_buff_len, void* camera_num,
					void *camera_active, void *decoder_running_restart_req, void *roi_coord_info_pt,
					void *roi_deco_rslt_info_pt, void *deco_succ_roi_info_pt, void* ocr_decoder_req);

s32 Decoder_PreInit(void* deco_sys_api_pt1, void* deco_sys_api_pt2, void* camera_num, void *camera_active);

void Decoder_OutputStateInfo(s32(*fun_printf_pt)(const s8*,...));
void Decoder_Destroy_Callback(void);

s32 Decoder_IsDecoDone();
void Decoder_RequestTerminate_Sys();
#endif

#endif


