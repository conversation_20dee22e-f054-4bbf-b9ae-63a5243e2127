#ifndef _APP_DECODER_H
#define _APP_DECODER_H

#include "mdtypes.h"

#define TEST 1
#define BOARDTEST 0

//#define min(x1,x2) ((x1<x2)? x1:x2)
//#define max(x1,x2) ((x1>x2)? x1:x2)


#define MACR_APP_DECODER_INCREASE_ONE	1
#define MACR_APP_DECODER_INCREASE_SET	2
#define MACR_APP_DECODER_INCREASE_CLR	3

#define MACR_APP_DECODER_INCREASE_ONE	1
#define MACR_APP_DECODER_INCREASE_SET	2
#define MACR_APP_DECODER_INCREASE_CLR	3
#define MACR_APP_DECODER_INCREASE_SUM	4

#define MACR_APP_DECODER_CodeDataLength		600 //code39: 10 peaks for one byte data, so in max 60 bytes data can be saved
//#define MACR_APP_DECODER_CodeDataLength		PulseEdgeMaxNo

#define MACR_APP_DECODER_AddOnDataLength	50

#define MACR_APP_DECODER_ResultLength		216  // original value 256

#define MACR_APP_DECODER_MID_BUFSIZE		128

// decode attempt in 1 second
#define DECODE_COUNT_1Sec					50

#define Margin								25   // unit is percent


// ===================================================================================================================
// Code type specification

#define MACR_CODE_TYPE_NUM			24

#define MACR_CODE_TYPE_EAN13		0
#define MACR_CODE_TYPE_EAN8			1
#define MACR_CODE_TYPE_UPCA			2
#define MACR_CODE_TYPE_UPCE			3
#define MACR_CODE_TYPE_UCCEAN128	4
#define MACR_CODE_TYPE_CODE128		5
#define MACR_CODE_TYPE_CODE39		6
#define MACR_CODE_TYPE_CODABAR		7
#define MACR_CODE_TYPE_INTL25		8
#define MACR_CODE_TYPE_CODE93		9
#define MACR_CODE_TYPE_INDS25		10
#define MACR_CODE_TYPE_MATR25		11
#define MACR_CODE_TYPE_CHNPST		12
#define MACR_CODE_TYPE_CODE11		13
#define MACR_CODE_TYPE_UKPL			14
#define MACR_CODE_TYPE_MSIPL		15
#define MACR_CODE_TYPE_GS1DB		16
#define MACR_CODE_TYPE_GS1LIMI		17
#define MACR_CODE_TYPE_GS1EXPA		18
#define MACR_CODE_TYPE_PDF417		19
#define MACR_CODE_TYPE_MICPDF417	20
#define MACR_CODE_TYPE_ISBT128  	21
#define MACR_CODE_TYPE_ISBN       	22
#define MACR_CODE_TYPE_UPCE1        23
#define MACR_CODE_TYPE_NONE			255



#define Flag_Mapping_RowNum			MACR_CODE_TYPE_NUM
#define Flag_Mapping_ColumnNum		8


// Column
#define TABLE_COL_POS_CODENAME			0
#define TABLE_COL_POS_CODENAME_LEN		1
#define TABLE_COL_POS_READ				2
#define TABLE_COL_POS_CODEID_LEN		3
#define TABLE_COL_POS_AIMID				4
#define TABLE_COL_POS_CodeLength_Max	5
#define TABLE_COL_POS_CodeLength_Min	6
#define TABLE_COL_POS_InsertStrGrp		7


// Flag_Mapping_Table
//extern const void * const Flag_Mapping_Table[Flag_Mapping_RowNum][Flag_Mapping_ColumnNum]; 



// =================================================================================
struct myvect
{
    u32 keyn;
    u32 ascii;
};

// const struct App_MultiSacn_Struct app_multiscan_data[MACR_CODE_TYPE_NUM] = 
// {
// 	//codetype default-multi-scan-times
// 		0, 1, 0,	// EAN-13 
// 		1, 1, 0,	// EAN-8
// 		2, 1, 0,	// UPC-A
// 		3, 3, 8,	// UPC-E
// 		4, 1, 0,	// UCC/EAN-128
// 		5, 1, 0,	// Code 128
// 		6, 3, 99,	// Code 39
// 		7, 3, 8,	// Codabar
// 		8, 3, 8,	// Interleaved 2/5
// 		9, 1, 0,	// Code 93
// 		10,3, 4,	// Industrial 2/5
// 		11,3, 99,	// Matrix 2/5
// 		12,2, 11,	// China Post
// 		13,3, 6,	// Code 11
// 		14,1, 0,	// UK plessey
// 		15,3, 6,	// MSI plessey
// 		16,1, 0,	// GS1 DataBar
// 		17,1, 0,	// GS1 DataBar Limited
// 		18,1, 0,	// GS1 DataBar Expanded
// 		19,1, 0,	// PDF417
// 		20,1, 0,	// MicroPDF417
// };

struct App_Decoder_Struct
{
    volatile u8 AppDecoder_RequiredFlag;


    u32 CodeAreaLength;

    u32 AddOnAreaLength;

    u32 AppDecoder_DecodeAttempt_s;

    __int64 AppDecoder_OldTickCnt;

    u32 MultiScanCounter_G;

    //  Code Type
    u8  CodeType_G;
    u8	CodeType_A;

    //  Code Data Length
    u8  CodeDataLength_G;


    volatile u8 Decode_Finish_Flag_G;

    //  CodeData[]: an array of widthes of bars and spaces;
    u16 CodeData[MACR_APP_DECODER_CodeDataLength];	

    //  AddOnData[]: an array of widthes of bars and spaces for addon 2 or 5 digit;
    u16 AddOnData[MACR_APP_DECODER_AddOnDataLength];


    //  an array of decoded characters;
    u8  CodeResult[MACR_APP_DECODER_ResultLength];

    //  an array of previous decoded characters;
    u8  OldCodeResult[MACR_APP_DECODER_ResultLength];

    //  CodeType[]: an array of data to store the type of barcode;
    u8 CodeType[MACR_CODE_TYPE_NUM];	

    volatile u8 MD_Truncate_Flag;

    volatile u32 StandbyDuration_Key;

    volatile u32 StandbyDuration_Sensor;

    u32 Inverted; // 0: have not inverted; 1: inverted

    u32 Code128_Optimized;
};

//typedef struct {
//    u16 BarBlankArray[MACR_APP_DECODER_CodeDataLength];    // a fram signal in max has 600 peak.
//    u8 PeakValue[MACR_APP_DECODER_CodeDataLength];    // a fram signal in max has 600 peak.
//    u16 BarBlankNum;
//}BarBlank_Keep;

// extern struct App_Decoder_Struct app_decoder_data;

// s32 decoder_1d(u16* bufPt, u16 DataLen);
// 
// s32 UE128_code128_shell(void);
// s32 codabar_shell(void);
// s32 code11_shell(void);
// s32 code39_shell(void);
// s32 code93_shell(void);
// s32 EAN13_UPCA_shell(void);
// s32 EAN8_shell(void);
// s32 UPCE_shell(void);
// s32 UPCE1_shell(void);
// s32 IL25_shell(void);
// s32 industrial25_shell(void);
// s32 MSI_plessey_shell(void);
// s32 MT25_CPOST_shell(void);
// s32 UE128_code128_shell(void);
// s32 UK_plessey_shell(void);
// s32 UK_plessey_check(u32* digit_ary, u32 digit_ary_len);
// s32 GS1_shell(void);
// 
// u8 EAN8_ResultConver(void);
// u8 UPCA_ResultConver(void);
// u8 UPCE_ResultConver(void);
// u8 Omni_ResultConver(void);
// u8 Limi_ResultConver(void);
// u8 Expa_ResultConver(void);
// u8 UPCE1_ResultConver(void);
#endif //_APP_DECODER_H
