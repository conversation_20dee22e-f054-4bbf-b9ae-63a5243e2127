
#include <math.h>
#include "transform.h"
//--------------------------------------------------------------------
#define  MAX_RANK  8
#define  TINY 1e-10
//---------------------------------------------------------------------
void CDecoTransform::mdHomInit(MdHom* hom,const MdPoint2D32f* p, const MdPoint2D32f* q)
{
	double aa[8][8];
	double *a[8];
	double b[8];
	double coef[8];
	int i;

	for (i=0;i<8;i++)
	{
		a[i]=aa[i];
	}

	/* init matrix a and vector b */
	for( i = 0; i < 4; ++i )
    {
        a[i][0] = a[i+4][3] = q[i].x;
        a[i][1] = a[i+4][4] = q[i].y;
        a[i][2] = a[i+4][5] = 1;
        a[i][3] = a[i][4] = a[i][5] =
		a[i+4][0] = a[i+4][1] = a[i+4][2] = 0;
        a[i][6] = -q[i].x*p[i].x;
        a[i][7] = -q[i].y*p[i].x;
        a[i+4][6] = -q[i].x*p[i].y;
        a[i+4][7] = -q[i].y*p[i].y;
        b[i] = p[i].x;
        b[i+4] = p[i].y;
    }

	GetCoef1(a, b, coef, 8);

	hom->fwd[0][0]=coef[0];
	hom->fwd[0][1]=coef[1];
	hom->fwd[0][2]=coef[2];
	hom->fwd[1][0]=coef[3];
	hom->fwd[1][1]=coef[4];
	hom->fwd[1][2]=coef[5];
	hom->fwd[2][0]=coef[6];
	hom->fwd[2][1]=coef[7];
	hom->fwd[2][2]=1;

	/* init matrix a and vector b */
	for( i = 0; i < 4; ++i )
    {
        a[i][0] = a[i+4][3] = p[i].x;
        a[i][1] = a[i+4][4] = p[i].y;
        a[i][2] = a[i+4][5] = 1;
        a[i][3] = a[i][4] = a[i][5] =
		a[i+4][0] = a[i+4][1] = a[i+4][2] = 0;
        a[i][6] = -p[i].x*q[i].x;
        a[i][7] = -p[i].y*q[i].x;
        a[i+4][6] = -p[i].x*q[i].y;
        a[i+4][7] = -p[i].y*q[i].y;
        b[i] = q[i].x;
        b[i+4] = q[i].y;
    }

	GetCoef1(a, b, coef, 8);
	
	hom->inv[0][0]=coef[0];
	hom->inv[0][1]=coef[1];
	hom->inv[0][2]=coef[2];
	hom->inv[1][0]=coef[3];
	hom->inv[1][1]=coef[4];
	hom->inv[1][2]=coef[5];
	hom->inv[2][0]=coef[6];
	hom->inv[2][1]=coef[7];
	hom->inv[2][2]=1;

	return;
}

//map from q to p
void CDecoTransform::mdHomProject(const MdHom* hom, MdPoint2D32f* p, const MdPoint2D32f* q)
{
	double x,y,w;
	
	x=hom->fwd[0][0]*q->x+hom->fwd[0][1]*q->y+hom->fwd[0][2];
	y=hom->fwd[1][0]*q->x+hom->fwd[1][1]*q->y+hom->fwd[1][2];
	w=hom->fwd[2][0]*q->x+hom->fwd[2][1]*q->y+hom->fwd[2][2];

	p->x=(float)(x/w);
	p->y=(float)(y/w);

	return;
}

//map from p to q
s32 CDecoTransform::mdHomUnproject(const MdHom* hom,const MdPoint2D32f* p,MdPoint2D32f* q)
{
	double x,y,w;

	x=hom->inv[0][0]*p->x+hom->inv[0][1]*p->y+hom->inv[0][2];
	y=hom->inv[1][0]*p->x+hom->inv[1][1]*p->y+hom->inv[1][2];
	w=hom->inv[2][0]*p->x+hom->inv[2][1]*p->y+hom->inv[2][2];
	
	if(w==0)
		return 0;

	q->x=(float)(x/w);
	q->y=(float)(y/w);

	return 1;
}


//map from q to p
void CDecoTransform::mdHomProject_int(const MdHom* hom, MdPoint* p, const MdPoint* q)
{
	float x,y,w;
	
	x=(float)(hom->fwd[0][0]*q->x+hom->fwd[0][1]*q->y+hom->fwd[0][2]);
	y=(float)(hom->fwd[1][0]*q->x+hom->fwd[1][1]*q->y+hom->fwd[1][2]);
	w=(float)(hom->fwd[2][0]*q->x+hom->fwd[2][1]*q->y+hom->fwd[2][2]);
	
	p->x=(int)(x/w);
	p->y=(int)(y/w);
	
	return;
}

//map from p to q
s32 CDecoTransform::mdHomUnproject_int(const MdHom* hom,const MdPoint* p,MdPoint* q)
{
	float x,y,w;
	
	x=(float)(hom->inv[0][0]*p->x+hom->inv[0][1]*p->y+hom->inv[0][2]);
	y=(float)(hom->inv[1][0]*p->x+hom->inv[1][1]*p->y+hom->inv[1][2]);
	w=(float)(hom->inv[2][0]*p->x+hom->inv[2][1]*p->y+hom->inv[2][2]);
	
	if(w==0)
		return 0;
	q->x=(int)(x/w);
	q->y=(int)(y/w);
	
	return 1;
}


//---------------------------------------------------------------------------
void CDecoTransform::mdAffInit(MdAff* aff,const MdPoint2D32f* p, const MdPoint2D32f* q)
{
	double aa[6][6];
	double *a[6];
	double b[6];
	double coef[6];
	int i;
	
	for (i=0;i<6;i++)
	{
		a[i]=aa[i];
	}
	
	/* init matrix a and vector b */
	for( i = 0; i < 3; ++i )
    {
        a[i][0] = a[i+3][3] = q[i].x;
        a[i][1] = a[i+3][4] = q[i].y;
        a[i][2] = a[i+3][5] = 1;
        a[i][3] = a[i][4] = a[i][5] =
		a[i+3][0] = a[i+3][1] = a[i+3][2] = 0;

        b[i] = p[i].x;
        b[i+3] = p[i].y;
    }

	GetCoef1(a, b, coef, 6);
	
	aff->fwd[0][0]=coef[0];
	aff->fwd[0][1]=coef[1];
	aff->fwd[0][2]=coef[2];
	aff->fwd[1][0]=coef[3];
	aff->fwd[1][1]=coef[4];
	aff->fwd[1][2]=coef[5];
	
	/* init matrix a and vector b */
	for( i = 0; i < 3; ++i )
    {
        a[i][0] = a[i+3][3] = p[i].x;
        a[i][1] = a[i+3][4] = p[i].y;
        a[i][2] = a[i+3][5] = 1;
        a[i][3] = a[i][4] = a[i][5] =
		a[i+3][0] = a[i+3][1] = a[i+3][2] = 0;

        b[i] = q[i].x;
        b[i+3] = q[i].y;
    }
	
	GetCoef1(a, b, coef, 6);
	
	aff->inv[0][0]=coef[0];
	aff->inv[0][1]=coef[1];
	aff->inv[0][2]=coef[2];
	aff->inv[1][0]=coef[3];
	aff->inv[1][1]=coef[4];
	aff->inv[1][2]=coef[5];
	
	return;
}

void CDecoTransform::mdAffInitInt(MdAff* aff,const MdPoint* p, const MdPoint* q)
{
	double aa[6][6];
	double *a[6];
	double b[6];
	double coef[6];
	int i;
	
	for (i=0;i<6;i++)
	{
		a[i]=aa[i];
	}
	
	/* init matrix a and vector b */
	for( i = 0; i < 3; ++i )
    {
        a[i][0] = a[i+3][3] = q[i].x;
        a[i][1] = a[i+3][4] = q[i].y;
        a[i][2] = a[i+3][5] = 1;
        a[i][3] = a[i][4] = a[i][5] =
			a[i+3][0] = a[i+3][1] = a[i+3][2] = 0;
		
        b[i] = p[i].x;
        b[i+3] = p[i].y;
    }
	
	GetCoef1(a, b, coef, 6);
	
	aff->fwd[0][0]=coef[0];
	aff->fwd[0][1]=coef[1];
	aff->fwd[0][2]=coef[2];
	aff->fwd[1][0]=coef[3];
	aff->fwd[1][1]=coef[4];
	aff->fwd[1][2]=coef[5];
	
	/* init matrix a and vector b */
	for( i = 0; i < 3; ++i )
    {
        a[i][0] = a[i+3][3] = p[i].x;
        a[i][1] = a[i+3][4] = p[i].y;
        a[i][2] = a[i+3][5] = 1;
        a[i][3] = a[i][4] = a[i][5] =
			a[i+3][0] = a[i+3][1] = a[i+3][2] = 0;
		
        b[i] = q[i].x;
        b[i+3] = q[i].y;
    }
	
	GetCoef1(a, b, coef, 6);
	
	aff->inv[0][0]=coef[0];
	aff->inv[0][1]=coef[1];
	aff->inv[0][2]=coef[2];
	aff->inv[1][0]=coef[3];
	aff->inv[1][1]=coef[4];
	aff->inv[1][2]=coef[5];
	
	return;
}
//---------------------------------------------------------------------------
void CDecoTransform::mdAffProject(const MdAff* aff, MdPoint2D32f* p, const MdPoint2D32f* q)
{
	p->x = (float)(aff->fwd[0][0] * q->x + aff->fwd[0][1] * q->y + aff->fwd[0][2]);
	p->y = (float)(aff->fwd[1][0] * q->x + aff->fwd[1][1] * q->y + aff->fwd[1][2]);

	return;
}

void CDecoTransform::mdAffUnproject(const MdAff* aff,const MdPoint2D32f* p,MdPoint2D32f* q)
{
	q->x = (float)(aff->inv[0][0] * p->x + aff->inv[0][1] * p->y + aff->inv[0][2]);
	q->y = (float)(aff->inv[1][0] * p->x + aff->inv[1][1] * p->y + aff->inv[1][2]);
	
	return;
}
//----------------------------------------------------------------------------
void CDecoTransform::ludcmp1(double** a,int rank, int* indx)
{
	int i, imax=0, j, k;
	double  dum,  big, sum, temp;
	double vv[MAX_RANK];
		
	for (i=0; i<rank; i++)
	{
		big=0;
		for (j=0; j<rank; j++)	
		{
			if ((temp = fabs(a[i][j])) > big) 
			{
				big = temp;   
			}
		}
		vv[i] = 1.0/big;
	}
	
	for (j=0; j<rank; j++)
	{
		for (i=0; i<j; i++ )
		{
			sum = a[i][j];
			for (k=0; k<i; k++) 
			{
				sum -= a[i][k]*a[k][j];
			}
			a[i][j] = sum;
		}
		
		big=0.0;
		for (i=j; i<rank; i++)
		{
			sum=a[i][j];
			for (k=0; k<j; k++)
			{
				sum -= a[i][k]*a[k][j];
			}
			a[i][j]=sum;
			if ((dum=vv[i]*fabs(sum)) >= big) 
			{
				big=dum; 
				imax=i;
			}
		}
		
		if (j != imax) 
		{
			for (k=0; k<rank; k++)
			{
				dum=a[imax][k];
				a[imax][k]=a[j][k];
				a[j][k]=dum;
			}
			vv[imax]=vv[j];
		}
		
		indx[j]=imax;
		if (fabs(a[j][j]) < TINY) 
		{
			a[j][j]= TINY;
		}
		
		if (j != rank) 
		{
			dum=1.0/(a[j][j]);
			for (i=j+1; i<rank; i++) 
			{
				a[i][j] *= dum;
			}
		}
	}
	
	return;
}

void CDecoTransform::lubksb1(double** a, int rank, int* index, double* b,double* c)
{
	int i, ip, j;
	double sum;
	
	for ( i=0; i<rank; i++)
	{
		ip=index[i];
		sum=b[ip];
		b[ip]=b[i];
		for (j=0; j<=i-1; j++) 
		{
			sum -= a[i][j]*b[j];
		}
		b[i]=sum;
	}
	
	for ( i=rank-1; i>=0; i--)
	{
		sum=b[i];
		for ( j=i+1; j<rank; j++) 
		{
			sum -= a[i][j]*b[j];
		}
		b[i]=sum/(a[i][i]);
	}
	
	for (i=0;i<rank;i++)
	{
		c[i]=b[i];
	}
	
	return;
}

/* 
A*c=b,A:matrix,b:vector,c:vector,to be compute
*/
void CDecoTransform::GetCoef1(double** a , double* b,double* c,int rank)
{
	int index[MAX_RANK];
	
	ludcmp1(a, rank, index);
	
	lubksb1(a, rank, index, b, c);
	
	return;
}

//-------------------------------------------------------------------------



