
#ifndef _QR_FINDER_H
#define _QR_FINDER_H

#include "qr.h"
#include "decoder_config.h"
// #include "scanner.h"


/* QR Code symbol finder state */
typedef struct qr_finder_s {
    unsigned s5;                /* finder pattern width */
    qr_finder_line line;        /* position info needed by decoder */
    unsigned config;
} qr_finder_t;

/* reset QR finder specific state */
static  void qr_finder_reset (qr_finder_t *qrf)
{
    qrf->s5 = 0;
}


typedef struct md_decoder_s md_decoder_t;
typedef struct md_image_scanner_s  md_image_scanner_t;
// void qr_handler (md_image_scanner_t *iscn);
// void _md_qr_destroy(qr_reader *reader);
// void _md_qr_reset(qr_reader *reader);
// s32 _md_qr_found_line(qr_reader *reader,s32 direction,
// 					  const qr_finder_line *line);
s32 qr_finder_hline_cmp(const void *_a,const void *_b);


#endif
