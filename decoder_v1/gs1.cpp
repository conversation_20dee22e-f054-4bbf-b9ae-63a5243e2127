#include "oneD_dec.h"
#include "decoder_oneD.h"

//#define U32_MAX 0xFFFFFFFF
//#define U16_MAX 0xFFFF
//#define U8_MAX	0xFF
#define A1	0
#define A2	2
#define B1	4
#define B2	6
#define C1	8
#define C2	10
#define D1	12
#define D2	14
#define E1	16
#define E2	18
#define F1	20
#define F2	22

#define GS 29 //0x1D

#define SCHEME_NUM 1
#define SCHEME_ALP 2
#define SCHEME_ISO 3

struct GS1_sym_char
{
	u32 Gsum;
	u8 odd_widest;
	u8 even_widest;
	u16 Todd;
	u16 Teven;
};

const struct GS1_sym_char expe_char[5] =
{
	0,		7,		2,		87,		4,
	348,	5,		4,		52,		20,
	1388,	4,		5,		30,		52,
	2948,	3,		6,		10,		104,
	3988,	1,		8,		1,		204
};

const struct GS1_sym_char omni_outside_char[5] =
{
	0,		8,		1,		161,	1,
	161,	6,		3,		80,		10,
	961,	4,		5,		31,		34,
	2015,	3,		6,		10,		70,
	2715,	1,		8,		1,		126
};

const struct GS1_sym_char omni_inside_char[4] =
{
	0,		2,		7,		4,		84,
	336,	4,		5,		20,		35,
	1036,	6,		3,		48,		10,
	1516,	8,		1,		81,		1
};

const struct GS1_sym_char limi_char[7] =
{
	1996939,	1,		8,		1,		16632,
	820064,		3,		6,		28,		6454,
	1491021,	4,		5,		203,	2408,
	183064,		5,		4,		875,	728,
	1000776,	5,		4,		2415,	203,
	0,			6,		3,		6538,	28,
	1979845,	8,		1,		17094,	1,
};

const u16 limited_check[] =
{
	45,		52,		57,		63,		64,
	65,		66,		73,		74,		75,
	76,		77,		78,		79,		82,
	126,	127,	128,	129,	130,
	132,	141,	142,	143,	144,
	145,	146,	210,	211,	212,
	213,	214,	215,	216,	217,
	220,	316,	317,	318,	319,
	320,	322,	323,	326,	337
};

const u8 finder_pattern_sequences[10][11] =
{
	A1,A2, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	A1,B2,B1, 0, 0, 0, 0, 0, 0, 0, 0,
	A1,C2,B1,D2, 0, 0, 0, 0, 0, 0, 0,
	A1,E2,B1,D2,C1, 0, 0, 0, 0, 0, 0,
	A1,E2,B1,D2,D1,F2, 0, 0, 0, 0, 0,
	A1,E2,B1,D2,E1,F2,F1, 0, 0, 0, 0,
	A1,A2,B1,B2,C1,C2,D1,D2, 0, 0, 0,
	A1,A2,B1,B2,C1,C2,D1,E2,E1, 0, 0,
	A1,A2,B1,B2,C1,C2,D1,E2,F1,F2, 0,
	A1,A2,B1,B2,C1,D2,D1,E2,E1,F2,F1
};

const u16 finder_pattern_Omnidirectional[9] = {0x3821,0x3551,0x3371,0x3191,0x2741,0x2561,0x2381,0x1571,0x1391};

const u16 ele_weights_expended[24][8] = 
{
	0,		0,		0,		0,		0,		0,		0,		0,
	1,		3,		9,		27,		81,		32,		96,		77,
	20,		60,		180,	118,	143,	7,		21,		63,
	189,	145,	13,		39,		117,	140,	209,	205,
	193,	157,	49,		147,	19,		57,		171,	91,
	62,		186,	136,	197,	169,	85,		44,		132,
	185,	133,	188,	142,	4,		12,		36,		108,
	113,	128,	173,	97,		80,		29,		87,		50,
	150,	28,		84,		41,		123,	158,	52,		156,
	46,		138,	203,	187,	139,	206,	196,	166,
	76,		17,		51,		153,	37,		111,	122,	155,
	43,		129,	176,	106,	107,	110,	119,	146,
	16,		48,		144,	10,		30,		90,		59,		177,
	109,	116,	137,	200,	178,	112,	125,	164,
	70,		210,	208,	202,	184,	130,	179,	115,
	134,	191,	151,	31,		93,		68,		204,	190,
	148,	22,		66,		198,	172,	94,		71,		2,
	6,		18,		54,		162,	64,		192,	154,	40,
	120,	149,	25,		75,		14,		42,		126,	167,
	79,		26,		78,		23,		69,		207,	199,	175,
	103,	98,		83,		38,		114,	131,	182,	124,
	161,	61,		183,	127,	170,	88,		53,		159,
	55,		165,	73,		8,		24,		72,		5,		15,
	45,		135,	194,	160,	58,		174,	100,	89
};

const u16 ele_weights_omnidirectional[4][8] =
{
	1,		3,		9,		27,		2,		6,		18,		54,
	4,		12,		36,		29,		8,		24,		72,		58,
	16,		48,		65,		37,		32,		17,		51,		74,
	64,		34,		23,		69,		49,		68,		46,		59
};

const u16 ele_weights_limited[2][14] =
{
	1,		3,		9,		27,		81,		65,		17,		51,		64,		14,		42,		37,		22,		66,
	20,		60,		2,		6,		18,		54,		73,		41,		34,		13,		39,		28,		84,		74
};

const u16 combin[18][6] =
{
	1,		1,		1,		1,		1,		1,
	1,		1,		1,		1,		1,		1,
	1,		2,		1,		1,		1,		1,
	1,		3,		3,		1,		1,		1,
	1,		4,		6,		4,		1,		1,
	1,		5,		10,		10,		5,		1,
	1,		6,		15,		20,		15,		6,
	1,		7,		21,		35,		35,		21,
	1,		8,		28,		56,		70,		56,
	1,		9,		36,		84,		126,	126,
	1,		10,		45,		120,	210,	252,
	1,		11,		55,		165,	330,	462,
	1,		12,		66,		220,	495,	792,
	1,		13,		78,		286,	715,	1287,
	1,		14,		91,		364,	1001,	2002,
	1,		15,		105,	455,	1365,	3003,
	1,		16,		120,	560,	1820,	4368,
	1,		17,		136,	680,	2380,	6188,
};

const u8 ISO_char_232_252[] =
{33,34,37,38,39,40,41,42,43,44,45,46,47,58,59,60,61,62,63,95,32};


struct App_GS1_Dec_Struct
{
u32 symbol_value[22];
u8  result_ary[MACR_APP_DECODER_ResultLength];
u8 symbol_character_num;
u8 sym_index;
u8 bit_index;
u8 msg_index;
u8 remain_bits_num;     
};


// s32 GS1_shell(void);
// s32 GS1_Omnidirectional(struct App_GS1_Dec_Struct* GS1_Dec_DataPt);
// s32 GS1_Limited(struct App_GS1_Dec_Struct* GS1_Dec_DataPt);
// s32 GS1_Expanded(struct App_GS1_Dec_Struct* GS1_Dec_DataPt);
// s32 data_decode_expe(struct App_GS1_Dec_Struct* GS1_Dec_DataPt);
// u16 cal_val_expe(s16 *ele_width);
// u16 cal_outside_omni(s16 *ele_width);
// u16 cal_inside_omni(s16 *ele_width);
// u16 cal_check_limi(s16 *ele_width);
// u32 cal_data_limi(s16 *ele_width);
// u16 cal_finder_omni(s16 *ele_width);
// s32 width_normalization(s16 ele_width[],  u8 N, u8 K, u8 mini_even, u8 same_edge_min, u8 same_edge_max);
// u32 getRSSvalue(u16 widths[], u8 elements, u8 maxWidth, u8 noNarrow);
// u16 get_nbits(struct App_GS1_Dec_Struct* GS1_Dec_DataPt,u8 n, u16 data);
// u8 mod10_check(u8 *datain);
// s32 decompress_method1(struct App_GS1_Dec_Struct* GS1_Dec_DataPt);
// s32 decompress_method00(struct App_GS1_Dec_Struct* GS1_Dec_DataPt);
// s32 decompress_method010x(struct App_GS1_Dec_Struct* GS1_Dec_DataPt);
// s32 decompress_method0110x(struct App_GS1_Dec_Struct* GS1_Dec_DataPt);
// s32 decompress_method0111x(struct App_GS1_Dec_Struct* GS1_Dec_DataPt);
// s8 decodation_numeric(struct App_GS1_Dec_Struct* GS1_Dec_DataPt);
// s8 decodation_alphanumeric(struct App_GS1_Dec_Struct* GS1_Dec_DataPt);
// s8 decodation_ISO(struct App_GS1_Dec_Struct* GS1_Dec_DataPt);
// u16 decompaction_gp(struct App_GS1_Dec_Struct* GS1_Dec_DataPt);


#define MDFLAG_GS1DB_Conversion_None			0
#define MDFLAG_GS1DB_Conversion_UCCEAN128		1
#define MDFLAG_GS1DB_Conversion_UPCAEAN13		2
#define MDFLAG_GS1LIMI_Conversion_None			0
#define MDFLAG_GS1LIMI_Conversion_UCCEAN128		1
#define MDFLAG_GS1LIMI_Conversion_UPCAEAN13		2
#define MDFLAG_GS1EXPA_Conversion_None			0
#define MDFLAG_GS1EXPA_Conversion_UCCEAN128		1

extern s32  gGS1DB_Conversion;
extern s32  gGS1LIMI_Conversion;
extern s32  gGS1EXPA_Conversion;

/*
U32 symbol_value[22];
U8  result_ary[MACR_APP_DECODER_ResultLength];
U8 symbol_character_num = 0;
U8 sym_index;
U8 bit_index;
U8 msg_index;
U8 remain_bits_num;
*/

s32 CDecoOneDSubDec::GS1_shell(void)
{
    struct App_GS1_Dec_Struct GS1_Dec_data;
	u32 CodeAreaLength_local = app_decoder_data.CodeAreaLength;
	s32 flg = 0;
   
    GS1_Dec_data.symbol_character_num=0;

	if (CodeAreaLength_local<47)
	{
		return -1;
	}
	if (CodeAreaLength_local==47)
	{
		flg = GS1_Omnidirectional(&GS1_Dec_data);
		if (flg)
		{
			return 1;
		}
		else
		{
			flg = GS1_Limited(&GS1_Dec_data);
			//SYS_Debug_DisplayByte('O');
			if (flg)
			{
				return 2;
			}
			else
			{
				flg = GS1_Expanded(&GS1_Dec_data);
				if (flg)
				{
					return 3;
				}
			}
		}
	}
	else
	{
		flg = GS1_Expanded(&GS1_Dec_data);
		if (flg)
		{
			return 3;
		}
	}
	return -1;
}


s32 CDecoOneDSubDec::GS1_Omnidirectional(struct App_GS1_Dec_Struct* GS1_Dec_DataPt)
{
	u16 *CodeData_local = app_decoder_data.CodeData;

	u16 checksum;
	s16 ele_widths[8];
	u16 finder_left;
	u16 finder_right;
	u16 outside_left, outside_right, inside_left, inside_right;
	u32 Vleft,Vright;
	u64 Vsymbol;
	u32 sum2,sum4;

	u8 i;

	sum2 = CodeData_local[11]+CodeData_local[12];
	sum4 = sum2+CodeData_local[13]+CodeData_local[14];
	if ((sum2*24<sum4*19)||(sum2*28>sum4*25))
	{
		return 0;
	}
	sum2 = CodeData_local[33]+CodeData_local[34];
	sum4 = sum2+CodeData_local[31]+CodeData_local[32];
	if ((sum2*24<sum4*19)||(sum2*28>sum4*25))
	{
		return 0;
	}

	//SYS_Debug_DisplayByte('1');
	
	CodeData_local += 10;
	for (i=0;i<4;i++)
	{
		ele_widths[i] = CodeData_local[i];
	}
	finder_left = cal_finder_omni(ele_widths);
	if (finder_left == U16_MAX)
	{
		return 0;
	}
	
	//SYS_Debug_DisplayByte('2');
	
	CodeData_local += 22;
	for (i=0;i<4;i++)
	{
		ele_widths[i] = CodeData_local[3-i];
	}
	
	//SYS_Debug_Display_U8((U8)ele_widths[0]);
	//SYS_Debug_Display_U8((U8)ele_widths[1]);
	//SYS_Debug_Display_U8((U8)ele_widths[2]);
	//SYS_Debug_Display_U8((U8)ele_widths[3]);
	
	finder_right = cal_finder_omni(ele_widths);
	if (finder_right == U16_MAX)
	{
		return 0;
	}



	checksum = 0;
	CodeData_local = app_decoder_data.CodeData+2;
	for (i=0;i<8;i++)
	{
		ele_widths[i] = CodeData_local[i];
	}
	outside_left = cal_outside_omni(ele_widths);
	if (outside_left>2841)
	{
		return 0;
	}
	
	//SYS_Debug_DisplayByte('4');
	
	for ( i=0 ; i<8 ; i++ )
	{
		checksum += ele_widths[i]*ele_weights_omnidirectional[0][i];
	}
	CodeData_local += 13;
	for (i=0;i<8;i++)
	{
		ele_widths[i] = CodeData_local[7-i];
	}
	inside_left = cal_inside_omni(ele_widths);
	if (inside_left>1597)
	{
		return 0;
	}
	
	//SYS_Debug_DisplayByte('5');
	
	for ( i=0 ; i<8 ; i++ )
	{
		checksum += ele_widths[i]*ele_weights_omnidirectional[1][i];
	}
	CodeData_local += 8;
	for (i=0;i<8;i++)
	{
		ele_widths[i] = CodeData_local[i];
	}
	inside_right = cal_inside_omni(ele_widths);
	if (inside_right>1597)
	{
		return 0;
	}
	
	//SYS_Debug_DisplayByte('6');
	
	for ( i=0 ; i<8 ; i++ )
	{
		checksum += ele_widths[i]*ele_weights_omnidirectional[3][i];
	}
	CodeData_local += 13;
	for (i=0;i<8;i++)
	{
		ele_widths[i] = CodeData_local[7-i];
	}
	outside_right = cal_outside_omni(ele_widths);
	if (outside_right>2841)
	{
		return 0;
	}
	for ( i=0 ; i<8 ; i++ )
	{
		checksum += ele_widths[i]*ele_weights_omnidirectional[2][i];
	}

	//SYS_Debug_DisplayByte('O');
	

	checksum %= 79;
	if (checksum>=8)
	{
		checksum++;
	}
	if (checksum>=72)
	{
		checksum++;
	}
	if (checksum != (finder_left*9+finder_right))
	{
		return 0;
	}

	Vleft = (1597*outside_left)+inside_left;
	Vright = (1597*outside_right)+inside_right;

	Vsymbol = (u64)4537077*Vleft+Vright;

	for (i=0;i<13;i++)
	{
		GS1_Dec_DataPt->result_ary[12-i] = Vsymbol%10;
		Vsymbol /= 10;
	}
	if (Vsymbol)
	{
		return 0;
	}

	app_decoder_data.CodeResult[0] = '0';
	app_decoder_data.CodeResult[1] = '1';
	GS1_Dec_DataPt->msg_index = 2;
	for (i=0;i<13;i++)
	{
		app_decoder_data.CodeResult[(GS1_Dec_DataPt->msg_index)++] = GS1_Dec_DataPt->result_ary[i]+'0';
	}
	app_decoder_data.CodeResult[(GS1_Dec_DataPt->msg_index)++] = mod10_check(GS1_Dec_DataPt->result_ary)+'0';
	app_decoder_data.CodeResult[GS1_Dec_DataPt->msg_index] = '\0';

	return 1;
}


s32 CDecoOneDSubDec::GS1_Limited(struct App_GS1_Dec_Struct* GS1_Dec_DataPt)
{
	u16 *CodeData_local;

	u16 checksum;
	s16 ele_widths[14];
	u16 check_value;
	u32 Vleft,Vright;
	u64 Vsymbol;
	
	u8 i;

	CodeData_local = app_decoder_data.CodeData+16;
	for (i=0;i<14;i++)
	{
		ele_widths[i] = CodeData_local[i];
	}
	check_value = cal_check_limi(ele_widths);
	if (check_value == U16_MAX)
	{
		return 0;
	}
	
	//SYS_Debug_DisplayByte('1');
	
	checksum = 0;
	CodeData_local = app_decoder_data.CodeData+2;
	for (i=0;i<14;i++)
	{
		ele_widths[i] = CodeData_local[i];
	}
	Vleft = cal_data_limi(ele_widths);
	if (Vleft == U32_MAX)
	{
		return 0;
	}
	//SYS_Debug_DisplayByte('2');
	
	for (i=0;i<14;i++)
	{
		checksum += ele_widths[i]*ele_weights_limited[0][i];
	}
	//SYS_Debug_DisplayByte('3');
	
	CodeData_local += 28;
	for (i=0;i<14;i++)
	{
		ele_widths[i] = CodeData_local[i];
	}
	//SYS_Debug_DisplayByte('4');
	Vright = cal_data_limi(ele_widths);
	//SYS_Debug_DisplayByte('5');
	if (Vright == U32_MAX)
	{
		return 0;
	}
	//SYS_Debug_DisplayByte('3');
	
	for (i=0;i<14;i++)
	{
		checksum += ele_widths[i]*ele_weights_limited[1][i];
	}

	checksum %= 89;
	if (checksum != check_value)
	{
		return 0;
	}
	//SYS_Debug_DisplayByte('4');

	Vsymbol = (u64)2013571*Vleft+Vright;
	for (i=0;i<13;i++)
	{
		GS1_Dec_DataPt->result_ary[12-i] = Vsymbol%10;
		Vsymbol /= 10;
	}
	if (GS1_Dec_DataPt->result_ary[0]>1)
	{
		return 0;
	}
	//SYS_Debug_DisplayByte('5');
	
	app_decoder_data.CodeResult[0] = '0';
	app_decoder_data.CodeResult[1] = '1';
	GS1_Dec_DataPt->msg_index = 2;
	for (i=0;i<13;i++)
	{
		app_decoder_data.CodeResult[(GS1_Dec_DataPt->msg_index)++] = GS1_Dec_DataPt->result_ary[i]+'0';
	}
	app_decoder_data.CodeResult[(GS1_Dec_DataPt->msg_index)++] = mod10_check(GS1_Dec_DataPt->result_ary)+'0';
	app_decoder_data.CodeResult[GS1_Dec_DataPt->msg_index] = '\0';
	
	//SYS_Debug_DisplayByte('6');
	
	return 1;
}


s32 CDecoOneDSubDec::GS1_Expanded(struct App_GS1_Dec_Struct* GS1_Dec_DataPt)
{
	u32 CodeAreaLength_local = app_decoder_data.CodeAreaLength;
	u16 *CodeData_local = app_decoder_data.CodeData;
	
	u8 finder_pattern_num = 0;
	
	const u8 *finder_pattern;
	u32 checksum;
	s16 ele_widths[8];

	u8 i,j;
	u32 sum2,sum4;


	sum2 = CodeData_local[11]+CodeData_local[12];
	sum4 = sum2+CodeData_local[13]+CodeData_local[14];
	if ((sum2*24<sum4*19)||(sum2*28>sum4*25))
	{
		return 0;
	}
	finder_pattern_num = 1;
	GS1_Dec_DataPt->symbol_character_num = 1;
	CodeAreaLength_local -= 15;
	CodeData_local += 15;
	
	while (CodeAreaLength_local>21)
	{
		if (finder_pattern_num & 0x01)
		{
			sum2 = CodeData_local[18]+CodeData_local[19];
			sum4 = sum2+CodeData_local[16]+CodeData_local[17];
		}
		else
		{
			sum2 = CodeData_local[17]+CodeData_local[18];
			sum4 = sum2+CodeData_local[19]+CodeData_local[20];
		}
		if ((sum2*24<sum4*19)||(sum2*28>sum4*25))
		{
			return 0;
		}
		finder_pattern_num++;
		GS1_Dec_DataPt->symbol_character_num += 2;
		CodeAreaLength_local -= 21;
		CodeData_local += 21;
	}
	
	if (CodeAreaLength_local>8)
	{
		GS1_Dec_DataPt->symbol_character_num++;
		CodeAreaLength_local -= 8;
	}

	if (finder_pattern_num<2 || finder_pattern_num>11)
	{
		return 0;
	}
	finder_pattern = finder_pattern_sequences[finder_pattern_num-2];

	checksum = 0;
	CodeData_local = app_decoder_data.CodeData+2;
	for (i=0;i<finder_pattern_num;i++)
	{
		for (j=0;j<8;j++)
		{
			ele_widths[j] = CodeData_local[j];
		}
		GS1_Dec_DataPt->symbol_value[2*i] = cal_val_expe(ele_widths);
		if (GS1_Dec_DataPt->symbol_value[2*i]>4095)
		{
			return 0;
		}

		for ( j=0 ; j<8 ; j++ )
		{
			checksum += ele_widths[j]*ele_weights_expended[finder_pattern[i]][j];
		}

		if (GS1_Dec_DataPt->symbol_character_num > 2*i+1)
		{
			CodeData_local += 13;
			
			for (j=0;j<8;j++)
			{
				ele_widths[j] = CodeData_local[7-j];
			}
			GS1_Dec_DataPt->symbol_value[2*i+1] = cal_val_expe(ele_widths);
			if (GS1_Dec_DataPt->symbol_value[2*i+1]>4095)
			{
				return 0;
			}
			
			for ( j=0 ; j<8 ; j++ )
			{
				checksum += ele_widths[j]*ele_weights_expended[finder_pattern[i]+1][j];
			}
			CodeData_local += 8;
		}
	}
	checksum %= 211;
	checksum += 211*(GS1_Dec_DataPt->symbol_character_num-4);
	if (checksum != GS1_Dec_DataPt->symbol_value[0])
	{
		return 0;
	}

	if (data_decode_expe(GS1_Dec_DataPt))
	{
		return 1;
	} 
	else
	{
		return 0;
	}
}

s32 CDecoOneDSubDec::data_decode_expe(struct App_GS1_Dec_Struct* GS1_Dec_DataPt)
{
	u16 msg_length;
	u16 i;

	GS1_Dec_DataPt->msg_index = 0;
	GS1_Dec_DataPt->sym_index = 1;
	GS1_Dec_DataPt->bit_index = 12;
	GS1_Dec_DataPt->remain_bits_num = (GS1_Dec_DataPt->symbol_character_num-1)*12;

	if (get_nbits(GS1_Dec_DataPt,1,0))
	{
		return -1;
	}

	if (get_nbits(GS1_Dec_DataPt,1,0))
	{
		msg_length = decompress_method1(GS1_Dec_DataPt);
	}
	else if (!get_nbits(GS1_Dec_DataPt,1,0))
	{
		msg_length = decompress_method00(GS1_Dec_DataPt);
	}
	else if (!get_nbits(GS1_Dec_DataPt,1,0))
	{
		msg_length = decompress_method010x(GS1_Dec_DataPt);
	}
	else if (!get_nbits(GS1_Dec_DataPt,1,0))
	{
		msg_length = decompress_method0110x(GS1_Dec_DataPt);
	}
	else
	{
		msg_length = decompress_method0111x(GS1_Dec_DataPt);
	}

	if (msg_length)
	{
		for (i=0;i<msg_length;i++)
		{
			app_decoder_data.CodeResult[i] = GS1_Dec_DataPt->result_ary[i];
		}
		if (app_decoder_data.CodeResult[msg_length-1]==GS)
		{
			app_decoder_data.CodeResult[msg_length-1] = '\0';
		}
		else
		{
			app_decoder_data.CodeResult[msg_length] = '\0';
		}
		return 1;
	}
	else
	{
		return -1;
	}

}


u16 CDecoOneDSubDec::cal_val_expe(s16 *ele_width)
{
	u16 val;
	u16 odd_width[4], even_width[4];
	u16 odd_mods = 0, even_mods = 0;
	u16 Vodd, Veven;
	s8 group;
	u8 i;

	if (!width_normalization(ele_width,17,4,0,2,9))
	{
		return U16_MAX;
	}

	for ( i=0 ; i<4 ; i++ )
	{
		odd_width[i] = ele_width[2*i];
		even_width[i] = ele_width[2*i+1];

		odd_mods += odd_width[i];
		even_mods += even_width[i];
	}

	if ((odd_mods&0x01) || (odd_mods<4) || (odd_mods>12))
	{
		return U16_MAX;
	}
	group = 6-(odd_mods>>1);
	
	Vodd = getRSSvalue(odd_width,4,expe_char[group].odd_widest,0);
	Veven = getRSSvalue(even_width,4,expe_char[group].even_widest,1);

	val = Vodd * expe_char[group].Teven + Veven +expe_char[group].Gsum; 

	return val;
}


u16 CDecoOneDSubDec::cal_outside_omni(s16 *ele_width)
{
	u16 val;
	u16 odd_width[4], even_width[4];
	u16 odd_mods = 0, even_mods = 0;
	u16 Vodd, Veven;
	u8 group;
	u8 i;
	
	if (!width_normalization(ele_width,16,4,1,2,9))
	{
		return U16_MAX;
	}
	
	for ( i=0 ; i<4 ; i++ )
	{
		odd_width[i] = ele_width[2*i];
		even_width[i] = ele_width[2*i+1];
		
		odd_mods += odd_width[i];
		even_mods += even_width[i];
	}
	
	if ((odd_mods&0x01) || (odd_mods<4) || (odd_mods>12))
	{
		return U16_MAX;
	}
	group = 6-(odd_mods>>1);
	
	Vodd = getRSSvalue(odd_width,4,omni_outside_char[group].odd_widest,1);
	Veven = getRSSvalue(even_width,4,omni_outside_char[group].even_widest,0);
	
	val = Vodd * omni_outside_char[group].Teven + Veven +omni_outside_char[group].Gsum; 
	
	return val;
}


u16 CDecoOneDSubDec::cal_inside_omni(s16 *ele_width)
{
	u16 val;
	u16 odd_width[4], even_width[4];
	u16 odd_mods = 0, even_mods = 0;
	u16 Vodd, Veven;
	u8 group;
	u8 i;
	
	if (!width_normalization(ele_width,15,4,0,2,9))
	{
		return U16_MAX;
	}
	
	for ( i=0 ; i<4 ; i++ )
	{
		odd_width[i] = ele_width[2*i];
		even_width[i] = ele_width[2*i+1];
		
		odd_mods += odd_width[i];
		even_mods += even_width[i];
	}
	
	if ((even_mods&0x01) || (even_mods<4) || (even_mods>10))
	{
		return U16_MAX;
	}
	group = 5-(even_mods>>1);
	
	Vodd = getRSSvalue(odd_width,4,omni_inside_char[group].odd_widest,0);
	Veven = getRSSvalue(even_width,4,omni_inside_char[group].even_widest,1);
	
	val = Veven * omni_inside_char[group].Todd + Vodd +omni_inside_char[group].Gsum; 
	
	return val;
}


u16 CDecoOneDSubDec::cal_check_limi(s16 *ele_width)
{
	u16 val;
	u16 odd_width[6], even_width[6];
	u16 odd_mods = 0, even_mods = 0;
	u16 Vodd, Veven;
	u8 i;
	u16 top,bottom,mid;

	if (!width_normalization(ele_width,18,7,1,2,5))
	{
		return U16_MAX;
	}
	
	for (i=0;i<6;i++)
	{
		odd_width[i] = ele_width[2*i];
		even_width[i] = ele_width[2*i+1];
		
		odd_mods += odd_width[i];
		even_mods += even_width[i];
	}

	if (odd_mods!=8 || even_mods!=8)
	{
		return U16_MAX;
	}

	Vodd = getRSSvalue(odd_width,6,3,1);
	Veven = getRSSvalue(even_width,6,3,1);

	val =  Vodd * 21 + Veven;
	if (val>43)
	{
		bottom = 0;
		top = 44;
		if (val == limited_check[bottom])
		{
			val = 44;
		}
		else if (val == limited_check[top])
		{
			val = 88;
		}
		else
		{
			mid = (top+bottom)>>1;
			while (mid!=bottom)
			{
				if (val == limited_check[mid])
				{
					val = 44+mid;
					break;
				}
				else if (val>limited_check[mid])
				{
					bottom = mid;
				}
				else
				{
					top = mid;
				}
				mid = (top+bottom)>>1;
			}
			if (mid == bottom)
			{
				return U16_MAX;
			}
		}
	}

	return val;
}

u32 CDecoOneDSubDec::cal_data_limi(s16 *ele_width)
{
	u32 val;
	u16 odd_width[7], even_width[7];
	u16 odd_mods = 0, even_mods = 0;
	u16 Vodd, Veven;
	u8 group;
	u8 i;
	
//	S16 temp[14];
//	
//	for (i=0;i<14;i++)
//	{
//		temp[i] = ele_width[i];
//	}
	
	//SYS_Debug_DisplayByte('1');
	
	if (!width_normalization(ele_width,26,7,1,2,9))
	{
		
		//for (i=0;i<14;i++)
		//{
		//	SYS_Debug_DisplayByte((U8)temp[i]);
		//	SYS_Debug_DisplayByte((U8)(temp[i]>>8));
		//	SYS_Debug_DisplayByte(',');
		//}
		return U32_MAX;
	}
	
	//SYS_Debug_DisplayByte('2');
	
	for (i=0;i<7;i++)
	{
		odd_width[i] = ele_width[2*i];
		even_width[i] = ele_width[2*i+1];
		
		odd_mods += odd_width[i];
		even_mods += even_width[i];
	}
	
	//SYS_Debug_DisplayByte('3');
	
	if (((even_mods&0x01)==0) || (even_mods<7) || (even_mods>19))
	{
		return U32_MAX;
	}
	group = 9-(even_mods>>1);
	
	//SYS_Debug_DisplayByte('4');
	/*
	for (i=0;i<7;i++)
	{
		SYS_Debug_Display_U8((U8)odd_width[i]);
	}
	*/
	//SYS_Debug_Display_U8((U8)even_mods);
	//SYS_Debug_Display_U8((U8)group);
	Vodd = getRSSvalue(odd_width,7,limi_char[group].odd_widest,1);
	//SYS_Debug_DisplayByte('5');
	
	Veven = getRSSvalue(even_width,7,limi_char[group].even_widest,0);
	
	//SYS_Debug_DisplayByte('6');
	
	val = Vodd * limi_char[group].Teven + Veven +limi_char[group].Gsum;
	//SYS_Debug_DisplayByte('7');
	return val;
}


u16 CDecoOneDSubDec::cal_finder_omni(s16 *ele_width)
{
	u16 finder_pattern;
	u16 i;
	
	//SYS_Debug_DisplayByte('3');
	
	if (!width_normalization(ele_width,14,2,1,4,12))
	{
		//SYS_Debug_DisplayByte('3');
		return U16_MAX;
	}
	
	//SYS_Debug_DisplayByte('M');
	
	finder_pattern = (ele_width[0]<<12)+(ele_width[1]<<8)+(ele_width[2]<<4)+ele_width[3];
	for (i=0;i<9;i++)
	{
		if (finder_pattern == finder_pattern_Omnidirectional[i])
		{
			break;
		}
	}
	if (i==9)
	{
		return U16_MAX;
	}
	return i;
}


void CDecoOneDSubDec::elements_min_even(s32 eDist[], s16 widths[], u8 N, u8 K) 
{
	s32 i;
	s32 minEven;
	s32 barSum;
	
	/* derive element widths from normalized edge-to-similar-edge measurements */	
	minEven = 10; /* start with a too big minimum */
	barSum = widths[0] = 1; /* first assume 1st bar is 1 */
	
	for (i = 1; i < K*2-2; i += 2) 
	{
		widths[i] = eDist[i-1] - widths[i-1];
		widths[i+1] = eDist[i] - widths[i];
		barSum += widths[i] + widths[i+1];
		if(widths[i] < minEven) 
			minEven = widths[i];
	}
	
	widths[K*2-1] = N - barSum; /* last even element makes N modules */
	
	if (widths[K*2-1] < minEven) 
		minEven = widths[K*2-1];
	
	if(minEven > 1) 
	{
		minEven -= 1;
		/* minimum even width is too big, readjust so minimum even is 1 */
		for (i = 0; i < K*2; i += 2) 
		{
			widths[i] += minEven;
			widths[i+1] -= minEven;
		}
	}
	
	return;
}

void CDecoOneDSubDec::elements_min_odd(s32 eDist[], s16 widths[], u8 N, u8 K) 
{
	s32 i;
	s32 minOdd;
	s32 barSum;
	
	/* derive element widths from normalized edge-to-similar-edge measurements */	
	minOdd = 10; /* start with a too big minimum */
	barSum = widths[0] = eDist[0]-1; /* first assume 2nd bar is 1 */
	
	for (i = 1; i < K*2-2; i += 2) 
	{
		widths[i] = eDist[i-1] - widths[i-1];
		widths[i+1] = eDist[i] - widths[i];
		barSum += widths[i] + widths[i+1];
		if(widths[i-1] < minOdd) 
			minOdd = widths[i-1];
	}
	
	widths[K*2-1] = N - barSum; /* last even element makes N modules */
	
	if (widths[K*2-2] < minOdd)
		minOdd = widths[K*2-2];
	
	if(minOdd > 1) 
	{
		minOdd -= 1;
		/* minimum odd width is too big, readjust so minimum odd is 1 */
		for (i = 0; i < K*2; i += 2) 
		{
			widths[i] -= minOdd;
			widths[i+1] += minOdd;
		}
	}
	return;
}

s32 CDecoOneDSubDec::width_normalization( s16 ele_width[], u8 N, u8 K, u8 mini_even, u8 same_edge_min, u8 same_edge_max )
{
	s32 eDist[14], pDist = 0, pDist_half/*,eDist_k[14]*/;
	u8 eDist_num;
	u8 i,j;
	
//	__asm {NOP;NOP;NOP;}
	eDist_num = 2*K-2;
	
	//SYS_Debug_Display_U32(eDist_num);
	
	for (i=0;i<eDist_num;i++)
	{
		eDist[i] = (ele_width[i] + ele_width[i+1]) * N;
		pDist += ele_width[i];
	}
	pDist += ele_width[i] + ele_width[i+1];
	pDist_half = (pDist+1)>>1;
	
	//SYS_Debug_Display_U32(pDist);
	
	for(i=0; i<eDist_num; i++)
	{
				
		eDist[i] -= (same_edge_min-1)*pDist + pDist_half;
		
		
		if (eDist[i]<0)
		{
			return 0;
		}
		for (j=0;j<=(same_edge_max-same_edge_min);j++)
		{
			if (eDist[i]<pDist)
			{
				
				eDist[i] = j+same_edge_min;
				break;
			}
			eDist[i] -= pDist;
		}
		
		if ( j>(same_edge_max-same_edge_min) )
		{
		return 0;
		}
	}
	
	//SYS_Debug_Display_U32(ele_width[0]);
	//SYS_Debug_Display_U32(ele_width[1]);
	
	//for(i=0; i<eDist_num; i++)
	//{
	//	eDist[i] = ele_width[i];
	//}
	
	
	if (mini_even)
	{
		elements_min_even(eDist, ele_width, N, K);
	}
	else
	{
		elements_min_odd(eDist, ele_width, N, K);
	}
	
	for (i=0;i<2*K;i++)
	{
		if (ele_width[i]<1)
		{
			return 0;
		}
	}
	
	return 1;
}

u32 CDecoOneDSubDec::combins(s32 n, u32 r) 
{
	if (n<0)
	{
		return 0;
	}
	else
	{
		return combin[n][r];
	}
	
	/*
	S32 i, j;
	S32 maxDenom, minDenom;
	S32 val;

	if(n<0)
		return 0;

	if (n-r > r) 
	{
		minDenom = r;
		maxDenom = n-r;
	}
	else 
	{
		minDenom = n-r;
		maxDenom = r;
	}
	
	val = 1;	

	j = 1;

	for (i = n; i > maxDenom; i--) 
	{
		val *= i;
		if (j <= minDenom) 
		{
			if(j==0)
				return 0;

			val /= j;
			j++;
		}
	}

	for ( ; j <= minDenom; j++) 
	{
		if(j==0)
			return 0;

		val /= j;
	}

	return(val);
	*/
}

u32 CDecoOneDSubDec::getRSSvalue(u16 widths[], u8 elements, u8 maxWidth, u8 noNarrow)
{
	s32 val = 0;
	s32 n;
	s32 bar;
	s32 elmWidth;
	s32 i;
	s32 mxwElement;
	s32 subVal, lessVal;
	s32 narrowMask = 0;

	for (n = i = 0; i < elements; i++)
	{
		n += widths[i];
	}

	for (bar = 0; bar < elements-1; bar++)
	{
		for (elmWidth = 1, narrowMask |= (1<<bar); elmWidth < widths[bar]; elmWidth++, narrowMask &= ~(1<<bar)) 
		{
			/* get all nk combinations */
			subVal = combins(n-elmWidth-1, elements-bar-2);

			/* less combinations with no narrow */
			if ((!noNarrow) && (narrowMask == 0) && (n-elmWidth-(elements-bar-1)>= elements-bar-1))
			{
				subVal -= combins(n-elmWidth-(elements-bar), elements-bar-2);
			}

			/* less combinations with elements > maxVal */
			if (elements-bar-1 > 1)
			{
				lessVal = 0;
				
				for (mxwElement = n-elmWidth-(elements-bar-2); mxwElement > maxWidth; mxwElement--)
				{
					lessVal += combins(n-elmWidth-mxwElement-1, elements-bar-3);
				}
				
				subVal -= lessVal * (elements-1-bar);
			}
			else if (n-elmWidth > maxWidth)
			{
				subVal--;
			}
			
			val += subVal;
		}
		
		n -= elmWidth;
	}
	
	return(val);
}

u16 CDecoOneDSubDec::get_nbits(struct App_GS1_Dec_Struct* GS1_Dec_DataPt,u8 n, u16 data)
{
	if (n > GS1_Dec_DataPt->remain_bits_num)
	{
		return U16_MAX;
	}
	GS1_Dec_DataPt->remain_bits_num -= n;
	
	if (n>GS1_Dec_DataPt->bit_index)
	{
		data = (data<<(GS1_Dec_DataPt->bit_index)) + GS1_Dec_DataPt->symbol_value[GS1_Dec_DataPt->sym_index];
		GS1_Dec_DataPt->sym_index++;
		n -= GS1_Dec_DataPt->bit_index;
		GS1_Dec_DataPt->bit_index = 12;
	}
	
	GS1_Dec_DataPt->bit_index -= n;
	data = (data<<n) + ((GS1_Dec_DataPt->symbol_value[GS1_Dec_DataPt->sym_index])>>(GS1_Dec_DataPt->bit_index));
	if (GS1_Dec_DataPt->bit_index==0)
	{
		GS1_Dec_DataPt->sym_index++;
		GS1_Dec_DataPt->bit_index = 12;
	}
	else
	{
		GS1_Dec_DataPt->symbol_value[GS1_Dec_DataPt->sym_index] &= (1<<(GS1_Dec_DataPt->bit_index))-1;
	}

	return data;
}


s8 CDecoOneDSubDec::decodation_numeric(struct App_GS1_Dec_Struct* GS1_Dec_DataPt)
{
	u8 digits;
	u8 char1,char2;

	while ((GS1_Dec_DataPt->remain_bits_num)>=7)
	{
		digits = get_nbits(GS1_Dec_DataPt,4,0);
		if (digits)
		{
			digits = get_nbits(GS1_Dec_DataPt,3,digits);
			digits -= 8;
			char1 = digits/11+'0';
			char2 = digits%11+'0';
			//':' = '0'+10
			if (char1==':')
			{
				char1 = GS;
			}
			if (char2==':')
			{
				char2 = GS;
			}
			GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = char1;
			GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = char2;
		} 
		else
		{
			return SCHEME_ALP;
		}
	}

	if (GS1_Dec_DataPt->remain_bits_num>=4)
	{
		digits = get_nbits(GS1_Dec_DataPt,4,0);
		if (digits)
		{
			GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = digits-1+'0';
		}
	}
	return SCHEME_ALP;
}


s8 CDecoOneDSubDec::decodation_alphanumeric(struct App_GS1_Dec_Struct* GS1_Dec_DataPt)
{
	u8 digits;
	
	while (GS1_Dec_DataPt->remain_bits_num>=5)
	{
		digits = get_nbits(GS1_Dec_DataPt,1,0);
		if (digits)
		{
			digits = get_nbits(GS1_Dec_DataPt,5,digits); // digits = 0x1yyyyy
			if (digits<=57) // 111001b
			{
				GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = digits+33;
			} 
			else if (digits==58) // 111010b
			{
				GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = '*';
			}
			else
			{
				GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = digits-15;
			}
		}
		else
		{
			digits = get_nbits(GS1_Dec_DataPt,2,digits);
			if (digits)
			{
				digits = get_nbits(GS1_Dec_DataPt,2,digits); // digits = 0x0yyyy
				if (digits==4) // 00100b
				{
					return SCHEME_ISO;
				}
				else if (digits<=14) // 01110b
				{
					GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = digits+43;
				}
				else
				{
					GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = GS;
					return SCHEME_NUM;
				}
			}
			else
			{
				return SCHEME_NUM; // digits = 0x000
			}
		}
	}
	return SCHEME_ISO;
}

s8 CDecoOneDSubDec::decodation_ISO(struct App_GS1_Dec_Struct* GS1_Dec_DataPt)
{
	u8 digits;
	
	while (GS1_Dec_DataPt->remain_bits_num>=5)
	{
		digits = get_nbits(GS1_Dec_DataPt,3,0);
		if (digits)
		{
			digits = get_nbits(GS1_Dec_DataPt,2,digits);
			if (digits<=15)
			{
				// 00100b -- 01111b
				if (digits==4) // 00100b
				{
					return SCHEME_ALP;
				}
				else if (digits<=14) // 01110b
				{
					GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = digits+43;
				}
				else
				{
					GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = GS;
					return SCHEME_NUM;
				}
			}
			else if (digits<=28) // 11100b
			{
				digits = get_nbits(GS1_Dec_DataPt,2,digits); // 1000000b -- 1110011b
				if (digits<=89) // 1011001b
				{
					GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = digits+1;
				}
				else
				{
					GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = digits+7;
				}
			}
			else
			{
				digits = get_nbits(GS1_Dec_DataPt,3,digits); // 11101000b -- 11111111b
				if (digits<=252) // 11101000b -- 11111100b
				{
					GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = ISO_char_232_252[digits-232];
				}
				else
				{
					return 0;
				}
			}
		}
		else
		{
			return SCHEME_NUM; //0x000
		}
	}
	return SCHEME_ALP;
}


u16 CDecoOneDSubDec::decompaction_gp(struct App_GS1_Dec_Struct* GS1_Dec_DataPt)
{
	s8 scheme = SCHEME_NUM;

	while (GS1_Dec_DataPt->remain_bits_num>=5)
	{
		switch(scheme)
		{
		case SCHEME_NUM:
			scheme = decodation_numeric(GS1_Dec_DataPt);
			break;
		case SCHEME_ALP:
			scheme = decodation_alphanumeric(GS1_Dec_DataPt);
			break;
		case SCHEME_ISO:
			scheme = decodation_ISO(GS1_Dec_DataPt);
			break;
		default:
			break;
		}
	}
	return GS1_Dec_DataPt->msg_index;
}


s32 CDecoOneDSubDec::decompress_method1(struct App_GS1_Dec_Struct* GS1_Dec_DataPt)
{
	u16 digits;
	u8 AI01[13];

	u8 i,j;
	
	if (GS1_Dec_DataPt->symbol_character_num<5)
	{
		return 0;
	}

	digits = get_nbits(GS1_Dec_DataPt,1,0);
	if ((GS1_Dec_DataPt->symbol_character_num&0x01)!=digits)
	{
		return 0;
	}
	digits = get_nbits(GS1_Dec_DataPt,1,0);
	if ((GS1_Dec_DataPt->symbol_character_num>14)!=digits)
	{
		return 0;
	}

	GS1_Dec_DataPt->result_ary[0] = '0';
	GS1_Dec_DataPt->result_ary[1] = '1';
	digits = get_nbits(GS1_Dec_DataPt,4,0);
	if (digits>9)
	{
		return 0;
	}
	GS1_Dec_DataPt->result_ary[2] = digits+'0';
	AI01[0] = digits;

	GS1_Dec_DataPt->msg_index = 3;
	j = 1;
	for (i=0;i<4;i++)
	{
		digits = get_nbits(GS1_Dec_DataPt,10,0);
		if (digits>999)
		{
			return 0;
		}
		AI01[j] = digits/100;
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = AI01[j++]+'0';
		digits %=100;
		AI01[j] = digits/10;
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = AI01[j++]+'0';
		AI01[j] = digits%10;
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = AI01[j++]+'0';
	}
	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = mod10_check(AI01)+'0';

	decompaction_gp(GS1_Dec_DataPt);
	return GS1_Dec_DataPt->msg_index;
}


s32 CDecoOneDSubDec::decompress_method00(struct App_GS1_Dec_Struct* GS1_Dec_DataPt)
{
	u16 digits;

	if (GS1_Dec_DataPt->symbol_character_num<4)
	{
		return 0;
	}
	
	digits = get_nbits(GS1_Dec_DataPt,1,0);
	if (((GS1_Dec_DataPt->symbol_character_num)&0x01)!=digits)
	{
		return 0;
	}
	digits = get_nbits(GS1_Dec_DataPt,1,0);
	if ((GS1_Dec_DataPt->symbol_character_num>14)!=digits)
	{
		return 0;
	}

	decompaction_gp(GS1_Dec_DataPt);
	return GS1_Dec_DataPt->msg_index;
}


s32 CDecoOneDSubDec::decompress_method010x(struct App_GS1_Dec_Struct* GS1_Dec_DataPt)
{
	u8 method;
	u16 digits;
	u8 AI01[13];

	u8 i,j;

	if (GS1_Dec_DataPt->symbol_character_num!=6)
	{
		return 0;
	}

	method = get_nbits(GS1_Dec_DataPt,1,0);

	GS1_Dec_DataPt->result_ary[0] = '0';
	GS1_Dec_DataPt->result_ary[1] = '1';
	GS1_Dec_DataPt->result_ary[2] = '9';
	GS1_Dec_DataPt->msg_index = 3;
	AI01[0] = 9;
	j = 1;
	for (i=0;i<4;i++)
	{
		digits = get_nbits(GS1_Dec_DataPt,10,0);
		if (digits>999)
		{
			return 0;
		}
		AI01[j] = digits/100;
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = AI01[j++]+'0';
		digits %=100;
		AI01[j] = digits/10;
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = AI01[j++]+'0';
		AI01[j] = digits%10;
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = AI01[j++]+'0';
	}
	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = mod10_check(AI01)+'0';

	digits = get_nbits(GS1_Dec_DataPt,8,0);
	digits = get_nbits(GS1_Dec_DataPt,7,digits);

	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = '3';
	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = method+'1';
	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = '0';
	if (method)
	{
		if (digits<10000)
		{
			GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = '2';
		}
		else
		{
			GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = '3';
		}
	}
	else
	{
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = '3';
	}
	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = '0';
	
	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = digits/10000+'0';
	digits %=10000;
	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = digits/1000+'0';
	digits %=1000;
	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = digits/100+'0';
	digits %=100;
	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = digits/10+'0';
	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = digits%10+'0';

	app_decoder_data.CodeResult[(GS1_Dec_DataPt->msg_index)++] = '\0';

	return GS1_Dec_DataPt->msg_index;
}


s32 CDecoOneDSubDec::decompress_method0110x(struct App_GS1_Dec_Struct* GS1_Dec_DataPt)
{
	u8 method;
	u16 digits;
	u8 AI01[13];
		
	u8 i,j;
	
	method = get_nbits(GS1_Dec_DataPt,1,0);

	if (GS1_Dec_DataPt->symbol_character_num<(6+method))
	{
		return 0;
	}

	digits = get_nbits(GS1_Dec_DataPt,1,0);
	if ((GS1_Dec_DataPt->symbol_character_num&0x01)!=digits)
	{
		return 0;
	}
	digits = get_nbits(GS1_Dec_DataPt,1,0);
	if ((GS1_Dec_DataPt->symbol_character_num>14)!=digits)
	{
		return 0;
	}

	GS1_Dec_DataPt->result_ary[0] = '0';
	GS1_Dec_DataPt->result_ary[1] = '1';
	GS1_Dec_DataPt->result_ary[2] = '9';
	GS1_Dec_DataPt->msg_index = 3;
	AI01[0] = 9;
	j = 1;
	for (i=0;i<4;i++)
	{
		digits = get_nbits(GS1_Dec_DataPt,10,0);
		if (digits>999)
		{
			return 0;
		}
		AI01[j] = digits/100;
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = AI01[j++]+'0';
		digits %=100;
		AI01[j] = digits/10;
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = AI01[j++]+'0';
		AI01[j] = digits%10;
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = AI01[j++]+'0';
	}
	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = mod10_check(AI01)+'0';

	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = '3';
	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = '9';
	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = method+'2';
	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = get_nbits(GS1_Dec_DataPt,2,0)+'0';
	if (method)
	{
		digits = get_nbits(GS1_Dec_DataPt,10,0);
		if (digits>999)
		{
			return 0;
		}
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = digits/100+'0';
		digits %=100;
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = digits/10+'0';
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = digits%10+'0';
	}
	decompaction_gp(GS1_Dec_DataPt);
	return GS1_Dec_DataPt->msg_index;
}


s32 CDecoOneDSubDec::decompress_method0111x(struct App_GS1_Dec_Struct* GS1_Dec_DataPt)
{
	u8 method;
	u16 digits;
	u32 digits_32;
	u8 TT;
	u8 AI01[13];
	
	u8 i,j;

	if (GS1_Dec_DataPt->symbol_character_num!=8)
	{
		return 0;
	}

	method = get_nbits(GS1_Dec_DataPt,3,0);

	GS1_Dec_DataPt->result_ary[0] = '0';
	GS1_Dec_DataPt->result_ary[1] = '1';
	GS1_Dec_DataPt->result_ary[2] = '9';
	GS1_Dec_DataPt->msg_index = 3;
	AI01[0] = 9;
	j = 1;
	for (i=0;i<4;i++)
	{
		digits = get_nbits(GS1_Dec_DataPt,10,0);
		if (digits>999)
		{
			return 0;
		}
		AI01[j] = digits/100;
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = AI01[j++]+'0';
		digits %=100;
		AI01[j] = digits/10;
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = AI01[j++]+'0';
		AI01[j] = digits%10;
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = AI01[j++]+'0';
	}
	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = mod10_check(AI01)+'0';

	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = '3';
	if (method&1)
	{
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = '2';
	}
	else
	{
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = '1';
	}
	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = '0';
	digits_32 = get_nbits(GS1_Dec_DataPt,10,0);
	digits_32 = digits_32<<10;
	digits_32 += get_nbits(GS1_Dec_DataPt,10,0);
	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = digits_32/100000+'0';
	digits_32 %=100000;
	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = '0';
	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = digits_32/10000+'0';
	digits_32 %=10000;
	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = digits_32/1000+'0';
	digits_32 %=1000;
	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = digits_32/100+'0';
	digits_32 %=100;
	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = digits_32/10+'0';
	GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = digits_32%10+'0';

	digits = get_nbits(GS1_Dec_DataPt,8,0);
	digits = get_nbits(GS1_Dec_DataPt,8,digits);
	if (digits<38400)
	{
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = '1';
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = (method|0x01)+'0';
		
		TT = digits/384;
		digits %= 384;
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = TT/10+'0';
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = TT%10+'0';

		TT = digits/32;
		TT++;
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = TT/10+'0';
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = TT%10+'0';

		TT = digits%32;
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = TT/10+'0';
		GS1_Dec_DataPt->result_ary[(GS1_Dec_DataPt->msg_index)++] = TT%10+'0';
	}
	app_decoder_data.CodeResult[(GS1_Dec_DataPt->msg_index)++] = '\0';
	return GS1_Dec_DataPt->msg_index;
}


u8 CDecoOneDSubDec::mod10_check(u8 *datain)
{
	u8 check = 0;
	u8 i;
	
	for (i=0;i<13;i+=2)
	{
		check += datain[i];
	}
	check *= 3;
	for (i=1;i<13;i+=2)
	{
		check += datain[i];
	}
	check %= 10;
	if (check)
	{
		check = 10-check;
	}
	return check;
}

u8 CDecoOneDSubDecode::conver_UPC_EAN(void)
{
	u8 i=0;
	
	if (CDecoOneDDecObj.app_decoder_data.CodeResult[2] == '0')
	{
		if (CDecoOneDDecObj.app_decoder_data.CodeResult[3] == '0')
		{
			for (;i<13;i++)
			{
				CDecoOneDDecObj.app_decoder_data.CodeResult[i] = CDecoOneDDecObj.app_decoder_data.CodeResult[i+4];
			}
			CDecoOneDDecObj.app_decoder_data.CodeDataLength_G -= 4;
			return 3;
		}
		else
		{
			for (;i<14;i++)
			{
				CDecoOneDDecObj.app_decoder_data.CodeResult[i] = CDecoOneDDecObj.app_decoder_data.CodeResult[i+3];
			}
			CDecoOneDDecObj.app_decoder_data.CodeDataLength_G -= 3;
			return 2;
		}
	}
	return 0;
}


u8 CDecoOneDSubDecode::Omni_ResultConver(void)
{
	if (gGS1DB_Conversion == MDFLAG_GS1DB_Conversion_None)
	{
		return 0;
	}
	else if (gGS1DB_Conversion == MDFLAG_GS1DB_Conversion_UCCEAN128)
	{
		return 1;
	}
	else if (gGS1DB_Conversion == MDFLAG_GS1DB_Conversion_UPCAEAN13)
	{
		return conver_UPC_EAN();
	}
	return 0;
}


u8 CDecoOneDSubDecode::Limi_ResultConver(void)
{
	if (gGS1LIMI_Conversion == MDFLAG_GS1LIMI_Conversion_None)
	{
		return 0;
	}
	else if (gGS1LIMI_Conversion == MDFLAG_GS1LIMI_Conversion_UCCEAN128)
	{
		return 1;
	}
	else if (gGS1LIMI_Conversion == MDFLAG_GS1LIMI_Conversion_UPCAEAN13)
	{
		return conver_UPC_EAN();
	}
	return 0;
}


u8 CDecoOneDSubDecode::Expa_ResultConver(void)
{
	if (gGS1EXPA_Conversion == MDFLAG_GS1EXPA_Conversion_None)
	{
		return 0;
	}
	else if (gGS1EXPA_Conversion == MDFLAG_GS1EXPA_Conversion_UCCEAN128)
	{
		return 1;
	}
	return 0;
}
