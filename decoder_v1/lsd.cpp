#include "decoder_config.h"
#ifdef CV_DEBUG
#include "cv.h"
#include "highgui.h"
#endif

#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include <string.h>
#include "mdtypes.h"
#include "decoder_memory.h"
#include "lsd.h"
#include "md_math.h"


#ifndef M_LN10
#define M_LN10 2.30258509299404568402
#endif /* !M_LN10 */

#ifndef M_PI
#define M_PI   3.14159265358979323846
#endif /* !M_PI */

#ifndef FALSE
#define FALSE 0
#endif /* !FALSE */

#ifndef TRUE
#define TRUE 1
#endif /* !TRUE */

#define NOTDEF -300
#define M_3_2_PI 4.71238898038
#define M_2__PI  6.28318530718
#define NOTUSED 0
#define USED    1
#define TAN_SHIFT 18


void CDecoCommonLSD::free_image_char(image_char i)
{
  if( i == NULL || i->data == NULL )
    return;//error("free_image_char: invalid input image.");
  pCDecoMemObj->mdMemFree(i->data);
}

image_char CDecoCommonLSD::new_image_char(unsigned int xsize, unsigned int ysize)
{
  image_char image;

  if( xsize == 0 || ysize == 0 ) 
	  return 0;//error("new_image_char: invalid image size.");

	image = (image_char)pCDecoMemObj->mdMemAlloc( sizeof(struct image_char_s),STRUCT_ALIGN,MEM_ALLOC_PTR );
  if( image == NULL ) 
	  return 0;//error("not enough memory.");
	image->data = (unsigned char *)pCDecoMemObj->mdMemAlloc( xsize*ysize, sizeof(unsigned char),MEM_ALLOC_BLOCK );
  if( image->data == NULL )
	  return 0;

  image->xsize = xsize;
  image->ysize = ysize;

  return image;
}


void CDecoCommonLSD::free_image_int(image_int i)
{
  if( i == NULL || i->data == NULL )
   return ;// error("free_image_int: invalid input image.");

  pCDecoMemObj->mdMemFree(i->data);
}

image_int CDecoCommonLSD::new_image_int(unsigned int xsize, unsigned int ysize)
{
  image_int image;

  if( xsize == 0 || ysize == 0 ) 
	  return (image_int)0;//error("new_image_int: invalid image size.");

	image = (image_int)pCDecoMemObj->mdMemAlloc( sizeof(struct image_int_s) ,STRUCT_ALIGN,MEM_ALLOC_PTR);
  if( image == NULL ) 
	  return (image_int)0;//error("not enough memory.");
	image->data = (int *)pCDecoMemObj->mdMemAlloc( xsize*ysize*sizeof(int), sizeof(int) ,MEM_ALLOC_BLOCK);
  if( image->data == NULL ) 
	  return (image_int)0;//error("not enough memory.");

  image->xsize = xsize;
  image->ysize = ysize;

  return image;
}

void CDecoCommonLSD::free_image_double(image_double i)
{
  if( i == NULL || i->data == NULL )
    return;//error("free_image_double: invalid input image.");
  pCDecoMemObj->mdMemFree(i->data);
}


image_double CDecoCommonLSD::new_image_double(unsigned int xsize, unsigned int ysize)
{
  image_double image;

  if( xsize == 0 || ysize == 0 ) return (image_double)0;//error("new_image_double: invalid image size.");
	image = (image_double)pCDecoMemObj->mdMemAlloc( sizeof(struct image_double_s),STRUCT_ALIGN,MEM_ALLOC_PTR );
  if( image == NULL ) 
	  return (image_double)0;//error("not enough memory.");
	image->data = (double *)pCDecoMemObj->mdMemAlloc( xsize * ysize*sizeof(double), sizeof(double),MEM_ALLOC_BLOCK);
  if( image->data == NULL ) 
	  return (image_double)0;//error("not enough memory.");

  image->xsize = xsize;
  image->ysize = ysize;

  return image;
}

int CDecoCommonLSD::mbisearch(int value)
{
	int i;
	const int n = 90;
	int low = 0;
	int high = n-1;
	int mid;
	static int init=0;
	static int table[90];

	if (0 == init)
	{
		for (i=0;i<n;i++)
		{
			table[i] = (int)(tan((i+0.5)*M_PI/180)*(1<<TAN_SHIFT));
		}
		++init;
	}
	
	if (value <= table[0])
	{
		return 0;
	}
	if (value > table[n-1])
	{
		return 90;
	}

	while(low < high-1)
	{
		mid = (low+high)/2;
		if (value < table[mid])
		{
			high = mid;
		}
		else if (value > table[mid])
		{
			low = mid;
		}
		else
		{
			return mid;
		}
	}
	
	return high;
}

int CDecoCommonLSD::matan2(int dy,int dx)
{
	int flags = 0;
	int value;
	int angle;

	if (dy < 0)
	{
		dy = -dy;
		flags |= 1;
	}
	if (dx < 0)
	{
		dx = -dx;
		flags |= 1<<1;
	}

	if (0 == dx)
	{
		angle = dy > 0 ? 90 : -90;
	}
	else
	{
		value = ((dy<<TAN_SHIFT)+(dx>>1))/dx;	
		angle = mbisearch(value);
	}

	if ((flags&1) && (flags&2))
	{
		angle = angle - 180;
	}
	else if (flags&1)
	{
		angle = -angle;
	}
	else if (flags&2)
	{
		angle = 180 - angle;
	}

	return angle;
}

double CDecoCommonLSD::mcos(int angle)
{
	const int n=91;
	static int init=0;
	static double table[90];

	if ( 0 == init )
	{
		int i;
		for (i=0;i<n;i++)
		{
			table[i]=cos(i*M_PI/180);
		}
	}
	
	if (angle < -180 || angle > 180)
	{
		return 0; //error
	}

	if (angle > 90 && angle <= 180)
	{
		return -table[180 - angle];
	}
	else if (angle >= -180 && angle < -90)
	{
		return -table[180 + angle];
	}
	else if (angle >= -90 && angle < 0)
	{
		return table[-angle];
	}

	return table[angle];
}

double CDecoCommonLSD::msin(int angle)
{
	int theta = 90 - angle;

	if (theta < -180)
	{
		theta += 360;
	}
	else if (theta > 180)
	{
		theta -= 360;
	}

	return mcos(theta);
}

image_int CDecoCommonLSD::ll_angle1( image_char in, int _threshold,
                              struct coorlist ** list_p, void ** mem_p,
                              image_char * modgrad, unsigned int n_bins,
                              int _max_grad )
{
  image_int g;
  unsigned int n,p,x,y,adr;
  unsigned int i;
  int com1,com2,gx,gy,norm1;
  int threshold = _threshold;
  int max_grad = _max_grad;

  /* the rest of the variables are used for pseudo-ordering
     the gradient magnitude values */
  int list_count = 0;
  struct coorlist * list;
  struct coorlist ** range_l_s; /* array of pointers to start of bin list */
  struct coorlist ** range_l_e; /* array of pointers to end of bin list */
  struct coorlist * start;
  struct coorlist * end;

  CDecoMath CDecoMathObj = CDecoMath(pCDecoMemObj);

  /* check parameters */
  if( in == NULL || in->data == NULL || in->xsize <= 0 || in->ysize <= 0 )
	  return (image_int)0;//error("ll_angle: invalid image.");
  if( threshold < 0.0 ) return (image_int)0;//error("ll_angle: 'threshold' must be positive.");
  if( list_p == NULL ) return (image_int)0;//error("ll_angle: NULL pointer 'list_p'.");
  if( mem_p == NULL ) return (image_int)0;//error("ll_angle: NULL pointer 'mem_p'.");
  if( modgrad == NULL ) return (image_int)0;//error("ll_angle: NULL pointer 'modgrad'.");
  if( n_bins <= 0 ) return (image_int)0;//error("ll_angle: 'n_bins' must be positive.");
  if( max_grad <= 0.0 ) return (image_int)0;//error("ll_angle: 'max_grad' must be positive.");

  n = in->ysize;
  p = in->xsize;

  /* allocate output image */
  g = new_image_int(in->xsize,in->ysize);
#if MEMORY_CHECK_ENABLE
   if (g==0)
      return 0;
#endif  

  /* get memory for the image of gradient modulus */
  *modgrad = new_image_char(in->xsize,in->ysize);
#if MEMORY_CHECK_ENABLE
  if (*modgrad==0)
  {
	  pCDecoMemObj->mdMemFree(g->data);
	  return 0;
  }
#endif 
  /* get memory for "ordered" coordinate list */
  list = (struct coorlist *)pCDecoMemObj->mdMemAlloc(n*p*sizeof(struct coorlist),STRUCT_ALIGN,MEM_ALLOC_BLOCK);
#if MEMORY_CHECK_ENABLE
  if (list==0)
  {
	  pCDecoMemObj->mdMemFree(g->data);
	  pCDecoMemObj->mdMemFree((*modgrad)->data);
	  return 0;
  }
#endif  
  memset(list,0,n*p*sizeof(struct coorlist));
  *mem_p = (void *) list;
  range_l_s = (struct coorlist **)pCDecoMemObj->mdMemAlloc(n_bins*sizeof(struct coorlist *),sizeof(struct coorlist *),MEM_ALLOC_BLOCK);
#if MEMORY_CHECK_ENABLE
  if (range_l_s==0)
  {
	  pCDecoMemObj->mdMemFree(g->data);
	  pCDecoMemObj->mdMemFree((*modgrad)->data);
	  pCDecoMemObj->mdMemFree(list);
	  return 0;
  }
#endif 
  memset(range_l_s,0,n_bins*sizeof(struct coorlist *));
  range_l_e = (struct coorlist **)pCDecoMemObj->mdMemAlloc(n_bins*sizeof(struct coorlist *),sizeof(struct coorlist *),MEM_ALLOC_BLOCK);
#if MEMORY_CHECK_ENABLE
  if (range_l_e==0)
  {
	  pCDecoMemObj->mdMemFree(g->data);
	  pCDecoMemObj->mdMemFree((*modgrad)->data);
	  pCDecoMemObj->mdMemFree(list);
	  pCDecoMemObj->mdMemFree(range_l_s);
	  return 0;
  }
#endif 
  memset(range_l_e,0,n_bins*sizeof(struct coorlist *));
  if( list == NULL || range_l_s == NULL || range_l_e == NULL )
    return (image_int)0;//error("not enough memory.");
  for(i=0;i<n_bins;i++) 
	  range_l_s[i] = range_l_e[i] = NULL;

  /* 'undefined' on the down and right boundaries */
  for(x=0;x<p;x++) g->data[(n-1)*p+x] = NOTDEF;
  for(y=0;y<n;y++) g->data[p*y+p-1]   = NOTDEF;
  for(x=0;x<p;x++) g->data[x] = NOTDEF;
  for(y=0;y<n;y++) g->data[p*y]   = NOTDEF;
  /*** remaining part ***/
  for(x=1;x<p-1;x++)
    for(y=1;y<n-1;y++)
      {
        adr = y*p+x;
        /*
           Norm 2 computation using 2x2 pixel window:
             A B
             C D
           and
             com1 = D-A,  com2 = B-C.
           Then
             gx = B+D - (A+C)   horizontal difference
             gy = C+D - (A+B)   vertical difference
           com1 and com2 are just to avoid 2 additions.
         */
        com1 = in->data[adr+p+1] - in->data[adr];
        com2 = in->data[adr+1]   - in->data[adr+p];
        gx = com1+com2;
        gy = com1-com2;
        //norm2 = gx*gx+gy*gy;
		norm1 = CDecoMathObj.md_isqrt( (gx*gx+gy*gy) >> 2 );
        //norm = sqrt( norm2 / 4.0 );
        (*modgrad)->data[adr] = norm1;

        if( norm1 < threshold ) /* norm too small, gradient no defined */
          g->data[adr] = NOTDEF;
        else
          {
            /* angle computation */
            g->data[adr] = matan2(gy,gx);//g->data[adr] = atan2(gx,-gy);
			//g->data[adr] = (int)(atan2(gy,gx)*180/M_PI);

            /* store the point in the right bin according to its norm */
            i = (norm1 * n_bins / max_grad);
            if( i >= n_bins ) i = n_bins-1;
            if( range_l_e[i] == NULL )
              range_l_s[i] = range_l_e[i] = list+list_count++;
            else
              {
                range_l_e[i]->next = list+list_count;
                range_l_e[i] = list+list_count++;
              }
            range_l_e[i]->x = (int) x;
            range_l_e[i]->y = (int) y;
            range_l_e[i]->next = NULL;
          }
      }

  /* Make the list of points "ordered" by norm value.
     It starts by the larger bin, so the list starts by the
     pixels with higher gradient value.
   */
  for(i=n_bins-1; i>0 && range_l_s[i]==NULL; i--);
  start = range_l_s[i];
  end = range_l_e[i];
  if( start != NULL )
    for(i--;i>0; i--)
      if( range_l_s[i] != NULL )
        {
          end->next = range_l_s[i];
          end = range_l_e[i];
        }
  *list_p = start;

  /* free memory */
  pCDecoMemObj->mdMemFree( (void *) range_l_s );
  pCDecoMemObj->mdMemFree( (void *) range_l_e );

  return g;
}

// image_int ll_angle3( image_char in, int _threshold,
//                               struct coorlist ** list_p, void ** mem_p,
// 							  unsigned int n_bins,int _max_grad )
// {
//   image_int g;
//   unsigned int n,p,x,y,adr;
//   unsigned int i;
//   int com1,com2,gx,gy,norm2;
//   int threshold = 2*_threshold;
//   int max_grad = 4*_max_grad;
// 
//   /* the rest of the variables are used for pseudo-ordering
//      the gradient magnitude values */
//   int list_count = 0;
//   struct coorlist * list;
//   struct coorlist ** range_l_s; /* array of pointers to start of bin list */
//   struct coorlist ** range_l_e; /* array of pointers to end of bin list */
//   struct coorlist * start;
//   struct coorlist * end;
// 
//   /* check parameters */
//   if( in == NULL || in->data == NULL || in->xsize <= 0 || in->ysize <= 0 )
//      return (image_int)0;//error("ll_angle: invalid image.");
//   if( threshold < 0.0 ) return (image_int)0;//error("ll_angle: 'threshold' must be positive.");
//   if( list_p == NULL ) return (image_int)0;//error("ll_angle: NULL pointer 'list_p'.");
//   if( mem_p == NULL ) return (image_int)0;//error("ll_angle: NULL pointer 'mem_p'.");
//   if( n_bins <= 0 ) return (image_int)0;//error("ll_angle: 'n_bins' must be positive.");
//   if( max_grad <= 0.0 ) return (image_int)0;//error("ll_angle: 'max_grad' must be positive.");
// 
//   n = in->ysize;
//   p = in->xsize;
// 
//   /* allocate output image */
//   g = new_image_int(in->xsize,in->ysize);
// 
//   /* get memory for "ordered" coordinate list */
//   //list = (struct coorlist *) calloc(n*p,sizeof(struct coorlist));
//   list = (struct coorlist *) mdMemAlloc(n*p*sizeof(struct coorlist),STRUCT_ALIGN,MEM_ALLOC_BLOCK);
//   memset(list,0,n*p*sizeof(struct coorlist));
//   *mem_p = (void *) list;
//   range_l_s = (struct coorlist **) mdMemAlloc(n_bins*sizeof(struct coorlist *),sizeof(struct coorlist *),MEM_ALLOC_BLOCK);
//   memset(range_l_s,0,n_bins*sizeof(struct coorlist *));
//   range_l_e = (struct coorlist **) mdMemAlloc(n_bins*sizeof(struct coorlist *),sizeof(struct coorlist *),MEM_ALLOC_BLOCK);
//   memset(range_l_e,0,n_bins*sizeof(struct coorlist *));
//   if( list == NULL || range_l_s == NULL || range_l_e == NULL )
//    return (image_int)0;//error("not enough memory.");
//   for(i=0;i<n_bins;i++) 
// 	  range_l_s[i] = range_l_e[i] = NULL;
// 
//   /* 'undefined' on the down and right boundaries */
//   for(x=0;x<p;x++) g->data[(n-1)*p+x] = NOTDEF;
//   for(y=0;y<n;y++) g->data[p*y+p-1]   = NOTDEF;
// 
//   for(x=0;x<p;x++) g->data[x] = NOTDEF;
//   for(y=0;y<n;y++) g->data[p*y]   = NOTDEF;
//   /*** remaining part ***/
//   for(x=1;x<p-1;x++)
//     for(y=1;y<n-1;y++)
//       {
//         adr = y*p+x;
//         /*
//            Norm 2 computation using 2x2 pixel window:
//              A B
//              C D
//            and
//              com1 = D-A,  com2 = B-C.
//            Then
//              gx = B+D - (A+C)   horizontal difference
//              gy = C+D - (A+B)   vertical difference
//            com1 and com2 are just to avoid 2 additions.
//          */
//         com1 = in->data[adr+p+1] - in->data[adr];
//         com2 = in->data[adr+1]   - in->data[adr+p];
//         gx = com1+com2;
//         gy = com1-com2;
//         //norm2 = gx*gx+gy*gy;
// 		norm2 = abs(gx)+abs(gy);
//         //norm = sqrt( norm2 / 4.0 );
//         if( norm2 <= threshold ) /* norm too small, gradient no defined */
//           g->data[adr] = NOTDEF;
//         else
//           {
//             /* angle computation */
//             g->data[adr] = matan2(gy,gx);
// 
//             /* store the point in the right bin according to its norm */
//             i = (unsigned int)norm2;// (norm2 * (double) n_bins / max_grad);
//             if( i >= n_bins ) i = n_bins-1;
//             if( range_l_e[i] == NULL )
//               range_l_s[i] = range_l_e[i] = list+list_count++;
//             else
//               {
//                 range_l_e[i]->next = list+list_count;
//                 range_l_e[i] = list+list_count++;
//               }
//             range_l_e[i]->x = (int) x;
//             range_l_e[i]->y = (int) y;
//             range_l_e[i]->next = NULL;
//           }
//       }
// 
//   /* Make the list of points "ordered" by norm value.
//      It starts by the larger bin, so the list starts by the
//      pixels with higher gradient value.
//    */
//   for(i=n_bins-1; i>0 && range_l_s[i]==NULL; i--);
//   start = range_l_s[i];
//   end = range_l_e[i];
//   if( start != NULL )
//     for(i--;i>0; i--)
//       if( range_l_s[i] != NULL )
//         {
//           end->next = range_l_s[i];
//           end = range_l_e[i];
//         }
//   *list_p = start;
// 
//   /* free memory */
//   mdMemFree( (void *) range_l_s );
//   mdMemFree( (void *) range_l_e );
// 
//   return g;
// }

int CDecoCommonLSD::isaligned1( int x, int y, image_int angles, int theta, int prec )
{
  int a;

  /* check parameters */
  if( angles == NULL || angles->data == NULL )
    return 0;//error("isaligned: invalid image 'angles'.");
  if( x < 0 || y < 0 || x >= (int) angles->xsize || y >= (int) angles->ysize )
     return 0;//error("isaligned: (x,y) out of the image.");
  if( prec < 0.0 )  return 0;//error("isaligned: 'prec' must be positive.");

  a = angles->data[ x + y * angles->xsize ];

  if( a == NOTDEF ) return FALSE;  /* there is no risk of double comparison
                                      problem here because we are only
                                      interested in the exact NOTDEF value */

  /* it is assumed that 'theta' and 'a' are in the range [-180,180] */
  theta -= a;
  if( theta < 0 ) theta = -theta;
  if( theta > 270 )
    {
      theta -= 360;
      if( theta < 0 ) theta = -theta;
    }

  return theta < prec;
}


double CDecoCommonLSD::angle_diff(double a, double b)
{
  a -= b;
  while( a <= -M_PI ) a += M_2__PI;
  while( a >   M_PI ) a -= M_2__PI;
  if( a < 0.0 ) a = -a;
  return a;
}
// int iangle_diff(int a, int b)
// {
// 	a -= b;
// 	while( a <= -180 ) a += 360;
// 	while( a > 180 ) a -= 360;
// 	if( a < 0 ) a = -a;
// 	return a;
// }

// double angle_diff_signed(double a, double b)
// {
//   a -= b;
//   while( a <= -M_PI ) a += M_2__PI;
//   while( a >   M_PI ) a -= M_2__PI;
//   return a;
// }

void CDecoCommonLSD::region_grow8( int x, int y, image_int angles, struct point* reg,
						 int* _reg_size, int* _reg_angle, image_char used,
						 int prec )
{
	int xx,yy,i;
	int reg_size;
	int reg_angle;
	int sum_angle=0;
	
	/* check parameters */
	if( x < 0 || y < 0 || x >= (int) angles->xsize || y >= (int) angles->ysize )
		return;//error("region_grow: (x,y) out of the image.");
	if( angles == NULL || angles->data == NULL )
		return;//error("region_grow: invalid image 'angles'.");
	if( reg == NULL ) return;//error("region_grow: invalid 'reg'.");
	if( _reg_size == NULL ) return;//error("region_grow: invalid pointer 'reg_size'.");
	if( _reg_angle == NULL ) return;//error("region_grow: invalid pointer 'reg_angle'.");
	if( used == NULL || used->data == NULL )
		return;//error("region_grow: invalid image 'used'.");
	
	/* first point of the region */
	reg_size = 1;
	reg[0].x = x;
	reg[0].y = y;
	reg_angle = angles->data[x+y*angles->xsize];
	sum_angle = reg_angle;
	used->data[x+y*used->xsize] = USED;
	
	/* try neighbors as new region points */
	for(i=0; i<reg_size; i++)
	{
		for(xx=reg[i].x-1; xx<=reg[i].x+1; xx++)
		{
			for(yy=reg[i].y-1; yy<=reg[i].y+1; yy++)
			{
				if( used->data[xx+yy*used->xsize] != USED &&
					isaligned1(xx,yy,angles,reg_angle,prec) )
				{
					/* add point */
					used->data[xx+yy*used->xsize] = USED;
					reg[reg_size].x = xx;
					reg[reg_size].y = yy;
					++reg_size;
					
					/* update region's angle */
					if (abs(reg_angle - angles->data[xx+yy*used->xsize])>prec)
					{
						if (angles->data[xx+yy*used->xsize] > 0)
						{
							sum_angle += angles->data[xx+yy*used->xsize]-360;
							reg_angle = sum_angle/reg_size;
						}
						else
						{
							sum_angle += angles->data[xx+yy*used->xsize]+360;	
							reg_angle = sum_angle/reg_size;
						}
					}
					else
					{
						sum_angle += angles->data[xx+yy*used->xsize];
						reg_angle = sum_angle/reg_size;
					}
					//printf("reg_angle=%4d,angle=%4d\n",reg_angle,angles->data[xx+yy*used->xsize]);
				}
			}
		}
	}
	//printf("\n");
	if (reg_angle > 180)
	{
		reg_angle -= 360;
	}
	else if (reg_angle < -180)
	{
		reg_angle += 360;
	}
	*_reg_angle = reg_angle;
	*_reg_size = reg_size;
}

static const int neighbor[10][2]={{-1,-1},{0,-1},{1,-1},
							{1,0},{1,1},{0,1},
							{-1,1},{-1,0},{-1,-1},{0,-1}};

 //---------------------------------------------------------------------------------------

// void region_grow2( int x, int y, image_int angles, struct point * reg,
//                          int * _reg_size, int * _reg_angle, image_char used,
//                          int prec )
// {
//   int xx,yy,i,j;
//   int reg_size;
//   int reg_angle;
//   int angle,bias;
//   int reg_angle2;
//   int sum_angle=0;
// 
//   /* check parameters */
//   if( x < 0 || y < 0 || x >= (int) angles->xsize || y >= (int) angles->ysize )
// 	return;//error("region_grow: (x,y) out of the image.");
//   if( angles == NULL || angles->data == NULL )
// 	return;//error("region_grow: invalid image 'angles'.");
//   if( reg == NULL ) return;//error("region_grow: invalid 'reg'.");
//   if( _reg_size == NULL ) return;//error("region_grow: invalid pointer 'reg_size'.");
//   if( _reg_angle == NULL ) return;//error("region_grow: invalid pointer 'reg_angle'.");
//   if( used == NULL || used->data == NULL )
// 	return;//error("region_grow: invalid image 'used'.");
// 
//   /* first point of the region */
//   reg_size = 1;
//   reg[0].x = x;
//   reg[0].y = y;
//   reg_angle = angles->data[x+y*angles->xsize];
// 
//   used->data[x+y*used->xsize] = USED;
// 
//   reg_angle2 = reg_angle;
// 	if (reg_angle2 < 0)
// 	{
// 		reg_angle2 += 180;
// 	}
// 	reg_angle2 += 23;
// 
// 	angle = reg_angle2/45;
// 
// 	if (reg_angle2 - angle*45 > 23)
// 	{
// 		bias = 1;
// 	}
// 	else
// 	{
// 		bias = -1;
// 	}
// 	//printf("reg_angle=%5.2f,angle=%d\n",reg_angle,angle);
// 	/* try neighbors as new region points */
// 
// 	if (angle >= 4)
// 	{
// 		angle -= 4;
// 	}
// 	angle += 1;
// 	sum_angle = reg_angle;
//   for(i=0; i<reg_size; i++)
//   {
// 	  for (j=angle;j!=angle+2*bias;j+=bias)
// 	  {
// 		  xx=reg[i].x+neighbor[j][0];
// 		  yy=reg[i].y+neighbor[j][1];
// 		  if( used->data[xx+yy*used->xsize] != USED &&
// 			  isaligned1(xx,yy,angles,reg_angle,prec) )
//           {
// 			  /* add point */
// 			  used->data[xx+yy*used->xsize] = USED;
// 			  reg[reg_size].x = xx;
// 			  reg[reg_size].y = yy;
// 			  ++reg_size;
// 			  
// 			  /* update region's angle */
// 			  if (abs(reg_angle - angles->data[xx+yy*used->xsize])>prec)
// 			  {
// 				  if (angles->data[xx+yy*used->xsize] > 0)
// 				  {
// 					  sum_angle += angles->data[xx+yy*used->xsize]-360;
// 					  reg_angle = sum_angle/reg_size;
// 					  if (reg_angle < -180)
// 					  {
// 						  reg_angle += 360;
// 					  }
// 				  }
// 				  else
// 				  {
// 					  sum_angle += angles->data[xx+yy*used->xsize]+360;	
// 					  reg_angle = sum_angle/reg_size;
// 					  if (reg_angle > 180)
// 					  {
// 						  reg_angle -= 360;
// 					  }
// 				  }
// 			  }
// 			  else
// 			  {
// 				sum_angle += angles->data[xx+yy*used->xsize];
// 				reg_angle = sum_angle/reg_size;
// 			  }	  
//           }	
// 	  }
//   }
//  
//     //reversed direction grow
// 	reg[0].x = reg[reg_size-1].x;
// 	reg[0].y = reg[reg_size-1].y;
// 	reg[reg_size-1].x = x;
// 	reg[reg_size-1].y = y;
// 	angle+=4;
// 
//   for(i=reg_size-1; i<reg_size; i++)
//   {
// 	  for (j=angle; j!=angle+2*bias; j+=bias)
// 	  {
// 		  xx=reg[i].x+neighbor[j][0];
// 		  yy=reg[i].y+neighbor[j][1];
// 		  if( used->data[xx+yy*used->xsize] != USED &&
// 			  isaligned1(xx,yy,angles,reg_angle,prec) )
//           {
// 			  /* add point */
// 			 // printf("reg_angle=%5.2f,angles=%5.2f\n",reg_angle,angles->data[xx+yy*angles->xsize]);
// 			  used->data[xx+yy*used->xsize] = USED;
// 			  reg[reg_size].x = xx;
// 			  reg[reg_size].y = yy;
// 			  ++reg_size;
// 			  
// 			  /* update region's angle */
// 			  if (abs(reg_angle - angles->data[xx+yy*used->xsize]) > prec)
// 			  {
// 				  if (angles->data[xx+yy*used->xsize] > 0)
// 				  {
// 					  sum_angle += angles->data[xx+yy*used->xsize]-360;
// 					  reg_angle = sum_angle/reg_size;
// 					  if (reg_angle < -180)
// 					  {
// 						  reg_angle += 360;
// 					  }
// 				  }
// 				  else
// 				  {
// 					  sum_angle += angles->data[xx+yy*used->xsize]+360;	
// 					  reg_angle = sum_angle/reg_size;
// 					  if (reg_angle > 180)
// 					  {
// 						  reg_angle -= 360;
// 					  }
// 				  }
// 			  }
// 			  else
// 			  {
// 				  sum_angle += angles->data[xx+yy*used->xsize];
// 				  reg_angle = sum_angle/reg_size;
// 			  }	  
//           }
// 	  }
//   }
//   //reg[0].x = reg[reg_size].x;
//   //reg[0].y = reg[reg_size].y;
//   *_reg_angle = reg_angle;
//   *_reg_size = reg_size;
//   return;
//  }

double CDecoCommonLSD::get_theta1( struct point * reg, int reg_size, double x, double y,
                         image_char modgrad, double reg_angle, double prec )
{
  double lambda1,lambda2,tmp,theta,weight,sum;
  double Ixx = 0.0;
  double Iyy = 0.0;
  double Ixy = 0.0;
  int i;

  /* check parameters */
  if( reg == NULL )return 0;// error("get_theta: invalid region.");
  if( reg_size <= 1 ) return 0;//error("get_theta: region size <= 1.");
  if( modgrad == NULL || modgrad->data == NULL )
   return 0;//error("get_theta: invalid 'modgrad'.");
  if( prec < 0.0 ) return 0;//error("get_theta: 'prec' must be positive.");

  /*----------- theta ---------------------------------------------------*/
  /*
      Region inertia matrix A:
         Ixx Ixy
         Ixy Iyy
      where
        Ixx = \sum_i y_i^2
        Iyy = \sum_i x_i^2
        Ixy = -\sum_i x_i y_i

      lambda1 and lambda2 are the eigenvalues, with lambda1 >= lambda2.
      They are found by solving the characteristic polynomial
      det(\lambda I - A) = 0.

      To get the line segment direction we want to get the eigenvector of
      the smaller eigenvalue. We have to solve a,b in:
        a.Ixx + b.Ixy = a.lambda2
        a.Ixy + b.Iyy = b.lambda2
      We want the angle theta = atan(b/a). I can be computed with
      any of the two equations:
        theta = atan( (lambda2-Ixx) / Ixy )
      or
        theta = atan( Ixy / (lambda2-Iyy) )

      When |Ixx| > |Iyy| we use the first, otherwise the second
      (just to get better numeric precision).
   */
  sum = 0.0;
  for(i=0; i<reg_size; i++)
    {
      weight = modgrad->data[ reg[i].x + reg[i].y * modgrad->xsize ];
      Ixx += ( (double) reg[i].y - y ) * ( (double) reg[i].y - y ) * weight;
      Iyy += ( (double) reg[i].x - x ) * ( (double) reg[i].x - x ) * weight;
      Ixy -= ( (double) reg[i].x - x ) * ( (double) reg[i].y - y ) * weight;
      sum += weight;
    }
  if( sum <= 0.0 )// error("get_theta: weights sum less or equal to zero.");
	  return 0;
  Ixx /= sum;
  Iyy /= sum;
  Ixy /= sum;
  lambda1 = ( Ixx + Iyy + sqrt( (Ixx-Iyy)*(Ixx-Iyy) + 4.0*Ixy*Ixy ) ) / 2.0;
  lambda2 = ( Ixx + Iyy - sqrt( (Ixx-Iyy)*(Ixx-Iyy) + 4.0*Ixy*Ixy ) ) / 2.0;
  if( fabs(lambda1) < fabs(lambda2) )
    {
#ifdef PC
      fprintf(stderr,"Ixx %g Iyy %g Ixy %g lamb1 %g lamb2 %g - lamb1 < lamb2\n",
                      Ixx,Iyy,Ixy,lambda1,lambda2);
#endif
      tmp = lambda1;
      lambda1 = lambda2;
      lambda2 = tmp;
    }

  if( fabs(Ixx) > fabs(Iyy) )
    theta = atan2( lambda2-Ixx, Ixy );
  else
    theta = atan2( Ixy, lambda2-Iyy );

  /* The previous procedure don't cares about orientation,
     so it could be wrong by 180 degrees. Here is corrected if necessary. */
  if( angle_diff(theta,reg_angle) > prec ) theta += M_PI;

  return theta;
}

// int get_theta3( struct point * reg, int reg_size, int x, int y,
// 						 int reg_angle, int prec,int* _theta)
// {
//   int lambda1,lambda2,tmp,theta;//,sum=0;
//   int Ixx = 0;
//   int Iyy = 0;
//   int Ixy = 0;
//   int i;
// 
//   /* check parameters */
//   if( reg == NULL )return 0;// error("get_theta: invalid region.");
//   if( reg_size <= 1 ) return 0;//error("get_theta: region size <= 1.");
//   if( prec < 0.0 ) return 0;//error("get_theta: 'prec' must be positive.");
// 
//   /*----------- theta ---------------------------------------------------*/
//   /*
//       Region inertia matrix A:
//          Ixx Ixy
//          Ixy Iyy
//       where
//         Ixx = \sum_i y_i^2
//         Iyy = \sum_i x_i^2
//         Ixy = -\sum_i x_i y_i
// 
//       lambda1 and lambda2 are the eigenvalues, with lambda1 >= lambda2.
//       They are found by solving the characteristic polynomial
//       det(\lambda I - A) = 0.
// 
//       To get the line segment direction we want to get the eigenvector of
//       the smaller eigenvalue. We have to solve a,b in:
//         a.Ixx + b.Ixy = a.lambda2
//         a.Ixy + b.Iyy = b.lambda2
//       We want the angle theta = atan(b/a). I can be computed with
//       any of the two equations:
//         theta = atan( (lambda2-Ixx) / Ixy )
//       or
//         theta = atan( Ixy / (lambda2-Iyy) )
// 
//       When |Ixx| > |Iyy| we use the first, otherwise the second
//       (just to get better numeric precision).
//    */
// //  sum = 0;
//   for(i=0; i<reg_size; i++)
//     {
//       Ixx += ( reg[i].y - y ) * ( reg[i].y - y ) ;
//       Iyy += ( reg[i].x - x ) * ( reg[i].x - x ) ;
//       Ixy -= ( reg[i].x - x ) * ( reg[i].y - y ) ;
//     }
//   Ixx /= reg_size;
//   Iyy /= reg_size;
//   Ixy /= reg_size;
//   lambda1 = ( Ixx + Iyy + md_isqrt( (Ixx-Iyy)*(Ixx-Iyy) + 4*Ixy*Ixy ) ) / 2;
//   lambda2 = ( Ixx + Iyy - md_isqrt( (Ixx-Iyy)*(Ixx-Iyy) + 4*Ixy*Ixy ) ) / 2;
//   if( abs(lambda1) < abs(lambda2) )
//     {
// #ifdef PC
//       fprintf(stderr,"Ixx %g Iyy %g Ixy %g lamb1 %g lamb2 %g - lamb1 < lamb2\n",
//                       Ixx,Iyy,Ixy,lambda1,lambda2);
// #endif
//       tmp = lambda1;
//       lambda1 = lambda2;
//       lambda2 = tmp;
//     }
// 
//   if (abs(lambda2)*5 > abs(lambda1)) //ratio:length/width > 5
//   {
// 	  return 0;
//   }
// 
//   if( abs(Ixx) > abs(Iyy) )
//     theta = matan2( lambda2-Ixx, Ixy );
//   else
//     theta = matan2( Ixy, lambda2-Iyy );
// 
//   /* The previous procedure don't cares about orientation,
//      so it could be wrong by 180 degrees. Here is corrected if necessary. */
//   //if( iangle_diff(theta,reg_angle) > prec ) theta += 180;
// 
//   *_theta = theta;
// 
//   return 1;
// }

int CDecoCommonLSD::region2ls1( struct point * reg, int reg_size,
					  image_char modgrad, int reg_angle,MdLineSegment * ls )
{
	int sx,sy,sum;
	double x,y;
	double theta,dx,dy;
	int weight;
	double l,l_min,l_max;
	int i;
	
	/* check parameters */
	if( reg == NULL ) return 0;//error("region2rect: invalid region.");
	if( reg_size <= 1 ) return 0;//error("region2rect: region size <= 1.");
	if( modgrad == NULL || modgrad->data == NULL )
		return 0;//error("region2rect: invalid image 'modgrad'.");
	
	/* center */
    sx = sy = sum = 0;
    for(i=0; i<reg_size; i++)
	{
 	    weight = modgrad->data[ reg[i].x + reg[i].y * modgrad->xsize ];
	    sx +=  reg[i].x * weight;
	    sy +=  reg[i].y * weight;
	    sum += weight;
	}
    if( sum <= 0 )// error("region2rect: weights sum equal to zero.");
		return 0;
	x = (double)sx/sum;
	y = (double)sy/sum;
	
	/* theta */
	theta = get_theta1(reg,reg_size,x,y,modgrad,reg_angle*M_PI/180,22.5*M_PI/180);
	//printf("get_theta=%.1f",theta*180/M_PI);
	//theta = (reg_angle+90)*M_PI/180;
	
	/* length and width */
	dx = cos(theta);
	dy = sin(theta);
	l_min = l_max = 0.0;
	for(i=0; i<reg_size; i++)
	{
		l =  ( (double) reg[i].x - x) * dx + ( (double) reg[i].y - y) * dy;
		
		if( l > l_max ) l_max = l;
		if( l < l_min ) l_min = l;
	}
	//   l_min = (reg[0].x-x)*dx + (reg[0].y-y)*dy;
	//   l_max = (reg[reg_size-1].x-x)*dx + (reg[reg_size-1].y-y)*dy;
	
	/* store values */
	ls->ep1.x = (int)(x + l_min * dx + 0.5);
	ls->ep1.y = (int)(y + l_min * dy + 0.5);
	ls->ep2.x = (int)(x + l_max * dx + 0.5);
	ls->ep2.y = (int)(y + l_max * dy + 0.5);
	ls->len = (int)(l_max - l_min + 0.5);
	ls->angle = reg_angle;
	
	if (ls->len < 15/PYRAMID_LEVEL)
	{
		return 0;
	}
	return 1;
}


int CDecoCommonLSD::LineSegmentDetection1( image_char image, int pyr_level,
                                  int ang_th, int magn_th,int n_bins, int max_grad,MdLineSegment* lines)
{
  image_char modgrad=0;
  image_int angles;
  image_char used;
  struct coorlist * list_p=0;
  void * mem_p=0;
  struct point * reg;
  int reg_size=0,min_reg_size=0;
  unsigned int xsize,ysize;
  int reg_angle=0;
  int line_num=0;

  /* check parameters */
  if( image==NULL || image->data==NULL || image->xsize<=0 || image->ysize<=0 )
     return 0;// error("invalid image input.");
  if( pyr_level <= 0.0 ) 
    return 0;
  if( ang_th <= 0.0 || ang_th >= 180.0 )
    return 0;//error("'ang_th' value must be in the range (0,180).");
  if( n_bins <= 0 )
	  return 0;//error("'n_bins' value must be positive.");
  if( max_grad <= 0.0 ) 
	  return 0;//error("'max_grad' value must be positive.");

  /* scale image (if necessary) and compute angle at each pixel */
  if( pyr_level != 1 )
     return 0;
  else
  {
	  angles = ll_angle1( image, magn_th, &list_p, &mem_p, &modgrad,
                       (unsigned int) n_bins, max_grad );
  }
#if MEMORY_CHECK_ENABLE
   if(angles==0)
	   return 0;
#endif
  xsize = angles->xsize;
  ysize = angles->ysize;
/* minimal number of points in region
   that can give a meaningful event */
  min_reg_size = 30/PYRAMID_LEVEL;
  /* initialize some structures */
  used = new_image_char(xsize,ysize);
#if MEMORY_CHECK_ENABLE
  if (used == NULL)
  {
	  /* free memory */
	  free_image_int(angles);
	  free_image_char(modgrad);
	  pCDecoMemObj->mdMemFree(mem_p);
	  return 0;
  }
#endif
  memset(used->data,NOTUSED,xsize*ysize);
  reg = (struct point *)pCDecoMemObj->mdMemAlloc(xsize * ysize*sizeof(struct point),sizeof(struct point),MEM_ALLOC_BLOCK);
#if MEMORY_CHECK_ENABLE
 if( reg == NULL )
  {
	  /* free memory */
	  free_image_int(angles);
	  free_image_char(modgrad);
	  free_image_char(used);
	  pCDecoMemObj->mdMemFree(mem_p);
	  return 0;
  }
#endif
  memset(reg,0,xsize * ysize*sizeof(struct point));
  /* search for line segments */
  for(;list_p; list_p = list_p->next )
    if( used->data[ list_p->x + list_p->y * used->xsize ] == NOTUSED &&
        angles->data[ list_p->x + list_p->y * angles->xsize ] != NOTDEF )
       /* there is no risk of double comparison problem here
          because we are only interested in the exact NOTDEF value */
      {
        /* find the region of connected point and ~equal angle */
		region_grow8( list_p->x, list_p->y,angles, reg, &reg_size,
                     &reg_angle, used, ang_th );
		
		if (reg_size > min_reg_size)
		{
			MdLineSegment ls;
			if (region2ls1(reg,reg_size,modgrad,reg_angle,&ls))
			{
				lines[line_num]=ls;
				line_num++;
			}			
		}//end if()		
	}//end for if()
  /* free memory */
  free_image_int(angles);
  free_image_char(modgrad);
  free_image_char(used);
  pCDecoMemObj->mdMemFree(reg);
  pCDecoMemObj->mdMemFree(mem_p);

  return line_num;
}


int CDecoCommonLSD::lsd(image_char image,MdLineSegment* lines,int ang_th,int magn_th)
{
  /* LSD parameters */
  int pyr_level = 1;
//   int ang_th = 23;//22.5     /* Gradient angle tolerance in degrees.           */
  int n_bins = 1024;        /* Number of bins in pseudo-ordering of gradient
                               modulus.                                       */
  int max_grad = 255;  /* Gradient modulus in the highest bin. The
                               default value corresponds to the highest
                               gradient modulus on images with gray
                               levels in [0,255].                             */

   return LineSegmentDetection1( image, pyr_level, ang_th,magn_th, n_bins, max_grad,lines);

}

