
/*******************************************************************************
Copyright (c), 2012, MinDe Electronics Technology Ltd.
File Name:    	bullseye_finder.c
Author:			Huhq
Version:      	1.0
Start:		  	2012/4/5
Date:         	
Description:  	Using like-edge measurements to find the bullseye finder pattern.
				(Aztec code Maxicode)

Call List:		
History:			  	
*******************************************************************************/

#include <stdio.h>
#include <string.h>
#include <math.h>
#include <stdlib.h>
#include <limits.h>
#include "mdtypes.h"
#include "decoder_memory.h"
#include "bullseye_finder.h"
#include "scanner.h"
#include "decoder_globalVari.h"
#include "finderMWR_scanner.h"

#ifdef CV
#include "cv.h"
#include "highgui.h"
IplImage *debug_img;
#endif

//----------------------------------bullseye_finder---------------------------------------------
/*Initializes a client reader handle.*/
s32 CDecoFinderMWRScannerSub::bullseye_reader_init(bullseye_reader *reader)
{	
//    bullseye_isaac_init(&reader->bullseye_isaac, NULL, 0);
	//  rs_gf256_init(&reader->gf, BULLSEYE_PPOLY);
	reader->finder_lines[0].lines = (bullseye_finder_line * )pCDecoMemObj->mdMemAlloc((MD_WIDTH*MD_HEIGHT)/32*sizeof(bullseye_finder_line),sizeof(s32), MEM_ALLOC_BLOCK);//MD_MAX_COL*MD_MAX_ROW
	if (reader->finder_lines[0].lines==NULL)
		return 0;
	reader->finder_lines[1].lines = (bullseye_finder_line * )pCDecoMemObj->mdMemAlloc((MD_WIDTH*MD_HEIGHT)/32*sizeof(bullseye_finder_line),sizeof(s32), MEM_ALLOC_BLOCK);//MD_MAX_COL*MD_MAX_ROW
	if (reader->finder_lines[1].lines==NULL)
		return 0;
	return 1;
}
/*Allocates a client reader handle.*/
bullseye_reader *CDecoFinderMWRScannerSub::_md_bullseye_create (void)
{
	// 	static bullseye_reader s_reader;
	// 	bullseye_reader *reader = &s_reader;
	bullseye_reader *reader = (bullseye_reader * )pCDecoMemObj->mdMemAlloc(sizeof(bullseye_reader),4,MEM_ALLOC_PTR);
    if(reader==NULL)
		return NULL;
	memset(reader,0,sizeof(bullseye_reader));	
    if(!bullseye_reader_init(reader))
		return NULL;
    return(reader);
}

// /* reset finder state between scans */
// void _md_bullseye_reset (bullseye_reader *reader)
// {
//     reader->finder_lines[0].nlines = 0;
//     reader->finder_lines[1].nlines = 0;
// }

/* at this point lengths are all decode unit offsets from the decode edge
* NB owned by finder
*/
bullseye_finder_line * CDecoFinderMWRScannerSub::_md_bullseye_decoder_get_finder_line (md_decoder_t *dcode)
{
    return(&dcode->bullseyef.line);
}

md_MWR_symbol_type CDecoFinderMWRScannerSub::_md_find_bullseye(md_decoder_t *dcode)
{
	s32 k,ei;
	u32 s;
	u32 qz;
	u32 w;
    bullseye_finder_t *bullseyef = &dcode->bullseyef;	
    /* update latest finder pattern width */
    bullseyef->s7 -= get_width(dcode, 8);
    bullseyef->s7 += get_width(dcode, 1);
    s = bullseyef->s7;	
    if(s < 7)
        return((md_MWR_symbol_type)0);
	if((pCDecoGlobalVariObj->g_enable_reverseAZTEC_Both==0) &&
		(pCDecoGlobalVariObj->g_enable_reverseAZTEC_Only==0)&&
		(get_color(dcode) == MD_BAR))
		return((md_MWR_symbol_type)0);
	if((pCDecoGlobalVariObj->g_enable_reverseAZTEC_Both==0) &&
		(pCDecoGlobalVariObj->g_enable_reverseAZTEC_Only==1) && 
		(get_color(dcode) == MD_SPACE))
		return((md_MWR_symbol_type)0);	
 	for (k=0;k<7;k++)
	{	
		ei = decode_e(pair_width(dcode, k),s, 7);
		if(ei)
			return (md_MWR_symbol_type)0;
	}	
/* valid bullseye finder symbol
* mark positions needed by decoder
*/

/*A line crossing a finder pattern.
  Whether the line is horizontal or vertical is determined by context.
  The offsts to various parts of the finder pattern are as follows:
    |		|*****|		|*****|		|*****|		|
    |		|*****|     |*****|     |*****|		|
    ^		^                             ^		^
    |		|              ^              |		|
	|		|              |              |	pos[v]+len+eoffs
	|		|              |           pos[v]+len
	|     pos[v]         center
pos[v]-boffs
Here v is 0 for horizontal and 1 for vertical lines.*/

	bullseyef->line.isReverse = !get_color(dcode);
    qz = get_width(dcode, 0);
    w = get_width(dcode, 1);
    bullseyef->line.eoffs = qz;
    bullseyef->line.len = qz + w;
    bullseyef->line.center = qz + w + get_width(dcode, 2)+ get_width(dcode, 3)+ get_width(dcode, 4)/2;
    bullseyef->line.pos[0] = bullseyef->line.len + get_width(dcode, 2)+ get_width(dcode, 3)
		+ get_width(dcode, 4)+ get_width(dcode, 5)+ get_width(dcode, 6);

    bullseyef->line.pos[1] = bullseyef->line.pos[0];
    w = get_width(dcode, 7);
    bullseyef->line.boffs = bullseyef->line.pos[0] +w ;
    return(MWR_TYPE_BULLSEYE);	
}

s32 CDecoFinderMWRScannerSub::bullseye_found_line (bullseye_reader *reader,s32 dir,const bullseye_finder_line *line)
{
    /* minimally intrusive brute force version */
    bullseye_finder_lines *lines = &reader->finder_lines[dir];	

	if (lines->nlines>(MD_WIDTH*MD_HEIGHT)/32)
	{
		return 0;
	}
    memcpy(lines->lines + lines->nlines++, line, sizeof(*line));	
    return 0;
}

#define BULLSEYE_FIXED(v, rnd) ((((v) << 1) + (rnd)) << (BULLSEYE_FINDER_SUBPREC - 1))

void CDecoFinderMWRScannerSub::bullseye_handler (md_image_scanner_t *iscn)
{
	s32 vert;
    bullseye_finder_line *line = _md_bullseye_decoder_get_finder_line(iscn->dcode);	
	u32 u = md_scanner_get_edge(iscn->scn, line->pos[0],BULLSEYE_FINDER_SUBPREC); 
	line->boffs = u - md_scanner_get_edge(iscn->scn, line->boffs,BULLSEYE_FINDER_SUBPREC);	
    line->len = md_scanner_get_edge(iscn->scn, line->len,BULLSEYE_FINDER_SUBPREC);  
	line->center = md_scanner_get_edge(iscn->scn, line->center,BULLSEYE_FINDER_SUBPREC);	
    line->eoffs = md_scanner_get_edge(iscn->scn, line->eoffs,BULLSEYE_FINDER_SUBPREC) - line->len;	
    line->len -= u;
	
    u = BULLSEYE_FIXED(iscn->umin, 0) + iscn->du * u;
    if(iscn->du < 0) 
	{
		s32 tmp = line->boffs;
        u -= line->len;
        line->boffs = line->eoffs;
        line->eoffs = tmp;
    }	
    vert = !iscn->dx;
	line->pos[vert] = u;
    line->pos[!vert] = BULLSEYE_FIXED(iscn->v, 1);

	{
#ifdef BULLSEYE_DEBUG0
		CvPoint pt1,pt2,pt0;
		if(vert==0)
		{
			pt0.x=(line->center)>>2;
			pt0.y=line->pos[1]>>2;
			pt1.x=(line->pos[0]-line->boffs)>>2;
			pt1.y=line->pos[1]>>2;
			pt2.x=(line->pos[0]+line->len+line->eoffs)>>2;
			pt2.y=line->pos[1]>>2;
			cvLine(debug_img,pt1,pt2,CV_RGB(255,0,0),1,8,0);
			cvCircle(debug_img,pt0,2,CV_RGB(0,0,255),1,8,0);			
		}
		else
		{
			pt0.x=line->pos[0]>>2;
			pt0.y=(line->center)>>2;
			pt1.x=line->pos[0]>>2;
			pt1.y=(line->pos[1]-line->boffs)>>2;
			pt2.x=line->pos[0]>>2;
			pt2.y=(line->pos[1]+line->len+line->eoffs)>>2;
			cvLine(debug_img,pt1,pt2,CV_RGB(0,255,0),1,8,0);
			cvCircle(debug_img,pt0,2,CV_RGB(255,0,255),1,8,0);
		}	
		cvNamedWindow("ROI img",0);
		cvShowImage("ROI img",debug_img);
		cvWaitKey(0);
#endif
  }

    bullseye_found_line(iscn->bullseye, vert, line);
}

/*Clusters adjacent lines into groups that are large enough to be crossing a
finder pattern (relative to their length).
_clusters:  The buffer in which to store the clusters found.
_neighbors: The buffer used to store the lists of lines in each cluster.
_lines:     The list of lines to cluster.
Horizontal lines must be sorted in ascending order by Y
coordinate, with ties broken by X coordinate.
Vertical lines must be sorted in ascending order by X coordinate,
with ties broken by Y coordinate.
_nlines:    The number of lines in the set of lines to cluster.
_v:         0 for horizontal lines, or 1 for vertical lines.
Return: The number of clusters.*/
s32 CDecoFinderMWRScannerSub::bullseye_finder_cluster_lines(bullseye_finder_cluster *_clusters,
								   bullseye_finder_line **_neighbors,bullseye_finder_line *_lines,s32 _nlines,s32 _v)
{
	u8   *mark;
	bullseye_finder_line **neighbors;
	s32              nneighbors;
	s32              nclusters;
	s32              i=0;
	s32              isReverse;
	
	/*TODO: Kalman filters!*/
	mark = (u8*)pCDecoMemObj->mdMemAlloc(_nlines*sizeof(*mark),sizeof(*mark),MEM_ALLOC_BLOCK);
	if(mark==NULL)
		return 0;
	memset(mark,0,_nlines*sizeof(*mark));
	
	neighbors=_neighbors;
	nclusters=0;
	for(i=0;i<_nlines-1;i++)
		if(!mark[i])
		{
			const bullseye_finder_line *a;
			s32 len;
			s32 j;
			nneighbors=1;
			neighbors[0]=_lines+i;
			len=_lines[i].len;
			isReverse=neighbors[nneighbors-1]->isReverse;
			for(j=i+1;j<_nlines;j++)
			{
				const bullseye_finder_line *b;
				b=_lines+j;	
				if((!mark[j])&&isReverse==b->isReverse)
				{
					s32   thresh;
					a=neighbors[nneighbors-1];
					
					/*The clustering threshold is proportional to the size of the lines,
					since minor noise in large areas can interrupt patterns more easily
					at high resolutions.*/
					thresh=(a->len+15)>>3; //thresh=a->len+7>>2;
					if(((s32)abs((f32)(a->pos[1-_v]-b->pos[1-_v])))>thresh)
						break;
					if(((s32)abs((f32)(a->pos[_v]-b->pos[_v])))>thresh)
						continue;
					if(((s32)abs((f32)(a->pos[_v]+a->len-b->pos[_v]-b->len)))>thresh)
						continue;
					if(a->center>0 && b->center>0 &&
							((s32)abs((f32)(a->center-b->center)))>thresh
							)
					{
						continue;
					}
					if(a->eoffs>0 && b->eoffs>0 &&
							((s32)abs((f32)(a->pos[_v]+a->len+a->eoffs-b->pos[_v]-b->len-b->eoffs)))>thresh
							)
					{
						continue;
					}
					neighbors[nneighbors++]=_lines+j;
					len+=b->len;
				}
			}
			/*We require at least three lines to form a cluster, which eliminates a
			large number of false positives, saving considerable decoding time.
			This should still be sufficient for 1-pixel codes with no noise.*/
			if(nneighbors<2) //if(nneighbors<3)
				continue;
				/*The expected number of lines crossing a finder pattern is equal to their
				average length.
				We accept the cluster if size is at least 1/3 their average length (this
			is a very small threshold, but was needed for some test images).*/
			len=((len<<1)+nneighbors)/(nneighbors<<1);
#ifndef AZTEC_NOT_STANDARD	
			if(nneighbors*(8<<BULLSEYE_FINDER_SUBPREC)>=len) //if(nneighbors*(5<<BULLSEYE_FINDER_SUBPREC)>=len)
#endif		
			{
				_clusters[nclusters].lines=neighbors;
				_clusters[nclusters].nlines=nneighbors;
				_clusters[nclusters].isReverse = isReverse;
				for(j=0;j<nneighbors;j++)
					mark[neighbors[j]-_lines]=1;
				neighbors+=nneighbors;
				nclusters++;
			}

		}//end of for(i=0;i<_nlines-1;i++)
		
		pCDecoMemObj->mdMemFree(mark);
		return nclusters;
}

s32 CDecoFinderMWRScannerSub::bullseye_finder_cluster_hlines(bullseye_reader *reader,bullseye_finder_cluster **_hclusters,bullseye_finder_line *** _hneighbors)
{
	bullseye_finder_line     *hlines = reader->finder_lines[0].lines;
	s32                 nhlines = reader->finder_lines[0].nlines;
	
	bullseye_finder_line    **hneighbors;
	bullseye_finder_cluster  *hclusters;
	s32                 nhclusters;

	/*Cluster the detected lines.*/
	hneighbors=(bullseye_finder_line **)pCDecoMemObj->mdMemAlloc(nhlines*sizeof(*hneighbors),sizeof(*hneighbors),MEM_ALLOC_BLOCK);
	if(hneighbors==NULL)
		return 0;
	/*We require more than one line per cluster, so there are at most nhlines/2.*/
	hclusters=(bullseye_finder_cluster *)pCDecoMemObj->mdMemAlloc((nhlines>>1)*sizeof(*hclusters),STRUCT_ALIGN,MEM_ALLOC_BLOCK);
	if(hclusters==NULL)
	{
		pCDecoMemObj->mdMemFree(hneighbors);
		return 0;
	}

	nhclusters=bullseye_finder_cluster_lines(hclusters,hneighbors,hlines,nhlines,0);	

	*_hclusters=hclusters;
    * _hneighbors = hneighbors;
	return nhclusters;
}

/*Adds the coordinates of the edge points from the lines contained in the
given list of clusters to the list of edge points for a finder center.
Only the edge point position is initialized.
The edge label and extent are set by bullseye_finder_edge_pts_aff_classify()
or bullseye_finder_edge_pts_hom_classify().
_edge_pts:   The buffer in which to store the edge points.
_nedge_pts:  The current number of edge points in the buffer.
_neighbors:  The list of lines in the cluster.
_nneighbors: The number of lines in the list of lines in the cluster.
_v:          0 for horizontal lines and 1 for vertical lines.
Return: The new total number of edge points.*/
s32 CDecoFinderMWRScannerSub::bullseye_finder_edge_pts_fill(bullseye_finder_edge_pt *_edge_pts,s32 _nedge_pts,
								   bullseye_finder_cluster **_neighbors,s32 _nneighbors,s32 _v)
{
	s32 i;		
	for(i=0;i<_nneighbors;i++)
	{
		bullseye_finder_cluster *c;
		s32                j;
		c=_neighbors[i];
		for(j=0;j<c->nlines;j++)
		{
			bullseye_finder_line *l;
			l=c->lines[j];
			if(l->boffs>0)
			{
				_edge_pts[_nedge_pts].pos[0] = l->pos[0];
				_edge_pts[_nedge_pts].pos[1] = l->pos[1];
				_edge_pts[_nedge_pts].pos[_v] -= l->boffs ;
				_nedge_pts++;
			}
			if(l->eoffs>0)
			{
				_edge_pts[_nedge_pts].pos[0] = l->pos[0];
				_edge_pts[_nedge_pts].pos[1] = l->pos[1];
				_edge_pts[_nedge_pts].pos[_v] += l->len+l->eoffs;
				_nedge_pts++;
			}
		}
	}
	return _nedge_pts;
}
/*Determine if a horizontal line crosses a vertical line.
_hline: The horizontal line.
_vline: The vertical line.
Return: A non-zero value if the lines cross, or zero if they do not.*/
s32 CDecoFinderMWRScannerSub::bullseye_finder_lines_are_crossing(const bullseye_finder_line *_hline,
										const bullseye_finder_line *_vline)
{
	return
		_hline->pos[0]<=_vline->pos[0] && _vline->pos[0]<_hline->pos[0]+_hline->len &&
		_vline->pos[1]<=_hline->pos[1] && _hline->pos[1]<_vline->pos[1]+_vline->len;
}


static s32 bullseye_finder_center_cmp(const void *_a,const void *_b)
{
	const bullseye_finder_center *a;
	const bullseye_finder_center *b;
	a=(const bullseye_finder_center *)_a;
	b=(const bullseye_finder_center *)_b;
	return (((b->nedge_pts>a->nedge_pts)-(b->nedge_pts<a->nedge_pts))<<2)+(((a->pos[1]>b->pos[1])-(a->pos[1]<b->pos[1]))<<1)+(a->pos[0]>b->pos[0])-(a->pos[0]<b->pos[0]);
}

/*Finds horizontal clusters that cross corresponding vertical clusters,
presumably corresponding to a finder center.
_center:     The buffer in which to store putative finder centers.
_edge_pts:   The buffer to use for the edge point lists for each finder
center.
_hclusters:  The clusters of horizontal lines crossing finder patterns.
_nhclusters: The number of horizontal line clusters.
_vclusters:  The clusters of vertical lines crossing finder patterns.
_nvclusters: The number of vertical line clusters.
Return: The number of putative finder centers.*/
s32 CDecoFinderMWRScannerSub::bullseye_finder_find_crossings(bullseye_finder_center *_centers,
									bullseye_finder_edge_pt *_edge_pts,bullseye_finder_cluster *_hclusters,s32 _nhclusters,
									bullseye_finder_cluster *_vclusters,s32 _nvclusters)
{
	bullseye_finder_cluster **hneighbors;
	bullseye_finder_cluster **vneighbors;
	u8      *hmark;
	u8      *vmark;
	s32                 ncenters=0;
	s32                 i,j;
	s32 x_len=0,y_len=0,x0=0,y0=0,t1=0,t2=0;
	f64 r1=0,r2=0;
	
	hneighbors = (bullseye_finder_cluster **)pCDecoMemObj->mdMemAlloc(_nhclusters*sizeof(*hneighbors),4,MEM_ALLOC_BLOCK);
	if(hneighbors==NULL)
		return 0;
	vneighbors = (bullseye_finder_cluster **)pCDecoMemObj->mdMemAlloc(_nvclusters*sizeof(*vneighbors),STRUCT_ALIGN,MEM_ALLOC_BLOCK);
	if(vneighbors==NULL)
	{
		pCDecoMemObj->mdMemFree(hneighbors);
		return 0;
	}
	hmark = (u8 *)pCDecoMemObj->mdMemAlloc(_nhclusters*sizeof(*hmark),sizeof(*hmark),MEM_ALLOC_BLOCK);
	if(hmark==NULL)
	{
		pCDecoMemObj->mdMemFree(vneighbors);
		pCDecoMemObj->mdMemFree(hneighbors);
		return 0;
	}
	vmark = (u8 *)pCDecoMemObj->mdMemAlloc(_nvclusters*sizeof(*vmark),sizeof(*vmark),MEM_ALLOC_BLOCK);
	if(vmark==NULL)
	{
		pCDecoMemObj->mdMemFree(hmark);
		pCDecoMemObj->mdMemFree(vneighbors);
		pCDecoMemObj->mdMemFree(hneighbors);
		return 0;
	}
	memset(hmark,0,_nhclusters);
	memset(vmark,0,_nvclusters);
	ncenters=0;
	
		
/*TODO: This may need some re-working.
	We should be finding groups of clusters such that _all_ horizontal lines in
	_all_ horizontal clusters in the group cross _all_ vertical lines in _all_
	vertical clusters in the group.
    This is equivalent to finding the maximum bipartite clique in the
	connectivity graph, which requires linear progamming to solve efficiently.
    In principle, that is easy to do, but a realistic implementation without
	floating point is a lot of work (and computationally expensive).
    Right now we are relying on a sufficient border around the finder patterns
	to prevent false positives.*/
	for(i=0;i<_nhclusters;i++)
		if(!hmark[i])
		{
			bullseye_finder_line *a;
			bullseye_finder_line *b;
			s32             nvneighbors;
			s32             nedge_pts;
			s32             x;
			s32            isReverse;
			a=_hclusters[i].lines[_hclusters[i].nlines>>1];
			x=nvneighbors=0;
			isReverse = _hclusters[i].isReverse;
			y_len=0;
			for(j=0;j<_nvclusters;j++)
			{			
				if((!vmark[j])/*&&isReverse==_vclusters[j].isReverse*/)
				{
					b=_vclusters[j].lines[_vclusters[j].nlines>>1];	
					if(bullseye_finder_lines_are_crossing(a,b) /*&& r1>0.6&& r2>0.6*/)
					{
						vmark[j]=1;
						x+=(b->pos[0]<<1);
						y_len+=(b->len+b->boffs+b->eoffs)<<1;
						vneighbors[nvneighbors++]=_vclusters+j;
					}
				}
			}				
			if(nvneighbors>0)
			{
				bullseye_finder_center *c;
				s32               nhneighbors;
				s32               y;
				
				y=(a->pos[1]<<1);
				x_len=(a->len+a->boffs+a->eoffs)<<1;
//				hclen+=x_len;
				hneighbors[0]=_hclusters+i;
				nhneighbors=1;
				j=nvneighbors>>1;
				b=vneighbors[j]->lines[vneighbors[j]->nlines>>1];

				for(j=i+1;j<_nhclusters;j++)
				{
					if((!hmark[j])/*&&(isReverse==_hclusters[j].isReverse)*/)
					{
						a=_hclusters[j].lines[_hclusters[j].nlines>>1];
						t1=a->pos[0]+a->len-b->pos[0];
						t2=b->pos[0]-a->pos[0];
						r1 = t1 < t2 ? (f64)t1/(f64)t2 : (f64)t2/(f64)t1;
						t1=b->pos[1]+b->len-a->pos[1];
						t2=a->pos[1]-b->pos[1];
						r2 = t1 < t2 ? (f64)t1/(f64)t2 : (f64)t2/(f64)t1;
						if(bullseye_finder_lines_are_crossing(a,b) && r1>0.6 && r2>0.6)
						{
							hmark[j]=1;
							y+=(a->pos[1]<<1);		
							x_len+=(a->len+a->boffs+a->eoffs)<<1;
							hneighbors[nhneighbors++]=_hclusters+j;
						}
					}
				}//end for(j=i+1;j<_nhclusters;j++)	  
				
				if (!nvneighbors || !nhneighbors)
					continue;

				x_len=(x_len+nhneighbors)/(nhneighbors<<1);
				y_len=(y_len+nvneighbors)/(nvneighbors<<1);			
				y0=(y+nhneighbors)/(nhneighbors<<1);
				x0=(x+nvneighbors)/(nvneighbors<<1);
			
				c=_centers+ncenters++;
				c->pos[1]=y0;
				c->pos[0]=x0;
				c->x_len=x_len>>1;
				c->y_len=y_len>>1;
				c->isReverse = isReverse;
				c->edge_pts=_edge_pts;
				
				nedge_pts=bullseye_finder_edge_pts_fill(_edge_pts,0,hneighbors,nhneighbors,0);			
				c->nh_pts = nedge_pts;	
				nedge_pts=bullseye_finder_edge_pts_fill(_edge_pts,nedge_pts,vneighbors,nvneighbors,1);			
				c->nv_pts = nedge_pts - c->nh_pts;			
				c->nedge_pts=nedge_pts;
				_edge_pts+=nedge_pts;
			}
		}//end of for(i=0;i<_nhclusters;i++)	
	pCDecoMemObj->mdMemFree(vmark);
	pCDecoMemObj->mdMemFree(hmark);
	pCDecoMemObj->mdMemFree(vneighbors);
	pCDecoMemObj->mdMemFree(hneighbors);
	/*Sort the centers by decreasing numbers of edge points.*/
	qsort(_centers,ncenters,sizeof(*_centers),bullseye_finder_center_cmp);
	return ncenters;
}

/*Locates a set of putative finder centers in the image.
First we search for horizontal and vertical lines that have
(dark:light:dark:light:dark) runs with size ratios of roughly (1:1:3:1:1).
Then we cluster them into groups such that each subsequent pair of endpoints
is close to the line before it in the cluster.
This will locate many line clusters that don't cross a finder pattern, but
bullseye_finder_find_crossings() will filter most of them out.
Where horizontal and vertical clusters cross, a prospective finder center is
returned.
_centers:  Returns a pointer to a freshly-allocated list of finder centers.
This must be freed by the caller.
_edge_pts: Returns a pointer to a freshly-allocated list of edge points
around those centers.
This must be freed by the caller.
_img:      The binary image to search.
_width:    The width of the image.
_height:   The height of the image.
Return: The number of putative finder centers located.*/
s32 CDecoFinderMWRScannerSub::bullseye_finder_centers_locate(bullseye_finder_center **_centers,
							 bullseye_finder_edge_pt **_edge_pts,bullseye_reader *reader,
							 bullseye_finder_cluster *hclusters,s32 nhclusters, MdImage *img)
{
	bullseye_finder_line    *vlines = reader->finder_lines[1].lines;
	s32                  nvlines = reader->finder_lines[1].nlines;
	bullseye_finder_line    **vneighbors;
	bullseye_finder_cluster  *vclusters;
	s32                 nvclusters;
	s32                 ncenters=0;
	bullseye_finder_center   *centers;
	/*Cluster the detected lines.*/
	vneighbors=(bullseye_finder_line **)pCDecoMemObj->mdMemAlloc(nvlines*sizeof(*vneighbors),sizeof(*vneighbors),MEM_ALLOC_BLOCK);
	if(vneighbors==NULL)
		return 0;
	/*We require more than one line per cluster, so there are at most nvlines/2.*/
	vclusters=(bullseye_finder_cluster *)pCDecoMemObj->mdMemAlloc((nvlines>>1)*sizeof(*vclusters),STRUCT_ALIGN,MEM_ALLOC_BLOCK);
	if(vclusters==NULL)
	{
		pCDecoMemObj->mdMemFree(vneighbors);
		return 0;
	}

	/*We need vertical lines to be sorted by X coordinate, with ties broken by Y
	coordinate, for clustering purposes.
    We scan the image in the opposite order for cache efficiency, so sort the
	lines we found here.*/
	nvclusters=bullseye_finder_cluster_lines(vclusters,vneighbors,vlines,nvlines,1);	
	{
#ifdef BULLSEYE_DEBUG
		s32 i,j;
 		cvNamedWindow("ROI img",0);
		for (i=0;i<nhclusters;i++)
		{
			for (j=0;j<hclusters[i].nlines;j++)
			{
				CvPoint pt1,pt2;
				pt1.x=(hclusters[i].lines[j]->pos[0]-hclusters[i].lines[j]->boffs)>>2;
				pt1.y=hclusters[i].lines[j]->pos[1]>>2;
				pt2.x=(hclusters[i].lines[j]->pos[0]+hclusters[i].lines[j]->len+hclusters[i].lines[j]->eoffs)>>2;
				pt2.y=hclusters[i].lines[j]->pos[1]>>2;
				cvLine(debug_img,pt1,pt2,CV_RGB(255,i*50%255,0),1,8,0);
			}
		}
		for (i=0;i<nvclusters;i++)
		{
			for (j=0;j<vclusters[i].nlines;j++)
			{
				CvPoint pt1,pt2;
				pt1.x=vclusters[i].lines[j]->pos[0]>>2;
				pt1.y=(vclusters[i].lines[j]->pos[1]-vclusters[i].lines[j]->boffs)>>2;
				pt2.x=vclusters[i].lines[j]->pos[0]>>2;
				pt2.y=(vclusters[i].lines[j]->pos[1]+vclusters[i].lines[j]->len+vclusters[i].lines[j]->eoffs)>>2;
				cvLine(debug_img,pt1,pt2,CV_RGB(255,i*50%255,0),1,8,0);
			}
		}	
 		cvShowImage("ROI img",debug_img);
		cvWaitKey(0);
#endif
	}	
	/*Find line crossings among the clusters.*/
	if(nhclusters>=1 && nvclusters>=1)
	{		
		bullseye_finder_edge_pt  *edge_pts;
		s32                   nedge_pts;
		s32                   i;
		nedge_pts=0;
		for(i=0;i<nhclusters;i++)
			nedge_pts+=hclusters[i].nlines;
		for(i=0;i<nvclusters;i++)
			nedge_pts+=vclusters[i].nlines;
		nedge_pts<<=1;

		edge_pts=(bullseye_finder_edge_pt *)pCDecoMemObj->mdMemAlloc(nedge_pts*sizeof(*edge_pts),STRUCT_ALIGN,MEM_ALLOC_BLOCK);
		if(edge_pts==NULL)
		{
			pCDecoMemObj->mdMemFree(vclusters);
			pCDecoMemObj->mdMemFree(vneighbors);
			return 0;
		}
		centers=(bullseye_finder_center *)pCDecoMemObj->mdMemAlloc(BULLSEYE_MINI(nhclusters,nvclusters)*sizeof(*centers),STRUCT_ALIGN,MEM_ALLOC_BLOCK);
		if(centers==NULL)
		{
			pCDecoMemObj->mdMemFree(edge_pts);
			pCDecoMemObj->mdMemFree(vclusters);
			pCDecoMemObj->mdMemFree(vneighbors);
			return 0;
		}
		ncenters=bullseye_finder_find_crossings(centers,edge_pts,hclusters,nhclusters,vclusters,nvclusters);  // edge_pts unclassified
				
		*_centers=centers;
		*_edge_pts=edge_pts;	
		if (ncenters<1)
		{
			pCDecoMemObj->mdMemFree(centers);
			pCDecoMemObj->mdMemFree(edge_pts);
		}		
	}
	else 
		ncenters=0;
	pCDecoMemObj->mdMemFree(vclusters);
	pCDecoMemObj->mdMemFree(vneighbors);
	return ncenters;
 }						 
//-------------------------------end of file-------------------------------------------------------
