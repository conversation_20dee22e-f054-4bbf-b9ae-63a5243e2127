
#ifndef   _DES_
#define   _DES_ 



typedef int                     s32;
typedef unsigned int            u32;
typedef short int               s16;
typedef unsigned short          u16;
typedef signed char             s8;
typedef unsigned char           u8;
typedef float                   f32;
typedef double                  f64;


#define		DESENCRY		0
#define		DESDECRY		1

extern s32 DdesN(u8 *data, u8 **key, u32 n_key,u32 readlen);
extern s32 desN(u8 *data, u8 **key, u32 n_key,u32 readlen);
extern s32 des3(u8 *data, u8 *key,u32 n ,u32 readlen);
extern s32 Ddes3(u8 *data,u8 *key,u32 n ,u32 readlen);
extern s32 des(u8 *data, u8 *key,s32 readlen);
extern s32 Ddes(u8 *data,u8 *key,s32 readlen);
extern s32 handle_data(u32 *left , u8 choice);
extern s32 makedata(u32  *left ,u32  *right ,u32 number) ;
extern s32 makefirstkey( u32 *keyP );
extern s32 makekey(  u32 *keyleft,u32 *keyright ,u32 number);

#endif
