import os
import subprocess
from mecho import MEcho

_vkey_, _args_, _opts_ = None, [], {}


def check_param_with_key(inputs):
    global _vkey_, _args_, _opts_
    for idx, arg in enumerate(inputs):
        if arg.startswith("--"):
            if "=" in arg:
                key, val = arg[2::].split("=", 1)
                _opts_[key] = val
            else:
                _opts_[arg[2::]] = True
        elif idx == 0:
            continue
        elif _vkey_ is None:
            _vkey_ = arg
        else:
            _args_.append(arg)
    return _vkey_, _args_, _opts_


def find_opt(name, defi=None):
    return _opts_.get(name, defi)


def add_env_var(name, value):
    old_value = os.environ.get(name, '')
    os.environ[name] = value + os.pathsep + old_value


def set_env_var(name, value):
    os.environ[name] = value


def source(envs):
    for line in envs:
        temp, value = line.split("=", 1)
        name, opt = temp[0:-1], temp[-1]
        if opt == "+":
            add_env_var(name, value)
        elif opt == ":":
            set_env_var(name, value)


def check_deps(outs, deps):
    if len(outs) == 0:
        return True
    earliest_mtime = 0
    for out in outs:
        if not os.path.exists(out):
            return True
        mtime = os.path.getmtime(out)
        if mtime < earliest_mtime or earliest_mtime == 0:
            earliest_mtime = mtime
    for dep in deps:
        if not os.path.exists(dep):
            return True
        mtime = os.path.getmtime(dep)
        if mtime > earliest_mtime:
            return True
    return False


def run_for_ret(command):
    try:
        result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                shell=True, check=True, universal_newlines=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        return None


def run_by_str(cmd, quite=False, hand=None, que=False):
    if que:
        yes = input(f"{cmd} Y/n?")
        if yes not in ["", "Y"]:
            MEcho.quit("abort")
    try:
        if quite:
            result = subprocess.run(cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, shell=True)
        else:
            result = subprocess.run(cmd, shell=True)
        if result.returncode:
            raise Exception(result.returncode)
    except:
        if hand:
            hand()
        else:
            MEcho.quit("Failed:\n   ", cmd)


def run_by_list(cmd):
    ret = subprocess.run(cmd)
    return ret.returncode
