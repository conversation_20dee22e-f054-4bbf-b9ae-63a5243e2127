
import os
import json


def _replace_by_env_path_(bin):
    paths = os.environ["PATH"]
    for path in paths.split(":"):
        path = os.path.join(path, bin)
        if os.path.exists(path):
            return path
    return bin


def _find_src_out_(clips):
    i, pre, cur = 1, clips[0], None
    out, src = None, None
    while i < len(clips):
        cur, i = clips[i], i + 1
        if pre in {"-MT", "-MF"}:
            pre = cur
            continue
        if pre in {"-o"}:
            out, pre = cur, cur
            if out and src:
                return src, out
            continue
        src_typs = [".c", ".cpp"]
        for typ in src_typs:
            if cur.endswith(typ):
                src = cur
                if out and src:
                    return src, out
        pre = cur
    return src, out


class Clangd:
    def __init__(self):
        self.data = []

    def run(self, cmd, deps, msg, outs, bar):
        src, out = _find_src_out_(cmd)
        if not src or not out:
            return
        if not os.path.exists(src):
            return
        cmd[0] = _replace_by_env_path_(cmd[0])
        item = {
            "arguments": cmd,
            "file": src,
            "output": out,
            "directory": os.getcwd()
        }
        self.data.append(item)

    def print(self, msg, color):
        pass

    def proc(self):
        with open("compile_commands.json", "w") as fp:
            json.dump(self.data, fp, indent=4)
