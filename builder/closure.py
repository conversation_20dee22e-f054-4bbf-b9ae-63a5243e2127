def find_closure(metas, apps):
    def _find_deps_(name):
        return metas[name].get("DEPS", [])
    closure = {}
    while apps:
        app = apps.pop()
        if app in closure:
            continue
        i, deps = 0, _find_deps_(app)
        while i < len(deps):
            if deps[i] not in closure:
                apps.append(app)
                break
            i = i + 1
        if i == len(deps):
            closure[app] = metas[app]
            continue
        while i < len(deps):
            dep, i = deps[i], i + 1
            if dep not in closure:
                apps.append(dep)
    return closure
