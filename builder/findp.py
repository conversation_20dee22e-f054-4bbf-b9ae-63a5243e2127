#!/usr/bin/python3

import os
import fnmatch

from mecho import MEcho


class Findp:
    TRA = 0
    SUB = 1
    CUR = 2
    DEF = 0
    DEL = 1
    ADD = 2
    DIR = 0x01
    FIL = 0x02
    LNK = 0x04
    ANY = 0x07

    @staticmethod
    def scatter(lines):
        paths = []
        rules = []
        for line in lines:
            try:
                typx, path = line.split(maxsplit=1)
                if typx == ">>":
                    paths.append((Findp.TRA, path))
                elif typx == "+>":
                    paths.append((Findp.SUB, path))
                elif typx == "->":
                    paths.append((Findp.CUR, path))
                elif typx == "+a":
                    rules.append((Findp.ADD, Findp.ANY, path))
                elif typx == "-a":
                    rules.append((Findp.DEL, Findp.ANY, path))
                elif typx == "+d":
                    rules.append((Findp.ADD, Findp.DIR, path))
                elif typx == "-d":
                    rules.append((Findp.DEL, Findp.DIR, path))
                elif typx == "+f":
                    rules.append((Findp.ADD, Findp.FIL, path))
                elif typx == "-f":
                    rules.append((Findp.DEL, Findp.FIL, path))
                elif typx == "+l":
                    rules.append((Findp.ADD, Findp.FIL, path))
                elif typx == "-l":
                    rules.append((Findp.DEL, Findp.FIL, path))
            except ValueError:
                continue
        return paths, rules

    @staticmethod
    def determine(path, rules):
        def typmatch(path, typ):
            if typ == Findp.ANY:
                return True
            if typ == Findp.FIL:
                return os.path.isfile(path)
            if typ == Findp.DIR:
                return os.path.isdir(path)
            if typ == Findp.LNK:
                return os.path.islink(path)
            return False

        back = Findp.DEF
        for opt, typ, pat in reversed(rules):
            if not typmatch(path, typ):
                continue
            if not fnmatch.fnmatch(path, pat):
                continue
            back = opt
            break
        MEcho.d(f"{back} {path}")
        return back

    @staticmethod
    def findout(rules, stack):
        outs = []
        while stack:
            typ, pth = stack.pop(0)
            opt = Findp.determine(pth, rules)
            if opt == Findp.DEL:
                continue
            if opt == Findp.ADD:
                outs.append(pth)
            if typ == Findp.CUR:
                continue
            if typ == Findp.TRA:
                subs = []
                for sub in os.listdir(pth):
                    if pth != ".":
                        sub = os.path.join(pth, sub)
                    if os.path.isdir(sub):
                        subs.append((Findp.TRA, sub))
                    else:
                        subs.append((Findp.CUR, sub))
                stack = subs + stack
                continue
            if typ == Findp.SUB:
                subs = []
                for sub in os.listdir(pth):
                    if pth != ".":
                        sub = os.path.join(pth, sub)
                    subs.append((Findp.CUR, sub))
                stack = subs + stack
                continue
        return outs

    @staticmethod
    def find(scat):
        stack, rules = Findp.scatter(scat)
        return Findp.findout(rules, stack)
