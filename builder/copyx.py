import os
import re
import shutil
import hashlib
import json


def copyx(application):
    """Copy source files and their dependencies"""
    copy_path = application.get("COPY_PATH", "build/src")
    srcs = application.get("SRCS", [])
    incp = application.get("INCP", [])

    copied_headers = set()

    def get_file_md5(file_path):
        if not os.path.exists(file_path):
            return None
        with open(file_path, 'rb') as f:
            return hashlib.md5(f.read()).hexdigest()

    def ensure_dir(file_path):
        dir_path = os.path.dirname(file_path)
        if dir_path and not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)

    def copy_file_with_path(src_file, dest_base):
        dest_file = os.path.join(dest_base, src_file)

        # Check if files are identical using MD5
        src_md5 = get_file_md5(src_file)
        dest_md5 = get_file_md5(dest_file)

        if src_md5 == dest_md5:
            return

        ensure_dir(dest_file)
        shutil.copy2(src_file, dest_file)
        print(f"Copied: {src_file} -> {dest_file}")

    def find_header_in_paths(header_name, search_paths):
        for path in search_paths:
            header_path = os.path.join(path, header_name)
            if os.path.exists(header_path):
                return header_path
        return None

    def extract_headers_from_file(file_path):
        if not os.path.exists(file_path):
            return []

        headers = []
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # Find headers with double quotes
        pattern = r'#include\s*"([^"]+)"'
        matches = re.findall(pattern, content)
        headers.extend(matches)

        return headers

    def copy_headers_recursive(file_path, search_paths, dest_base):
        headers = extract_headers_from_file(file_path)

        for header in headers:
            if header in copied_headers:
                continue

            header_path = find_header_in_paths(header, search_paths)
            if not header_path:
                continue

            copied_headers.add(header)
            copy_file_with_path(header_path, dest_base)
            copy_headers_recursive(header_path, search_paths, dest_base)

    def generate_makefile():
        makefile_path = os.path.join(copy_path, "Makefile")

        # Extract variables from application
        arch = application.get("ARCH", "")
        copt = " ".join(application.get("COPT", []))
        lopt = " ".join(application.get("LOPT", []))
        name = application.get("NAME", "output")

        # Get command templates
        cc_template = application.get("CC", "gcc -o $OBJ -c $SRC $INCLUDE")
        cx_template = application.get("CX", "g++ -o $OBJ -c $SRC $INCLUDE")
        lr_template = application.get("LR", "gcc -o $OUTPUTS $OBJS")

        # Get source files that were actually copied
        copied_srcs = []
        for src in srcs:
            _, typ = os.path.splitext(src)
            if typ not in [".c", ".cpp", ".cc", ".cxx"]:
                continue
            if os.path.exists(src):
                copied_srcs.append(src)

        # Write Makefile
        with open(makefile_path, 'w') as f:
            f.write("# Auto-generated Makefile\n\n")

            # Variables
            f.write(f"TARGET = {name}\n")
            f.write(f"ARCH = {arch}\n")
            f.write(f"COPT = {copt}\n")
            f.write(f"LOPT = {lopt}\n\n")

            # Header list
            f.write("INCP = \\\n")
            for i, inc in enumerate(incp):
                f.write(f"\t{inc} \\\n")
            f.write(f"\t.\n")
            f.write(f"INCLUDES = $(INCP:%=-I%)\n\n")

            # Source list
            f.write("SRCS = \\\n")
            for i, src in enumerate(copied_srcs):
                if i == len(copied_srcs) - 1:
                    f.write(f"\t{src}\n\n")
                else:
                    f.write(f"\t{src} \\\n")

            # Object files
            f.write("OBJS = $(SRCS:.c=.o)\n")
            f.write("OBJS := $(OBJS:.cpp=.o)\n")
            f.write("OBJS := $(OBJS:.cc=.o)\n")
            f.write("OBJS := $(OBJS:.cxx=.o)\n\n")

            # Dependency files
            f.write("DEPS = $(OBJS:.o=.d)\n\n")

            # Default target
            f.write("all: $(TARGET)\n\n")

            # Link rule - replace variables in LR template
            link_cmd = lr_template.replace("$ARCH", "$(ARCH)")
            link_cmd = link_cmd.replace("$OUTPUTS", "$@")
            link_cmd = link_cmd.replace("$OBJS", "$^")
            link_cmd = link_cmd.replace("$LOPT", "$(LOPT)")
            link_cmd = link_cmd.replace("$LIBRARY", "")
            f.write("$(TARGET): $(OBJS)\n")
            f.write(f"\t@echo \"Linking {name}\"\n")
            f.write(f"\t@{link_cmd}\n\n")

            # Compile rules - replace variables in CC/CX templates
            def make_compile_rule(template, pattern, target):
                compile_cmd = template.replace("$ARCH", "$(ARCH)")
                compile_cmd = compile_cmd.replace("$COPT", "$(COPT)")
                compile_cmd = compile_cmd.replace("$OBJ", "$@")
                compile_cmd = compile_cmd.replace("$SRC", "$<")
                compile_cmd = compile_cmd.replace("$INCLUDE", "$(INCLUDES)")
                compile_cmd = compile_cmd.replace("$DEP", "$(@:.o=.d)")
                f.write(f"{pattern}: {target}\n")
                f.write(f"\t@echo \"Compiling $<\"\n")
                f.write(f"\t@{compile_cmd}\n\n")

            make_compile_rule(cc_template, "%.o", "%.c")
            make_compile_rule(cx_template, "%.o", "%.cpp")
            make_compile_rule(cx_template, "%.o", "%.cc")
            make_compile_rule(cx_template, "%.o", "%.cxx")

            # Include dependency files
            f.write("-include $(DEPS)\n\n")

            # Clean rule
            f.write("clean:\n")
            f.write("\t@rm -f $(OBJS) $(DEPS) $(TARGET)\n\n")
            f.write("\t@echo \"Cleaned $(TARGET)\"\n\n")

            f.write(".PHONY: all clean\n")

        domain = application.get("DOMAIN", None)
        if domain:
            domain_file = f"{domain}.mk"
            domain_path = os.path.join(copy_path, domain_file)
            os.rename(makefile_path, domain_path)
            print(f"Generated Makefile: {domain_path}")
            with open(makefile_path, 'w') as f:
                f.write(f"# Default {domain}, but you can change it\n")
                f.write(f"include {domain_file}\n")

        print(f"Generated Makefile: {makefile_path}")

    # Copy source files
    for src in srcs:
        _, typ = os.path.splitext(src)
        if typ not in [".c", ".cpp", ".cc", ".cxx"]:
            continue
        if not os.path.exists(src):
            print(f"Warning: Source file not found: {src}")
            continue
        copy_file_with_path(src, copy_path)
        copy_headers_recursive(src, incp + [os.path.dirname(src), "."], copy_path)

    # Generate json file
    json_path = os.path.join(copy_path, application.get("DOMAIN", "build") + ".json")
    with open(json_path, 'w') as f:
        json.dump(application, f, indent=4)
    print(f"Generated json file: {json_path}")

    # Generate Makefile after copying
    generate_makefile()

    print(f"Copy completed to: {copy_path}")
