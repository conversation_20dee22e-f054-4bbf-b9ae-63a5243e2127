import os
import toml
import ddict
import fnmatch
from findp import Findp


def _find_by_rule_(rule, root):
    back = os.getcwd()
    os.chdir(root)
    files = Findp.find(rule.split("\n"))
    os.chdir(back)
    return [os.path.join(root, one) for one in files]


def _find_path_(paths, root):
    if isinstance(paths, str):
        return _find_by_rule_(paths, root)
    assert isinstance(paths, list)
    outs = []
    for path in paths:
        path = os.path.normpath(os.path.join(root, path))
        if "*" not in path:
            if path not in outs:
                outs.append(path)
            continue
        dirpath, filepats = os.path.split(path)
        assert "*" not in dirpath
        filepats = filepats.split("|")
        for item in os.listdir(dirpath):
            for filepat in filepats:
                if fnmatch.fnmatch(item, filepat):
                    outs.append(os.path.join(dirpath, item))
                    break
    return outs


def _split_opt_(cont):
    if isinstance(cont, str):
        return cont.split()
    assert isinstance(cont, list)
    outs = []
    for item in cont:
        assert isinstance(item, str)
        outs.extend(item.split())
    return outs


def load(path):
    root = os.path.dirname(path)

    def _reload_(meta):
        out = {}
        for key, cont in meta.items():
            if key in {"srcs", "incp", "libp"}:
                cont = _find_path_(cont, root)
            elif key in {"lopt", "copt"}:
                cont = _split_opt_(cont)
            out[key.upper()] = cont
        return out

    def _reload_copy_(meta, copy):
        if copy:
            out = {}
            ddict.update(out, copy)
            ddict.update(out, _reload_(meta))
            return out
        else:
            return _reload_(meta)

    data = toml.load(path)
    apps, dels, hide = {}, [], {}

    def _find_copy_(app):
        if "copy" not in app:
            return None
        copy = app.pop("copy")
        if copy in apps:
            return apps[copy]
        elif copy in hide:
            return hide[copy]
        return None

    for name, app in data.items():
        if not isinstance(app, dict):
            continue
        app = _reload_copy_(app, _find_copy_(app))
        if app.pop("HIDE", False):
            hide[name] = app
        else:
            apps[name] = app
        dels.append(name)
    for name in dels:
        del data[name]
    data["TARGETS"] = apps

    return data
