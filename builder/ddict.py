import json


class MyEncoder(json.JSONEncoder):
    def default(self, obj):
        if callable(obj):
            return obj.__name__
        return super().default(obj)


def update(origin, addend, filter=None):
    for key, val in addend.items():
        if filter and key in filter:
            continue
        if type(val) == dict:
            if key not in origin or type(origin[key]) != dict:
                origin[key] = {}
            update(origin[key], val)
        elif type(val) == list:
            if key not in origin or type(origin[key]) != list:
                origin[key] = []
            origin[key].extend(val)
        else:
            origin[key] = val


def dump(meta, fp):
    json.dump(meta, fp, cls=MyEncoder, indent=4)
