#!python3

import os
import sys
import dutil
import ddict
import shutil
import inspect
import importlib.util
from mecho import MEcho

from closure import find_closure
from stoml import load as _load_toml_
from clike import build as _build_clike_
from copyx import copyx as _build_copyx_
from version import fdver as _build_fdver_
from version import upver as _build_upver_
from clangd import Clangd
from tpool import Tpool

MEcho.level = 3


def _build_meta_(loaders, filter):
    meta = {}

    def _filter_(cfg):
        with open(cfg, "r") as fp:
            line = fp.readline()
        if not line.startswith("# @"):
            return True
        domains = set([item.strip() for item in line[3::].split(",")])
        if not domains.isdisjoint(filter):
            return True
        return False

    def _try_load_(path, cfg):
        typx = cfg.split(".")[-1]
        if typx not in loaders:
            return
        cfg = os.path.join(path, cfg)
        if not os.access(cfg, os.R_OK):
            return
        if not _filter_(cfg):
            return
        data = loaders[typx](cfg)
        if data:
            ddict.update(meta, data)

    def _try_load_dir_(path):
        for dm in os.listdir(path):
            _try_load_(path, dm)

    with open("components.ver", "r") as fp:
        for line in fp:
            _, path = line.strip().split()
            _try_load_dir_(path)
    for item in os.listdir("package"):
        _try_load_dir_(os.path.join("package", item))

    apps = meta.pop("TARGETS", {})
    return meta, apps


def _build_dumpc_(workspace, applications, name):
    out = {"META": workspace, "APPS": applications}
    name = list(applications.keys())[-1]
    build_path = workspace.get("BUILD_PATH", "build")
    output_path = f"{build_path}/dump/{name}.dmake.json"
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    with open(output_path, "w") as fp:
        ddict.dump(out, fp)


def _build_clean_(workspace, applications, names):
    build_path = workspace.get("BUILD_PATH", "build")
    MEcho.i(f"[---][RM] {build_path}")
    if os.path.exists(build_path):
        shutil.rmtree(build_path)


def _build_depsh_(workspace, applications, names):
    names = names if names else list(applications.keys())
    map = {key: ([], []) for key in names}  # (followers, dependencies)

    for app in applications:
        deps = find_closure(applications, [app]).keys()
        for name in names:
            if name in deps:
                map[name][0].append(app)
            if name == app:
                map[name][1].extend(deps)

    for name, (followers, deps) in map.items():
        MEcho.c(MEcho.BLUE, name)
        deps.remove(name)
        if deps:
            deps = ", ".join(deps)
            MEcho.c(MEcho.GREEN, f"    {deps}")
        followers.remove(name)
        if followers:
            followers = ", ".join(followers)
            MEcho.c(MEcho.RED, f"    {followers}")


def _load_function_from_dir(scripts_dir, function_name):
    if not os.path.isdir(scripts_dir):
        return None

    try:
        # Add scripts directory to sys.path temporarily for relative imports
        abs_scripts_dir = os.path.abspath(scripts_dir)
        path_added = False
        if abs_scripts_dir not in sys.path:
            sys.path.insert(0, abs_scripts_dir)
            path_added = True
        # Try to load the function from each Python file
        for filename in os.listdir(scripts_dir):
            if not filename.endswith('.py') or filename.startswith('_'):
                continue
            file_path = os.path.join(scripts_dir, filename)
            try:
                spec = importlib.util.spec_from_file_location("_splugin_", file_path)
                if spec is None:
                    continue
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                return getattr(module, function_name)
            except:
                continue
        return None
    finally:
        # Clean up sys.path
        if path_added and abs_scripts_dir in sys.path:
            sys.path.remove(abs_scripts_dir)


def _find_builder_(vkey):
    builders = {
        "build": _build_clike_,
        "clean": _build_clean_,
        "dumpc": _build_dumpc_,
        "fdver": _build_fdver_,
        "upver": _build_upver_,
        "depsh": _build_depsh_,
        "copyx": _build_copyx_,
    }

    # First check built-in builders
    if vkey in builders:
        return builders[vkey]

    # Search in component directories from components.ver
    try:
        with open("components.ver", "r") as fp:
            for line in fp:
                _, component_path = line.strip().split()
                scripts_dir = os.path.join(component_path, "scripts")
                found_func = _load_function_from_dir(scripts_dir, vkey)
                if found_func:
                    return found_func
    except (FileNotFoundError, ValueError):
        pass

    # Search in package directories
    if os.path.isdir("package"):
        for item in os.listdir("package"):
            scripts_dir = os.path.join("package", item, "scripts")
            found_func = _load_function_from_dir(scripts_dir, vkey)
            if found_func:
                return found_func

    raise KeyError(f"Builder function '{vkey}' not found")


def _find_loaders_():
    loaders = {
        "toml": _load_toml_,
    }
    return loaders


def main():
    vkey, args, opts = dutil.check_param_with_key(sys.argv)

    if (dutil.find_opt("vbose")):
        MEcho.limit(MEcho.VERBOSE)

    _workspace_, _applications_, _groups_, _host_ = None, None, None, None

    def _load_meta_():
        loaders = _find_loaders_()
        domain = dutil.find_opt("domain")
        filter = args + domain.split(",") if domain else args
        meta, apps = _build_meta_(loaders, filter)
        for key, val in opts.items():
            meta[key.upper()] = val
        dutil.source(meta.get("ENVS", []))
        return meta, apps

    def _find_workspace_():
        nonlocal _workspace_, _applications_
        if _workspace_:
            return _workspace_
        _workspace_, _applications_ = _load_meta_()
        return _workspace_

    def _find_applications_():
        nonlocal _workspace_, _applications_
        if _applications_:
            return _applications_
        _workspace_, _applications_ = _load_meta_()
        return _applications_

    def _find_names_():
        if args:
            return args
        return list(_find_applications_().keys())

    def _find_groups_():
        nonlocal _groups_
        if _groups_:
            return _groups_
        apps = _find_applications_()
        _groups_ = []
        for item in _find_names_():
            assert item in apps
            app = apps[item]
            if app.get("TYPE", "src") == "src":
                continue
            group = {}
            ddict.update(group, _find_workspace_())
            for application in find_closure(apps, [item]).values():
                ddict.update(group, application, filter={"DEPS"})
            group["HEAD"] = item
            _groups_.append(group)
        return _groups_

    def _find_host_():
        nonlocal _host_
        if not _host_:
            if dutil.find_opt("clangd", False):
                _host_ = Clangd()
            else:
                _host_ = Tpool()
        return _host_

    def _proc_host_():
        if _host_:
            _host_.proc()

    for key in vkey.split(","):
        builder = _find_builder_(key)
        plen = len(inspect.signature(builder).parameters)
        if plen == 0:
            builder()
        elif plen == 1:
            for group in _find_groups_():
                builder(group)
        elif plen == 2:
            for group in _find_groups_():
                builder(group, _find_host_())
        elif plen == 3:
            builder(_find_workspace_(), _find_applications_(), _find_names_())
    _proc_host_()


if __name__ == '__main__':
    main()
