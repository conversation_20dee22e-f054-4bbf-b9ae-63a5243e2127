
class MEcho:
    RED = '\033[31m'
    GREEN = '\033[32m'
    YELLOW = '\033[33m'
    BLUE = '\033[34m'
    PURPLE = '\033[35m'
    CYAN = '\033[36m'
    GRAY = '\033[37m'
    RESET = '\033[0m'

    ERROR = 1
    WARNING = 2
    INFO = 3
    DEBUG = 4
    VERBOSE = 5

    level = 5

    @staticmethod
    def limit(lv):
        MEcho.level = lv

    @staticmethod
    def echo(color, *args, **kwargs):
        clips = [color]
        sep = kwargs.get("sep", " ")
        for msg in args:
            clips.append(str(msg))
            clips.append(sep)
        clips.pop()
        clips.append(MEcho.RESET)
        print("".join(clips))

    @staticmethod
    def lecho(color, lv, *args, **kwargs):
        if MEcho.level >= lv:
            MEcho.echo(color, *args, **kwargs)

    @staticmethod
    def quit(*args, **kwargs):
        MEcho.lecho(MEcho.RED, MEcho.ERROR, *args, **kwargs)
        exit(1)

    def e(*args, **kwargs):
        MEcho.lecho(MEcho.RED, MEcho.ERROR, *args, **kwargs)

    @staticmethod
    def w(*args, **kwargs):
        MEcho.lecho(MEcho.YELLOW, MEcho.WARNING, *args, **kwargs)

    @staticmethod
    def i(*args, **kwargs):
        MEcho.lecho(MEcho.BLUE, MEcho.INFO, *args, **kwargs)

    @staticmethod
    def c(color, *args, **kwargs):
        MEcho.lecho(color, MEcho.INFO, *args, **kwargs)

    @staticmethod
    def d(*args, **kwargs):
        MEcho.lecho(MEcho.GRAY, MEcho.DEBUG, *args, **kwargs)

    @staticmethod
    def v(*args, **kwargs):
        MEcho.lecho(MEcho.GREEN, MEcho.VERBOSE, *args, **kwargs)
