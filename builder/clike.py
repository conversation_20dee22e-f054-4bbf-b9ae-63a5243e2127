import os
from mecho import MEcho
from tpool import Tpool


def _expand_cmd_(command, options):
    new_clips, replace_table = [], []
    for clip in command:
        if not clip.startswith("$"):
            new_clips.append(clip)
            continue
        replace_key = clip[1::]
        if replace_key in options:
            replace_cont = options[replace_key]
            if type(replace_cont) == list:
                new_clips += options[replace_key]
            else:
                new_clips.append(replace_cont)
        else:
            replace_table.append((len(new_clips), clip[1::]))
            new_clips.append(clip)
    return new_clips, replace_table


def _complete_cmd_(command, rtable, meta=None):
    if meta is None:
        meta = {}
    copy_clips = command[:]
    for idx, key in reversed(rtable):
        if key not in meta:
            copy_clips.pop(idx)
            continue
        clips = meta[key]
        if type(clips) == list:
            copy_clips = copy_clips[0:idx] + clips + copy_clips[idx + 1::]
        else:
            copy_clips[idx] = clips
    return copy_clips


def _read_dep_(pdep):
    outs = []
    if os.path.isfile(pdep):
        with open(pdep, "r", encoding="ascii") as fd:
            lines = fd.readlines()
        lines[0] = lines[0].split(":")[1]
        for line in lines:
            line = line.strip(" \r\n\t\\")
            outs += line.split()
    return outs


def _choose_cmd_(src):
    tb = [(".c", "CC"), (".cpp", "CX")]
    for typ, cmd in tb:
        if src.endswith(typ):
            return cmd
    raise Exception()


def build(application, host):
    def _invoke_cmd_(cmd, deps, outs, bar, **kwargs):
        if cmd not in application:
            return
        command, rtable = application[cmd]
        clips = _complete_cmd_(command, rtable, kwargs)
        msg = " ".join(outs) if outs else " ".join(clips)
        host.run(clips, deps, f"[{cmd}] " + msg, outs, bar)

    def _compile_(src, bpath):
        cmd = _choose_cmd_(src)
        dep, obj = os.path.join(bpath, src + ".d"), os.path.join(bpath, src + ".o")
        os.makedirs(os.path.dirname(obj), exist_ok=True)
        _invoke_cmd_(cmd, _read_dep_(dep), [obj], Tpool.NBAR, SRC=src, DEP=dep, OBJ=obj)
        return obj

    def _do_build_src_():
        objs = []
        for src in application.get("SRCS", []):
            try:
                obj = _compile_(src, object_path)
                objs.append(obj)
            except Exception:
                continue
        return objs

    def _do_build_exe_():
        objs = _do_build_src_()
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        _invoke_cmd_("LR", objs, [output_path], Tpool.FBAR, OBJS=objs)
        _invoke_cmd_("PO", [], [], Tpool.FBAR)
        host.print(f"[OK] {output_path}", MEcho.GREEN)

    def _find_name_():
        if "NAME" in application:
            return application["NAME"]
        head = application.get("HEAD", "hello")
        if "FIX" in application:
            return head + application["FIX"]
        return head

    name = _find_name_()
    build_path = application.get("BUILD_PATH", "build")
    object_path = os.path.join(build_path, name)
    output_dir = os.path.join(build_path, "output")
    output_path = os.path.join(output_dir, name)
    application["OUTPUTS"] = output_path
    application["INCLUDE"] = ["-I" + inc for inc in application.get("INCP", []) + ["."]]
    application["LIBRARY"] = ["-L" + inc for inc in application.get("LIBP", []) + [output_dir]]
    for key, value in application.items():
        if key not in {"AS", "CC", "CX", "LR", "PO"}:
            continue
        command = value.split()
        clips, reptb = _expand_cmd_(command, application)
        application[key] = (clips, reptb)

    _do_build_exe_()
