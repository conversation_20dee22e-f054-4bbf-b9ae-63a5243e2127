#!python3
import os
import queue
import threading
import dutil
from mecho import MEcho


class MyThread(threading.Thread):
    def __init__(self, hold):
        super().__init__(target=self._work_)
        self.idle_event = threading.Event()
        self.hold = hold

    def _work_(self):
        while self.hold.running:
            self.idle_event.clear()
            try:
                func, argv = self.hold.queue.get(True, 0.01)
                ret = func(argv)
                if ret:
                    self.hold.running = 0
                    break
            except queue.Empty:
                self.idle_event.set()
                pass
        self.idle_event.set()

    def wait(self):
        self.idle_event.wait()


class MyPool:
    def __init__(self, tnum):
        self.busy = 0
        self.queue = queue.Queue()
        self.running = 1
        self.threads = []
        for i in range(tnum):
            thread = MyThread(self)
            thread.start()
            self.threads.append(thread)

    def submit(self, func, argv):
        self.queue.put((func, argv))

    def flush(self):
        while True:
            if not self.running:
                exit(-1)
            for thread in self.threads:
                thread.wait()
            if self.queue.empty():
                break

    def finish(self):
        self.flush()
        self.running = 0
        for thread in self.threads:
            thread.join()


class Tpool:
    NBAR = 0x0
    BBAR = 0x1
    ABAR = 0x2
    FBAR = 0x3

    def __init__(self):
        self.cmds = []
        self.count = 0
        self.done = 0
        self.lock = threading.Lock()

    def run(self, cmd, deps, msg, outs, bar):
        self.cmds.append((cmd, deps, msg, outs, bar))
        self.count += 1

    def print(self, msg, color):
        self.cmds.append((msg, color))

    def chdir(self, path):
        self.cmds.append(path)

    def proc(self):
        def do_compile(vmeta):
            vmsg, vcmd = vmeta
            self.lock.acquire()
            MEcho.i(f"[{int(self.done / self.count * 100):02}%]{vmsg}")
            self.done += 1
            self.lock.release()
            return dutil.run_by_list(vcmd)

        def print_msg(vmeta):
            vmsg, vcolor = vmeta
            self.lock.acquire()
            MEcho.c(vcolor, f"[---]{vmsg}")
            self.lock.release()

        tp = MyPool(4)
        for meta in self.cmds:
            if type(meta) == str:
                tp.flush()
                os.chdir(meta)
                MEcho.d(f"[------->] {meta}")
                continue
            if len(meta) != 5:
                tp.submit(print_msg, meta)
                continue
            cmd, deps, msg, outs, bar = meta
            if Tpool.BBAR & bar:
                tp.flush()
            if dutil.check_deps(outs, deps):
                tp.submit(do_compile, (msg, cmd))
            else:
                self.lock.acquire()
                self.done += 1
                self.lock.release()
            if Tpool.ABAR & bar:
                tp.flush()
        tp.finish()
