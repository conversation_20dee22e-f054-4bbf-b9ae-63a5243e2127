import os
import dutil
import shutil


def _check_dirty_():
    dirty = os.system("git diff --quiet --exit-code")
    if dirty == 0:
        dirty = os.system("git diff --staged --quiet --exit-code")
    return dirty


def _read_hash_():
    return dutil.run_for_ret("git rev-parse HEAD")[0:8]


def _find_version_(path):
    back = os.getcwd()
    os.chdir(path)
    version = "00000000" if _check_dirty_() else _read_hash_()
    os.chdir(back)
    return version


def fdver():
    with open("components.ver", "w") as fp:
        for item in sorted(os.listdir()):
            if not os.path.isdir(os.path.join(item, ".git")):
                continue
            fp.write(f"{_find_version_(item):8} {item}\n")


def upver():
    with open("components.ver", "r") as input, open("components.ver.new", "w") as output:
        for line in input:
            _, path = line.strip().split()
            output.write(f"{_find_version_(path):8} {path}\n")
    shutil.move("components.ver.new", "components.ver")
