//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: bobject.c
// Author		: XuCF
// Created On	: 2024/07/13
// Description	: bobject.c
//
// History
// 1. V1.0, Created by XuCF. 2024/07/13
//=============================================================================

#include "cbeans.h"

static any _beans_tb_[BEAN_EXPORT_CNT] = {0};

any cbeans_find(int id)
{
	clike_assert(BEAN_EXPORT_CNT <= id, return 0);
	return _beans_tb_[id];
}

int cbeans_fill(any obj, int id)
{
	if (BEAN_EXPORT_CNT <= id) return -1;
	_beans_tb_[id] = obj;
	return 0;
}
