//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: cbeans.h
// Author		: XuCF
// Created On	: 2024/07/13
// Description	: cbeans.h
//
// History
// 1. V1.0, Created by XuCF. 2024/07/13
//=============================================================================

#ifndef _CBEANS_H
#define _CBEANS_H

#include "libcustom.h"
#include "cbeans.otb.h"
#include <stdarg.h>

any cbeans_find(int id);
int cbeans_fill(any obj, int id);

#define otb			  va_list _var
#define cbeans_pop(t) va_arg(_var, t)

#endif //_CBEANS_H
