// Auto generated, don't edit.
#include "libcustom.h"
typedef struct
{
	any objy; // obja
	any _obj0; // obja
} hello_t;
typedef struct
{
	any _obj1; // obja
	any _obj2; // obja
	any _obj3; // obja
	any objx; // objb
	hello_t comp; // hello_t
} cbeans_t;
non hello_init(hello_t *thiz, int va, int vb);
non cbeans_main(cbeans_t *thiz, int va, int vb);
int hello_free(hello_t *thiz);
int cbeans_exit(cbeans_t *thiz);
#define BEAN_OBJY 0
#define BEAN_OBJX 1
#define BEAN_EXPORT_CNT 2
