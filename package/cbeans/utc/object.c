//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: object.c
// Author		: XuCF
// Created On	: 2024/07/13
// Description	: object.c
//
// History
// 1. V1.0, Created by XuCF. 2024/07/13
//=============================================================================

#include "cbeans.h"
#include "cbeans.cst.h"

typedef struct
{
	int a;
	int b;
	str s;
} obja_t;

any obja_new(int a, int b, str s)
{
	obja_t *thiz = clike_new(obja_t);
	thiz->a = a;
	thiz->b = b;
	thiz->s = s;
	return thiz;
}

non obja_del(any thiz)
{
	clike_free(thiz);
}

void obja_do(obja_t *thiz)
{
	LOGI("%d + %d = %d", thiz->a, thiz->b, thiz->a + thiz->b);
	LOGI("str = %s", thiz->s);
}

typedef struct
{
	char c;
} objb_t;

any objb_new(otb)
{
	obja_t *obja;
	objb_t *thiz = clike_new(objb_t);
	thiz->c = cbeans_pop(int);
	while ((obja = cbeans_pop(obja_t *))) obja_do(obja);
	return thiz;
}

int objb_del(any obj)
{
	objb_t *thiz = obj;
	if (--thiz->c != 'u') return -1;
	clike_free(thiz);
	return 0;
}

void objb_do(objb_t *thiz)
{
	LOGI("char = %c", thiz->c);
}

int main(int argc, const char **args)
{
	LOGD("hello");

	cbeans_t demo;
	cbeans_main(&demo, 6, 3);

	objb_t *obj1 = cbeans_find(BEAN_OBJX);
	objb_do(obj1);

	obja_t *obj2 = cbeans_find(BEAN_OBJY);
	obja_do(obj2);

	obja_t *obj3 = demo._obj3;
	obja_do(obj3);

	while (cbeans_exit(&demo)) LOGI("exit");

	return 0;
}
