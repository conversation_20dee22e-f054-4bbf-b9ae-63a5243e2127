// Auto generated, don't edit.
#include "cbeans.h"
#include "macros.h"
any obja_new(int a, int b, str s);
any objb_new(otb);
non obja_del(any thiz);
int objb_del(any obj);
any _objb_new_wrap_(int fix, ...)
{
	any ret;
	va_list _var;
	va_start(_var, fix);
	ret = objb_new(_var);
	va_end(_var);
	return ret;
}
non hello_init(hello_t *thiz, int va, int vb)
{
	if (!thiz) return;
	thiz->objy = obja_new(TEST1, TEST2, "okay");
	cbeans_fill(thiz->objy, BEAN_OBJY);
	thiz->_obj0 = obja_new(va, vb, "yes");
}
non cbeans_main(cbeans_t *thiz, int va, int vb)
{
	if (!thiz) return;
	thiz->_obj1 = obja_new(1, 2, "hello you");
	thiz->_obj2 = obja_new(3, 4, "hello");
	thiz->_obj3 = obja_new(5, 6, "you");
	thiz->objx = _objb_new_wrap_(0, 'z', thiz->_obj1, thiz->_obj2, thiz->_obj3, 0);
	cbeans_fill(thiz->objx, BEAN_OBJX);
	hello_init(&thiz->comp, va, vb);
}
int hello_free(hello_t *thiz)
{
	if (!thiz) return 0;
	if (thiz->_obj0)
	{
		obja_del(thiz->_obj0);
		thiz->_obj0 = 0;
	}
	if (thiz->objy)
	{
		obja_del(thiz->objy);
		thiz->objy = 0;
		cbeans_fill(0, BEAN_OBJY);
	}
	return 0;
}
int cbeans_exit(cbeans_t *thiz)
{
	if (!thiz) return 0;
	if (hello_free(&thiz->comp)) return -1;
	if (thiz->objx)
	{
		if (objb_del(thiz->objx)) return -1;
		thiz->objx = 0;
		cbeans_fill(0, BEAN_OBJX);
	}
	if (thiz->_obj3)
	{
		obja_del(thiz->_obj3);
		thiz->_obj3 = 0;
	}
	if (thiz->_obj2)
	{
		obja_del(thiz->_obj2);
		thiz->_obj2 = 0;
	}
	if (thiz->_obj1)
	{
		obja_del(thiz->_obj1);
		thiz->_obj1 = 0;
	}
	return 0;
}
