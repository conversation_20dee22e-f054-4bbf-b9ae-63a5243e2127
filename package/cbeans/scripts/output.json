{"MyMain": {"main": ["// Auto generated, don't edit.\n#include \"cbeans.h\"\n", ["imports", "norm", ""], ["newcsts", "main_decl", ""], ["otbcsts", "main_decl", ""], ["delcsts", "exit_decl", ""], ["otbcsts", "main_wrap", ""], ["combins", "main_defi", ""], ["combins", "exit_defi", ""], ""], "head": ["// Auto generated, don't edit.\n#include \"libcustom.h\"\n", ["combins", "clzz_decl", ""], ["combins", "main_decl", ""], ["combins", "exit_decl", ""], ["exports", "macr", ""], "#define BEAN_EXPORT_CNT ", ["expn"], "\n"]}, "MyOtbf": {"main_name": ["_", ["clazz"], "_", ["iface"], "_wrap_"], "main_decl": [["itype"], " ", ["clazz"], "_", ["iface"], "(", ["iargt"], ");\n"], "main_wrap": ["any _", ["clazz"], "_", ["iface"], "_wrap_(int fix, ...)\n{\n\tany ret;\n\tva_list _var;\n\tva_start(_var, fix);\n\tret = ", ["clazz"], "_", ["iface"], "(_var);\n\tva_end(_var);\n\treturn ret;\n}\n"]}, "MyFunc": {"main_name": [["clazz"], "_", ["iface"], ""], "exit_name": [["clazz"], "_", ["eface"], ""], "main_decl": [["itype"], " ", ["main_name"], "(", ["iargt"], ");\n"], "exit_decl": [["etype"], " ", ["exit_name"], "(", ["eargt"], ");\n"], "clzz_decl": ["typedef struct\n{", 1, "\n", ["calls", "decl", ""], 2, "} ", ["tpack"], ";\n"], "main_defi": [["itype"], " ", ["main_name"], "(", ["iargt"], ")\n{\n", 1, 0, "if (!thiz) return;\n", ["calls", "norm", ""], 2, "}\n"], "exit_defi": [["etype"], " ", ["exit_name"], "(", ["eargt"], ")\n{\n", 1, 0, "if (!thiz) return 0;\n", ["exits", "exit", ""], 0, "return 0;\n", 2, "}\n"]}, "MyCall": {"decl": [0, "any ", ["name"], "; // ", ["func", "clazz"], "\n"], "norm": [0, "thiz->", ["name"], " = ", ["func", "main_name"], "(", ["args", "call", ", "], ");", ["fill"], "\n"], "exit": [0, "if (thiz->", ["name"], ")\n", 0, "{", 1, "\n", 0, ["func", "exit_name"], "(thiz->", ["name"], ");\n", 0, "thiz->", ["name"], " = 0;", ["free"], 2, "\n", 0, "}\n"], "fill": ["\n", 0, "cbeans_fill(thiz->", ["name"], ", ", ["iden"], ");"], "free": ["\n", 0, "cbeans_fill(0, ", ["iden"], ");"], "macr": ["#define ", ["iden"], " ", ["expo"], "\n"]}, "MyRetc": {"exit": [0, "if (thiz->", ["name"], ")\n", 0, "{", 1, "\n", 0, "if (", ["func", "exit_name"], "(thiz->", ["name"], ")) return -1;\n", 0, "thiz->", ["name"], " = 0;", ["free"], 2, "\n", 0, "}\n"]}, "MyRmod": {"decl": [0, ["func", "tpack"], " ", ["name"], "; // ", ["func", "tpack"], "\n"], "norm": [0, ["func", "main_name"], "(", ["args", "call", ", "], ");\n"], "exit": [0, "if (", ["func", "exit_name"], "(&thiz->", ["name"], ")) return -1;\n"]}}