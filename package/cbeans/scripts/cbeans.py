#!python3
import os
import re
from dutil import MEcho
from xlexe import XLexe
from xform import XForm
from xpase import <PERSON>Com<PERSON><PERSON>, XPase, XToken

_CONSTR_PT_ = re.compile(r"\s*(any|non|int)\s+(([A-Za-z0-9]+)_([Nn]ew|[Dd]el))\s*\(([\w\s\,\*]+)\)[^;]")


class MyImpo:
    def __init__(self, path):
        self.path = path

    def find(self, key):
        if "norm" == key:
            return f"#include \"{self.path}\"\n"


class MyFunc:
    def __init__(self, **kwargs):
        self.clazz = kwargs.get("clazz", None)
        self.tpack = kwargs.get("tpack", None)
        self.iface = kwargs.get("iface", None)
        self.eface = kwargs.get("eface", None)
        self.iargt = kwargs.get("iargt", None)
        self.eargt = kwargs.get("eargt", None)
        self.itype = kwargs.get("itype", None)
        self.etype = kwargs.get("etype", None)
        self.calls = kwargs.get("calls", None)

    def find(self, key):
        if "exits" == key:
            calls = []
            for call in reversed(self.calls):
                if call.func.eface is None:
                    continue
                calls.append(call)
            return calls


class MyOutf(MyFunc):
    def __init__(self, name, typd):
        def _make_clazz_(name):
            if "_" not in name:
                clzz = name
                if any(c.isupper() for c in clzz):
                    return clzz, "New", "Del", clzz
                else:
                    return clzz, "new", "del", f"{clzz}_t"
            clzz, face = name.split("_", 1)
            free_map = {
                'new': 'del',
                'init': 'free',
                'main': 'exit',
                'build': 'clean',
                'create': 'delete'
            }
            if face.lower() not in free_map:
                free = "del"
            else:
                free = free_map[face.lower()]
            if any(c.isupper() for c in face):
                free = free.capitalize()
                pack = clzz
            else:
                pack = f"{clzz}_t"
            return clzz, face, free, pack

        clzz, face, free, pack = _make_clazz_(name)
        iargs = ", ".join([f"{pack} *thiz"] + typd)
        eargs = f"{pack} *thiz"
        MyFunc.__init__(
            self, clazz=clzz, tpack=pack,
            iface=face, eface=free,
            iargt=iargs, eargt=eargs,
            itype="non", etype="int",
            calls=[]
        )


class MyNewf(MyFunc):
    def __init__(self, clzz, face, argt, typx):
        MyFunc.__init__(self, clazz=clzz, iface=face, iargt=argt, itype=typx)


class MyOtbf(MyNewf):
    pass


class MyCall:
    def __init__(self, func, name, expo=None):
        self.func = func
        self.name = name.replace("-", "_")
        self.expo = expo
        self.args = []
        if type(func) == MyOtbf:
            self.args.append(MyPara("0", "i"))
        elif type(func) == MyOutf:
            self.args.append(MyPara(name, "m"))

    def find(self, key):
        if "newo" == key:
            if self.name:
                return f"any {self.name} = "
            else:
                return ""
        if "fill" == key:
            if self.expo is None:
                return ""
            return
        if "free" == key:
            if self.expo is None:
                return ""
            return
        if "iden" == key:
            return "BEAN_" + self.name.upper()
        if "expo" == key:
            if self.expo is None:
                return
            return str(self.expo)


class MyRetc(MyCall):
    pass


class MyRmod(MyCall):
    pass


class MyPara:
    def __init__(self, value, typx):
        self.value = value.replace("-", "_")
        self.typx = typx

    def find(self, key):
        if "decl" == key:
            return self.value
        elif "call" == key:
            if self.typx == "p":
                return f"thiz->{self.value}"
            elif self.typx == "m":
                return f"&thiz->{self.value}"
            return self.value


class MyMain(XCompiler):
    INT = 0
    CHR = 1
    STR = 2
    MAC = 3
    OBI = 4
    IMP = 5

    @staticmethod
    def type_name(typ):
        for key, val in MyMain.__dict__.items():
            if typ == val:
                return key

    def __init__(self):
        self.parser = XPase(f"{os.path.dirname(__file__)}/parser.json")
        self.lexer = XLexe(f"{os.path.dirname(__file__)}/lexer.json")
        self.outer = XForm(f"{os.path.dirname(__file__)}/output.json")
        self.pstack = []
        self.combins = []
        self.otbcsts = []
        self.newcsts = []
        self.delcsts = []
        self.imports = []
        self.exports = []
        self.anonymous = 0
        self.function = None

    def _check_name_(self, name):
        name = name.replace("-", "_")
        for call in self.function.calls:
            if name == call.name:
                return True
        return False

    def _find_call_(self, name):
        for obj in self.newcsts + self.otbcsts + self.combins:
            if name in obj.clazz:
                return obj
        assert False, f"{name} not found"

    def _make_call_(self, func_name, name, expo):
        if name is None:
            name = f"_obj{self.anonymous}"
            self.anonymous += 1
        func = self._find_call_(func_name)
        if type(func) == MyOutf:
            assert expo is None, f"{func_name} {name} can not expo"
            call = MyRmod(func, name)
        elif func.etype == "int":
            call = MyRetc(func, name, expo)
        else:
            call = MyCall(func, name, expo)
        return call

    def find(self, key):
        if "expn" == key:
            if self.exports:
                return str(len(self.exports))
            return "1"

    def error(self, tips, line, pos, cont):
        MEcho.e(tips)
        MEcho.e(f"{line} {pos}")
        MEcho.e(cont)
        MEcho.quit("")

    def shift(self, code, idx):
        while True:
            if idx >= len(code):
                return XToken(code, "%end", idx, idx), idx
            token, idx = self.lexer.nxt(code, idx)
            if token is None:
                break
            typ, top, bot = token
            if typ in {"blan", "comm"}:
                continue
            return XToken(code, typ, top, bot), idx
        return None, 0

    def reduce(self, key, clips):
        if key in [1, 11, 17, 13, 4, 3, 7]:
            return
        if key in [2]:
            obj = MyImpo(str(clips[1]))
            self.imports.append(obj)
            return
        if key in [6]:
            self.pstack.append([])
            return
        if key in [5]:
            typd = self.pstack.pop()
            obj = MyOutf(str(clips[0]), typd)
            self.combins.append(obj)
            self.function = obj
            return
        if key in [8]:
            obj = self.pstack.pop()
            self.pstack.append([obj])
            return
        if key in [9]:
            obj = self.pstack.pop()
            self.pstack[-1].append(obj)
            return
        if key in [10]:
            typx, name = str(clips[0]), str(clips[1])
            obj = f"{typx} {name}"
            self.pstack.append(obj)
            return
        if key in [12]:
            call = self.pstack.pop()
            self.function.calls.append(call)
            return
        if key in [14]:
            obj = self._make_call_(str(clips[0]), None, None)
            self.pstack.append(obj)
            return
        if key in [15]:
            obj = self._make_call_(str(clips[0]), str(clips[2]), None)
            self.pstack.append(obj)
            return
        if key in [16]:
            name = str(clips[2]).replace("-", "_")
            numb = len(self.exports)
            for idx, call in enumerate(self.exports):
                if name == call.name:
                    numb = idx
                    break
            obj = self._make_call_(str(clips[0]), name, numb)
            if numb == len(self.exports):
                self.exports.append(obj)
            self.pstack.append(obj)
            return
        if key in [18, 19]:
            obj = self.pstack.pop()
            call = self.pstack[-1]
            call.args.append(obj)
            return
        if key in [20]:
            if self._check_name_(str(clips[0])):
                obj = MyPara(str(clips[0]), "p")
            else:
                obj = MyPara(str(clips[0]), "x")
            self.pstack.append(obj)
            return
        if key in [21]:
            obj = MyPara(str(clips[0]), "i")
            self.pstack.append(obj)
            return
        if key in [22]:
            obj = MyPara(str(clips[0]), "c")
            self.pstack.append(obj)
            return
        if key in [23]:
            obj = MyPara(str(clips[0]), "s")
            self.pstack.append(obj)
            return
        if key in [24]:
            call = self.pstack.pop()
            self.function.calls.append(call)
            obj = MyPara(call.name, "p")
            self.pstack.append(obj)
            return
        if key in [25]:
            obj = MyPara("&" + str(clips[1]), "x")
            self.pstack.append(obj)
            return
        MEcho.quit(f"did not done {key} {str([str(tk) for tk in clips])}")

    def cmp(self, src):
        with open(src, "r", errors="ignore") as fp:
            text = fp.read()
        self.parser.cmp(self, text)
        self.pstack.clear()

    def find_constr_in_c(self, file):
        def _find_func_(line):
            mat = _CONSTR_PT_.match(line)
            if not mat:
                return
            typx = mat.group(1)
            clzz = mat.group(3)
            face = mat.group(4)
            argt = mat.group(5)
            if "any" == typx:
                if "otb" == argt:
                    self.otbcsts.append(MyOtbf(clzz, face, argt, typx))
                else:
                    self.newcsts.append(MyNewf(clzz, face, argt, typx))
            else:
                for obj in self.newcsts + self.otbcsts:
                    if clzz == obj.clazz:
                        obj.eface = face
                        obj.eargt = argt
                        obj.etype = typx

        with open(file, "r", errors="ignore") as fp:
            for line in fp:
                _find_func_(line)

    def do(self, otbs, srcs, cst, otb):
        for item in sorted(srcs):
            self.find_constr_in_c(item)
        for item in sorted(otbs):
            self.cmp(item)

        # Collect all used class names
        used_classes = set()
        for func in self.combins:
            for call in func.calls:
                # Add class names of called constructors to the used set
                if hasattr(call, 'func') and call.func is not None:
                    used_classes.add(call.func.clazz)
        # Filter unused constructors and destructors based on class name
        self.newcsts = [cst for cst in self.newcsts if cst.clazz in used_classes]
        self.otbcsts = [cst for cst in self.otbcsts if cst.clazz in used_classes]
        self.delcsts = [cst for cst in self.newcsts + self.otbcsts if cst.eface]

        text = self.outer.fmt(self, "main")
        text = "".join(text)
        cst.write(text)

        text = self.outer.fmt(self, "head")
        text = "".join(text)
        otb.write(text)


def mkotb(application):
    # find srcs
    if "SRCS" not in application:
        return
    sources, products, entry_cst, entry_otb = [], [], None, None
    for item in application["SRCS"]:
        if item.endswith(".c") and os.access(item, os.R_OK):
            sources.append(item)
        elif item.endswith(".ob"):
            products.append(item)
        elif entry_otb is None and item.endswith("cbeans.otb.h"):
            entry_otb = item
        elif entry_cst is None and item.endswith("cbeans.cst.h"):
            entry_cst = item

    if not entry_otb:
        return

    with open(entry_cst, "w") as cst, open(entry_otb, "w") as otb:
        MyMain().do(products, sources, cst, otb)
