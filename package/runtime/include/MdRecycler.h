//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: MdRecycler.h
// Author		: XuCF
// Created On	: 2024/12/19
// Description	: Thread-safe memory recycler (simple wrapper of cblock)
//
// History
// 1. V1.0, Created by XuCF. 2024/12/19
//=============================================================================

#ifndef _MD_RECYCLER_H
#define _MD_RECYCLER_H

#include "libcustom.h"
#include "cblock.h"

ClassDeclare(MdRecycler);

// Main functions
any MdRecycler_New_(int block_size, int init_capacity, cblock_init_func_t fi, cblock_free_func_t ff);
non MdRecycler_Del(MdRecycler *thiz);
any MdRecycler_Get(MdRecycler *thiz);
non MdRecycler_Put(MdRecycler *thiz, any block);

// Dump functions
non MdRecycler_DumpEx(MdRecycler *thiz, cblock_dump_func_t df);
non MdRecycler_Dump(MdRecycler *thiz);

// Convenient macros for type-safe usage
#define MdRecycler_New(type, init_capacity)			   MdRecycler_New_(sizeof(type), init_capacity, NULL, NULL)
#define MdRecycler_NewAdv(type, init_capacity, fi, ff) MdRecycler_New_(sizeof(type), init_capacity, fi, ff)

#endif // _MD_RECYCLER_H