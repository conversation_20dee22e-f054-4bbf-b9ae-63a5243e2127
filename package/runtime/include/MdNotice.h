//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: MdNotice.h
// Author		: XuCF
// Created On	: 2024/09/04
// Description	: MdNotice.h
//
// History
// 1. V1.0, Created by XuCF. 2024/09/04
//=============================================================================

#ifndef _MYNOTICE_H
#define _MYNOTICE_H

#include "libcustom.h"

#define MdNoticeId_Task			  0
#define MdNoticeId_NetworkMonitor 1
#define MdNoticeId_Invisible	  2
#define MdNoticeId_Numb			  MdNoticeId_Invisible

#define MD_NOTICE_FLAG_AL		  (0x1u << 0u) // always invoke
#define MD_NOTICE_FLAG_DT		  (0x1u << 1u) // auto detach

ClassDeclare(MdNoticeNode);
ClassDeclare(MdNoticeUser);

void MdNotice_Init(u32 size);
void MdNotice_Exit(void);

MdNoticeNode *MdNotice_CreateNode(int id);
MdNoticeNode *MdNotice_FindNode(int id);
void MdNotice_DeleteNode(MdNoticeNode *node);

MdNoticeUser *MdNotice_AttachUser(MdNoticeNode *node, any func, any pobj, u32 flag);
int MdNotice_DetachUser(MdNoticeUser *user);

int MdNotice_Invoke(MdNoticeNode *node, unv what, any ptr, unv num);

void MdNotice_DumpSpace(void (*df)(const char *fmt, ...));

#endif //_MYNOTICE_H