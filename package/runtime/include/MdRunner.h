//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: MdRunner.h
// Author		: XuCF
// Created On	: 2024/12/07
// Description	: MdRunner.h
//
// History
// 1. V1.0, Created by XuCF. 2024/12/07
//=============================================================================

#ifndef _MDRUNNER_H
#define _MDRUNNER_H

#include "libcustom.h"

#ifdef __cplusplus
extern "C"
{
#endif

ClassDeclare(MdThread);
MdThread *MdThread_Boot(any route, any obj, str name);
void MdThread_Detach(MdThread *thiz);
int MdThread_Wait(MdThread *thiz);
int MdThread_Main(void);
int MdThread_List(void);

#define MD_TICKER_FLAG_CY (0x1u << 0u) // cyclic
#define MD_TICKER_FLAG_IM (0x1u << 1u) // immediate
#define MD_TICKER_FLAG_RE (0x1u << 2u) // retain
ClassDeclare(MdTicker);
MdTicker *MdTicker_Boot(any route, any obj, u32 flag, u32 ms);
int MdTicker_Kill(MdTicker *thiz);
int MdTicker_KillByRoute(any route);
int MdTicker_Dump(any writer);

ClassDeclare(MdMutex);
MdMutex *MdMutex_Create(void);
non MdMutex_Delete(MdMutex *thiz);
non MdMutex_Lock(MdMutex *thiz);
non MdMutex_Unlock(MdMutex *thiz);

ClassDeclare(MdWaiter);
MdWaiter *MdWaiter_Create(void);
non MdWaiter_Delete(MdWaiter *thiz);
int MdWaiter_Wait(MdWaiter *thiz, MdMutex *mutex, int wait);
non MdWaiter_Wake(MdWaiter *thiz);

ClassDeclare(MdSynchronizer);
MdSynchronizer *MdSynchronizer_Create(void);
non MdSynchronizer_Delete(MdSynchronizer *thiz);
non MdSynchronizer_Lock(MdSynchronizer *thiz);
non MdSynchronizer_Unlock(MdSynchronizer *thiz);
int MdSynchronizer_Wait(MdSynchronizer *thiz, int timeout);
int MdSynchronizer_Until(MdSynchronizer *thiz, int until);
non MdSynchronizer_Wake(MdSynchronizer *thiz);
#define MdSynchronizer_Timeout(_s, _c, _t) \
	do { \
		int _until = _t + clike_time_ms(); \
		while (_c) MdSynchronizer_Until(_s, _until); \
	} while (0)

#define MD_EVENT_FLAG_OR (0x1 << 0)
#define MD_EVENT_FLAG_NC (0x1 << 1)
ClassDeclare(MdEvent);
MdEvent *MdEvent_Create(void);
s32 MdEvent_Delete(MdEvent *thiz);
u32 MdEvent_Put(MdEvent *thiz, u32 bits);
u32 MdEvent_Clr(MdEvent *thiz, u32 bits);
u32 MdEvent_Get(MdEvent *thiz, u32 bits, u32 flag, int wait);

#define MD_TASK_FLAG_ND (1u << 8u)
ClassDeclare(MdTask);
int MdTask_Init(int prio, int volu);
non MdTask_Free(void);
MdTask *MdTask_Boot(any route, unv arg1, unv arg2, u32 flag, str name);
MdTask *MdTask_Find(any route);
int MdTask_Kill(MdTask *thiz);
int MdTask_KillByRoute(any route);
int MdTask_Loop(void);
int MdTask_Dump(any writer);

#ifdef __cplusplus
}
#endif

#endif //_MDRUNNER_H
