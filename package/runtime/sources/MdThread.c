//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: MdThread.c
// Author		: XuCF
// Created On	: 2025/04/23
// Description	: MdThread.c
//
// History
// 1. V1.0, Created by XuCF. 2025/04/23
//=============================================================================

#if defined(_WIN32) || defined(_WIN64)
#include "winpc/MdThread.c"
#elif defined(__linux__)
#include "linux/MdThread.c"
#else
#error "Unsupported platform"
#endif

MdMutex *MdMutex_Create(void)
{
	MdMutex *thiz = clike_new(MdMutex);
	_MdMutex_Init_(thiz);
	return thiz;
}

non MdMutex_Delete(MdMutex *thiz)
{
	_MdMutex_Exit_(thiz);
	clike_free(thiz);
}

MdWaiter *MdWaiter_Create(void)
{
	MdWaiter *thiz = clike_new(MdWaiter);
	_MdWaiter_Init_(thiz);
	return thiz;
}

non MdWaiter_Delete(MdWaiter *thiz)
{
	_MdWaiter_Exit_(thiz);
	clike_free(thiz);
}

ClassRealize(MdSynchronizer)
{
	MdMutex mutex;
	MdWaiter waiter;
};

void _MdSynchronizer_Init_(MdSynchronizer *thiz)
{
	_MdMutex_Init_(&thiz->mutex);
	_MdWaiter_Init_(&thiz->waiter);
}

void _MdSynchronizer_Exit_(MdSynchronizer *thiz)
{
	_MdWaiter_Exit_(&thiz->waiter);
	_MdMutex_Exit_(&thiz->mutex);
}

MdSynchronizer *MdSynchronizer_Create(void)
{
	MdSynchronizer *thiz = clike_new(MdSynchronizer);
	_MdSynchronizer_Init_(thiz);
	return thiz;
}

non MdSynchronizer_Delete(MdSynchronizer *thiz)
{
	_MdSynchronizer_Exit_(thiz);
	clike_free(thiz);
}

non MdSynchronizer_Lock(MdSynchronizer *thiz)
{
	MdMutex_Lock(&thiz->mutex);
}

non MdSynchronizer_Unlock(MdSynchronizer *thiz)
{
	MdMutex_Unlock(&thiz->mutex);
}

int MdSynchronizer_Wait(MdSynchronizer *thiz, int timeout)
{
	return MdWaiter_Wait(&thiz->waiter, &thiz->mutex, timeout);
}

int MdSynchronizer_Until(MdSynchronizer *thiz, int until)
{
	int wait = until - clike_time_ms();
	if (wait <= 0) return ETIMEDOUT;
	return MdWaiter_Wait(&thiz->waiter, &thiz->mutex, wait);
}

non MdSynchronizer_Wake(MdSynchronizer *thiz)
{
	MdWaiter_Wake(&thiz->waiter);
}

ClassRealize(MdEvent)
{
	MdSynchronizer sync;
	u32 bits;
	int ref;
};

static inline void _mdevent_acquire_(MdEvent *thiz)
{
	thiz->ref++;
}

static inline void _mdevent_release_(MdEvent *thiz)
{
	thiz->ref--;
}

MdEvent *MdEvent_Create(void)
{
	MdEvent *thiz = clike_new(MdEvent);
	_MdSynchronizer_Init_(&thiz->sync);
	thiz->bits = 0;
	thiz->ref = 0;
	return thiz;
}

s32 MdEvent_Delete(MdEvent *thiz)
{
	int ref;
	MdSynchronizer_Lock(&thiz->sync);
	ref = thiz->ref;
	MdSynchronizer_Unlock(&thiz->sync);
	if (ref > 0) return -1;
	_MdSynchronizer_Exit_(&thiz->sync);
	clike_free(thiz);
	return 0;
}

u32 MdEvent_Put(MdEvent *thiz, u32 bits)
{
	u32 backup;
	MdSynchronizer_Lock(&thiz->sync);
	_mdevent_acquire_(thiz);
	backup = thiz->bits;
	thiz->bits |= bits;
	MdSynchronizer_Wake(&thiz->sync);
	_mdevent_release_(thiz);
	MdSynchronizer_Unlock(&thiz->sync);
	return backup;
}

u32 MdEvent_Clr(MdEvent *thiz, u32 bits)
{
	u32 backup;
	MdSynchronizer_Lock(&thiz->sync);
	_mdevent_acquire_(thiz);
	backup = thiz->bits;
	thiz->bits &= ~bits;
	_mdevent_release_(thiz);
	MdSynchronizer_Unlock(&thiz->sync);
	return backup;
}

u32 MdEvent_Get(MdEvent *thiz, u32 bits, u32 flag, int wait)
{
	u32 backup;
	MdSynchronizer_Lock(&thiz->sync);
	_mdevent_acquire_(thiz);
	if (wait > 0)
	{
		int until = clike_time_ms() + wait;
		if (MD_EVENT_FLAG_OR & flag)
			while (!(thiz->bits & bits) && !MdSynchronizer_Until(&thiz->sync, until));
		else
			while ((thiz->bits & bits) != bits && !MdSynchronizer_Until(&thiz->sync, until));
	}
	else if (wait < 0)
	{
		if (MD_EVENT_FLAG_OR & flag)
			while (!(thiz->bits & bits)) MdSynchronizer_Wait(&thiz->sync, -1);
		else
			while ((thiz->bits & bits) != bits) MdSynchronizer_Wait(&thiz->sync, -1);
	}
	backup = thiz->bits;
	if (!(MD_EVENT_FLAG_NC & flag)) thiz->bits &= ~bits;
	_mdevent_release_(thiz);
	MdSynchronizer_Unlock(&thiz->sync);
	return backup;
}
