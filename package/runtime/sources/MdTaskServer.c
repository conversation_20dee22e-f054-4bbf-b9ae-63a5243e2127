//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: MdTaskServer.c
// Author		: XuCF
// Created On	: 2024/12/10
// Description	: MdTaskServer.c
//
// History
// 1. V1.0, Created by XuCF. 2024/12/10
//=============================================================================

#include "MdRunner.h"

ClassDefined(MdTaskServer)
{
	MdThread *thread;
	int stop;
};

static any _route_(any arg)
{
	MdTaskServer *thiz = arg;
	while (!thiz->stop)
	{
		if (!MdTask_Loop()) clike_delay_ms(10);
	}
	return 0;
}

any MdTaskServer_New(int count)
{
	MdTaskServer *thiz = clike_new(MdTaskServer);
	MdTask_Init(3, count);
	thiz->stop = 0;
	thiz->thread = MdThread_Boot(_route_, thiz, "task_server");
	return thiz;
}

non MdTaskServer_Del(any obj)
{
	MdTaskServer *thiz = obj;
	thiz->stop = 1;
	MdThread_Wait(thiz->thread);
	MdTask_Free();
	clike_free(thiz);
}
