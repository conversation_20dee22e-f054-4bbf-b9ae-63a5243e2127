//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: MdThread.c
// Author		: XuCF
// Created On	: 2024/12/16
// Description	: MdThread.c
//
// History
// 1. V1.0, Created by XuCF. 2024/12/16
//=============================================================================

#include <Windows.h>
#include "MdRunner.h"

ClassRealize(MdThread)
{
	DWORD tid;
	HANDLE hand;
};

MdThread *MdThread_Boot(any route, any obj, str name)
{
	MdThread *thiz = clike_new(MdThread);
	thiz->hand = CreateThread(NULL, 0, route, obj, 0, &thiz->tid);
	(void)name;
	return thiz;
}

void MdThread_Detach(MdThread *thiz)
{
	CloseHandle(thiz->hand);
	clike_free(thiz);
}

int MdThread_Wait(MdThread *thiz)
{
	WaitForSingleObject(thiz->hand, INFINITE);
	CloseHandle(thiz->hand);
	clike_free(thiz);
	return 0;
}

int MdThread_Main(void)
{
	while (1) clike_delay_ms(1000);
}

int MdThread_List(void)
{
	return 0;
}

ClassRealize(MdMutex)
{
	CRITICAL_SECTION core;
};

void _MdMutex_Init_(MdMutex *thiz)
{
	InitializeCriticalSection(&thiz->core);
}

void _MdMutex_Exit_(MdMutex *thiz)
{
	DeleteCriticalSection(&thiz->core);
}

non MdMutex_Lock(MdMutex *thiz)
{
	EnterCriticalSection(&thiz->core);
}

non MdMutex_Unlock(MdMutex *thiz)
{
	LeaveCriticalSection(&thiz->core);
}

ClassRealize(MdWaiter)
{
	CONDITION_VARIABLE cond;
};

void _MdWaiter_Init_(MdWaiter *thiz)
{
	InitializeConditionVariable(&thiz->cond);
}

void _MdWaiter_Exit_(MdWaiter *thiz)
{
	// don't need to delete cv
}

int MdWaiter_Wait(MdWaiter *thiz, MdMutex *mutex, int wait)
{
	if (!SleepConditionVariableCS(&thiz->cond, &mutex->core, wait))
	{
		DWORD error = GetLastError();
		switch (error)
		{
			case ERROR_TIMEOUT: return ETIMEDOUT;
			case ERROR_INVALID_HANDLE: return EBADF;
			case ERROR_NOT_ENOUGH_MEMORY: return ENOMEM;
			case ERROR_INVALID_PARAMETER: return EINVAL;
			default: return EAGAIN;
		}
	}
	return 0;
}

non MdWaiter_Wake(MdWaiter *thiz)
{
	WakeAllConditionVariable(&thiz->cond);
}
