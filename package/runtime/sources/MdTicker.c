//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: MdTicker.c
// Author		: XuCF
// Created On	: 2024/08/27
// Description	: MdTicker.c
//
// History
// 1. V1.0, Created by XuCF. 2024/08/27
//=============================================================================

#include "MdRunner.h"
#include "clink.h"

#define _HIDDEN_FLAG_DEAD_ (0x1u << 31u) // dead
#define _HIDDEN_FLAG_KILL_ (0x1u << 30u) // kill

ClassRealize(MdTicker)
{
	slink_t super;
	any (*route)(any);
	any obj;
	unsigned int flag;
	int period;
	int stamp;
};

static slink_t *_ticker_slink_ = 0;
static slink_t *_ticker_space_ = 0;
static MdMutex *_ticker_mutex_ = 0;
static MdThread *_ticker_thread_ = 0;
static MdWaiter *_ticker_waiter_ = 0;
static int _ticker_running_ = 0;

static inline void _ticker_mutex_lock_(void)
{
	MdMutex_Lock(_ticker_mutex_);
}

static inline void _ticker_mutex_unlock_(void)
{
	MdMutex_Unlock(_ticker_mutex_);
}

static void _ticker_dump_(void)
{
#if MACR_LOG_LEVEL >= 4
	MdTicker *prev, *curr;
	slink_iter(_ticker_slink_, prev, curr)
	{
		clike_print("ticker(%p) stamp=%d, period=%d, flag=%d\n", curr->route, curr->stamp, curr->period, curr->flag);
	}
	clike_print("-----------------%u---------------\n", clike_time_ms());
#endif
}

static void _ticker_insert_(MdTicker *ticker)
{
	MdTicker *prev, *curr;
	slink_iter(_ticker_slink_, prev, curr)
	{
		if (ticker->stamp - curr->stamp < 0) break;
	}
	slink_attach_next((slink_t *)prev, (slink_t *)ticker);
}

static void _ticker_wake_(void)
{
	MdWaiter_Wake(_ticker_waiter_);
}

static void _ticker_wait_(int delta)
{
	MdWaiter_Wait(_ticker_waiter_, _ticker_mutex_, delta);
}

static any _ticker_route_(any arg)
{
	int delta;
	MdTicker *ticker;
	_ticker_mutex_lock_();
	while (_ticker_running_)
	{
		while (clink_empty(_ticker_slink_)) _ticker_wait_(-1); // no ticker
		_ticker_dump_();
		delta = ((MdTicker *)(_ticker_slink_->next))->stamp - clike_time_ms();
		if (delta > 0) // ticker not
		{
			_ticker_wait_(delta);
			continue;
		}
		// pick up ticker
		ticker = (MdTicker *)slink_detach_next(_ticker_slink_);
		// execute ticker
		_ticker_mutex_unlock_();
		ticker->route(ticker->obj);
		_ticker_mutex_lock_();
		// check flag
		if ((MD_TICKER_FLAG_CY & ticker->flag) && !(_HIDDEN_FLAG_KILL_ & ticker->flag))
		{
			// cycle and not be killed
			ticker->stamp += ticker->period;
			_ticker_insert_(ticker);
		}
		else if (MD_TICKER_FLAG_RE & ticker->flag)
		{
			// mark as dead, but not put to space
			ticker->flag |= _HIDDEN_FLAG_DEAD_;
		}
		else
		{
			// put to space
			slink_put(_ticker_space_, (slink_t *)ticker);
		}
	}
	_ticker_mutex_unlock_();
	return 0;
}

static void _ticker_prepare_(void)
{
	if (_ticker_thread_) return;
	_ticker_running_ = 1;
	_ticker_mutex_ = MdMutex_Create();
	_ticker_waiter_ = MdWaiter_Create();
	_ticker_slink_ = clike_new(slink_t);
	slink_reset(_ticker_slink_);
	_ticker_space_ = slink_new(slink_t, MdTicker, 16);
	_ticker_thread_ = MdThread_Boot(_ticker_route_, 0, "ticker");
}

static void _ticker_cleanup_(void)
{
	if (!_ticker_thread_) return;
	_ticker_running_ = 0;
	MdThread_Wait(_ticker_thread_);
	_ticker_thread_ = 0;
	MdWaiter_Delete(_ticker_waiter_);
	_ticker_waiter_ = 0;
	MdMutex_Delete(_ticker_mutex_);
	_ticker_mutex_ = 0;
	clike_free(_ticker_space_);
	_ticker_space_ = 0;
	clike_free(_ticker_slink_);
	_ticker_slink_ = 0;
}

MdTicker *MdTicker_Boot(any route, any obj, u32 flag, u32 ms)
{
	MdTicker *thiz = 0;
	_ticker_prepare_();
	_ticker_mutex_lock_();
	thiz = slink_pop(_ticker_space_, goto label_exit);
	thiz->route = route;
	thiz->obj = obj;
	thiz->period = ms;
	thiz->flag = flag;
	thiz->stamp = clike_time_ms() + thiz->period;
	_ticker_insert_(thiz);
	_ticker_wake_();
label_exit:
	_ticker_mutex_unlock_();
	return thiz;
}

int MdTicker_Kill(MdTicker *thiz)
{
	MdTicker *node = 0;
	_ticker_mutex_lock_();
	if (_HIDDEN_FLAG_DEAD_ & thiz->flag) goto label_drop; // already dead
	if (_HIDDEN_FLAG_KILL_ & thiz->flag) goto label_fail; // killing
	slink_drop(_ticker_slink_, node, node == thiz, {
		thiz->flag |= _HIDDEN_FLAG_KILL_; // mark as killing
		goto label_fail;
	});
label_drop:
	slink_put(_ticker_space_, (slink_t *)thiz);
	_ticker_mutex_unlock_();
	return 1; // one ticker killed
label_fail:
	_ticker_mutex_unlock_();
	return 0;
}

int MdTicker_KillByRoute(any route)
{
	int count = 0;
	MdTicker *prev, *curr;
	_ticker_mutex_lock_();
	slink_iter(_ticker_slink_, prev, curr)
	{
		if (curr->route == route)
		{
			slink_detach_next((slink_t *)prev);
			slink_put(_ticker_space_, (slink_t *)curr);
			curr = prev; // when delete one, curr should back to prev
			count++;
		}
	}
	_ticker_mutex_unlock_();
	return count;
}

int MdTicker_Init(void)
{
	_ticker_prepare_();
	return 0;
}

int MdTicker_Exit(void)
{
	_ticker_cleanup_();
	return 0;
}
