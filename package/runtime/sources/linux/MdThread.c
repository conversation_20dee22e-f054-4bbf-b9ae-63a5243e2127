//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: MdThread.c
// Author		: XuCF
// Created On	: 2024/08/21
// Description	: MdThread.c
//
// History
// 1. V1.0, Created by XuCF. 2024/08/21
//=============================================================================

#include "MdRunner.h"
#include "clink.h"
#include <dirent.h>

ClassRealize(MdThread)
{
	slink_t super;
	any (*route)(any);
	any obj;
	pthread_t thread;
	pid_t tid;
	unsigned long long prev_cpu_time;
};

static slink_t *_threads_space_ = 0;
static slink_t *_threads_slink_ = 0;
static MdMutex *_threads_mutex_ = 0;

static inline void _threads_mutex_lock_(void)
{
	if (!_threads_mutex_) _threads_mutex_ = MdMutex_Create();
	MdMutex_Lock(_threads_mutex_);
}

static inline void _threads_mutex_unlock_(void)
{
	MdMutex_Unlock(_threads_mutex_);
}

static any _thread_entry_(any arg)
{
	MdThread *thiz = arg;
	thiz->tid = gettid();
	return thiz->route(thiz->obj);
}

static void _thread_prepare_(void)
{
	if (!_threads_slink_)
	{
		_threads_space_ = slink_new(slink_t, MdThread, 10);
		_threads_slink_ = clike_new(slink_t);
		slink_reset(_threads_slink_);
	}
}

MdThread *MdThread_Boot(any route, any obj, str name)
{
	MdThread *thiz = 0;
	_threads_mutex_lock_();
	_thread_prepare_();
	// add to slink
	thiz = slink_pop(_threads_space_, goto label_exit);
	thiz->route = route;
	thiz->obj = obj;
	thiz->prev_cpu_time = 0;
	slink_add(_threads_slink_, (slink_t *)thiz);
	// create thread
	pthread_create(&thiz->thread, NULL, _thread_entry_, thiz);
	pthread_setname_np(thiz->thread, name);
label_exit:
	_threads_mutex_unlock_();
	return (MdThread *)thiz;
}

void MdThread_Detach(MdThread *thiz)
{
	MdThread *node;
	_threads_mutex_lock_();
	slink_drop(_threads_slink_, node, node == thiz, goto label_exit);
	if (thiz->thread == -1) goto label_exit;
	_threads_mutex_unlock_();
	pthread_detach(thiz->thread);
	_threads_mutex_lock_();
	slink_put(_threads_space_, (slink_t *)thiz);
label_exit:
	_threads_mutex_unlock_();
}

int MdThread_Wait(MdThread *thiz)
{
	MdThread *node;
	_threads_mutex_lock_();
	slink_drop(_threads_slink_, node, node == thiz, goto label_exit);
	if (thiz->thread == -1) goto label_exit;
	_threads_mutex_unlock_();
	pthread_join(thiz->thread, NULL);
	_threads_mutex_lock_();
	slink_put(_threads_space_, (slink_t *)thiz);
label_exit:
	_threads_mutex_unlock_();
	return 0;
}

int MdThread_Main(void)
{
	MdThread *thread;
	_threads_mutex_lock_();
	while (1)
	{
		thread = slink_pop(_threads_slink_, break);
		if (thread->thread == -1) continue;
		_threads_mutex_unlock_();
		pthread_join(thread->thread, NULL);
		_threads_mutex_lock_();
		slink_put(_threads_space_, (slink_t *)thread);
	}
	_threads_mutex_unlock_();
	return 0;
}

static unsigned long long _read_thread_cpu_time_(pid_t pid, pid_t tid)
{
	unsigned long long cputime = 0;
	char stat_path[256];
	snprintf(stat_path, sizeof(stat_path), "/proc/%d/task/%d/stat", pid, tid);
	FILE *file = fopen(stat_path, "r");
	clike_assert(!file, return -1);
	char buffer[1024];
	clike_assert(!fgets(buffer, sizeof(buffer), file), goto label_close);
	unsigned long long utime, stime;
	sscanf(buffer, "%*d %*s %*c %*d %*d %*d %*d %*d %*u %*u %*u %*u %*u %llu %llu", &utime, &stime);
	cputime = utime + stime;
label_close:
	fclose(file);
	return cputime;
}

static unsigned long long _read_total_cpu_time_()
{
	unsigned long long cputime = 0;
	FILE *file = fopen("/proc/stat", "r");
	clike_assert(!file, return 0);
	char buffer[1024];
	clike_assert(!fgets(buffer, sizeof(buffer), file), goto label_close);
	unsigned long long user, nice, system, idle, iowait, irq, softirq;
	sscanf(buffer, "cpu %llu %llu %llu %llu %llu %llu %llu", &user, &nice, &system, &idle, &iowait, &irq, &softirq);
	cputime = user + nice + system + idle + iowait + irq + softirq;
label_close:
	fclose(file);
	return cputime;
}

static int _sync_tasks_(void)
{
	pid_t pid = getpid();
	char task_dir[256];
	snprintf(task_dir, sizeof(task_dir), "/proc/%d/task", pid);
	DIR *dir = opendir(task_dir);
	clike_assert(!dir, return -1);
	struct dirent *entry;
	pid_t tid;
	MdThread *thread;
label_next:
	entry = readdir(dir);
	if (!entry) goto label_close;
	if (DT_DIR != entry->d_type) goto label_next;
	tid = atoi(entry->d_name);
	if (!tid) goto label_next;
	clink_nfor(_threads_slink_, thread) if (thread->tid == tid) goto label_next;
	thread = slink_pop(_threads_space_, goto label_close);
	thread->tid = tid;
	thread->thread = -1;
	thread->route = 0;
	thread->obj = 0;
	thread->prev_cpu_time = 0;
	slink_add(_threads_slink_, (slink_t *)thread);
	goto label_next;
label_close:
	closedir(dir);
	return 0;
}

static int _list_cpu_usage_per_(void)
{
	static unsigned long long total_cpu_time_prev = 0;
	static long ncpu = 0;
	if (ncpu == 0)
	{
		ncpu = sysconf(_SC_NPROCESSORS_ONLN);
		clike_assert(ncpu <= 0, ncpu = 1);
	}
	unsigned long long total_cpu_time_curr = _read_total_cpu_time_();
	unsigned long long total_cpu_time_diff = total_cpu_time_curr - total_cpu_time_prev;
	if (total_cpu_time_diff == 0) return 0;
	unsigned long long thread_cpu_time;
	float cpu_per;
	pid_t pid = getpid(); // Change this to monitor other processes
	MdThread *prev, *curr;
	clike_print("cpu per:\n");
	slink_iter(_threads_slink_, prev, curr)
	{
		thread_cpu_time = _read_thread_cpu_time_(pid, curr->tid);
		if (thread_cpu_time == -1)
		{
			slink_put(_threads_space_, slink_detach_next((slink_t *)prev));
			curr = prev; // back one node
			continue;
		}
		cpu_per = (float)(thread_cpu_time - curr->prev_cpu_time) * 100 * ncpu / total_cpu_time_diff;
		clike_print("tid=%d, cpu=%5.2f%%\n", curr->tid, cpu_per);
		curr->prev_cpu_time = thread_cpu_time;
	}
	total_cpu_time_prev = total_cpu_time_curr;
	return 0;
}

int MdThread_List(void)
{
	_threads_mutex_lock_();
	_thread_prepare_();
	_sync_tasks_();
	_list_cpu_usage_per_();
	_threads_mutex_unlock_();
	return 0;
}

ClassRealize(MdMutex)
{
	pthread_mutex_t mutex;
};

void _MdMutex_Init_(MdMutex *thiz)
{
	pthread_mutex_init(&thiz->mutex, NULL);
}

void _MdMutex_Exit_(MdMutex *thiz)
{
	pthread_mutex_destroy(&thiz->mutex);
}

void MdMutex_Lock(MdMutex *thiz)
{
	pthread_mutex_lock(&thiz->mutex);
}

void MdMutex_Unlock(MdMutex *thiz)
{
	pthread_mutex_unlock(&thiz->mutex);
}

ClassRealize(MdWaiter)
{
	pthread_cond_t cond;
};

void _MdWaiter_Init_(MdWaiter *thiz)
{
	pthread_cond_init(&thiz->cond, NULL);
}

void _MdWaiter_Exit_(MdWaiter *thiz)
{
	pthread_cond_destroy(&thiz->cond);
}

int MdWaiter_Wait(MdWaiter *thiz, MdMutex *mutex, int wait)
{
	if (wait == 0) return ETIMEDOUT;
	if (wait < 0) return pthread_cond_wait(&thiz->cond, &mutex->mutex);
	struct timespec request;
	unsigned long long nsec, sec;
	clock_gettime(CLOCK_REALTIME, &request);
	nsec = wait * 1000000LL + request.tv_nsec;
	sec = nsec / 1000000000LL;
	request.tv_sec += sec;
	request.tv_nsec = nsec - sec * 1000000000LL;
	return pthread_cond_timedwait(&thiz->cond, &mutex->mutex, &request);
}

non MdWaiter_Wake(MdWaiter *thiz)
{
	pthread_cond_broadcast(&thiz->cond);
}
