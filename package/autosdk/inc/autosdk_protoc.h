//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: autosdk_protoc.h
// Author		: XuCF
// Created On	: 2024/12/07
// Description	: autosdk_protoc.h
//
// History
// 1. V1.0, Created by XuCF. 2024/12/07
//=============================================================================

#ifndef _AUTOSDK_PROTOC_H
#define _AUTOSDK_PROTOC_H

#define AUTOSDK_PACK_TYP(d) (((unsigned char *)(d))[0])
#define AUTOSDK_PACK_IDX(d) (((unsigned char *)(d))[1])
#define AUTOSDK_PACK_CID(d) (((unsigned short *)(d))[1])
#define AUTOSDK_PACK_BLK(d) (((unsigned short *)(d))[1])
#define AUTOSDK_PACK_LEN(d) (((unsigned int *)(d))[1])
#define AUTOSDK_PACK_VOL(d) (((unsigned int *)(d))[2])
#define AUTOSDK_PACK_RET(d) (((int *)(d))[2])
#define AUTOSDK_PACK_CHK(d) (((unsigned int *)(d))[3])

#define AUTOSDK_BASE_LEN	16

#define AUTOSDK_TYPE_REQ	0xff
#define AUTOSDK_TYPE_ACK	0xfe
#define AUTOSDK_TYPE_ABT	0xfd
#define AUTOSDK_TYPE_LIM	0xfd

#define AUTOSDK_ERR_CODE(d) ((int)((d) | 0xc0000000))

#endif //_AUTOSDK_PROTOC_H