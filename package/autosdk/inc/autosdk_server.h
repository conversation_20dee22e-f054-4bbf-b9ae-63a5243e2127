//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: autosdk_server.h
// Author		: XuCF
// Created On	: 2024/11/25
// Description	: autosdk_server.h
//
// History
// 1. V1.0, Created by XuCF. 2024/11/25
//=============================================================================

#ifndef _AUTOSDK_SERVER_H
#define _AUTOSDK_SERVER_H

typedef struct
{
	void *ptr;
	int len;
	void (*clean)(void *arg);
	void *arg;
} autosdk_block_t;
typedef struct
{
	void *ptr;
	int len;
} autosdk_array_t;

#define I
#define O *
#define R

#define CHR(d, ...) char d
#define BYT(d, ...) unsigned char d
#define S16(d, ...) short d
#define U16(d, ...) unsigned short d
#define S32(d, ...) int d
#define U32(d, ...) unsigned int d
#define S64(d, ...) long long d
#define U64(d, ...) unsigned long long d
#define F32(d, ...) float d
#define F64(d, ...) double d
#define STR(d, ...) const char * // input only
#define ARR(d, ...) autosdk_array_t *
#define BLK(d, ...) autosdk_block_t * // output only

#define API
#define RES
#define ACT
int autosdk_portme_write(void *hand, void *data, int size);
int autosdk_portme_flush(void *hand);
void *autosdk_portme_deque(void *hand, int cid);
void *autosdk_portme_remap(void *node);
void autosdk_portme_relax(void *node);
int autosdk_portme_awake(void *hand, int cid);

#define AUTOSDK_STAT_DONE 0
#define AUTOSDK_STAT_PROC 1
#define AUTOSDK_STAT_CONT 2

#endif //_AUTOSDK_SERVER_H
