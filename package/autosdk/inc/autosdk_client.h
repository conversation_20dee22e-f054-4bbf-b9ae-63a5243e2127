//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: autosdk_client.h
// Author		: XuCF
// Created On	: 2024/12/07
// Description	: autosdk_client.h
//
// History
// 1. V1.0, Created by XuCF. 2024/12/07
//=============================================================================

#ifndef _AUTOSDK_CLIENT_H
#define _AUTOSDK_CLIENT_H

int autosdk_portme_alloc(void *hand, void *hook, void *cbk, void *ctx);
int autosdk_portme_prior(void *hand);
int autosdk_portme_write(void *hand, void *data, int size);
int autosdk_portme_flush(void *hand);

#endif //_AUTOSDK_CLIENT_H
