// Auto generated, don't edit
#include "libcustom.h"
#include "autosdk_protoc.h"
#include "autosdk_client.h"
int _MyAutoSdkDemo_Add_hook_(any hand, any data, any _ctx, void (*_cbk)(any ctx, f32 s, int ret))
{
	if (!data)
	{
		_cbk(_ctx, 0, 1);
		return 1;
	}
	int _ret = AUTOSDK_PACK_RET(data);
	f32 s = *(f32 *)(data + 16);
	_cbk(_ctx, s, _ret);
	return _ret;
}
int MyAutoSdkDemo_Add(any hand, f32 a, f32 b, any _ctx, void (*_cbk)(any ctx, f32 s, int ret))
{
	int _idx = autosdk_portme_alloc(hand, _MyAutoSdkDemo_Add_hook_, _cbk, _ctx);
	if (0 > _idx) return _idx;
	byt _data[24];
	*(f32 *)(_data + 16) = a;
	*(f32 *)(_data + 20) = b;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_REQ;
	AUTOSDK_PACK_IDX(_data) = _idx;
	AUTOSDK_PACK_CID(_data) = 0;
	AUTOSDK_PACK_LEN(_data) = 24;
	AUTOSDK_PACK_VOL(_data) = 20;
	autosdk_portme_prior(hand);
	autosdk_portme_write(hand, _data, 24);
	autosdk_portme_flush(hand);
	return _idx;
}
int _MyAutoSdkDemo_Blk_hook_(any hand, any data, any _ctx, void (*_cbk)(any ctx, any data_ptr, int data_len, int ret))
{
	if (!data)
	{
		_cbk(_ctx, 0, 0, 1);
		return 1;
	}
	int _ret = AUTOSDK_PACK_RET(data);
	any _ptr = data + 20;
	any data_ptr = _ptr;
	int data_len = *(int *)(data + 16);
	_cbk(_ctx, data_ptr, data_len, _ret);
	return _ret;
}
int MyAutoSdkDemo_Blk(any hand, int data_len, any _ctx, void (*_cbk)(any ctx, any data_ptr, int data_len, int ret))
{
	int _idx = autosdk_portme_alloc(hand, _MyAutoSdkDemo_Blk_hook_, _cbk, _ctx);
	if (0 > _idx) return _idx;
	byt _data[20];
	*(int *)(_data + 16) = data_len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_REQ;
	AUTOSDK_PACK_IDX(_data) = _idx;
	AUTOSDK_PACK_CID(_data) = 1;
	AUTOSDK_PACK_LEN(_data) = 20;
	AUTOSDK_PACK_VOL(_data) = 20;
	autosdk_portme_prior(hand);
	autosdk_portme_write(hand, _data, 20);
	autosdk_portme_flush(hand);
	return _idx;
}
int _MyAutoSdkDemo_Fmt_hook_(any hand, any data, any _ctx, void (*_cbk)(any ctx, any data_ptr, int data_len, int ret))
{
	if (!data)
	{
		_cbk(_ctx, 0, 0, 1);
		return 1;
	}
	int _ret = AUTOSDK_PACK_RET(data);
	any _ptr = data + 20;
	any data_ptr = _ptr;
	int data_len = *(int *)(data + 16);
	_cbk(_ctx, data_ptr, data_len, _ret);
	return _ret;
}
int MyAutoSdkDemo_Fmt(any hand, u32 numb, int data_len, any _ctx, void (*_cbk)(any ctx, any data_ptr, int data_len, int ret))
{
	int _idx = autosdk_portme_alloc(hand, _MyAutoSdkDemo_Fmt_hook_, _cbk, _ctx);
	if (0 > _idx) return _idx;
	byt _data[24];
	*(u32 *)(_data + 16) = numb;
	*(int *)(_data + 20) = data_len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_REQ;
	AUTOSDK_PACK_IDX(_data) = _idx;
	AUTOSDK_PACK_CID(_data) = 2;
	AUTOSDK_PACK_LEN(_data) = 24;
	AUTOSDK_PACK_VOL(_data) = 20 + data_len;
	autosdk_portme_prior(hand);
	autosdk_portme_write(hand, _data, 24);
	autosdk_portme_flush(hand);
	return _idx;
}
int _MyAutoSdkDemo_Log_hook_(any hand, any data, any _ctx, void (*_cbk)(any ctx, int ret))
{
	if (!data)
	{
		_cbk(_ctx, 1);
		return 1;
	}
	int _ret = AUTOSDK_PACK_RET(data);
	_cbk(_ctx, _ret);
	return _ret;
}
int MyAutoSdkDemo_Log(any hand, str line, any _ctx, void (*_cbk)(any ctx, int ret))
{
	int _idx = autosdk_portme_alloc(hand, _MyAutoSdkDemo_Log_hook_, _cbk, _ctx);
	if (0 > _idx) return _idx;
	byt _data[20];
	int _line_len = strlen(line) + 1;
	*(int *)(_data + 16) = _line_len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_REQ;
	AUTOSDK_PACK_IDX(_data) = _idx;
	AUTOSDK_PACK_CID(_data) = 3;
	AUTOSDK_PACK_LEN(_data) = 20 + _line_len;
	AUTOSDK_PACK_VOL(_data) = 16;
	autosdk_portme_prior(hand);
	autosdk_portme_write(hand, _data, 20);
	autosdk_portme_write(hand, (any)line, _line_len);
	autosdk_portme_flush(hand);
	return _idx;
}
int _MyAutoSdkDemo_Typ_hook_(any hand, any data, any _ctx, void (*_cbk)(any ctx, byt ochr, s16 os16, s32 os32, s64 os64, byt obyt, u16 ou16, u32 ou32, u64 ou64, f32 of32, f64 of64, any oarr_ptr, int oarr_len, any oblk_ptr, int oblk_len, int ret))
{
	if (!data)
	{
		_cbk(_ctx, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1);
		return 1;
	}
	int _ret = AUTOSDK_PACK_RET(data);
	byt ochr = *(byt *)(data + 16);
	s16 os16 = *(s16 *)(data + 17);
	s32 os32 = *(s32 *)(data + 19);
	s64 os64 = *(s64 *)(data + 23);
	byt obyt = *(byt *)(data + 31);
	u16 ou16 = *(u16 *)(data + 32);
	u32 ou32 = *(u32 *)(data + 34);
	u64 ou64 = *(u64 *)(data + 38);
	f32 of32 = *(f32 *)(data + 46);
	f64 of64 = *(f64 *)(data + 50);
	any _ptr = data + 66;
	any oarr_ptr = _ptr;
	int oarr_len = *(int *)(data + 58);
	_ptr += oarr_len;
	any oblk_ptr = _ptr;
	int oblk_len = *(int *)(data + 62);
	_cbk(_ctx, ochr, os16, os32, os64, obyt, ou16, ou32, ou64, of32, of64, oarr_ptr, oarr_len, oblk_ptr, oblk_len, _ret);
	return _ret;
}
int MyAutoSdkDemo_Typ(any hand, chr ichr, s16 is16, s32 is32, s64 is64, byt ibyt, u16 iu16, u32 iu32, u64 iu64, f32 if32, f64 if64, str istr, any iarr_ptr, int iarr_len, int oarr_len, int oblk_len, any _ctx, void (*_cbk)(any ctx, byt ochr, s16 os16, s32 os32, s64 os64, byt obyt, u16 ou16, u32 ou32, u64 ou64, f32 of32, f64 of64, any oarr_ptr, int oarr_len, any oblk_ptr, int oblk_len, int ret))
{
	int _idx = autosdk_portme_alloc(hand, _MyAutoSdkDemo_Typ_hook_, _cbk, _ctx);
	if (0 > _idx) return _idx;
	byt _data[74];
	*(chr *)(_data + 16) = ichr;
	*(s16 *)(_data + 17) = is16;
	*(s32 *)(_data + 19) = is32;
	*(s64 *)(_data + 23) = is64;
	*(byt *)(_data + 31) = ibyt;
	*(u16 *)(_data + 32) = iu16;
	*(u32 *)(_data + 34) = iu32;
	*(u64 *)(_data + 38) = iu64;
	*(f32 *)(_data + 46) = if32;
	*(f64 *)(_data + 50) = if64;
	int _istr_len = strlen(istr) + 1;
	*(int *)(_data + 58) = _istr_len;
	*(int *)(_data + 62) = iarr_len;
	*(int *)(_data + 66) = oarr_len;
	*(int *)(_data + 70) = oblk_len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_REQ;
	AUTOSDK_PACK_IDX(_data) = _idx;
	AUTOSDK_PACK_CID(_data) = 4;
	AUTOSDK_PACK_LEN(_data) = 74 + _istr_len + iarr_len;
	AUTOSDK_PACK_VOL(_data) = 66 + oarr_len;
	autosdk_portme_prior(hand);
	autosdk_portme_write(hand, _data, 74);
	autosdk_portme_write(hand, (any)istr, _istr_len);
	autosdk_portme_write(hand, iarr_ptr, iarr_len);
	autosdk_portme_flush(hand);
	return _idx;
}
static const any _autosdk_func_tb_[] = {
	_MyAutoSdkDemo_Add_hook_,
	_MyAutoSdkDemo_Blk_hook_,
	_MyAutoSdkDemo_Fmt_hook_,
	_MyAutoSdkDemo_Log_hook_,
	_MyAutoSdkDemo_Typ_hook_,
};
const char *_autosdk_version_ = "ee502cbc";
