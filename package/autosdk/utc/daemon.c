//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: daemon.c
// Author		: XuCF
// Created On	: 2024/12/11
// Description	: daemon.c
//
// History
// 1. V1.0, Created by XuCF. 2024/12/11
//=============================================================================

#include "demo.cli.h"

typedef struct
{
	int fdu;
	int fdd;
} autosdk_handle_t;

static any _cbk_ = 0, _ctx_ = 0, _hook_ = 0;

int autosdk_portme_alloc(void *hand, void *hook, void *func, void *ctx)
{
	_cbk_ = func;
	_ctx_ = ctx;
	_hook_ = hook;
	return 0;
}

int autosdk_portme_prior(void *hand)
{
	return 0;
}

int autosdk_portme_write(void *hand, void *data, int size)
{
	autosdk_handle_t *thiz = hand;
	size = write(thiz->fdd, data, size);
	return size;
}

int autosdk_portme_flush(void *hand)
{
	return 0;
}

any MyAutoSdkDemo_Open(void)
{
	autosdk_handle_t *thiz = clike_new(autosdk_handle_t);
	thiz->fdu = open("/tmp/fifo_up", O_RDONLY);
	thiz->fdd = open("/tmp/fifo_dn", O_WRONLY);
	return thiz;
}

non MyAutoSdkDemo_Close(any thiz)
{
	autosdk_handle_t *hand = thiz;
	close(hand->fdu);
	close(hand->fdd);
	clike_free(hand);
}

non MyAutoSdkDemo_Await(any hand)
{
	autosdk_handle_t *thiz = hand;
	int size = 0;
	any buffer = clike_malloc(1024);
	while (1)
	{
		size += read(thiz->fdu, buffer + size, 1024 - size);
		if (16 <= size && AUTOSDK_PACK_LEN(buffer) <= size) break;
		clike_delay_ms(100);
	}

	int (*hook)(any hand, any data, any _ctx, any _hook) = _hook_;
	if (hook) hook(hand, buffer, _ctx_, _cbk_);
	_hook_ = 0, _ctx_ = 0, _cbk_ = 0;

	clike_free(buffer);
}
