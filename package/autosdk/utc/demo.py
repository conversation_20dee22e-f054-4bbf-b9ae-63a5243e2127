#!python
from typing import Callable
import struct
import os
import time


class MyAutoSdkDemo:
    """Simulates the autosdk_handle_t structure from C"""

    def __init__(self):
        self.fdu = os.open("/tmp/fifo_up", os.O_RDONLY)
        self.fdd = os.open("/tmp/fifo_dn", os.O_WRONLY)
        self.callback = None  # Directly store the callback as a member variable

    def __enter__(self):
        """Supports 'with' statement"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Clean up resources when exiting 'with' block"""
        os.close(self.fdu)
        os.close(self.fdd)
        return False  # Do not suppress exceptions

    def execute(self):
        """Wait and process the response"""
        # Read header (16 bytes)
        buffer = b''
        while len(buffer) < 16:
            buffer += os.read(self.fdu, 128)
        # Get total packet length
        packet_len = struct.unpack_from('I', buffer, 4)[0]
        # Read complete packet
        while len(buffer) < packet_len:
            buffer += os.read(self.fdu, 128)
        # Get command ID
        func_idx = struct.unpack_from('H', buffer, 2)[0]
        # Process response with appropriate hook function
        if 0 <= func_idx < len(MyAutoSdkDemo._autosdk_func_tb_) and self.callback:
            hook_func = MyAutoSdkDemo._autosdk_func_tb_[func_idx]
            hook_func(self, buffer[:packet_len], self.callback)
            self.callback = None  # Reset callback after use

    def alloc(self, callback: Callable) -> int:
        """Allocate an index and store callback function"""
        self.callback = callback
        return 0  # Always return 0 as index in this simplified version
    
    def prior(self) -> None:
        """Prior prepare for write"""
        pass

    def write(self, data: bytes) -> None:
        """Write data to the pipe"""
        os.write(self.fdd, data)


    def flush(self) -> None:
        """Flush data - no special implementation needed in Python"""
        pass

# functions-py >>
    def MyAutoSdkDemo_Add(self, a: float, b: float, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(24)  # Allocate fixed size buffer
        struct.pack_into('f', _data, 16, a)  # Pack a
        struct.pack_into('f', _data, 20, b)  # Pack b
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 0)  # Command ID
        struct.pack_into('I', _data, 4, 24)  # Request total length
        struct.pack_into('I', _data, 8, 20)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _MyAutoSdkDemo_Add_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        s = struct.unpack_from('f', _data, 16)[0]  # Read s
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(s, _ret)
        return _ret

    def MyAutoSdkDemo_Blk(self, data_len: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(20)  # Allocate fixed size buffer
        struct.pack_into('i', _data, 16, data_len)  # data buffer size
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 1)  # Command ID
        struct.pack_into('I', _data, 4, 20)  # Request total length
        struct.pack_into('I', _data, 8, 20)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _MyAutoSdkDemo_Blk_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ptr = 20  # Start index for variable data section
        data_len = struct.unpack_from('i', _data, 16)[0]  # data length
        data = _data[_ptr:_ptr+data_len]  # Extract data bytes
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(data, _ret)
        return _ret

    def MyAutoSdkDemo_Fmt(self, numb: int, data_len: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(24)  # Allocate fixed size buffer
        struct.pack_into('I', _data, 16, numb)  # Pack numb
        struct.pack_into('i', _data, 20, data_len)  # data buffer size
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 2)  # Command ID
        struct.pack_into('I', _data, 4, 24)  # Request total length
        struct.pack_into('I', _data, 8, 20 + data_len)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _MyAutoSdkDemo_Fmt_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ptr = 20  # Start index for variable data section
        data_len = struct.unpack_from('i', _data, 16)[0]  # data length
        data = _data[_ptr:_ptr+data_len]  # Extract data bytes
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(data, _ret)
        return _ret

    def MyAutoSdkDemo_Log(self, line: str, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(20)  # Allocate fixed size buffer
        line_len = len(line) + 1  # String length + null
        struct.pack_into('i', _data, 16, line_len)  # line length
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 3)  # Command ID
        struct.pack_into('I', _data, 4, 20 + line_len)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.write(line.encode() + b'\0')  # Write string
        self.flush()  # Flush all data
        return _idx

    def _MyAutoSdkDemo_Log_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def MyAutoSdkDemo_Typ(self, ichr: int, is16: int, is32: int, is64: int, ibyt: int, iu16: int, iu32: int, iu64: int, if32: float, if64: float, istr: str, iarr: bytes, oarr_len: int, oblk_len: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(74)  # Allocate fixed size buffer
        struct.pack_into('b', _data, 16, ichr)  # Pack ichr
        struct.pack_into('h', _data, 17, is16)  # Pack is16
        struct.pack_into('i', _data, 19, is32)  # Pack is32
        struct.pack_into('q', _data, 23, is64)  # Pack is64
        struct.pack_into('B', _data, 31, ibyt)  # Pack ibyt
        struct.pack_into('H', _data, 32, iu16)  # Pack iu16
        struct.pack_into('I', _data, 34, iu32)  # Pack iu32
        struct.pack_into('Q', _data, 38, iu64)  # Pack iu64
        struct.pack_into('f', _data, 46, if32)  # Pack if32
        struct.pack_into('d', _data, 50, if64)  # Pack if64
        istr_len = len(istr) + 1  # String length + null
        struct.pack_into('i', _data, 58, istr_len)  # istr length
        iarr_len = len(iarr)  # Get array length
        struct.pack_into('i', _data, 62, iarr_len)  # iarr length
        struct.pack_into('i', _data, 66, oarr_len)  # oarr buffer size
        struct.pack_into('i', _data, 70, oblk_len)  # oblk buffer size
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 4)  # Command ID
        struct.pack_into('I', _data, 4, 74 + istr_len + iarr_len)  # Request total length
        struct.pack_into('I', _data, 8, 66 + oarr_len)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.write(istr.encode() + b'\0')  # Write string
        self.write(iarr)  # Write array directly
        self.flush()  # Flush all data
        return _idx

    def _MyAutoSdkDemo_Typ_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        ochr = struct.unpack_from('B', _data, 16)[0]  # Read ochr
        os16 = struct.unpack_from('h', _data, 17)[0]  # Read os16
        os32 = struct.unpack_from('i', _data, 19)[0]  # Read os32
        os64 = struct.unpack_from('q', _data, 23)[0]  # Read os64
        obyt = struct.unpack_from('B', _data, 31)[0]  # Read obyt
        ou16 = struct.unpack_from('H', _data, 32)[0]  # Read ou16
        ou32 = struct.unpack_from('I', _data, 34)[0]  # Read ou32
        ou64 = struct.unpack_from('Q', _data, 38)[0]  # Read ou64
        of32 = struct.unpack_from('f', _data, 46)[0]  # Read of32
        of64 = struct.unpack_from('d', _data, 50)[0]  # Read of64
        _ptr = 66  # Start index for variable data section
        oarr_len = struct.unpack_from('i', _data, 58)[0]  # oarr length
        oarr = _data[_ptr:_ptr+oarr_len]  # Extract oarr bytes
        _ptr += oarr_len  # Move to next block
        oblk_len = struct.unpack_from('i', _data, 62)[0]  # oblk length
        oblk = _data[_ptr:_ptr+oblk_len]  # Extract oblk bytes
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(ochr, os16, os32, os64, obyt, ou16, ou32, ou64, of32, of64, oarr, oblk, _ret)
        return _ret

    _autosdk_func_tb_ = [
        _MyAutoSdkDemo_Add_hook_,
        _MyAutoSdkDemo_Blk_hook_,
        _MyAutoSdkDemo_Fmt_hook_,
        _MyAutoSdkDemo_Log_hook_,
        _MyAutoSdkDemo_Typ_hook_,
]
# functions-py <<


def _demo_():
    with MyAutoSdkDemo() as hand:
        # Call MyAutoSdkDemo_Add function
        def add_callback(sum, ret):
            print(f"fsum={sum}, ret={ret}")
        hand.MyAutoSdkDemo_Add(12.6, 6.3, add_callback)
        hand.execute()

        # Call MyAutoSdkDemo_Log function
        def log_callback(ret):
            print(f"ret={ret}")
        hand.MyAutoSdkDemo_Log("hello you", log_callback)
        hand.execute()

        # Call MyAutoSdkDemo_Fmt function
        def fmt_callback1(data, ret):
            print(f"data={data.decode()}, size={len(data)}")
        hand.MyAutoSdkDemo_Fmt(9, 64, fmt_callback1)
        hand.execute()

        # Call MyAutoSdkDemo_Blk function
        def blk_callback(data, ret):
            print(f"data={data.decode()}, size={len(data)}")
        hand.MyAutoSdkDemo_Blk(64, blk_callback)
        hand.execute()

        # Call MyAutoSdkDemo_Fmt function with different parameters
        def fmt_callback2(data, ret):
            print(f"data={data.decode()}, size={len(data)}")
        hand.MyAutoSdkDemo_Fmt(5, 64, fmt_callback2)
        hand.execute()

        # Call MyAutoSdkDemo_Fmt function again with different parameters
        def fmt_callback3(data, ret):
            print(f"data={data.decode()}, size={len(data)}")
        hand.MyAutoSdkDemo_Fmt(3, 64, fmt_callback3)
        hand.execute()

        # Call MyAutoSdkDemo_Add function again with different parameters
        def add_callback2(sum, ret):
            print(f"fsum={sum}")
        hand.MyAutoSdkDemo_Add(8.1, 1.2, add_callback2)
        hand.execute()


if __name__ == "__main__":
    _demo_()
