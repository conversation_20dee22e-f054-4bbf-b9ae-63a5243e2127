//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: case.c
// Author		: XuCF
// Created On	: 2024/11/25
// Description	: case.c
//
// History
// 1. V1.0, Created by XuCF. 2024/11/25
//=============================================================================

#include "libcustom.h"
#include "autosdk_server.h"

static void _clean_up_(void *arg)
{
	clike_print("clean %s\n", (str)arg);
}

API int MyAutoSdkDemo_Typ(/* demo api case */
	CHR(I) ichr,		  /* input byte demo */
	S16(I) is16,		  /* input short demo */
	S32(I) is32,		  /* input int demo */
	S64(I) is64,		  /* input long demo */
	BYT(I) ibyt,		  /* input unsigned byte demo */
	U16(I) iu16,		  /* input unsigned short demo */
	U32(I) iu32,		  /* input unsigned int demo */
	U64(I) iu64,		  /* input unsigned long demo */
	F32(I) if32,		  /* input float demo */
	F64(I) if64,		  /* input double demo */
	STR(I) istr,		  /* input text demo */
	ARR(I) iarr,		  /* input array demo */
	BYT(O) ochr,		  /* output byte demo */
	S16(O) os16,		  /* output short demo */
	S32(O) os32,		  /* output int demo */
	S64(O) os64,		  /* output long demo */
	BYT(O) obyt,		  /* output unsigned byte demo */
	U16(O) ou16,		  /* output unsigned short demo */
	U32(O) ou32,		  /* output unsigned int demo */
	U64(O) ou64,		  /* output unsigned long demo */
	F32(O) of32,		  /* output float demo */
	F64(O) of64,		  /* output double demo */
	ARR(O) oarr,		  /* output array demo */
	BLK(O) oblk			  /* output block demo */
)
{
	*ochr = ichr + 1;
	*obyt = ibyt + 1;
	*os16 = is16 + 1;
	*os32 = is32 + 1;
	*os64 = is64 + 1;
	*ou16 = iu16 + 1;
	*ou32 = iu32 + 1;
	*ou64 = iu64 + 1;
	*of32 = if32 + 1;
	*of64 = if64 + 1;

	clike_print("STR=%s\n", istr);

	clike_copy(oarr->ptr, iarr->ptr, iarr->len);
	oarr->len = iarr->len;

	oblk->arg = 0;
	oblk->clean = _clean_up_;
	oblk->ptr = "hello you\n";
	oblk->len = 12;

	return 0;
}

RES int MyAutoSdkDemo_Add_(void *_hand, int _ret, /* add two float */
	F32(R) s									  /* sum */
);
API int MyAutoSdkDemo_Add(void *_hand, /* add two float */
	F32(I) a,						   /* addend  */
	F32(I) b						   /* addend */
)
{
	clike_delay_ms(500);
	MyAutoSdkDemo_Add_(_hand, 0, a + b);
	return 0;
}

API int MyAutoSdkDemo_Log(/* log */
	STR(I) line			  /* log line */
)
{
	clike_delay_ms(500);
	clike_print("%s\n", line);
	return 0;
}

ACT int MyAutoSdkDemo_Fmt_(void *_hand); /* format str */
API int MyAutoSdkDemo_Fmt(				 /* format str */
	U32(I) numb,						 /* input number */
	ARR(O) data							 /* output */
)
{
	clike_delay_ms(500);
	data->len = snprintf(data->ptr, data->len, "okay.%d", numb);
	((byt *)data->ptr)[data->len++] = 0;
	return 0;
}

API int MyAutoSdkDemo_Blk(/* block demo */
	BLK(O) data			  /* output */
)
{
	data->ptr = "block";
	data->len = 6;
	data->clean = _clean_up_;
	data->arg = (any) "block-demo";
	return 0;
}
