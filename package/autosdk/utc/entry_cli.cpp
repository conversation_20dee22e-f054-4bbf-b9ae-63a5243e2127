//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: entry.c
// Author		: XuCF
// Created On	: 2024/12/11
// Description	: entry.c
//
// History
// 1. V1.0, Created by XuCF. 2024/12/11
//=============================================================================
#include "libcustom.h"
#include "demo.api.h"

int main(int argc, const char **args)
{
	any hand = MyAutoSdkDemo_Open();
	MyAutoSdkDemo_Add(hand, 12.6f, 6.3f, [](float sum, int ret) {
		clike_print("fsum=%f, ret=%d\n", sum, ret);
	});
	MyAutoSdkDemo_Await(hand);

	MyAutoSdkDemo_Log(hand, "hello you", [](int ret) {
		clike_print("ret=%d\n", ret);
	});
	MyAutoSdkDemo_Await(hand);

	int len = 64;
	chr buffer[64];
	MyAutoSdkDemo_Fmt(hand, 9, len, [&buffer, &len](any data_ptr, int data_len, int ret) {
		memcpy(buffer, data_ptr, data_len);
		len = data_len;
		clike_print("data=%s, size=%d\n", buffer, len);
	});
	MyAutoSdkDemo_Await(hand);

	MyAutoSdkDemo_Blk(hand, len, [&buffer, &len](any data_ptr, int data_len, int ret) {
		memcpy(buffer, data_ptr, data_len);
		len = data_len;
		clike_print("data=%s, size=%d\n", buffer, len);
	});
	MyAutoSdkDemo_Await(hand);

	MyAutoSdkDemo_Fmt(hand, 5, 64, [](any data_ptr, int data_len, int ret) {
		clike_print("data=%s, size=%d\n", (str)data_ptr, data_len);
	});
	MyAutoSdkDemo_Await(hand);

	MyAutoSdkDemo_Fmt(hand, 3, 64, [](any data_ptr, int data_len, int ret) {
		clike_print("data=%s, size=%d\n", (str)data_ptr, data_len);
	});
	MyAutoSdkDemo_Await(hand);

	MyAutoSdkDemo_Add(hand, 8.1, 1.2, [](float sum, int ret) {
		clike_print("fsum=%f\n", sum);
	});
	MyAutoSdkDemo_Await(hand);

	MyAutoSdkDemo_Close(hand);

	return 0;
}
