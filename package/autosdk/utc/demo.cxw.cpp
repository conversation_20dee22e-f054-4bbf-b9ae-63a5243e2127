#include "demo.api.h"
// functions-cpp >>
static MyAutoSdkDemo_Add_callback_t _MyAutoSdkDemo_Add_callback_ = nullptr;
EXP int MyAutoSdkDemo_Add(any hand, f32 a, f32 b, const MyAutoSdkDemo_Add_callback_t &callback)
{
	_MyAutoSdkDemo_Add_callback_ = callback;
	return MyAutoSdkDemo_Add(hand, a, b, 0, [](void *ctx, f32 s, int ret) -> void {
		_MyAutoSdkDemo_Add_callback_(s, ret);
		_MyAutoSdkDemo_Add_callback_ = nullptr;
	});
}
static MyAutoSdkDemo_Blk_callback_t _MyAutoSdkDemo_Blk_callback_ = nullptr;
EXP int MyAutoSdkDemo_Blk(any hand, int data_len, const MyAutoSdkDemo_Blk_callback_t &callback)
{
	_MyAutoSdkDemo_Blk_callback_ = callback;
	return MyAutoSdkDemo_Blk(hand, data_len, 0, [](void *ctx, any data_ptr, int data_len, int ret) -> void {
		_MyAutoSdkDemo_Blk_callback_(data_ptr, data_len, ret);
		_MyAutoSdkDemo_Blk_callback_ = nullptr;
	});
}
static MyAutoSdkDemo_Fmt_callback_t _MyAutoSdkDemo_Fmt_callback_ = nullptr;
EXP int MyAutoSdkDemo_Fmt(any hand, u32 numb, int data_len, const MyAutoSdkDemo_Fmt_callback_t &callback)
{
	_MyAutoSdkDemo_Fmt_callback_ = callback;
	return MyAutoSdkDemo_Fmt(hand, numb, data_len, 0, [](void *ctx, any data_ptr, int data_len, int ret) -> void {
		_MyAutoSdkDemo_Fmt_callback_(data_ptr, data_len, ret);
		_MyAutoSdkDemo_Fmt_callback_ = nullptr;
	});
}
static MyAutoSdkDemo_Log_callback_t _MyAutoSdkDemo_Log_callback_ = nullptr;
EXP int MyAutoSdkDemo_Log(any hand, str line, const MyAutoSdkDemo_Log_callback_t &callback)
{
	_MyAutoSdkDemo_Log_callback_ = callback;
	return MyAutoSdkDemo_Log(hand, line, 0, [](void *ctx, int ret) -> void {
		_MyAutoSdkDemo_Log_callback_(ret);
		_MyAutoSdkDemo_Log_callback_ = nullptr;
	});
}
static MyAutoSdkDemo_Typ_callback_t _MyAutoSdkDemo_Typ_callback_ = nullptr;
EXP int MyAutoSdkDemo_Typ(any hand, chr ichr, s16 is16, s32 is32, s64 is64, byt ibyt, u16 iu16, u32 iu32, u64 iu64, f32 if32, f64 if64, str istr, any iarr_ptr, int iarr_len, int oarr_len, int oblk_len, const MyAutoSdkDemo_Typ_callback_t &callback)
{
	_MyAutoSdkDemo_Typ_callback_ = callback;
	return MyAutoSdkDemo_Typ(hand, ichr, is16, is32, is64, ibyt, iu16, iu32, iu64, if32, if64, istr, iarr_ptr, iarr_len, oarr_len, oblk_len, 0, [](void *ctx, byt ochr, s16 os16, s32 os32, s64 os64, byt obyt, u16 ou16, u32 ou32, u64 ou64, f32 of32, f64 of64, any oarr_ptr, int oarr_len, any oblk_ptr, int oblk_len, int ret) -> void {
		_MyAutoSdkDemo_Typ_callback_(ochr, os16, os32, os64, obyt, ou16, ou32, ou64, of32, of64, oarr_ptr, oarr_len, oblk_ptr, oblk_len, ret);
		_MyAutoSdkDemo_Typ_callback_ = nullptr;
	});
}
// functions-cpp <<