// Auto generated, don't edit
// MyAutoSdkDemo_Add I.F32 I.F32 O.F32
// MyAutoSdkDemo_Blk O.BLK
// MyAutoSdkDemo_Fmt I.U32 O.ARR
// MyAutoSdkDemo_Log I.STR
// MyAutoSdkDemo_Typ I.CHR I.S16 I.S32 I.S64 I.BYT I.U16 I.U32 I.U64 I.F32 I.F64 I.STR I.ARR O.BYT O.S16 O.S32 O.S64 O.BYT O.U16 O.U32 O.U64 O.F32 O.F64 O.ARR O.BLK
#include "libcustom.h"
#include "autosdk_protoc.h"
#include "autosdk_server.h"
int MyAutoSdkDemo_Add(void *_hand, f32 a, f32 b);
static int _MyAutoSdkDemo_Add_req_(any _hand, any _data, any _ctx)
{
	f32 a = *(f32 *)(_data + 16);
	f32 b = *(f32 *)(_data + 20);
	return MyAutoSdkDemo_Add(_hand, a, b);
}
int MyAutoSdkDemo_Add_(void *_hand, int _ret, f32 s)
{
	void *_node = autosdk_portme_deque(_hand, 0);
	if (!_node) return -1;
	void *_data = autosdk_portme_remap(_node);
	if (!_data)
	{
		autosdk_portme_relax(_node);
		return -1;
	}
	*(f32 *)(_data + 16) = s;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_flush(_hand);
	autosdk_portme_relax(_node);
	return _ret;
}
int MyAutoSdkDemo_Blk(autosdk_block_t *data);
static int _MyAutoSdkDemo_Blk_svr_(any _hand, any _data)
{
	autosdk_block_t data = {0, *(int *)(_data + 16), 0, 0};
	int _ret = MyAutoSdkDemo_Blk(&data);
	if (_ret > 0) return _ret;
	*(int *)(_data + 16) = data.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20 + data.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_write(_hand, data.ptr, data.len);
	if (data.clean) data.clean(data.arg);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MyAutoSdkDemo_Fmt(u32 numb, autosdk_array_t *data);
static int _MyAutoSdkDemo_Fmt_svr_(any _hand, any _data)
{
	u32 numb = *(u32 *)(_data + 16);
	any _wptr = _data + 20;
	autosdk_array_t data = {_wptr, *(int *)(_data + 20)};
	_wptr += data.len;
	int _ret = MyAutoSdkDemo_Fmt(numb, &data);
	if (_ret > 0) return _ret;
	*(int *)(_data + 16) = data.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20 + data.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_write(_hand, data.ptr, data.len);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MyAutoSdkDemo_Fmt_(void *_hand)
{
	if (!_hand) return -1;
	return autosdk_portme_awake(_hand, 2);
}
int MyAutoSdkDemo_Log(str line);
static int _MyAutoSdkDemo_Log_svr_(any _hand, any _data)
{
	any _rptr = _data + 20;
	str line = _rptr;
	int _ret = MyAutoSdkDemo_Log(line);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MyAutoSdkDemo_Typ(chr ichr, s16 is16, s32 is32, s64 is64, byt ibyt, u16 iu16, u32 iu32, u64 iu64, f32 if32, f64 if64, str istr, autosdk_array_t *iarr, byt *ochr, s16 *os16, s32 *os32, s64 *os64, byt *obyt, u16 *ou16, u32 *ou32, u64 *ou64, f32 *of32, f64 *of64, autosdk_array_t *oarr, autosdk_block_t *oblk);
static int _MyAutoSdkDemo_Typ_svr_(any _hand, any _data)
{
	chr ichr = *(chr *)(_data + 16);
	s16 is16 = *(s16 *)(_data + 17);
	s32 is32 = *(s32 *)(_data + 19);
	s64 is64 = *(s64 *)(_data + 23);
	byt ibyt = *(byt *)(_data + 31);
	u16 iu16 = *(u16 *)(_data + 32);
	u32 iu32 = *(u32 *)(_data + 34);
	u64 iu64 = *(u64 *)(_data + 38);
	f32 if32 = *(f32 *)(_data + 46);
	f64 if64 = *(f64 *)(_data + 50);
	byt *ochr = (byt *)(_data + 16);
	s16 *os16 = (s16 *)(_data + 17);
	s32 *os32 = (s32 *)(_data + 19);
	s64 *os64 = (s64 *)(_data + 23);
	byt *obyt = (byt *)(_data + 31);
	u16 *ou16 = (u16 *)(_data + 32);
	u32 *ou32 = (u32 *)(_data + 34);
	u64 *ou64 = (u64 *)(_data + 38);
	f32 *of32 = (f32 *)(_data + 46);
	f64 *of64 = (f64 *)(_data + 50);
	any _rptr = _data + 74;
	str istr = _rptr;
	_rptr += *(int *)(_data + 58);
	autosdk_array_t iarr = {_rptr, *(int *)(_data + 62)};
	any _wptr = _data + 66;
	autosdk_array_t oarr = {_wptr, *(int *)(_data + 66)};
	_wptr += oarr.len;
	autosdk_block_t oblk = {0, *(int *)(_data + 70), 0, 0};
	int _ret = MyAutoSdkDemo_Typ(ichr, is16, is32, is64, ibyt, iu16, iu32, iu64, if32, if64, istr, &iarr, ochr, os16, os32, os64, obyt, ou16, ou32, ou64, of32, of64, &oarr, &oblk);
	if (_ret > 0) return _ret;
	*(int *)(_data + 58) = oarr.len;
	*(int *)(_data + 62) = oblk.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 66 + oarr.len + oblk.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 66);
	autosdk_portme_write(_hand, oarr.ptr, oarr.len);
	autosdk_portme_write(_hand, oblk.ptr, oblk.len);
	if (oblk.clean) oblk.clean(oblk.arg);
	autosdk_portme_flush(_hand);
	return _ret;
}
static const any _autosdk_func_tb_[] = {
	_MyAutoSdkDemo_Add_req_,
	_MyAutoSdkDemo_Blk_svr_,
	_MyAutoSdkDemo_Fmt_svr_,
	_MyAutoSdkDemo_Log_svr_,
	_MyAutoSdkDemo_Typ_svr_,
};
static const any _autosdk_hand_tb_[] = {
	MyAutoSdkDemo_Add,
	MyAutoSdkDemo_Blk,
	MyAutoSdkDemo_Fmt,
	MyAutoSdkDemo_Log,
	MyAutoSdkDemo_Typ,
};
const char *_autosdk_version_ = "ee502cbc";
