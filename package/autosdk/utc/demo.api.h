//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: demo.api.h
// Author		: XuCF
// Created On	: 2024/12/09
// Description	: demo.api.h
//
// History
// 1. V1.0, Created by XuCF. 2024/12/09
//=============================================================================

#ifndef _DEMO_API_H
#define _DEMO_API_H

#include "libcustom.h"

#ifdef __cplusplus
extern "C"
{
#endif

any MyAutoSdkDemo_Open(void);
non MyAutoSdkDemo_Close(any thiz);
non MyAutoSdkDemo_Await(any hand);

// functions >>
int MyAutoSdkDemo_Add(any hand, f32 a, f32 b, any _ctx, void (*_cbk)(any ctx, f32 s, int ret));
int MyAutoSdkDemo_Blk(any hand, int data_len, any _ctx, void (*_cbk)(any ctx, any data_ptr, int data_len, int ret));
int MyAutoSdkDemo_Fmt(any hand, u32 numb, int data_len, any _ctx, void (*_cbk)(any ctx, any data_ptr, int data_len, int ret));
int MyAutoSdkDemo_Log(any hand, str line, any _ctx, void (*_cbk)(any ctx, int ret));
int MyAutoSdkDemo_Typ(any hand, chr ichr, s16 is16, s32 is32, s64 is64, byt ibyt, u16 iu16, u32 iu32, u64 iu64, f32 if32, f64 if64, str istr, any iarr_ptr, int iarr_len, int oarr_len, int oblk_len, any _ctx, void (*_cbk)(any ctx, byt ochr, s16 os16, s32 os32, s64 os64, byt obyt, u16 ou16, u32 ou32, u64 ou64, f32 of32, f64 of64, any oarr_ptr, int oarr_len, any oblk_ptr, int oblk_len, int ret));
// functions <<

#ifdef __cplusplus
}
#endif

#ifdef __cplusplus
#include <functional>
// functions-cpp >>
using MyAutoSdkDemo_Add_callback_t = std::function<void(f32 s, int ret)>;
int MyAutoSdkDemo_Add(any hand, f32 a, f32 b, const MyAutoSdkDemo_Add_callback_t &callback);
using MyAutoSdkDemo_Blk_callback_t = std::function<void(any data_ptr, int data_len, int ret)>;
int MyAutoSdkDemo_Blk(any hand, int data_len, const MyAutoSdkDemo_Blk_callback_t &callback);
using MyAutoSdkDemo_Fmt_callback_t = std::function<void(any data_ptr, int data_len, int ret)>;
int MyAutoSdkDemo_Fmt(any hand, u32 numb, int data_len, const MyAutoSdkDemo_Fmt_callback_t &callback);
using MyAutoSdkDemo_Log_callback_t = std::function<void(int ret)>;
int MyAutoSdkDemo_Log(any hand, str line, const MyAutoSdkDemo_Log_callback_t &callback);
using MyAutoSdkDemo_Typ_callback_t = std::function<void(byt ochr, s16 os16, s32 os32, s64 os64, byt obyt, u16 ou16, u32 ou32, u64 ou64, f32 of32, f64 of64, any oarr_ptr, int oarr_len, any oblk_ptr, int oblk_len, int ret)>;
int MyAutoSdkDemo_Typ(any hand, chr ichr, s16 is16, s32 is32, s64 is64, byt ibyt, u16 iu16, u32 iu32, u64 iu64, f32 if32, f64 if64, str istr, any iarr_ptr, int iarr_len, int oarr_len, int oblk_len, const MyAutoSdkDemo_Typ_callback_t &callback);
// functions-cpp <<
#endif

#endif //_DEMO_API_H
