//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: entry.c
// Author		: XuCF
// Created On	: 2024/12/11
// Description	: entry.c
//
// History
// 1. V1.0, Created by XuCF. 2024/12/11
//=============================================================================
#include "libcustom.h"
#include "demo.svr.h"

#include <sys/types.h>
#include <sys/stat.h>

typedef struct
{
	int fdu;
	int fdd;
	any buffer;
} autosdk_handle_t;

int autosdk_portme_write(any hand, any data, int size)
{
	autosdk_handle_t *thiz = hand;
	size = write(thiz->fdu, data, size);
	return size;
}

int autosdk_portme_flush(void *hand)
{
	return 0;
}

void *autosdk_portme_deque(void *hand, int cid)
{
	autosdk_handle_t *thiz = hand;
	return thiz->buffer;
}

void *autosdk_portme_remap(void *node)
{
	return node;
}

void autosdk_portme_relax(void *node)
{
}

int main(int argc, const char **args)
{
	autosdk_handle_t *hand = clike_new(autosdk_handle_t);

	unlink("/tmp/fifo_up");
	unlink("/tmp/fifo_dn");
	mkfifo("/tmp/fifo_up", 0666);
	mkfifo("/tmp/fifo_dn", 0666);

	hand->fdu = open("/tmp/fifo_up", O_WRONLY);
	hand->fdd = open("/tmp/fifo_dn", O_RDONLY);

	int size = 0, cid;
	any buffer = clike_malloc(1024);
	hand->buffer = buffer;

	int (*func)(any, any, any);

	while (1)
	{
		size += read(hand->fdd, buffer + size, 1024);
		if (16 > size || AUTOSDK_PACK_LEN(buffer) > size)
		{
			clike_delay_ms(100);
			continue;
		}
		cid = AUTOSDK_PACK_CID(buffer);
		clike_print("CID=%d\n", cid);
		func = _autosdk_func_tb_[cid];
		func(hand, buffer, NULL);
		size = 0;
	}

	close(hand->fdd);
	close(hand->fdu);

	clike_free(hand);

	return 0;
}
