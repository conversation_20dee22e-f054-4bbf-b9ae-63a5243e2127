[autosdk]
type = "src"
incp = ["inc"]
deps = ["cbasic"]

[autosdk-svr]
type = "src"
srcs = ["src/svr/*.c"]
incp = ["inc", "src/svr"]
deps = ["autosdk"]

[autosdk-cli]
type = "src"
srcs = ["src/cli/*.c"]
incp = ["inc", "src/cli"]
deps = ["autosdk"]

[myautosdk-svr]
type = "exe"
srcs = [
    "utc/case.c",
    "utc/entry_svr.c",
    "utc/demo.api.h",
    "utc/demo.cli.h",
    "utc/demo.cxw.cpp:cxw",
    "utc/demo.py:cli",
    "utc/demo.svr.h",
]
incp = ["utc"]
deps = ["autosdk"]

[myautosdk-cli]
type = "exe"
srcs = [
    "utc/daemon.c",
    "utc/demo.cxw.cpp",
    "utc/entry_cli.cpp",
]
incp = ["utc"]
lopt = ["-lstdc++"]
deps = ["autosdk"]
