//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: AutoSdkDaemon.h
// Author		: XuCF
// Created On	: 2025/03/18
// Description	: AutoSdkDaemon.h
//
// History
// 1. V1.0, Created by XuCF. 2025/03/18
//=============================================================================

#ifndef _AUTOSDKDAEMON_H
#define _AUTOSDKDAEMON_H

#include "libcustom.h"

#define MD_NOTIFY_DISC -1 // disconnected
#define MD_NOTIFY_PROP 1  // property update

ClassDeclare(AutoSdkDaemon);

any AutoSdkDaemon_New(any notice, any writer, int space, const any *func_tb, any hand, any ctx,
	void (*_cbk)(any hand, any ctx, int typ, any arg));
int AutoSdkDaemon_Del(any obj);
int AutoSdkDaemon_Abort(any hand, int idx);
int AutoSdkDaemon_Notify(any hand, int typ, any arg);

#endif //_AUTOSDKDAEMON_H
