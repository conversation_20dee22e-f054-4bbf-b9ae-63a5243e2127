//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: daemon.c
// Author		: XuCF
// Created On	: 2024/12/12
// Description	: daemon.c
//
// History
// 1. V1.0, Created by XuCF. 2024/12/12
//=============================================================================

#include "MdNotice.h"
#include "MdRunner.h"
#include "MdBuffer.h"
#include "MdWriter.h"
#include "autosdk_protoc.h"
#include "AutoSdkDaemon.h"

#define _BCALL_CNT_ 3
#define _ITYPE_CBK_ 1
#define _ITYPE_ASY_ 2

typedef struct
{
	slink_t super;
	MdBuffer *buffer;
	any hook;
	any cbk;
	any ctx;
	int idx;
} autosdk_index_t;

ClassRealize(AutoSdkDaemon)
{
	MdWriter *writer;
	MdMutex *mutex;
	MdNoticeUser *user;
	slink_t *req_space;
	slink_t req_queue;
	const any *func_tb;
	byt uuid;
	int tasked;
	void (*notify)(any hand, any ctx, int typ, any arg);
	any notify_hand;
	any notify_ctx;
	int alive;
};

static void _response_node_(autosdk_index_t *node, any data)
{
	int (*pfunc)(any, any, any, any) = node->hook;
	pfunc(0, data, node->ctx, node->cbk);
}

static void _task_route_(unv arg1, unv arg2)
{
	AutoSdkDaemon *thiz = (AutoSdkDaemon *)arg1;
	autosdk_index_t *node = (autosdk_index_t *)arg2;
	MdBuffer *buffer = node->buffer;
	_response_node_(node, buffer->data);
	MdBuffer_Drop(buffer);
	MdMutex_Lock(thiz->mutex);
	slink_put(thiz->req_space, (slink_t *)node);
	thiz->tasked--;
	MdMutex_Unlock(thiz->mutex);
	return;
}

static void _errored_proc_(unv arg1, unv arg2)
{
	AutoSdkDaemon *thiz = (AutoSdkDaemon *)arg1;
	thiz->notify(thiz->notify_hand, thiz->notify_ctx, MD_NOTIFY_DISC, 0);
}

static int _received_(AutoSdkDaemon *thiz, MdBuffer *buffer, any ndata, snv nsize)
{
	if (nsize == 0) return 0; // connected
	if (nsize < 0)			  // channel error
	{
		if (thiz->notify) MdTask_Boot(_errored_proc_, (unv)thiz, 0, 0, "autosdk-cli");
		return 0;
	}
	if (AUTOSDK_PACK_TYP(buffer->data) != AUTOSDK_TYPE_ACK) return 0;
	unsigned int pack_len = AUTOSDK_PACK_LEN(buffer->data);
	if (buffer->size < pack_len)
	{
		// zoom buffer
		MdBuffer_Zoom(buffer, pack_len);
		return buffer->size - pack_len;
	}
	// find node and send to task
	int idx = AUTOSDK_PACK_IDX(buffer->data);
	autosdk_index_t *node;
	MdMutex_Lock(thiz->mutex);
	slink_drop(&thiz->req_queue, node, node->idx == idx, {
		// not found, unlock and return
		MdMutex_Unlock(thiz->mutex);
		return 0;
	});
	node->buffer = buffer;
	thiz->tasked++;
	MdMutex_Unlock(thiz->mutex);
	MdTask_Boot(_task_route_, (unv)thiz, (unv)node, 0, "autosdk-cli");
	return pack_len;
}

any AutoSdkDaemon_New(any notice, any writer, int space, const any *func_tb, any hand, any ctx,
	void (*_cbk)(any hand, any ctx, int typ, any arg))
{
	Assert(!notice, return 0);
	Assert(!writer, return 0);
	Assert(0 >= space, return 0);
	Assert(!func_tb, return 0);
	AutoSdkDaemon *thiz = clike_new(AutoSdkDaemon);
	thiz->writer = writer;
	thiz->mutex = MdMutex_Create();
	thiz->user = MdNotice_AttachUser(notice, _received_, thiz, 0);
	slink_reset(&thiz->req_queue);
	thiz->req_space = slink_new(slink_t, autosdk_index_t, space);
	thiz->uuid = 0;
	thiz->tasked = 0;
	thiz->func_tb = func_tb;
	thiz->notify_ctx = ctx;
	thiz->notify_hand = hand;
	thiz->notify = _cbk;
	thiz->alive = 1;
	return thiz;
}

int AutoSdkDaemon_Del(any obj)
{
	AutoSdkDaemon *thiz = (AutoSdkDaemon *)obj;
	MdMutex_Lock(thiz->mutex);
	thiz->alive = 0; // mark as dead
	if (!clink_empty(&thiz->req_queue)) goto label_error;
	if (thiz->tasked) goto label_error;
	MdMutex_Unlock(thiz->mutex);
	while (!MdNotice_DetachUser(thiz->user)) clike_delay_us(100);
	clike_free(thiz->req_space);
	MdMutex_Delete(thiz->mutex);
	clike_free(thiz);
	return 0;
label_error:
	MdMutex_Unlock(thiz->mutex);
	return -1;
}

static void _abort_task_(AutoSdkDaemon *thiz, autosdk_index_t *node)
{
	// request abort
	byt abort_req[AUTOSDK_BASE_LEN];
	AUTOSDK_PACK_TYP(abort_req) = AUTOSDK_TYPE_ABT;
	AUTOSDK_PACK_IDX(abort_req) = node->idx;
	AUTOSDK_PACK_CID(abort_req) = 0;
	AUTOSDK_PACK_LEN(abort_req) = AUTOSDK_BASE_LEN;
	AUTOSDK_PACK_VOL(abort_req) = 0;
	AUTOSDK_PACK_CHK(abort_req) = 0;
	MdWriter_Write(thiz->writer, abort_req, AUTOSDK_BASE_LEN);

	// release node
	MdMutex_Lock(thiz->mutex);
	slink_put(thiz->req_space, (slink_t *)node);
	MdMutex_Unlock(thiz->mutex);
}

int AutoSdkDaemon_Abort(any hand, int idx)
{
	AutoSdkDaemon *thiz = hand;
	autosdk_index_t *node;
	MdMutex_Lock(thiz->mutex);
	slink_drop(&thiz->req_queue, node, node->idx == idx, {
		MdMutex_Unlock(thiz->mutex);
		return -1;
	});
	MdMutex_Unlock(thiz->mutex);
	LOGD("abort idx=%d", node->idx);
	_abort_task_(thiz, node);
	return 0;
}

int AutoSdkDaemon_Notify(any hand, int typ, any arg)
{
	AutoSdkDaemon *thiz = hand;
	if (!thiz->alive) return -1;
	if (thiz->notify) thiz->notify(thiz->notify_hand, thiz->notify_ctx, typ, arg);
	return 0;
}

int autosdk_portme_alloc(void *hand, void *hook, void *cbk, void *ctx)
{
	int idx = -1;
	AutoSdkDaemon *thiz = hand;
	MdMutex_Lock(thiz->mutex);
	if (!thiz->alive) goto label_unlock;
	autosdk_index_t *node = slink_pop(thiz->req_space, goto label_unlock);
	idx = node->idx = thiz->uuid++;
	node->hook = hook;
	node->cbk = cbk;
	node->ctx = ctx;
	node->buffer = 0;
	slink_attach_next(&thiz->req_queue, (slink_t *)node);
label_unlock:
	MdMutex_Unlock(thiz->mutex);
	return idx;
}

int autosdk_portme_prior(void *hand)
{
	AutoSdkDaemon *thiz = (AutoSdkDaemon *)hand;
	MdMutex_Lock(thiz->mutex);
	return 0;
}

int autosdk_portme_write(void *hand, void *data, int size)
{
	AutoSdkDaemon *thiz = (AutoSdkDaemon *)hand;
	return MdWriter_Write(thiz->writer, data, size);
}

int autosdk_portme_flush(void *hand)
{
	AutoSdkDaemon *thiz = (AutoSdkDaemon *)hand;
	MdMutex_Unlock(thiz->mutex);
	return 0;
}
