//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: AutoSdkServer.c
// Author		: XuCF
// Created On	: 2024/12/02
// Description	: AutoSdkServer.c
//
// History
// 1. V1.0, Created by XuCF. 2024/12/02
//=============================================================================

#include "MdNotice.h"
#include "MdRunner.h"
#include "MdWriter.h"
#include "MdBuffer.h"
#include "autosdk_protoc.h"
#include "autosdk_server.h"

#define AUTOSDK_PACK_FUN(d) thiz->func_tb[AUTOSDK_PACK_CID(d)]
#define AUTOSDK_PACK_HDL(d) thiz->hand_tb[AUTOSDK_PACK_CID(d)]

ClassDefined(AutoSdkServer)
{
	MdWriter *writer;
	dlink_t proc_queue;
	MdMutex *mutex;
	const any *func_tb;
	const any *hand_tb;
	int func_cnt;
};

static void _server_route_(unv arg1, unv arg2)
{
	AutoSdkServer *thiz = (AutoSdkServer *)arg1;
	MdBuffer *buffer = (MdBuffer *)arg2;
	int (*pfunc)(any, any) = AUTOSDK_PACK_FUN(buffer->data);
	int ret = pfunc(thiz, buffer->data);
	if (AUTOSDK_STAT_PROC == ret)
	{
		// add to the queue
		MdMutex_Lock(thiz->mutex);
		dlink_attach_prev(&thiz->proc_queue, (dlink_t *)buffer);
		MdMutex_Unlock(thiz->mutex);
	}
	else if (AUTOSDK_STAT_CONT == ret)
	{
		// reboot the task
		MdTask_Boot(_server_route_, arg1, arg2, 0, "autosdk-svr");
	}
	else // done or error
	{
		MdBuffer_Drop(buffer);
	}
}

static void _drop_proc_(AutoSdkServer *thiz, unv unused)
{
	dlink_t *queue = &thiz->proc_queue;
	MdBuffer *buffer;
	while (1)
	{
		MdMutex_Lock(thiz->mutex);
		if (clink_empty(queue))
		{
			MdMutex_Unlock(thiz->mutex);
			break;
		}
		buffer = (MdBuffer *)dlink_detach_next(queue);
		MdMutex_Unlock(thiz->mutex);
		LOGI("drop api %d", AUTOSDK_PACK_CID(buffer->data));
		MdBuffer_Drop(buffer);
	}
}

static void _abort_req_(AutoSdkServer *thiz, int idx)
{
	MdBuffer *buffer;
	dlink_t *queue = &thiz->proc_queue;
	MdMutex_Lock(thiz->mutex);
	clink_nget(queue, buffer, AUTOSDK_PACK_IDX(buffer->data) == idx, {
		MdMutex_Unlock(thiz->mutex);
		return;
	});
	dlink_detach_curr((dlink_t *)buffer);
	MdMutex_Unlock(thiz->mutex);
	LOGI("abort api %d", AUTOSDK_PACK_CID(buffer->data));
	MdBuffer_Drop(buffer);
}

static int _received_(AutoSdkServer *thiz, MdBuffer *buffer, any ndata, snv nsize)
{
	LOGD("nsize: %d", nsize);
	if (nsize == 0) return 0;
	if (nsize < 0) // channel error
	{
		MdTask_Boot(_drop_proc_, (unv)thiz, 0, 0, "autosdk-svr");
		return 0;
	}
	unsigned int pack_typ = AUTOSDK_PACK_TYP(buffer->data);
	if (AUTOSDK_TYPE_LIM > pack_typ) return 0;									 // type not match
	if (AUTOSDK_BASE_LEN > buffer->size) return buffer->size - AUTOSDK_BASE_LEN; // header not enough
	unsigned int pack_cid = AUTOSDK_PACK_CID(buffer->data);
	if (thiz->func_cnt <= pack_cid) return 0; // function not found
	unsigned int pack_len = AUTOSDK_PACK_LEN(buffer->data);
	unsigned int pack_vol = AUTOSDK_PACK_VOL(buffer->data);
	MdBuffer_Zoom(buffer, pack_len > pack_vol ? pack_len : pack_vol);
	if (buffer->size >= pack_len)
	{
		LOGD("TYP: %x, CID: %d, LEN: %d, VOL: %d", pack_typ, pack_cid, pack_len, pack_vol);
		if (AUTOSDK_TYPE_REQ == pack_typ)
		{
			// add task
			MdTask_Boot(_server_route_, (unv)thiz, (unv)buffer, 0, "autosdk-svr");
		}
		else if (AUTOSDK_TYPE_ABT == pack_typ)
		{
			int idx = AUTOSDK_PACK_IDX(buffer->data);
			MdBuffer_Drop(buffer);
			_abort_req_(thiz, idx);
		}
		return pack_len;
	}
	return buffer->size - pack_len;
}

any AutoSdkServer_New(any notice, any writer, const any *func_tb, const any *hand_tb, int func_cnt)
{
	Assert(!notice, return 0);
	Assert(!writer, return 0);
	Assert(!func_tb, return 0);
	Assert(!hand_tb, return 0);
	Assert(func_cnt <= 0, return 0);
	AutoSdkServer *thiz = clike_new(AutoSdkServer);
	thiz->writer = writer;
	thiz->func_tb = func_tb;
	thiz->hand_tb = hand_tb;
	thiz->func_cnt = func_cnt;
	thiz->mutex = MdMutex_Create();
	dlink_reset(&thiz->proc_queue);
	MdNotice_AttachUser(notice, _received_, thiz, 0);
	return thiz;
}

void AutoSdkServer_Del(any obj)
{
	AutoSdkServer *thiz = (AutoSdkServer *)obj;
	if (!thiz) return;
	// Clean processes
	_drop_proc_(thiz, 0);
	// Release the mutex
	MdMutex_Delete(thiz->mutex);
	// Release the object
	clike_free(thiz);
}

int autosdk_portme_awake(void *hand, int cid)
{
	AutoSdkServer *thiz = hand;
	if (!thiz) return -1;
	MdMutex_Lock(thiz->mutex);
	MdBuffer *buffer, *temp;
	dlink_t *queue = &thiz->proc_queue;
	clink_nfor(queue, buffer)
	{
		if (AUTOSDK_PACK_CID(buffer->data) == cid)
		{
			temp = (MdBuffer *)clink_prev(buffer);
			dlink_detach_curr((dlink_t *)buffer);
			MdTask_Boot(_server_route_, (unv)thiz, (unv)buffer, 0, "autosdk-svr");
			buffer = temp;
		}
	}
	MdMutex_Unlock(thiz->mutex);
	return 0;
}

int autosdk_portme_write(void *hand, void *data, int size)
{
	AutoSdkServer *thiz = hand;
	return MdWriter_Write(thiz->writer, data, size);
}

int autosdk_portme_flush(void *hand)
{
	return 0;
}

any autosdk_portme_deque(void *hand, int cid)
{
	AutoSdkServer *thiz = hand;
	Assert(!thiz, return NULL);
	MdMutex_Lock(thiz->mutex);
	MdBuffer *buffer;
	dlink_t *queue = &thiz->proc_queue;
	clink_nget(queue, buffer, AUTOSDK_PACK_CID(buffer->data) == cid, {
		MdMutex_Unlock(thiz->mutex);
		return NULL;
	});
	dlink_detach_curr((dlink_t *)buffer);
	MdMutex_Unlock(thiz->mutex);
	return buffer;
}

any autosdk_portme_remap(void *node)
{
	MdBuffer *buffer = node;
	return buffer->data;
}

void autosdk_portme_relax(void *node)
{
	MdBuffer *buffer = node;
	MdBuffer_Drop(buffer);
}
