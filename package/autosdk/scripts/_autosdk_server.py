from _autosdk_core import FIXED_SIZE_MAP


def output_one_server_declaration(func, fp):
    clips = []
    
    if func.hand:
        clips.append("void *_hand")
    if func.ctx:
        clips.append("void *_ctx")

    for typx, dirc, name, _ in func.args:
        if dirc == "R":
            continue

        star = "*" if "O" == dirc else ""
        if "ARR" == typx:
            clips.append(f"autosdk_array_t *{name}")
        elif "BLK" == typx:
            clips.append(f"autosdk_block_t *{name}")
        elif "STR" == typx and dirc == "I":
            clips.append(f"str {name}")
        else:
            clips.append(f"{typx.lower()} {star}{name}")

    args = ", ".join(clips) if clips else "void"
    fp.write(f"int {func.name}({args});\n")


def output_one_server_request(func, fp):
    fp.write(f"static int _{func.name}_req_(any _hand, any _data, any _ctx)\n")
    fp.write("{\n")

    input_fix, input_var, _, _, _ = func.unpack()

    rpos = 16
    for typx, _, name, _ in input_fix:
        fp.write(f"\t{typx.lower()} {name} = *({typx.lower()} *)(_data + {rpos});\n")
        rpos += FIXED_SIZE_MAP[typx]

    if input_var:
        offset = len(input_var) * 4
        fp.write(f"\tany _rptr = _data + {rpos + offset};\n")
    for i, (typx, _, name, _) in enumerate(input_var):
        if "ARR" == typx:
            fp.write(f"\tautosdk_array_t {name} = {{_rptr, *(int *)(_data + {rpos})}};\n")
            if len(input_var) > i + 1:
                fp.write(f"\t_rptr += {name}.len;\n")
        elif "STR" == typx:
            fp.write(f"\tstr {name} = _rptr;\n")
            if len(input_var) > i + 1:
                fp.write(f"\t_rptr += *(int *)(_data + {rpos});\n")
        rpos += 4

    args = []
    if func.hand:
        args.append("_hand")
    if func.ctx:
        args.append("_ctx")

    for _, _, name, _ in input_fix:
        args.append(name)

    for typx, _, name, _ in input_var:
        if "ARR" == typx:
            args.append(f"&{name}")
        else:
            args.append(name)

    args = ", ".join(args)
    fp.write(f"\treturn {func.name}({args});\n")
    fp.write("}\n")


def output_one_server_response(func, idx, fp):
    fp.write(f"int {func.res}(void *_hand, int _ret")

    for typx, dirc, name, _ in func.args:
        if "R" == dirc:
            if "ARR" == typx:
                fp.write(f", autosdk_array_t *{name}")
            elif "BLK" == typx:
                fp.write(f", autosdk_block_t *{name}")
            else:
                fp.write(f", {typx.lower()} {name}")
    fp.write(")\n{\n")

    fp.write(f"\tvoid *_node = autosdk_portme_deque(_hand, {idx});\n")
    fp.write(f"\tif (!_node) return -1;\n")

    fp.write(f"\tvoid *_data = autosdk_portme_remap(_node);\n")
    fp.write(f"\tif (!_data)\n\t{{\n")
    fp.write(f"\t\tautosdk_portme_relax(_node);\n")
    fp.write(f"\t\treturn -1;\n")
    fp.write(f"\t}}\n")

    _, _, output_fix, output_arr, output_blk = func.unpack()
    output_var = output_arr + output_blk

    wpos = 16
    for typx, _, name, _ in output_fix:
        fp.write(f"\t*({typx.lower()} *)(_data + {wpos}) = {name};\n")
        wpos += FIXED_SIZE_MAP[typx]

    for _, _, name, _ in output_var:
        fp.write(f"\t*(int *)(_data + {wpos}) = {name}->len;\n")
        wpos += 4

    fp.write(f"\tAUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;\n")
    fp.write(f"\tAUTOSDK_PACK_LEN(_data) = {wpos}")
    for _, _, name, _ in output_var:
        fp.write(f" + {name}->len")
    fp.write(";\n")
    fp.write(f"\tAUTOSDK_PACK_RET(_data) = _ret;\n")

    fp.write(f"\tautosdk_portme_write(_hand, _data, {wpos});\n")
    for _, _, name, _ in output_arr:
        fp.write(f"\tautosdk_portme_write(_hand, {name}->ptr, {name}->len);\n")
    for _, _, name, _ in output_blk:
        fp.write(f"\tautosdk_portme_write(_hand, {name}->ptr, {name}->len);\n")
        fp.write(f"\tif ({name}->clean) {name}->clean({name}->arg);\n")
    fp.write(f"\tautosdk_portme_flush(_hand);\n")

    fp.write(f"\tautosdk_portme_relax(_node);\n")

    fp.write("\treturn _ret;\n")
    fp.write("}\n")


def output_one_server_action(func, idx, fp):
    fp.write(f"int {func.act}(void *_hand)\n")
    fp.write("{\n")
    fp.write("\tif (!_hand) return -1;\n")
    fp.write(f"\treturn autosdk_portme_awake(_hand, {idx});\n")
    fp.write("}\n")


def output_one_server_handler(func, fp):
    fp.write(f"static int _{func.name}_svr_(any _hand, any _data)\n")
    fp.write("{\n")

    input_fix, input_var, output_fix, output_arr, output_blk = func.unpack()

    args = []
    if func.hand:
        args.append("_hand")
    if func.ctx:
        args.append("_ctx")

    for typx, dirc, name, _ in func.args:
        if "O" == dirc:
            if "ARR" == typx or "BLK" == typx:
                args.append(f"&{name}")
            else:
                args.append(name)
        else:
            if "ARR" == typx:
                args.append(f"&{name}")
            else:
                args.append(name)

    rpos, wpos = 16, 16

    for typx, _, name, _ in input_fix:
        fp.write(f"\t{typx.lower()} {name} = *({typx.lower()} *)(_data + {rpos});\n")
        rpos += FIXED_SIZE_MAP[typx]

    for typx, _, name, _ in output_fix:
        fp.write(f"\t{typx.lower()} *{name} = ({typx.lower()} *)(_data + {wpos});\n")
        wpos += FIXED_SIZE_MAP[typx]

    if input_var:
        offset = (len(input_var) + len(output_arr) + len(output_blk)) * 4
        fp.write(f"\tany _rptr = _data + {rpos + offset};\n")
    for i, (typx, _, name, _) in enumerate(input_var):
        if "ARR" == typx:
            fp.write(f"\tautosdk_array_t {name} = {{_rptr, *(int *)(_data + {rpos})}};\n")
            if len(input_var) > i + 1:
                fp.write(f"\t_rptr += {name}.len;\n")
        elif "STR" == typx:
            fp.write(f"\tstr {name} = _rptr;\n")
            if len(input_var) > i + 1:
                fp.write(f"\t_rptr += *(int *)(_data + {rpos});\n")
        rpos += 4

    if output_arr:
        offset = (len(output_arr) + len(output_blk)) * 4
        fp.write(f"\tany _wptr = _data + {wpos + offset};\n")
    for _, _, name, _ in output_arr:
        fp.write(f"\tautosdk_array_t {name} = {{_wptr, *(int *)(_data + {rpos})}};\n")
        fp.write(f"\t_wptr += {name}.len;\n")
        rpos += 4
    for _, _, name, _ in output_blk:
        fp.write(f"\tautosdk_block_t {name} = {{0, *(int *)(_data + {rpos}), 0, 0}};\n")
        rpos += 4

    args = ", ".join(args)
    fp.write(f"\tint _ret = {func.name}({args});\n")
    fp.write("\tif (_ret > 0) return _ret;\n")

    for _, _, name, _ in output_arr + output_blk:
        fp.write(f"\t*(int *)(_data + {wpos}) = {name}.len;\n")
        wpos += 4

    fp.write(f"\tAUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;\n")
    fp.write(f"\tAUTOSDK_PACK_LEN(_data) = {wpos}")
    for _, _, name, _ in output_arr + output_blk:
        fp.write(f" + {name}.len")
    fp.write(";\n")
    fp.write(f"\tAUTOSDK_PACK_RET(_data) = _ret;\n")

    fp.write(f"\tautosdk_portme_write(_hand, _data, {wpos});\n")
    for _, _, name, _ in output_arr:
        fp.write(f"\tautosdk_portme_write(_hand, {name}.ptr, {name}.len);\n")
    for _, _, name, _ in output_blk:
        fp.write(f"\tautosdk_portme_write(_hand, {name}.ptr, {name}.len);\n")
        fp.write(f"\tif ({name}.clean) {name}.clean({name}.arg);\n")
    fp.write(f"\tautosdk_portme_flush(_hand);\n")

    fp.write("\treturn _ret;\n")
    fp.write("}\n")


def output_server_code(funcs, fp):
    fp.write("#include \"libcustom.h\"\n")
    fp.write("#include \"autosdk_protoc.h\"\n")
    fp.write("#include \"autosdk_server.h\"\n")

    for idx, func in enumerate(funcs):
        if func is None:
            continue
        output_one_server_declaration(func, fp)
        if func.res:
            output_one_server_request(func, fp)
            output_one_server_response(func, idx, fp)
            continue
        output_one_server_handler(func, fp)
        if func.act:
            output_one_server_action(func, idx, fp)

    fp.write("static const any _autosdk_func_tb_[] = {\n")
    for func in funcs:
        if func is None:
            fp.write("\tNULL,\n")
        elif func.res:
            fp.write(f"\t_{func.name}_req_,\n")
        else:
            fp.write(f"\t_{func.name}_svr_,\n")
    fp.write("};\n")
    fp.write("static const any _autosdk_hand_tb_[] = {\n")
    for func in funcs:
        if func is None:
            fp.write("\tNULL,\n")
        else:
            fp.write(f"\t{func.name},\n")
    fp.write("};\n") 