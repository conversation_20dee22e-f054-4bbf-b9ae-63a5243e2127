from _autosdk_core import FIXED_SIZE_MAP


# Rust type mapping for different data types
RUST_TYPE_MAP = {
    'CHR': 'i8', 'BYT': 'u8', 'S16': 'i16', 'U16': 'u16',
    'S32': 'i32', 'U32': 'u32', 'S64': 'i64', 'U64': 'u64',
    'F32': 'f32', 'F64': 'f64', 'STR': '&str'
}

# Rust put methods for BytesMut
RUST_PUT_MAP = {
    'CHR': 'put_i8', 'BYT': 'put_u8', 'S16': 'put_i16_le', 'U16': 'put_u16_le',
    'S32': 'put_i32_le', 'U32': 'put_u32_le', 'S64': 'put_i64_le', 'U64': 'put_u64_le',
    'F32': 'put_f32_le', 'F64': 'put_f64_le'
}

# Rust get methods for Bytes
RUST_GET_MAP = {
    'CHR': 'get_i8', 'BYT': 'get_u8', 'S16': 'get_i16_le', 'U16': 'get_u16_le',
    'S32': 'get_i32_le', 'U32': 'get_u32_le', 'S64': 'get_i64_le', 'U64': 'get_u64_le',
    'F32': 'get_f32_le', 'F64': 'get_f64_le'
}

# Rust keywords that need to be escaped
RUST_KEYWORDS = {
    'as', 'break', 'const', 'continue', 'crate', 'else', 'enum', 'extern',
    'false', 'fn', 'for', 'if', 'impl', 'in', 'let', 'loop', 'match', 'mod',
    'move', 'mut', 'pub', 'ref', 'return', 'self', 'Self', 'static', 'struct',
    'super', 'trait', 'true', 'type', 'unsafe', 'use', 'where', 'while',
    'async', 'await', 'dyn', 'abstract', 'become', 'box', 'do', 'final',
    'macro', 'override', 'priv', 'typeof', 'unsized', 'virtual', 'yield',
    'try', 'union'
}


def camel_to_snake(name):
    """Convert camelCase to snake_case"""
    result = []
    for i, char in enumerate(name):
        if i == 0:
            result.append(char.lower())
            continue
        if not char.isupper():
            result.append(char)
            continue
        if name[i - 1] == "_":
            result.append(char.lower())
            continue
        if not name[i - 1].isupper():
            result.append("_")
            result.append(char.lower())
            continue
        if name[i+1].isupper() or name[i+1] == "_":
            result.append(char.lower())
            continue
        result.append("_")
        result.append(char.lower())
    return ''.join(result)


def process_function_name(name):
    """Process function name: remove prefix before first underscore, convert to snake_case"""
    # Remove prefix before first underscore
    if '_' in name:
        name = name[name.index('_') + 1:]

    # Convert camelCase to snake_case
    snake_name = camel_to_snake(name)

    # Handle Rust keywords
    if snake_name in RUST_KEYWORDS:
        snake_name += '_'

    return snake_name


def process_param_name(name):
    """Process parameter name to avoid Rust keywords"""
    if name in RUST_KEYWORDS:
        return name + '_'
    return name


def write_rust_function(func, idx, fp):
    """Generate a Rust async function for the given API function"""

    # Process function name
    rust_func_name = process_function_name(func.name)

    # Build function signature
    params = []
    for typx, dirc, name, _ in func.args:
        param_name = process_param_name(name)
        if "O" == dirc or "R" == dirc:
            if "ARR" == typx or "BLK" == typx:
                params.append(f"{param_name}_len: u32")
        else:
            if "ARR" == typx or "BLK" == typx:
                params.append(f"{param_name}: &[u8]")
            elif "STR" == typx:
                params.append(f"{param_name}: &str")
            else:
                rust_type = RUST_TYPE_MAP.get(typx, typx.lower())
                params.append(f"{param_name}: {rust_type}")

    # Determine return type
    input_fix, input_var, output_fix, output_arr, output_blk = func.unpack()
    output_var = output_arr + output_blk
    
    total_outputs = len(output_fix) + len(output_var)
    
    if total_outputs == 0:
        return_type = "Result<(), Error>"
    elif total_outputs == 1:
        if len(output_fix) == 1:
            typx = output_fix[0][0]
            rust_type = RUST_TYPE_MAP.get(typx, typx.lower())
            return_type = f"Result<{rust_type}, Error>"
        else:  # len(output_var) == 1
            return_type = "Result<Bytes, Error>"
    else:
        # Multiple outputs - build tuple type
        output_types = []
        for typx, _, _, _ in output_fix:
            rust_type = RUST_TYPE_MAP.get(typx, typx.lower())
            output_types.append(rust_type)
        for _, _, _, _ in output_var:
            output_types.append("Bytes")
        return_type = f"Result<({', '.join(output_types)}), Error>"

    params_str = ", ".join(params)
    fp.write(f"    pub async fn {rust_func_name}(&self{', ' + params_str if params_str else ''}) -> {return_type} {{\n")

    # Allocate request id
    fp.write("        // Allocate request id\n")
    fp.write("        let (_id, _rx) = self.alloc();\n\n")

    # Calculate request size
    fixed_size = 16  # Header size
    for typx, _, _, _ in input_fix:
        fixed_size += FIXED_SIZE_MAP[typx]
    fixed_size += (len(input_var) + len(output_var)) * 4  # Length fields

    fp.write("        // Create request buffer\n")
    capacity_expr = [str(fixed_size)]
    for typx, _, name, _ in input_var:
        param_name = process_param_name(name)
        if "STR" == typx:
            capacity_expr.append(f"{param_name}.len() + 1")
        else:
            capacity_expr.append(f"{param_name}.len()")
    
    if len(capacity_expr) > 1:
        fp.write(f"        let mut _request = BytesMut::with_capacity({' + '.join(capacity_expr)});\n\n")
    else:
        fp.write(f"        let mut _request = BytesMut::with_capacity({capacity_expr[0]});\n\n")

    # Build request header
    fp.write("        // Build request header\n")
    fp.write("        _request.put_u8(0xff); // AUTOSDK(TYP)\n")
    fp.write("        _request.put_u8(_id); // AUTOSDK(IDX)\n")
    fp.write(f"        _request.put_u16_le({idx}); // AUTOSDK(CID)\n")

    # Calculate total length
    total_len_expr = [str(fixed_size)]
    for typx, _, name, _ in input_var:
        param_name = process_param_name(name)
        if "STR" == typx:
            total_len_expr.append(f"{param_name}.len() + 1")
        else:
            total_len_expr.append(f"{param_name}.len()")
    
    if len(total_len_expr) > 1:
        fp.write(f"        _request.put_u32_le(({' + '.join(total_len_expr)}) as u32); // AUTOSDK(LEN)\n")
    else:
        fp.write(f"        _request.put_u32_le({total_len_expr[0]}); // AUTOSDK(LEN)\n")
    
    # Calculate expected response size
    resp_size = 16  # Response header
    for typx, _, _, _ in output_fix:
        resp_size += FIXED_SIZE_MAP[typx]
    resp_size += len(output_var) * 4  # Length fields
    
    resp_expr = [str(resp_size)]
    for _, _, name, _ in output_arr:
        param_name = process_param_name(name)
        resp_expr.append(f"{param_name}_len")
    
    if len(resp_expr) > 1:
        fp.write(f"        _request.put_u32_le(({' + '.join(resp_expr)}) as u32); // AUTOSDK(VOL)\n")
    else:
        fp.write(f"        _request.put_u32_le({resp_expr[0]}); // AUTOSDK(VOL)\n")
    fp.write("        _request.put_u32_le(0); // Reserved\n\n")

    # Add input arguments
    if input_fix or input_var or output_var:
        fp.write("        // Add arguments\n")

    # Pack fixed input parameters
    for typx, _, name, _ in input_fix:
        param_name = process_param_name(name)
        put_method = RUST_PUT_MAP.get(typx, 'put_u32_le')
        fp.write(f"        _request.{put_method}({param_name}); // {name}\n")

    # Pack variable length parameters
    for typx, _, name, _ in input_var:
        param_name = process_param_name(name)
        if "STR" == typx:
            fp.write(f"        _request.put_u32_le(({param_name}.len() + 1) as u32); // {name}_len\n")
        else:
            fp.write(f"        _request.put_u32_le({param_name}.len() as u32); // {name}_len\n")

    # Pack output buffer sizes
    for _, _, name, _ in output_var:
        param_name = process_param_name(name)
        fp.write(f"        _request.put_u32_le({param_name}_len); // {name}_len\n")

    # Add variable data
    for typx, _, name, _ in input_var:
        param_name = process_param_name(name)
        if "STR" == typx:
            fp.write(f"        _request.put_slice({param_name}.as_bytes()); // {name}\n")
            fp.write(f"        _request.put_u8(0); // null terminator\n")
        else:
            fp.write(f"        _request.put_slice({param_name}); // {name}\n")

    fp.write("\n")

    # Send request and wait for response
    fp.write("        // Send request\n")
    fp.write("        self.write(_request.freeze()).await?;\n")
    fp.write("        // Wait for response\n")
    fp.write("        let mut _response = _rx.await?;\n\n")

    # Parse response header
    fp.write("        // Parse header\n")
    fp.write("        _response.advance(8); //  AUTOSDK(TYP + IDX + CID + LEN)\n")
    fp.write("        let _ret_code = _response.get_i32_le(); // AUTOSDK(RET)\n")
    fp.write("        if _ret_code != 0 {\n")
    fp.write("            return Err(Error::Rt(_ret_code));\n")
    fp.write("        }\n")
    fp.write("        _response.advance(4); // Reserved\n\n")

    # Parse output results
    if output_fix or output_var:
        fp.write("        // Get results\n")

    # Parse fixed outputs
    for typx, _, name, _ in output_fix:
        param_name = process_param_name(name)
        get_method = RUST_GET_MAP.get(typx, 'get_u32_le')
        fp.write(f"        let {param_name} = _response.{get_method}(); // {name}\n")

    # Parse variable outputs
    for _, _, name, _ in output_var:
        param_name = process_param_name(name)
        fp.write(f"        let actual_{param_name}_len = _response.get_u32_le() as usize; // {name}_len\n")
        fp.write(f"        let {param_name} = _response.split_to(actual_{param_name}_len); // {name}\n")

    fp.write("\n")

    # Return result
    fp.write("        // Return\n")
    if total_outputs == 0:
        fp.write("        Ok(())\n")
    elif total_outputs == 1:
        if len(output_fix) == 1:
            name = output_fix[0][2]
            param_name = process_param_name(name)
            fp.write(f"        Ok({param_name})\n")
        else:  # len(output_var) == 1
            name = output_var[0][2]
            param_name = process_param_name(name)
            fp.write(f"        Ok({param_name})\n")
    else:
        # Multiple outputs - return tuple
        return_values = []
        for _, _, name, _ in output_fix:
            param_name = process_param_name(name)
            return_values.append(f"{param_name}")
        for _, _, name, _ in output_var:
            param_name = process_param_name(name)
            return_values.append(f"{param_name}")
        fp.write(f"        Ok(({', '.join(return_values)}))\n")
    
    fp.write("    }\n\n")


def output_client_rust_code(funcs, fp):
    """Generate Rust client code for all functions"""
    for idx, func in enumerate(funcs):
        if func is None:
            continue
        write_rust_function(func, idx, fp)
