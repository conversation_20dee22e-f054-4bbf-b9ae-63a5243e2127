from keyword import iskeyword
from _autosdk_core import FIXED_SIZE_MAP, STRUCT_FORMAT_MAP


def write_hook_function(func, fp):
    fp.write(f"    def _{func.name}_hook_(self, _data: bytes, _hook: Callable) -> int:\n")
    fp.write("        if not _data:\n")
    fp.write("            return -1\n")

    wpos = 16

    for typx, dirc, name, _ in func.output_fix:
        if iskeyword(name):
            name = f"_{name}"
        fmt = STRUCT_FORMAT_MAP.get(typx, 'i')
        fp.write(f"        {name} = struct.unpack_from('{fmt}', _data, {wpos})[0]  # Read {name}\n")
        wpos += FIXED_SIZE_MAP[typx]

    if func.output_arr or func.output_blk:
        offset = wpos + len(func.output_arr + func.output_blk) * 4
        fp.write(f"        _ptr = {offset}  # Start index for variable data section\n")

    for i, (_, _, name, _) in enumerate(func.output_arr + func.output_blk):
        if iskeyword(name):
            name = f"_{name}"
        fp.write(f"        {name}_len = struct.unpack_from('i', _data, {wpos})[0]  # {name} length\n")
        fp.write(f"        {name} = _data[_ptr:_ptr+{name}_len]  # Extract {name} bytes\n")
        if i < len(func.output_arr + func.output_blk) - 1:
            fp.write(f"        _ptr += {name}_len  # Move to next block\n")
        wpos += 4

    fp.write("        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result\n")

    args = []
    for typx, dirc, name, _ in func.args:
        if iskeyword(name):
            name = f"_{name}"
        if "O" != dirc and "R" != dirc:
            continue
        args.append(name)
    args.append("_ret")
    fp.write(f"        _hook({', '.join(args)})\n")
    fp.write("        return _ret\n\n")


def write_main_function(func, idx, fp, py_type_map):
    clips = ["self"]
    for typx, dirc, name, _ in func.args:
        if iskeyword(name):
            name = f"_{name}"
        if "O" == dirc or "R" == dirc:
            if "ARR" == typx or "BLK" == typx:
                clips.append(f"{name}_len: int")
        else:
            if "ARR" == typx or "BLK" == typx:
                clips.append(f"{name}: bytes")
            else:
                py_type = py_type_map.get(typx, typx.lower())
                clips.append(f"{name}: {py_type}")
    clips.append("_callback: Callable")
    args = ", ".join(clips)

    fp.write(f"    def {func.name}({args}) -> int:\n")
    fp.write("        _idx = self.alloc(_callback)\n")
    fp.write("        if _idx < 0:\n")
    fp.write("            return _idx\n")

    input_fix, input_var, output_fix, output_arr, output_blk = func.unpack()
    output_var = output_arr + output_blk

    fixed_size = 16
    for typx, _, _, _ in input_fix:
        fixed_size += FIXED_SIZE_MAP[typx]
    fixed_size += (len(input_var) + len(output_var)) * 4

    fp.write(f"        _data = bytearray({fixed_size})  # Allocate fixed size buffer\n")

    rpos = 16
    for typx, _, name, _ in input_fix:
        fmt = STRUCT_FORMAT_MAP.get(typx, 'i')
        fp.write(f"        struct.pack_into('{fmt}', _data, {rpos}, {name})  # Pack {name}\n")
        rpos += FIXED_SIZE_MAP[typx]

    for typx, _, name, _ in input_var:
        if iskeyword(name):
            name = f"_{name}"
        if "STR" == typx:
            fp.write(f"        {name}_len = len({name}) + 1  # String length + null\n")
        else:
            fp.write(f"        {name}_len = len({name})  # Get array length\n")
        fp.write(f"        struct.pack_into('i', _data, {rpos}, {name}_len)  # {name} length\n")
        rpos += 4

    for _, _, name, _ in output_var:
        fp.write(f"        struct.pack_into('i', _data, {rpos}, {name}_len)  # {name} buffer size\n")
        rpos += 4

    fp.write("        # Pack protocol header\n")
    fp.write("        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ\n")
    fp.write(f"        struct.pack_into('B', _data, 1, _idx)  # Index\n")
    fp.write(f"        struct.pack_into('H', _data, 2, {idx})  # Command ID\n")

    total_expr = [str(rpos)]
    for typx, _, name, _ in input_var:
        if iskeyword(name):
            name = f"_{name}"
        total_expr.append(f"{name}_len")
    fp.write(f"        struct.pack_into('I', _data, 4, {' + '.join(total_expr)})  # Request total length\n")

    fixed_resp_size = 16
    for typx, _, _, _ in output_fix:
        fixed_resp_size += FIXED_SIZE_MAP[typx]
    fixed_resp_size += len(output_var) * 4

    resp_expr = [str(fixed_resp_size)]
    for _, _, name, _ in output_arr:
        if iskeyword(name):
            name = f"_{name}"
        resp_expr.append(f"{name}_len")
    fp.write(f"        struct.pack_into('I', _data, 8, {' + '.join(resp_expr)})  # Expected response length\n")
    fp.write("        struct.pack_into('I', _data, 12, 0)  # Checksum\n")

    fp.write(f"        self.prior()  # Prior prepare for write\n")
    fp.write(f"        self.write(_data)  # Write header and fixed data\n")
    for typx, _, name, _ in input_var:
        if iskeyword(name):
            name = f"_{name}"
        if "STR" == typx:
            fp.write(f"        self.write({name}.encode() + b'\\0')  # Write string\n")
        else:
            fp.write(f"        self.write({name})  # Write array directly\n")
    fp.write("        self.flush()  # Flush all data\n")
    fp.write("        return _idx\n\n")


def output_client_python_code(funcs, fp):
    py_type_map = {
        'S32': 'int', 'U32': 'int', 'S16': 'int', 'U16': 'int',
        'S64': 'int', 'U64': 'int', 'F32': 'float', 'F64': 'float',
        'CHR': 'int', 'BYT': 'int', 'STR': 'str'
    }

    for idx, func in enumerate(funcs):
        if func is None:
            continue
        write_main_function(func, idx, fp, py_type_map)
        write_hook_function(func, fp)

    fp.write("    _autosdk_func_tb_ = [\n")
    for func in funcs:
        if func is None:
            fp.write("        None,\n")
        else:
            fp.write(f"        _{func.name}_hook_,\n")
    fp.write("]\n") 