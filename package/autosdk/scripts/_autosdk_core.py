import os
import re
import hashlib
import shutil
# Type size mapping for different data types
FIXED_SIZE_MAP = {
    "CHR": 1, "BYT": 1, "S16": 2, "U16": 2,
    "S32": 4, "U32": 4, "S64": 8, "U64": 8,
    "F32": 4, "F64": 8,
}

# Struct format mapping for Python struct module
STRUCT_FORMAT_MAP = {
    "CHR": "b", "BYT": "B", "S16": "h", "U16": "H",
    "S32": "i", "U32": "I", "S64": "q", "U64": "Q",
    "F32": "f", "F64": "d",
}

# Regular expressions for parsing C code
API_PATTERN = re.compile(r"API int\s+(\w+)\(([^/]*?)(/\*\s*(.*?)\s*\*/)$")
RES_PATTERN = re.compile(r"RES int\s+(\w+)\(([^/]*?)(/\*\s*(.*?)\s*\*/)$")
ACT_PATTERN = re.compile(r"ACT int\s+(\w+)\(([^/]*?)(/\*\s*(.*?)\s*\*/)$")
ARG_PATTERN = re.compile(r"\s*(\w+)\((.*?)\)\s+(\w+),?\s*/\*([\s\S]*)?\*/")


class FunctionDef:
    def __init__(self):
        self.name = None
        self.hand = False
        self.ctx = False
        self.ret = None
        self.desc = None
        self.res = None
        self.act = None
        self.args = []
        self.input_fix = []
        self.input_var = []
        self.output_fix = []
        self.output_arr = []
        self.output_blk = []

    def __str__(self):
        clips = [self.name]
        for typx, inout, _, _ in self.args:
            if inout == "R":
                clips.append(f"O.{typx}")
            else:
                clips.append(f"{inout}.{typx}")
        return " ".join(clips)

    def parse_ctx_params(self, ctx_str):
        if not ctx_str:
            return
        ctx_parts = ctx_str.split(',')
        for part in ctx_parts:
            part = part.strip()
            if part.endswith('*_hand') or part.endswith('* _hand'):
                self.hand = True
            elif part.endswith('*_ctx') or part.endswith('* _ctx'):
                self.ctx = True

    def append(self, argp):
        self.args.append(argp)

    def finish(self):
        self._move_r_params_to_end()
        self._classify_params()

    def _move_r_params_to_end(self):
        r_args = []
        non_r_args = []
        for arg in self.args:
            typx, dirc, _, _ = arg
            if "R" == dirc:
                r_args.append(arg)
            else:
                non_r_args.append(arg)
        self.args = non_r_args + r_args

    def _classify_params(self):
        for arg in self.args:
            typx, dirc, _, _ = arg
            if "O" == dirc or "R" == dirc:
                assert "STR" != typx
                if "ARR" == typx:
                    self.output_arr.append(arg)
                elif "BLK" == typx:
                    self.output_blk.append(arg)
                else:
                    self.output_fix.append(arg)
            else:
                assert "BLK" != typx
                if "ARR" == typx or "STR" == typx:
                    self.input_var.append(arg)
                else:
                    self.input_fix.append(arg)

    def unpack(self):
        return self.input_fix, self.input_var, self.output_fix, self.output_arr, self.output_blk


def find_functions_in_c_file(path):
    funcs, func = [], None

    def find_api(line):
        nonlocal func
        mat = API_PATTERN.match(line)
        if not mat:
            return False
        if func is None:
            func = FunctionDef()
        func.name = mat.group(1)
        ctx_str = mat.group(2).strip()
        func.parse_ctx_params(ctx_str)
        func.desc = mat.group(4).strip()
        return True

    def find_res(line):
        nonlocal func
        mat = RES_PATTERN.match(line)
        if not mat:
            return False
        if func is None:
            func = FunctionDef()
        func.res = mat.group(1)
        return True

    def find_act(line):
        nonlocal func
        mat = ACT_PATTERN.match(line)
        if not mat:
            return False
        if func is None:
            func = FunctionDef()
        func.act = mat.group(1)
        func.desc = mat.group(4).strip() if mat.group(4) else ""
        return True

    def find_arg(line):
        nonlocal func
        if func is None:
            return False
        mat = ARG_PATTERN.match(line)
        if not mat:
            if func.name:
                func.finish()
                funcs.append(func)
                func = None
            return False
        func.append((mat.group(1), mat.group(2), mat.group(3), mat.group(4)))
        return True

    with open(path, "r", errors="ignore") as fp:
        for line in fp:
            if find_arg(line):
                continue
            if find_res(line):
                continue
            if find_act(line):
                continue
            if find_api(line):
                continue

    return funcs


def replace_file_sections(path, rules):
    def rule_pop(line):
        for idx, (key, func) in enumerate(rules):
            if line.lstrip().startswith(f"// {key} >>") or line.lstrip().startswith(f"# {key} >>"):
                rules.pop(idx)
                return key, func
        return None

    with open(path, "r") as rp, open(".tmp", "w") as wp:
        key = None
        for line in rp:
            if not key:
                pair = rule_pop(line)
                if pair is None:
                    wp.write(line)
                    continue
                key, func = pair
                wp.write(line)
                func(wp)
            if line.lstrip().startswith(f"// {key} <<") or line.lstrip().startswith(f"# {key} <<"):
                wp.write(line)
                key = None

    shutil.move(".tmp", path)


def find_old_signatures(file):
    signs = []
    if not file or not os.access(file, os.R_OK):
        return signs
    with open(file, "r", errors="ignore") as fp:
        fp.readline()
        for line in fp:
            if line.startswith("//"):
                line = line.strip()
                signs.append(line[3::])
    return signs


def calculate_functions_hash(funcs):
    combined_signs = "\n".join(str(func) for func in funcs)
    hash_obj = hashlib.sha256(combined_signs.encode('utf-8'))
    return hash_obj.hexdigest()[:8]


def find_callback_param_def(func, prev, post):
    params = []
    if prev:
        params.append(prev)
    for typx, dirc, name, _ in func.args:
        if "O" != dirc and "R" != dirc:
            continue
        if "ARR" == typx or "BLK" == typx:
            params.append(f"any {name}_ptr")
            params.append(f"int {name}_len")
        else:
            params.append(f"{typx.lower()} {name}")
    if post:
        params.append(post)
    if not params:
        params.append("void")
    return params 