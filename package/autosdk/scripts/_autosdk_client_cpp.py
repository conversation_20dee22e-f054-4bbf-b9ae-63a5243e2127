from _autosdk_core import find_callback_param_def


def output_api_cpp_declarations(funcs, fp):
    for func in funcs:
        if func is None:
            continue
        args = ", ".join(find_callback_param_def(func, None, "int ret"))
        fp.write(f"using {func.name}_callback_t = std::function<void({args})>;\n")
        
        clips = ["any hand"]
        for typx, dirc, name, _ in func.args:
            if "O" == dirc or "R" == dirc:
                if "ARR" == typx or "BLK" == typx:
                    clips.append(f"int {name}_len")
            else:
                if "ARR" == typx or "BLK" == typx:
                    clips.append(f"any {name}_ptr")
                    clips.append(f"int {name}_len")
                else:
                    clips.append(f"{typx.lower()} {name}")
        clips.append(f"const {func.name}_callback_t &callback")
        args = ", ".join(clips)
        fp.write(f"int {func.name}({args});\n")


def output_client_cpp_code(funcs, fp):
    for func in funcs:
        if func is None:
            continue
        
        fp.write(f"static {func.name}_callback_t _{func.name}_callback_ = nullptr;\n")

        clips = ["any hand"]
        for typx, dirc, name, _ in func.args:
            if "O" == dirc or "R" == dirc:
                if "ARR" == typx or "BLK" == typx:
                    clips.append(f"int {name}_len")
            else:
                if "ARR" == typx or "BLK" == typx:
                    clips.append(f"any {name}_ptr")
                    clips.append(f"int {name}_len")
                else:
                    clips.append(f"{typx.lower()} {name}")
        clips.append(f"const {func.name}_callback_t &callback")
        args = ", ".join(clips)
        fp.write(f"EXP int {func.name}({args})\n{{\n")

        fp.write(f"\t_{func.name}_callback_ = callback;\n")

        args = ["hand"]
        for typx, dirc, name, _ in func.args:
            if "O" == dirc or "R" == dirc:
                if "ARR" == typx or "BLK" == typx:
                    args.append(f"{name}_len")
            else:
                if "ARR" == typx or "BLK" == typx:
                    args.append(f"{name}_ptr")
                    args.append(f"{name}_len")
                else:
                    args.append(name)
        args.append("0")

        lambda_params = find_callback_param_def(func, "void *ctx", "int ret")
        lambda_params = ", ".join(lambda_params)
        args.append(f"[]({lambda_params}) -> void {{")
        fp.write(f"\treturn {func.name}({', '.join(args)}\n")

        cbk_args = []
        for typx, dirc, name, _ in func.args:
            if "O" != dirc and "R" != dirc:
                continue
            if "ARR" == typx or "BLK" == typx:
                cbk_args.append(f"{name}_ptr")
                cbk_args.append(f"{name}_len")
            else:
                cbk_args.append(name)
        cbk_args.append("ret")
        fp.write(f"\t\t_{func.name}_callback_({', '.join(cbk_args) if cbk_args else ''});\n")
        fp.write(f"\t\t_{func.name}_callback_ = nullptr;\n")
        fp.write("\t});\n")
        fp.write("}\n") 