from _autosdk_core import FIXED_SIZE_MAP, find_callback_param_def


def output_one_api_callback(func, fp):
    clips = ["any ctx"]
    for typx, dirc, name, _ in func.args:
        if "O" != dirc and "R" != dirc:
            continue
        if "ARR" == typx or "BLK" == typx:
            clips.append(f"any {name}_ptr")
            clips.append(f"int {name}_len")
        else:
            clips.append(f"{typx.lower()} {name}")
    clips.append("int ret")
    args = ", ".join(clips)
    fp.write(f"void (*_cbk)({args})")


def output_api_declarations(funcs, fp, suffix):
    for func in funcs:
        if func is None:
            continue
        clips = ["any hand"]
        for typx, dirc, name, _ in func.args:
            if "O" == dirc or "R" == dirc:
                if "ARR" == typx or "BLK" == typx:
                    clips.append(f"int {name}_len")
            else:
                if "ARR" == typx or "BLK" == typx:
                    clips.append(f"any {name}_ptr")
                    clips.append(f"int {name}_len")
                else:
                    clips.append(f"{typx.lower()} {name}")
        clips.append("any _ctx")
        args = ", ".join(clips)
        fp.write(f"int {func.name}{suffix}({args}, ")
        output_one_api_callback(func, fp)
        fp.write(");\n")


def output_one_client_async(func, idx, fp, suffix):
    clips = ["any hand"]
    for typx, dirc, name, _ in func.args:
        if "O" == dirc or "R" == dirc:
            if "ARR" == typx or "BLK" == typx:
                clips.append(f"int {name}_len")
        else:
            if "ARR" == typx or "BLK" == typx:
                clips.append(f"any {name}_ptr")
                clips.append(f"int {name}_len")
            else:
                clips.append(f"{typx.lower()} {name}")
    clips.append("any _ctx")
    args = ", ".join(clips)
    fp.write(f"int {func.name}{suffix}({args}, ")
    output_one_api_callback(func, fp)
    fp.write(")")

    fp.write("\n{\n")
    fp.write(f"\tint _idx = autosdk_portme_alloc(hand, _{func.name}_hook_, _cbk, _ctx);\n")
    fp.write("\tif (0 > _idx) return _idx;\n")

    input_fix, input_var, output_fix, output_arr, output_blk = func.unpack()
    output_var = output_arr + output_blk

    fixed_size = 16
    for typx, _, _, _ in input_fix:
        fixed_size += FIXED_SIZE_MAP[typx]
    fixed_size += (len(input_var) + len(output_var)) * 4
    fp.write(f"\tbyt _data[{fixed_size}];\n")

    rpos = 16
    for typx, _, name, _ in input_fix:
        fp.write(f"\t*({typx.lower()} *)(_data + {rpos}) = {name};\n")
        rpos += FIXED_SIZE_MAP[typx]
    for typx, _, name, _ in input_var:
        if "STR" == typx:
            fp.write(f"\tint _{name}_len = strlen({name}) + 1;\n")
            fp.write(f"\t*(int *)(_data + {rpos}) = _{name}_len;\n")
        else:
            fp.write(f"\t*(int *)(_data + {rpos}) = {name}_len;\n")
        rpos += 4
    for _, _, name, _ in output_var:
        fp.write(f"\t*(int *)(_data + {rpos}) = {name}_len;\n")
        rpos += 4

    fp.write(f"\tAUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_REQ;\n")
    fp.write(f"\tAUTOSDK_PACK_IDX(_data) = _idx;\n")
    fp.write(f"\tAUTOSDK_PACK_CID(_data) = {idx};\n")
    fp.write(f"\tAUTOSDK_PACK_LEN(_data) = {rpos}")
    for typx, _, name, _ in input_var:
        if "STR" == typx:
            fp.write(f" + _{name}_len")
        else:
            fp.write(f" + {name}_len")
    fp.write(";\n")

    fixed_output_size = 16
    for typx, _, _, _ in output_fix:
        fixed_output_size += FIXED_SIZE_MAP[typx]
    fixed_output_size += (len(output_var)) * 4
    fp.write(f"\tAUTOSDK_PACK_VOL(_data) = {fixed_output_size}")
    for _, _, name, _ in output_arr:
        fp.write(f" + {name}_len")
    fp.write(";\n")

    fp.write(f"\tautosdk_portme_prior(hand);\n")
    fp.write(f"\tautosdk_portme_write(hand, _data, {fixed_size});\n")
    for typx, _, name, _ in input_var:
        if "STR" == typx:
            fp.write(f"\tautosdk_portme_write(hand, (any){name}, _{name}_len);\n")
        else:
            fp.write(f"\tautosdk_portme_write(hand, {name}_ptr, {name}_len);\n")
    fp.write("\tautosdk_portme_flush(hand);\n")

    fp.write("\treturn _idx;\n")
    fp.write("}\n")


def output_one_client_hook(func, idx, fp):
    fp.write(f"int _{func.name}_hook_(any hand, any data, any _ctx, ")
    output_one_api_callback(func, fp)
    fp.write(")\n{\n")

    fp.write(f"\tif (!data)\n\t{{\n")

    args = ["_ctx"]
    for typx, dirc, name, _ in func.args:
        if "O" != dirc and "R" != dirc:
            continue
        if "ARR" == typx or "BLK" == typx:
            args.append("0")
            args.append("0")
        else:
            args.append("0")
    args.append("1")
    args = ", ".join(args)
    fp.write(f"\t\t_cbk({args});\n")
    fp.write(f"\t\treturn 1;\n")
    fp.write(f"\t}}\n")

    fp.write("\tint _ret = AUTOSDK_PACK_RET(data);\n")

    _, _, output_fix, output_arr, output_blk = func.unpack()
    output_var = output_arr + output_blk
    wpos = 16

    for typx, _, name, _ in output_fix:
        fp.write(f"\t{typx.lower()} {name} = *({typx.lower()} *)(data + {wpos});\n")
        wpos += FIXED_SIZE_MAP[typx]

    if output_var:
        offset = wpos + (len(output_var)) * 4
        fp.write(f"\tany _ptr = data + {offset};\n")
    for i, (typx, _, name, _) in enumerate(output_var):
        fp.write(f"\tany {name}_ptr = _ptr;\n")
        fp.write(f"\tint {name}_len = *(int *)(data + {wpos});\n")
        if len(output_var) > i + 1:
            fp.write(f"\t_ptr += {name}_len;\n")
        wpos += 4

    args = ["_ctx"]
    for typx, dirc, name, _ in func.args:
        if "O" != dirc and "R" != dirc:
            continue
        if "ARR" == typx or "BLK" == typx:
            args.append(f"{name}_ptr")
            args.append(f"{name}_len")
        else:
            args.append(name)
    args.append("_ret")
    args = ", ".join(args)
    fp.write(f"\t_cbk({args});\n")
    fp.write("\treturn _ret;\n")
    fp.write("}\n")


def output_client_code(funcs, fp, suffix):
    fp.write("// Auto generated, don't edit\n")
    fp.write("#include \"libcustom.h\"\n")
    fp.write("#include \"autosdk_protoc.h\"\n")
    fp.write("#include \"autosdk_client.h\"\n")
    for idx, func in enumerate(funcs):
        if func is None:
            continue
        output_one_client_hook(func, idx, fp)
        output_one_client_async(func, idx, fp, suffix)
    fp.write("static const any _autosdk_func_tb_[] = {\n")
    for func in funcs:
        if func is None:
            fp.write("\tNULL,\n")
        else:
            fp.write(f"\t_{func.name}_hook_,\n")
    fp.write("};\n") 