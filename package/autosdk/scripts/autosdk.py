import os
from _autosdk_core import (
    find_functions_in_c_file, replace_file_sections, find_old_signatures,
    calculate_functions_hash
)
from _autosdk_server import output_server_code
from _autosdk_client_c import output_api_declarations, output_client_code
from _autosdk_client_cpp import output_api_cpp_declarations, output_client_cpp_code
from _autosdk_client_python import output_client_python_code
from _autosdk_client_rust import output_client_rust_code


def mksdk(application):
    if "SRCS" not in application:
        return

    sources, entry_api, entry_svr, entry_cli, entry_cpp, entry_pyt, entry_rst = [], None, None, None, None, None, None

    for item in application["SRCS"]:
        if entry_api is None and item.endswith(".api.h"):
            entry_api = item
            print(entry_api)
        elif entry_svr is None and item.endswith(".svr.h"):
            entry_svr = item
            print(entry_svr)
        elif entry_cli is None and item.endswith(".cli.h"):
            entry_cli = item
            print(entry_cli)
        elif entry_cpp is None and item.endswith(".cpp:cxw"):
            entry_cpp = item[:-4]
            print(entry_cpp)
        elif entry_pyt is None and item.endswith(".py:cli"):
            entry_pyt = item[:-4]
            print(entry_pyt)
        elif entry_rst is None and item.endswith(".rs:cli"):
            entry_rst = item[:-4]
            print(entry_rst)
        elif item.endswith(".c") and os.access(item, os.R_OK):
            sources.append(item)

    funcs = []
    for item in sources:
        funcs.extend(find_functions_in_c_file(item))

    cur_signs = [str(f) for f in funcs]
    old_signs = find_old_signatures(entry_svr)
    legacy_signs = old_signs + sorted(list(set(cur_signs) - set(old_signs)))

    def get_func_by_sign(sign):
        for func in funcs:
            if str(func) == sign:
                return func
        return None

    legacy_funcs = []
    for sign in legacy_signs:
        legacy_funcs.append(get_func_by_sign(sign))
    funcs = legacy_funcs

    cur_hash = calculate_functions_hash(funcs)

    suffix = application.get("AUTOSDK_API_SUFFIX", "")

    if entry_api:
        replace_file_sections(entry_api, [
            ("functions", lambda fp: output_api_declarations(funcs, fp, suffix)),
            ("functions-cpp", lambda fp: output_api_cpp_declarations(funcs, fp))
        ])

    if entry_cli:
        with open(entry_cli, "w") as fp:
            output_client_code(funcs, fp, suffix)
            fp.write(f"const char *_autosdk_version_ = \"{cur_hash}\";\n")

    if entry_cpp:
        replace_file_sections(entry_cpp, [
            ("functions-cpp", lambda fp: output_client_cpp_code(funcs, fp))
        ])

    with open(entry_svr, "w") as fp:
        fp.write("// Auto generated, don't edit\n")
        for sign in legacy_signs:
            fp.write(f"// {sign}\n")
        output_server_code(funcs, fp)
        fp.write(f"const char *_autosdk_version_ = \"{cur_hash}\";\n")

    if entry_pyt:
        replace_file_sections(entry_pyt, [
            ("functions-py", lambda fp: output_client_python_code(funcs, fp))
        ])
        os.chmod(entry_pyt, 0o755)

    if entry_rst:
        replace_file_sections(entry_rst, [
            ("functions-rust", lambda fp: output_client_rust_code(funcs, fp))
        ])
