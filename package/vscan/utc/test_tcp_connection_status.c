#include "libcustom.h"
#include "autocmd.h"
#include "mdscanner.api.h"
#include "MdRunner.h"

#define DISCOVERY_TIMEOUT_MS 3000  // 3 seconds timeout for discovery

typedef struct
{
	u32 ip_address;
	u32 tcp_client_ip_before;  // TCP client IP before connection
	u32 tcp_client_ip_after;   // TCP client IP after connection
	char device_name[32];
	char serial_num[16];
	any device_handle;
	int is_connected;
	adapter_info_t adapter_info;
	scanner_info_t scanner_info;
} test_device_t;

typedef struct
{
	test_device_t device;
	MdSynchronizer *sync;
	int test_completed;
	int overall_result;
	
	// Test statistics
	int total_discovery_attempts;
	int total_discovery_success;
	int total_connect_attempts;
	int total_connect_success;
	int tcp_status_check_success;
} test_context_t;

static test_context_t g_test_ctx = {0};

// Convert IP address to string
static void _ip_to_str_(u32 ip, char *buf, u32 size)
{
	if (ip == 0) {
		snprintf(buf, size, "0.0.0.0");
	} else {
		snprintf(buf, size, "%d.%d.%d.%d", (ip >> 24) & 0xFF, (ip >> 16) & 0xFF, (ip >> 8) & 0xFF, ip & 0xFF);
	}
}

// Initialize test context
static void _init_test_context_(void)
{
	clike_memset(&g_test_ctx, 0, sizeof(g_test_ctx));
	g_test_ctx.sync = MdSynchronizer_Create();
}

// Cleanup test context
static void _cleanup_test_context_(void)
{
	if (g_test_ctx.sync)
	{
		MdSynchronizer_Delete(g_test_ctx.sync);
		g_test_ctx.sync = 0;
	}
}

// Disconnect notification callback
static void _on_disconnect_notify_(any hand, any ctx, int typ, any arg)
{
	test_context_t *test_ctx = (test_context_t *)ctx;
	
	if (MD_NOTIFY_DISC == typ)
	{
		char ip_str[16];
		_ip_to_str_(test_ctx->device.ip_address, ip_str, sizeof(ip_str));
		clike_print("\t\t\tdisconnect callback triggered for device %s [%s]\n", 
			test_ctx->device.device_name, ip_str);
		
		test_ctx->device.is_connected = 0;
	}
}

// Probe callback to find available devices for initial discovery (before connection)
static int _probe_callback_before_connect_(adapter_info_t *adapter, scanner_info_t *scanner, any ctx)
{
	test_context_t *test_ctx = (test_context_t *)ctx;
	
	if (!scanner || !adapter) return 0;
	if (test_ctx->device.ip_address != 0) return 0; // Already found one device
	
	char ip_str[16], tcp_ip_str[16];
	_ip_to_str_(scanner->ip_address, ip_str, sizeof(ip_str));
	_ip_to_str_(scanner->tcp_client_ip, tcp_ip_str, sizeof(tcp_ip_str));
	
	test_device_t *device = &test_ctx->device;
	device->ip_address = scanner->ip_address;
	device->tcp_client_ip_before = scanner->tcp_client_ip;
	device->device_handle = 0;
	device->is_connected = 0;
	
	// Store adapter and scanner information
	clike_copy(&device->adapter_info, adapter, sizeof(adapter_info_t));
	clike_copy(&device->scanner_info, scanner, sizeof(scanner_info_t));
	
	clike_copy(device->device_name, scanner->device_name, sizeof(device->device_name));
	clike_copy(device->serial_num, scanner->serial_num, sizeof(device->serial_num));
	
	clike_print("\tfound test device: %s [%s] sn:%s tcp_client_ip:%s\n", 
		device->device_name, ip_str, device->serial_num, tcp_ip_str);
	
	return 0;
}

// Probe callback to check TCP status after connection
static int _probe_callback_after_connect_(adapter_info_t *adapter, scanner_info_t *scanner, any ctx)
{
	test_context_t *test_ctx = (test_context_t *)ctx;
	
	if (!scanner || !adapter) return 0;
	
	// Only check the device we're testing
	if (scanner->ip_address != test_ctx->device.ip_address) return 0;
	
	char ip_str[16], tcp_ip_str[16];
	_ip_to_str_(scanner->ip_address, ip_str, sizeof(ip_str));
	_ip_to_str_(scanner->tcp_client_ip, tcp_ip_str, sizeof(tcp_ip_str));
	
	test_ctx->device.tcp_client_ip_after = scanner->tcp_client_ip;
	
	clike_print("\t\tafter connection - device: %s [%s] tcp_client_ip:%s\n", 
		scanner->device_name, ip_str, tcp_ip_str);
	
	return 0;
}

// Discover devices before connection
static int _discover_device_before_connect_(void)
{
	clike_print("step 1: discovering devices before connection\n");
	
	g_test_ctx.total_discovery_attempts++;
	
	int ret = MdScanner_Probe(&g_test_ctx, _probe_callback_before_connect_);
	clike_assert(0 > ret, return ret);
	
	// Wait for probe to complete
	clike_delay_ms(DISCOVERY_TIMEOUT_MS);
	
	// Stop probe
	MdScanner_Probe(0, 0);
	
	if (0 == g_test_ctx.device.ip_address)
	{
		clike_print("\tno devices found\n");
		return -1;
	}
	
	g_test_ctx.total_discovery_success++;
	clike_print("\ttest device selected for connection status test\n");
	return 0;
}

// Connect to the test device
static int _connect_device_(void)
{
	test_device_t *device = &g_test_ctx.device;
	char ip_str[16];
	_ip_to_str_(device->ip_address, ip_str, sizeof(ip_str));
	
	g_test_ctx.total_connect_attempts++;
	
	clike_print("step 2: connecting to device %s [%s]\n", device->device_name, ip_str);
	
	device->device_handle = MdScanner_Open(ip_str, ".", &g_test_ctx, _on_disconnect_notify_);
	if (device->device_handle)
	{
		device->is_connected = 1;
		g_test_ctx.total_connect_success++;
		clike_print("\tconnected to device %s [%s]\n", device->device_name, ip_str);
		return 0;
	}
	else
	{
		clike_print("\tfailed to connect device %s [%s]\n", device->device_name, ip_str);
		return -1;
	}
}

// Discover devices after connection to check TCP status
static int _discover_device_after_connect_(void)
{
	clike_print("step 3: discovering devices after connection to check TCP status\n");
	
	int ret = MdScanner_Probe(&g_test_ctx, _probe_callback_after_connect_);
	clike_assert(0 > ret, return ret);
	
	// Wait for probe to complete
	clike_delay_ms(DISCOVERY_TIMEOUT_MS);
	
	// Stop probe
	MdScanner_Probe(0, 0);
	
	return 0;
}

// Disconnect the test device
static void _disconnect_device_(void)
{
	test_device_t *device = &g_test_ctx.device;
	
	if (!device->is_connected || !device->device_handle) { return; }
	
	while (MdScanner_Close(device->device_handle)) { clike_delay_ms(10); }
	
	device->is_connected = 0;
	device->device_handle = 0;
	
	char ip_str[16];
	_ip_to_str_(device->ip_address, ip_str, sizeof(ip_str));
	clike_print("step 4: disconnected device %s [%s]\n", device->device_name, ip_str);
}

// Analyze TCP connection status results
static int _analyze_tcp_connection_status_(void)
{
	test_device_t *device = &g_test_ctx.device;
	char before_str[16], after_str[16];
	_ip_to_str_(device->tcp_client_ip_before, before_str, sizeof(before_str));
	_ip_to_str_(device->tcp_client_ip_after, after_str, sizeof(after_str));
	
	clike_print("tcp connection status analysis:\n");
	clike_print("\tbefore connection: tcp_client_ip = %s\n", before_str);
	clike_print("\tafter connection:  tcp_client_ip = %s\n", after_str);
	
	// Check if TCP status changed as expected
	int status_correct = 0;
	if (device->tcp_client_ip_before == 0 && device->tcp_client_ip_after != 0)
	{
		clike_print("\t  tcp connection status detected correctly\n");
		clike_print("\t  device was not connected before, now shows client IP\n");
		status_correct = 1;
		g_test_ctx.tcp_status_check_success = 1;
	}
	else if (device->tcp_client_ip_before != 0 && device->tcp_client_ip_after != 0)
	{
		if (device->tcp_client_ip_before == device->tcp_client_ip_after)
		{
			clike_print("\t! device was already connected by same client\n");
			clike_print("\t  (this is also a valid scenario)\n");
			status_correct = 1;
			g_test_ctx.tcp_status_check_success = 1;
		}
		else
		{
			clike_print("\t! device was connected by different client, now shows new client IP\n");
			clike_print("\t  (this is also a valid scenario)\n");
			status_correct = 1;
			g_test_ctx.tcp_status_check_success = 1;
		}
	}
	else if (device->tcp_client_ip_before == 0 && device->tcp_client_ip_after == 0)
	{
		clike_print("\t? tcp connection status not detected\n");
		clike_print("\t  device should show client IP after connection\n");
		status_correct = 0;
	}
	else
	{
		clike_print("\t? unexpected tcp status change\n");
		status_correct = 0;
	}
	
	return status_correct ? 0 : -1;
}

// Print detailed statistics
static void _print_test_statistics_(void)
{
	clike_print("detailed statistics:\n");
	clike_print("\tdiscovery attempts:        %d\n", g_test_ctx.total_discovery_attempts);
	clike_print("\tdiscovery success:         %d\n", g_test_ctx.total_discovery_success);
	clike_print("\tconnection attempts:       %d\n", g_test_ctx.total_connect_attempts);
	clike_print("\tconnection success:        %d\n", g_test_ctx.total_connect_success);
	clike_print("\ttcp status check success:  %d\n", g_test_ctx.tcp_status_check_success);
	
	// Calculate success rates
	int discovery_rate = (g_test_ctx.total_discovery_attempts > 0) 
		? (g_test_ctx.total_discovery_success * 100 / g_test_ctx.total_discovery_attempts) : 0;
	int connect_rate = (g_test_ctx.total_connect_attempts > 0) 
		? (g_test_ctx.total_connect_success * 100 / g_test_ctx.total_connect_attempts) : 0;
	
	clike_print("\tsuccess rates:\n");
	clike_print("\t\tdiscovery:         %d%%\n", discovery_rate);
	clike_print("\t\tconnection:        %d%%\n", connect_rate);
}

// Print final test results
static void _print_final_results_(void)
{
	int device_found = (g_test_ctx.device.ip_address != 0);
	int connection_success = (g_test_ctx.total_connect_success > 0);
	int tcp_status_success = (g_test_ctx.tcp_status_check_success > 0);
	int overall_success = device_found && connection_success && tcp_status_success;
	
	clike_print("test result:\n");
	clike_print("\tdevice found           %s\n", device_found ? "pass" : "fail");
	clike_print("\tconnection             %s\n", connection_success ? "pass" : "fail");
	clike_print("\ttcp status detection   %s\n", tcp_status_success ? "pass" : "fail");
	clike_print("\toverall result         %s\n", overall_success ? "PASS" : "FAIL");
	
	g_test_ctx.overall_result = overall_success ? 0 : -1;
}

CMD int test_tcp_connection_status(void) /* tcp connection status detection test */
{
	int ret = -1;
	
	clike_print("starting tcp connection status detection test\n");
	
	// Initialize test context
	_init_test_context_();
	clike_assert(!g_test_ctx.sync, goto label_cleanup);
	
	// Initialize scanner
	ret = MdScanner_Init(".", 1);
	clike_assert(0 > ret, goto label_cleanup);
	
	// Step 1: Discover devices before connection
	ret = _discover_device_before_connect_();
	clike_assert(0 > ret, goto label_free_scanner);
	
	// Step 2: Connect to device
	ret = _connect_device_();
	clike_assert(0 > ret, goto label_free_scanner);
	
	// Wait a moment for connection to stabilize
	clike_delay_ms(1000);
	
	// Step 3: Discover devices after connection to check TCP status
	ret = _discover_device_after_connect_();
	
	// Step 4: Analyze results
	ret = _analyze_tcp_connection_status_();
	
	// Step 5: Disconnect device
	_disconnect_device_();
	
	// Print statistics and results
	_print_test_statistics_();
	_print_final_results_();
	
	ret = g_test_ctx.overall_result;

label_free_scanner:
	// Ensure cleanup
	if (g_test_ctx.device.is_connected)
	{
		_disconnect_device_();
	}
	MdScanner_Free();

label_cleanup:
	_cleanup_test_context_();
	
	return ret;
} 