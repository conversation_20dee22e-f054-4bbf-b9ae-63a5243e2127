#include "libcustom.h"
#include "autocmd.h"
#include "mdscanner.api.h"
#include "MdRunner.h"

#define REBOOT_TIMEOUT_MS 10000  // 10 seconds timeout for reboot operation
#define DISCONNECT_TIMEOUT_MS 5000  // 5 seconds timeout for disconnect callback

typedef struct
{
	u32 ip_address;
	char device_name[32];
	char serial_num[16];
	any device_handle;
	int is_connected;
	int disconnect_callback_received;
	int reboot_initiated;
	adapter_info_t adapter_info;
	scanner_info_t scanner_info;
} test_device_t;

typedef struct
{
	test_device_t device;
	MdSynchronizer *sync;
	int test_completed;
	int overall_result;
	
	// Statistics
	int total_connect_attempts;
	int total_connect_success;
	int total_reboot_attempts;
	int total_disconnect_callbacks;
} test_context_t;

static test_context_t g_test_ctx = {0};

// Convert IP address to string
static void _ip_to_str_(u32 ip, char *buf, u32 size)
{
	snprintf(buf, size, "%d.%d.%d.%d", (ip >> 24) & 0xFF, (ip >> 16) & 0xFF, (ip >> 8) & 0xFF, ip & 0xFF);
}

// Initialize test context
static void _init_test_context_(void)
{
	clike_memset(&g_test_ctx, 0, sizeof(g_test_ctx));
	g_test_ctx.sync = MdSynchronizer_Create();
}

// Cleanup test context
static void _cleanup_test_context_(void)
{
	if (g_test_ctx.sync)
	{
		MdSynchronizer_Delete(g_test_ctx.sync);
		g_test_ctx.sync = 0;
	}
}

// Disconnect notification callback
static void _on_disconnect_notify_(any hand, any ctx, int typ, any arg)
{
	test_context_t *test_ctx = (test_context_t *)ctx;
	
	if (MD_NOTIFY_DISC == typ)
	{
		char ip_str[16];
		_ip_to_str_(test_ctx->device.ip_address, ip_str, sizeof(ip_str));
		clike_print("\t\t\tdisconnect callback triggered for device %s [%s]\n", 
			test_ctx->device.device_name, ip_str);
		
		test_ctx->device.is_connected = 0;
		test_ctx->device.disconnect_callback_received = 1;
		test_ctx->total_disconnect_callbacks++;
		
		// Wake up waiting thread
		MdSynchronizer_Wake(test_ctx->sync);
	}
	else if (MD_NOTIFY_PROP == typ)
	{
		// Ignore property notifications for this test
		clike_print("\t\t\tproperty notification received (ignored)\n");
	}
}

// Probe callback to find first available device
static int _probe_callback_(adapter_info_t *adapter, scanner_info_t *scanner, any ctx)
{
	test_context_t *test_ctx = (test_context_t *)ctx;
	
	if (!scanner || !adapter) return 0;
	if (test_ctx->device.ip_address != 0) return 0; // Already found one device
	
	char ip_str[16];
	_ip_to_str_(scanner->ip_address, ip_str, sizeof(ip_str));
	
	test_device_t *device = &test_ctx->device;
	device->ip_address = scanner->ip_address;
	device->device_handle = 0;
	device->is_connected = 0;
	device->disconnect_callback_received = 0;
	device->reboot_initiated = 0;
	
	// Store adapter and scanner information for reboot
	clike_copy(&device->adapter_info, adapter, sizeof(adapter_info_t));
	clike_copy(&device->scanner_info, scanner, sizeof(scanner_info_t));
	
	clike_copy(device->device_name, scanner->device_name, sizeof(device->device_name));
	clike_copy(device->serial_num, scanner->serial_num, sizeof(device->serial_num));
	
	clike_print("\tfound test device: %s [%s] sn:%s\n", device->device_name, ip_str, device->serial_num);
	
	return 0;
}

// Discover first available device
static int _discover_device_(void)
{
	clike_print("step 1: probing for test device\n");
	
	int ret = MdScanner_Probe(&g_test_ctx, _probe_callback_);
	clike_assert(0 > ret, return ret);
	
	// Wait for probe to complete
	clike_delay_ms(2000);
	
	// Stop probe
	MdScanner_Probe(0, 0);
	
	if (0 == g_test_ctx.device.ip_address)
	{
		clike_print("no devices found\n");
		return -1;
	}
	
	clike_print("test device selected\n");
	return 0;
}

// Connect to the test device
static int _connect_device_(void)
{
	test_device_t *device = &g_test_ctx.device;
	char ip_str[16];
	_ip_to_str_(device->ip_address, ip_str, sizeof(ip_str));
	
	g_test_ctx.total_connect_attempts++;
	
	clike_print("\tconnecting to device %s [%s]\n", device->device_name, ip_str);
	
	device->device_handle = MdScanner_Open(ip_str, ".", &g_test_ctx, _on_disconnect_notify_);
	if (device->device_handle)
	{
		device->is_connected = 1;
		g_test_ctx.total_connect_success++;
		clike_print("\tconnected to device %s [%s]\n", device->device_name, ip_str);
		return 0;
	}
	else
	{
		clike_print("\tfailed to connect device %s [%s]\n", device->device_name, ip_str);
		return -1;
	}
}

// Disconnect the test device
static void _disconnect_device_(void)
{
	test_device_t *device = &g_test_ctx.device;
	
	if (!device->is_connected || !device->device_handle) { return; }
	
	while (MdScanner_Close(device->device_handle)) { clike_delay_ms(10); }
	
	device->is_connected = 0;
	device->device_handle = 0;
	
	char ip_str[16];
	_ip_to_str_(device->ip_address, ip_str, sizeof(ip_str));
	clike_print("\tdisconnected device %s [%s]\n", device->device_name, ip_str);
}

// Execute reboot and wait for disconnect
static int _execute_reboot_test_(void)
{
	test_device_t *device = &g_test_ctx.device;
	char ip_str[16];
	_ip_to_str_(device->ip_address, ip_str, sizeof(ip_str));
	
	if (!device->is_connected || !device->device_handle)
	{
		clike_print("\tdevice not connected, cannot test reboot\n");
		return -1;
	}
	
	g_test_ctx.total_reboot_attempts++;
	
	clike_print("\t\tinitiating reboot for device %s [%s]\n", device->device_name, ip_str);
	
	// Send reboot command using adapter and scanner info
	int ret = MdScanner_Reboot(&device->adapter_info, &device->scanner_info);
	if (0 == ret)
	{
		clike_print("\t\t\treboot command sent successfully\n");
		device->reboot_initiated = 1;
	}
	else
	{
		clike_print("\t\t\treboot command failed: %d\n", ret);
		return -1;
	}
	
	// Wait for disconnect callback
	clike_print("\t\twaiting for disconnect callback after reboot\n");
	MdSynchronizer_Lock(g_test_ctx.sync);
	MdSynchronizer_Wait(g_test_ctx.sync, DISCONNECT_TIMEOUT_MS);
	MdSynchronizer_Unlock(g_test_ctx.sync);
	
	if (device->disconnect_callback_received)
	{
		clike_print("\t\tdisconnect callback received successfully\n");
		return 0;
	}
	else
	{
		clike_print("\t\tdisconnect callback not received within timeout\n");
		return -1;
	}
}

// Execute complete test: connect -> reboot -> verify disconnect callback
static int _execute_complete_test_(void)
{
	clike_print("step 2: executing reboot disconnect test\n");
	
	// Connect to device
	if (0 != _connect_device_())
	{
		clike_print("\tfailed to connect to test device\n");
		return -1;
	}
	
	// Wait a moment for connection to stabilize
	clike_delay_ms(1000);
	
	// Execute reboot test
	int ret = _execute_reboot_test_();
	
	// Note: Device should already be disconnected due to reboot
	// But attempt cleanup just in case
	if (g_test_ctx.device.is_connected)
	{
		_disconnect_device_();
	}
	
	return ret;
}

// Print detailed statistics
static void _print_test_statistics_(void)
{
	clike_print("detailed statistics:\n");
	clike_print("\tconnection attempts:       %d\n", g_test_ctx.total_connect_attempts);
	clike_print("\tconnection success:        %d\n", g_test_ctx.total_connect_success);
	clike_print("\treboot attempts:           %d\n", g_test_ctx.total_reboot_attempts);
	clike_print("\tdisconnect callbacks:      %d\n", g_test_ctx.total_disconnect_callbacks);
	
	// Calculate success rates
	int connect_rate = (g_test_ctx.total_connect_attempts > 0) 
		? (g_test_ctx.total_connect_success * 100 / g_test_ctx.total_connect_attempts) : 0;
	int disconnect_rate = (g_test_ctx.total_reboot_attempts > 0) 
		? (g_test_ctx.total_disconnect_callbacks * 100 / g_test_ctx.total_reboot_attempts) : 0;
	
	clike_print("\tsuccess rates:\n");
	clike_print("\t\tconnection:        %d%%\n", connect_rate);
	clike_print("\t\tdisconnect after reboot: %d%%\n", disconnect_rate);
}

// Print final test results
static void _print_final_results_(void)
{
	int device_found = (g_test_ctx.device.ip_address != 0);
	int connection_success = (g_test_ctx.total_connect_success > 0);
	int reboot_executed = (g_test_ctx.total_reboot_attempts > 0);
	int disconnect_callback_success = (g_test_ctx.total_disconnect_callbacks > 0);
	int overall_success = device_found && connection_success && reboot_executed && disconnect_callback_success;
	
	clike_print("test result:\n");
	clike_print("\tdevice found           %s\n", device_found ? "pass" : "fail");
	clike_print("\tconnection             %s\n", connection_success ? "pass" : "fail");
	clike_print("\treboot executed        %s\n", reboot_executed ? "pass" : "fail");
	clike_print("\tdisconnect callback    %s (%d received)\n", 
		disconnect_callback_success ? "pass" : "fail", g_test_ctx.total_disconnect_callbacks);
	clike_print("\toverall result         %s\n", overall_success ? "PASS" : "FAIL");
	
	g_test_ctx.overall_result = overall_success ? 0 : -1;
}

CMD int test_reboot_disconnect(void) /* device reboot disconnect callback test */
{
	int ret = -1;
	
	clike_print("starting device reboot disconnect callback test\n");
	
	// Initialize test context
	_init_test_context_();
	clike_assert(!g_test_ctx.sync, goto label_cleanup);
	
	// Initialize scanner
	ret = MdScanner_Init(".", 1);
	clike_assert(0 > ret, goto label_cleanup);
	
	// Discover test device
	ret = _discover_device_();
	clike_assert(0 > ret, goto label_free_scanner);
	
	// Execute reboot disconnect test
	ret = _execute_complete_test_();
	
	// Print statistics and results
	_print_test_statistics_();
	_print_final_results_();
	
	ret = g_test_ctx.overall_result;

label_free_scanner:
	MdScanner_Free();

label_cleanup:
	_cleanup_test_context_();
	
	return ret;
}
