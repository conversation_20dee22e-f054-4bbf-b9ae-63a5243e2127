//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: main.c
// Author		: XuCF
// Created On	: 2025/04/28
// Description	: main.c
//
// History
// 1. V1.0, Created by XuCF. 2025/04/28
//=============================================================================

#include <stdio.h>
#include <stdlib.h>
#include <dlfcn.h>
#include <string.h>
#include <limits.h>
#include <unistd.h>

int main(int argc, char *argv[])
{
    void *handle;
    void (*function)(void);
    char *error;
    char absolute_path[PATH_MAX];

    // Check command line arguments
    if (argc != 3) {
        fprintf(stderr, "Usage: %s <so_file_path> <function_name>\n", argv[0]);
        return 1;
    }

    // Convert to absolute path if needed
    if (argv[1][0] != '/') {
        // Not an absolute path, convert it
        if (realpath(argv[1], absolute_path) == NULL) {
            fprintf(stderr, "Failed to get absolute path for: %s\n", argv[1]);
            return 1;
        }
    } else {
        // Already an absolute path
        strncpy(absolute_path, argv[1], PATH_MAX - 1);
        absolute_path[PATH_MAX - 1] = '\0';  // Ensure null termination
    }

    printf("Loading shared library: %s\n", absolute_path);

    // Open shared library with absolute path
    handle = dlopen(absolute_path, RTLD_LAZY);
    if (!handle) {
        fprintf(stderr, "Failed to open so file: %s\n", dlerror());
        return 1;
    }

    // Clear existing errors
    dlerror();

    // Get function pointer
    *(void **)(&function) = dlsym(handle, argv[2]);
    if ((error = dlerror()) != NULL) {
        fprintf(stderr, "Failed to get function: %s\n", error);
        dlclose(handle);
        return 1;
    }

    // Execute function
    printf("Executing function '%s' from '%s'\n", argv[2], absolute_path);
    (*function)();

    // Close shared library
    dlclose(handle);
    printf("Execution completed\n");

    return 0;
}
