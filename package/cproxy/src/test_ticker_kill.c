//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: test_ticker_kill.c
// Author		: XuCF
// Created On	: 2025/04/09
// Description	: test_ticker_kill.c
//
// History
// 1. V1.0, Created by XuCF. 2025/04/09
//=============================================================================

#include "MdRunner.h"
#include "autocmd.h"

typedef struct
{
	MdTicker *ticker;
	int ms;
	int start;
	int count;
} _ticker_demo_t;

static any _ticker_route_(any obj)
{
	_ticker_demo_t *thiz = (_ticker_demo_t *)obj;
	thiz->count++;
	int error = clike_time_ms() - thiz->start - thiz->ms * thiz->count;
	clike_print("\t\tticker-%p error: %dms\n", thiz->ticker, error);
	return 0;
}

CMD int test_ticker_kill(void) /* test ticker kill. */
{
	// once
	clike_print("ticker once\n");
	_ticker_demo_t ticker_once = {0};
	ticker_once.ms = 100;
	ticker_once.start = clike_time_ms();
	ticker_once.ticker = MdTicker_Boot(_ticker_route_, (any)&ticker_once, 0, ticker_once.ms);
	clike_print("\tshould print ticker-%p once\n", ticker_once.ticker);
	clike_delay_ms(150);
	clike_print("\tshould not print any ticker\n");
	clike_delay_ms(500);

	// cycle with RE flag - must use MdTicker_Kill() with loop
	clike_print("test: ticker cycle with RE flag\n");
	_ticker_demo_t ticker_cycle = {0};
	ticker_cycle.ms = 100;
	ticker_cycle.start = clike_time_ms();
	ticker_cycle.ticker =
		MdTicker_Boot(_ticker_route_, (any)&ticker_cycle, MD_TICKER_FLAG_CY | MD_TICKER_FLAG_RE, ticker_cycle.ms);
	clike_print("\tshould print ticker-%p twice\n", ticker_cycle.ticker);
	clike_delay_ms(250);
	while (!MdTicker_Kill(ticker_cycle.ticker)) clike_delay_ms(10);
	clike_print("\tshould not print any ticker\n");
	clike_delay_ms(500);

	// cycle without RE flag - auto destroy, check kill result
	clike_print("test: ticker cycle without RE flag\n");
	_ticker_demo_t ticker_auto_destroy[2] = {0};
	ticker_auto_destroy[0].ms = 100;
	ticker_auto_destroy[0].start = clike_time_ms();
	ticker_auto_destroy[0].ticker =
		MdTicker_Boot(_ticker_route_, (any)&ticker_auto_destroy[0], MD_TICKER_FLAG_CY, ticker_auto_destroy[0].ms);
	clike_print("\tshould print ticker-%p multiple times\n", ticker_auto_destroy[0].ticker);
	clike_delay_ms(150);
	ticker_auto_destroy[1].ms = 100;
	ticker_auto_destroy[1].start = clike_time_ms();
	ticker_auto_destroy[1].ticker =
		MdTicker_Boot(_ticker_route_, (any)&ticker_auto_destroy[1], MD_TICKER_FLAG_CY, ticker_auto_destroy[1].ms);
	clike_delay_ms(50);
	clike_print("\tshould print both tickers\n");
	clike_delay_ms(150);
	
	// Kill first ticker and check result
	int kill_result = MdTicker_Kill(ticker_auto_destroy[0].ticker);
	if (kill_result) {
		clike_print("\tfirst ticker killed successfully\n");
	} else {
		clike_print("\tfirst ticker is executing, wait for completion\n");
		int old_count = ticker_auto_destroy[0].count;
		// Wait for execution to complete by monitoring count change
		while (ticker_auto_destroy[0].count == old_count) {
			clike_delay_ms(10);
		}
		clike_print("\tfirst ticker execution completed\n");
	}
	clike_print("\tshould print only ticker-%p\n", ticker_auto_destroy[1].ticker);
	clike_delay_ms(150);
	
	// Kill second ticker and check result
	kill_result = MdTicker_Kill(ticker_auto_destroy[1].ticker);
	if (kill_result) {
		clike_print("\tsecond ticker killed successfully\n");
	} else {
		clike_print("\tsecond ticker is executing, wait for completion\n");
		int old_count = ticker_auto_destroy[1].count;
		// Wait for execution to complete by monitoring count change
		while (ticker_auto_destroy[1].count == old_count) {
			clike_delay_ms(10);
		}
		clike_print("\tsecond ticker execution completed\n");
	}
	clike_print("\tshould not print any ticker\n");
	clike_delay_ms(500);

	// kill by route - not recommended but needs testing
	clike_print("test: kill by route (not recommended)\n");
	_ticker_demo_t ticker_kill_by_route[2] = {0};
	ticker_kill_by_route[0].ms = 100;
	ticker_kill_by_route[0].start = clike_time_ms();
	ticker_kill_by_route[0].ticker =
		MdTicker_Boot(_ticker_route_, (any)&ticker_kill_by_route[0], MD_TICKER_FLAG_CY, ticker_kill_by_route[0].ms);
	clike_print("\tshould print ticker-%p multiple times\n", ticker_kill_by_route[0].ticker);
	clike_delay_ms(150);
	ticker_kill_by_route[1].ms = 100;
	ticker_kill_by_route[1].start = clike_time_ms();
	ticker_kill_by_route[1].ticker =
		MdTicker_Boot(_ticker_route_, (any)&ticker_kill_by_route[1], MD_TICKER_FLAG_CY, ticker_kill_by_route[1].ms);
	clike_delay_ms(50);
	clike_print("\tshould print both tickers\n");
	clike_delay_ms(150);
	// KillByRoute may kill multiple tickers with same route
	MdTicker_KillByRoute(_ticker_route_);
	clike_print("\tshould not print any ticker (both killed by route)\n");
	clike_delay_ms(500);

	// multiple tickers with RE flag
	clike_print("test: multiple tickers with RE flag\n");
	_ticker_demo_t ticker_multiple[2] = {0};
	ticker_multiple[0].ms = 100;
	ticker_multiple[0].start = clike_time_ms();
	ticker_multiple[0].ticker =
		MdTicker_Boot(_ticker_route_, (any)&ticker_multiple[0], MD_TICKER_FLAG_CY | MD_TICKER_FLAG_RE, ticker_multiple[0].ms);
	clike_print("\tshould print ticker-%p multiple times\n", ticker_multiple[0].ticker);
	clike_delay_ms(150);
	ticker_multiple[1].ms = 100;
	ticker_multiple[1].start = clike_time_ms();
	ticker_multiple[1].ticker =
		MdTicker_Boot(_ticker_route_, (any)&ticker_multiple[1], MD_TICKER_FLAG_CY | MD_TICKER_FLAG_RE, ticker_multiple[1].ms);
	clike_delay_ms(50);
	clike_print("\tshould print both tickers\n");
	clike_delay_ms(150);
	while (!MdTicker_Kill(ticker_multiple[0].ticker)) clike_delay_ms(10);
	clike_print("\tshould print only ticker-%p\n", ticker_multiple[1].ticker);
	clike_delay_ms(150);
	while (!MdTicker_Kill(ticker_multiple[1].ticker)) clike_delay_ms(10);
	clike_print("\tshould not print any ticker\n");
	clike_delay_ms(500);

	clike_print("test result:\n");
	clike_print("\tticker once           %s\n", ticker_once.count == 1 ? "pass" : "fail");
	clike_print("\tticker cycle RE       %s\n", ticker_cycle.count >= 2 ? "pass" : "fail");
	clike_print("\tticker cycle no RE    %s\n",
		ticker_auto_destroy[0].count >= 1 && ticker_auto_destroy[1].count >= 1 ? "pass" : "fail");
	clike_print("\tticker kill by route  %s\n",
		ticker_kill_by_route[0].count >= 1 && ticker_kill_by_route[1].count >= 1 ? "pass" : "fail");
	clike_print("\tticker multiple RE    %s\n",
		ticker_multiple[0].count >= 1 && ticker_multiple[1].count >= 1 ? "pass" : "fail");
	return 0;
}
