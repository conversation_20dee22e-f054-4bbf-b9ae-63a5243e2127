// Auto generated by autocmd.py, don't edit
#include "autocmd.h"
code_t demo_pcre(text_t text, text_t pattern);
code_t demo_simple_notice(void);
code_t demo_task_loop(void);
code_t demo_thread_dump(void);
code_t test_cblock_advanced(void);
code_t test_cblock_basic(void);
code_t test_events(void);
code_t test_notice_auto_detach(void);
code_t test_notice_basic(void);
code_t test_notice_error_handling(void);
code_t test_notice_external_detach(void);
code_t test_recycler_advanced(void);
code_t test_recycler_basic(void);
code_t test_recycler_stress(void);
code_t test_task(void);
code_t test_thread_detach(void);
code_t test_ticker_kill(void);
code_t test_waiter(void);
static code_t _demo_pcre_cwrp_(void)
{
	code_t ret;
	text_t text = cmdparser_get_txt("text", "This is a test string.");
	text_t pattern = cmdparser_get_txt("pattern", "(\\w+\\s+\\w+)\\s+(\\w+\\s+\\w+)\\s+(\\w+)");
	ret = demo_pcre(text, pattern);
	return ret;
}
static code_t _demo_simple_notice_cwrp_(void)
{
	code_t ret;
	ret = demo_simple_notice();
	return ret;
}
static code_t _demo_task_loop_cwrp_(void)
{
	code_t ret;
	ret = demo_task_loop();
	return ret;
}
static code_t _demo_thread_dump_cwrp_(void)
{
	code_t ret;
	ret = demo_thread_dump();
	return ret;
}
static code_t _test_cblock_advanced_cwrp_(void)
{
	code_t ret;
	ret = test_cblock_advanced();
	return ret;
}
static code_t _test_cblock_basic_cwrp_(void)
{
	code_t ret;
	ret = test_cblock_basic();
	return ret;
}
static code_t _test_events_cwrp_(void)
{
	code_t ret;
	ret = test_events();
	return ret;
}
static code_t _test_notice_auto_detach_cwrp_(void)
{
	code_t ret;
	ret = test_notice_auto_detach();
	return ret;
}
static code_t _test_notice_basic_cwrp_(void)
{
	code_t ret;
	ret = test_notice_basic();
	return ret;
}
static code_t _test_notice_error_handling_cwrp_(void)
{
	code_t ret;
	ret = test_notice_error_handling();
	return ret;
}
static code_t _test_notice_external_detach_cwrp_(void)
{
	code_t ret;
	ret = test_notice_external_detach();
	return ret;
}
static code_t _test_recycler_advanced_cwrp_(void)
{
	code_t ret;
	ret = test_recycler_advanced();
	return ret;
}
static code_t _test_recycler_basic_cwrp_(void)
{
	code_t ret;
	ret = test_recycler_basic();
	return ret;
}
static code_t _test_recycler_stress_cwrp_(void)
{
	code_t ret;
	ret = test_recycler_stress();
	return ret;
}
static code_t _test_task_cwrp_(void)
{
	code_t ret;
	ret = test_task();
	return ret;
}
static code_t _test_thread_detach_cwrp_(void)
{
	code_t ret;
	ret = test_thread_detach();
	return ret;
}
static code_t _test_ticker_kill_cwrp_(void)
{
	code_t ret;
	ret = test_ticker_kill();
	return ret;
}
static code_t _test_waiter_cwrp_(void)
{
	code_t ret;
	ret = test_waiter();
	return ret;
}
static argv_t _demo_pcre_args_[] = {
	{"text", "text", "This is a test string."},
	{"pattern", "pattern", "(\\w+\\s+\\w+)\\s+(\\w+\\s+\\w+)\\s+(\\w+)"},
};
static func_t _demo_pcre_func_ = {
	// command data >>
	.cwrp = _demo_pcre_cwrp_,
	.name = "demo_pcre",
	.desc = "pcre.",
	.args = _demo_pcre_args_,
	.argn = 2
	// command data <<
};
static argv_t _demo_simple_notice_args_[] = {
};
static func_t _demo_simple_notice_func_ = {
	// command data >>
	.cwrp = _demo_simple_notice_cwrp_,
	.name = "demo_simple_notice",
	.desc = "simple notice demo.",
	.args = _demo_simple_notice_args_,
	.argn = 0
	// command data <<
};
static argv_t _demo_task_loop_args_[] = {
};
static func_t _demo_task_loop_func_ = {
	// command data >>
	.cwrp = _demo_task_loop_cwrp_,
	.name = "demo_task_loop",
	.desc = "auto task loop.",
	.args = _demo_task_loop_args_,
	.argn = 0
	// command data <<
};
static argv_t _demo_thread_dump_args_[] = {
};
static func_t _demo_thread_dump_func_ = {
	// command data >>
	.cwrp = _demo_thread_dump_cwrp_,
	.name = "demo_thread_dump",
	.desc = "dump cpu rate.",
	.args = _demo_thread_dump_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_cblock_advanced_args_[] = {
};
static func_t _test_cblock_advanced_func_ = {
	// command data >>
	.cwrp = _test_cblock_advanced_cwrp_,
	.name = "test_cblock_advanced",
	.desc = "test cblock with init/free functions",
	.args = _test_cblock_advanced_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_cblock_basic_args_[] = {
};
static func_t _test_cblock_basic_func_ = {
	// command data >>
	.cwrp = _test_cblock_basic_cwrp_,
	.name = "test_cblock_basic",
	.desc = "test cblock basic functionality",
	.args = _test_cblock_basic_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_events_args_[] = {
};
static func_t _test_events_func_ = {
	// command data >>
	.cwrp = _test_events_cwrp_,
	.name = "test_events",
	.desc = "test events",
	.args = _test_events_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_notice_auto_detach_args_[] = {
};
static func_t _test_notice_auto_detach_func_ = {
	// command data >>
	.cwrp = _test_notice_auto_detach_cwrp_,
	.name = "test_notice_auto_detach",
	.desc = "test MD_NOTICE_FLAG_DT one-time detach behavior",
	.args = _test_notice_auto_detach_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_notice_basic_args_[] = {
};
static func_t _test_notice_basic_func_ = {
	// command data >>
	.cwrp = _test_notice_basic_cwrp_,
	.name = "test_notice_basic",
	.desc = "test basic Notice functionality",
	.args = _test_notice_basic_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_notice_error_handling_args_[] = {
};
static func_t _test_notice_error_handling_func_ = {
	// command data >>
	.cwrp = _test_notice_error_handling_cwrp_,
	.name = "test_notice_error_handling",
	.desc = "test Notice error handling",
	.args = _test_notice_error_handling_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_notice_external_detach_args_[] = {
};
static func_t _test_notice_external_detach_func_ = {
	// command data >>
	.cwrp = _test_notice_external_detach_cwrp_,
	.name = "test_notice_external_detach",
	.desc = "test external thread detach loop",
	.args = _test_notice_external_detach_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_recycler_advanced_args_[] = {
};
static func_t _test_recycler_advanced_func_ = {
	// command data >>
	.cwrp = _test_recycler_advanced_cwrp_,
	.name = "test_recycler_advanced",
	.desc = "test MdRecycler with init/free functions",
	.args = _test_recycler_advanced_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_recycler_basic_args_[] = {
};
static func_t _test_recycler_basic_func_ = {
	// command data >>
	.cwrp = _test_recycler_basic_cwrp_,
	.name = "test_recycler_basic",
	.desc = "test MdRecycler basic functionality",
	.args = _test_recycler_basic_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_recycler_stress_args_[] = {
};
static func_t _test_recycler_stress_func_ = {
	// command data >>
	.cwrp = _test_recycler_stress_cwrp_,
	.name = "test_recycler_stress",
	.desc = "test MdRecycler thread safety",
	.args = _test_recycler_stress_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_task_args_[] = {
};
static func_t _test_task_func_ = {
	// command data >>
	.cwrp = _test_task_cwrp_,
	.name = "test_task",
	.desc = "test task module.",
	.args = _test_task_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_thread_detach_args_[] = {
};
static func_t _test_thread_detach_func_ = {
	// command data >>
	.cwrp = _test_thread_detach_cwrp_,
	.name = "test_thread_detach",
	.desc = "test thread detach",
	.args = _test_thread_detach_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_ticker_kill_args_[] = {
};
static func_t _test_ticker_kill_func_ = {
	// command data >>
	.cwrp = _test_ticker_kill_cwrp_,
	.name = "test_ticker_kill",
	.desc = "test ticker kill.",
	.args = _test_ticker_kill_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_waiter_args_[] = {
};
static func_t _test_waiter_func_ = {
	// command data >>
	.cwrp = _test_waiter_cwrp_,
	.name = "test_waiter",
	.desc = "test waiter",
	.args = _test_waiter_args_,
	.argn = 0
	// command data <<
};
func_t *_funcs_tb_[] = {
	&_demo_pcre_func_,
	&_demo_simple_notice_func_,
	&_demo_task_loop_func_,
	&_demo_thread_dump_func_,
	&_test_cblock_advanced_func_,
	&_test_cblock_basic_func_,
	&_test_events_func_,
	&_test_notice_auto_detach_func_,
	&_test_notice_basic_func_,
	&_test_notice_error_handling_func_,
	&_test_notice_external_detach_func_,
	&_test_recycler_advanced_func_,
	&_test_recycler_basic_func_,
	&_test_recycler_stress_func_,
	&_test_task_func_,
	&_test_thread_detach_func_,
	&_test_ticker_kill_func_,
	&_test_waiter_func_,
	0,
};
