//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: demo_task_loop.c
// Author		: XuCF
// Created On	: 2024/11/12
// Description	: demo_task_loop.c
//
// History
// 1. V1.0, Created by XuCF. 2024/11/12
//=============================================================================

#include "MdRunner.h"
#include "MdNotice.h"
#include "autocmd.h"

void _task_route_(unv arg1, unv arg2)
{
	clike_print("arg1=%zd, arg2=%zd, task=%p\n", arg1, arg2, MdTask_Find(_task_route_));
}

void _add_demo_task_(unv arg1, unv arg2, int prio)
{
	MdTask_Boot(_task_route_, arg1, arg2, prio, "demo");
}

static MdNoticeUser *user = 0;

int _task_notice_(void)
{
	MdTask_Dump(clike_print);
	MdNotice_DetachUser(user);
	user = 0;
	return 0;
}

CMD int demo_task_loop(void) /* auto task loop. */
{
	// init task
	MdNotice_Init(8);
	MdTask_Init(3, 64);
	user = MdNotice_AttachUser(MdNotice_FindNode(MdNoticeId_Task), _task_notice_, 0, 0);
	// add tasks
	for (int i = 1; i < 4; i++) _add_demo_task_(i, i + 1, 0);
	for (int i = 1; i < 4; i++) _add_demo_task_(i, i + 1, 1);
	for (int i = 1; i < 4; i++) _add_demo_task_(i, i + 1, 2);
	// show task list
	MdTask_Dump(clike_print);
	// task loop
	while (MdTask_Loop()) continue;
	return 0;
}
