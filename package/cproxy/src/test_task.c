//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: test_task.c
// Author		: XuCF
// Created On	: 2025/04/09
// Description	: test_task.c
//
// History
// 1. V1.0, Created by XuCF. 2025/04/09
//=============================================================================

#include "MdRunner.h"
#include "autocmd.h"

// Forward declarations for MdTaskServer (no header file)
any MdTaskServer_New(int count);
non MdTaskServer_Del(any obj);

typedef struct
{
	int count;
	unv arg1;
	unv arg2;
	str name;
	int executed;
} _task_demo_t;

static void _task_route_a_(unv arg1, unv arg2)
{
	_task_demo_t *thiz = (_task_demo_t *)arg1;
	thiz->count++;
	thiz->arg1 = arg1;
	thiz->arg2 = arg2;
	thiz->executed = 1;
	clike_print(
		"\t\ttask-%s executed: count=%d, arg1=%p, arg2=%p\n", thiz->name, thiz->count, (void *)arg1, (void *)arg2);
}

static void _task_route_b_(unv arg1, unv arg2)
{
	_task_demo_t *thiz = (_task_demo_t *)arg1;
	thiz->count++;
	thiz->arg1 = arg1;
	thiz->arg2 = arg2;
	thiz->executed = 1;
	clike_print(
		"\t\ttask-%s executed: count=%d, arg1=%p, arg2=%p\n", thiz->name, thiz->count, (void *)arg1, (void *)arg2);
}

static void _task_route_c_(unv arg1, unv arg2)
{
	_task_demo_t *thiz = (_task_demo_t *)arg1;
	thiz->count++;
	thiz->arg1 = arg1;
	thiz->arg2 = arg2;
	thiz->executed = 1;
	clike_print(
		"\t\ttask-%s executed: count=%d, arg1=%p, arg2=%p\n", thiz->name, thiz->count, (void *)arg1, (void *)arg2);
}

static void _slow_task_route_(unv arg1, unv arg2)
{
	_task_demo_t *thiz = (_task_demo_t *)arg1;
	thiz->count++;
	thiz->arg1 = arg1;
	thiz->arg2 = arg2;
	thiz->executed = 1;
	// Simulate slow task to keep tasks in queue
	clike_delay_ms(50);
	clike_print(
		"\t\tslow-task-%s executed: count=%d, arg1=%p, arg2=%p\n", thiz->name, thiz->count, (void *)arg1, (void *)arg2);
}

static void _non_existent_route_(unv arg1, unv arg2)
{
	(void)arg1;
	(void)arg2;
}

CMD int test_task(void) /* test task module. */
{
	any task_server = 0;

	// Create task server with small initial capacity to test auto-expansion
	clike_print("test: create task server with small capacity\n");
	task_server = MdTaskServer_New(3); // Small initial capacity: 3 task slots
	clike_assert(!task_server, goto cleanup);
	clike_print("\ttask server created with 3 initial slots\n");

	// Give the server thread time to start
	clike_delay_ms(50);

	// Test basic task boot and execution
	clike_print("test: basic task boot and execution\n");
	_task_demo_t task_basic = {0};
	task_basic.name = "basic";
	MdTask *basic_task = MdTask_Boot(_task_route_a_, (unv)&task_basic, 12345, 0, "basic_task");
	clike_assert(!basic_task, goto cleanup);
	clike_print("\ttask booted: %p\n", basic_task);

	// Wait for task execution
	clike_delay_ms(100);
	clike_print("\ttask executed count: %d (should be 1)\n", task_basic.count);

	// Test auto-expansion mechanism
	clike_print("test: auto-expansion mechanism\n");
	clike_print("\tcreating more tasks than initial capacity (3) to trigger expansion\n");

	// Dump initial state
	clike_print("\tinitial memory pool state:\n");
	MdTask_Dump(clike_print);

	// Create many slow tasks to fill up the initial capacity and trigger expansion
	_task_demo_t expand_tasks[8] = {0};
	MdTask *task_ptrs[8];

	for (int i = 0; i < 8; i++)
	{
		expand_tasks[i].name = "expand";
		task_ptrs[i] = MdTask_Boot(_slow_task_route_, (unv)&expand_tasks[i], 1000 + i, 1, "expand");
		clike_assert(!task_ptrs[i], goto cleanup);
		clike_print("\t\texpand task %d booted: %p\n", i, task_ptrs[i]);

		// Small delay to allow some tasks to be processed
		clike_delay_ms(10);
	}

	clike_print("\tcreated 8 tasks (more than initial capacity of 3)\n");

	// Dump state after creating many tasks to show expansion
	clike_print("\tmemory pool state after creating 8 tasks (should show expansion):\n");
	MdTask_Dump(clike_print);

	// Wait for all slow tasks to complete
	clike_print("\twaiting for all expand tasks to complete...\n");
	clike_delay_ms(1000); // Wait for slow tasks to finish

	// Check execution results
	int total_executed = 0;
	for (int i = 0; i < 8; i++)
	{
		total_executed += expand_tasks[i].count;
		clike_print("\t\texpand task %d executed: %d times\n", i, expand_tasks[i].count);
	}
	clike_print("\ttotal expand tasks executed: %d (should be 8)\n", total_executed);

	// Dump final state to show memory pool statistics
	clike_print("\tfinal memory pool state after expansion test:\n");
	MdTask_Dump(clike_print);

	// Test priority-based execution (higher priority first)
	clike_print("test: priority-based execution\n");
	_task_demo_t task_low = {0}, task_high = {0};
	task_low.name = "low";
	task_high.name = "high";

	// Boot low priority task first (priority 2)
	MdTask *low_task = MdTask_Boot(_task_route_b_, (unv)&task_low, 100, 2, "low_priority");
	clike_assert(!low_task, goto cleanup);
	clike_print("\tlow priority task booted: %p\n", low_task);

	// Boot high priority task (priority 0)
	MdTask *high_task = MdTask_Boot(_task_route_c_, (unv)&task_high, 200, 0, "high_priority");
	clike_assert(!high_task, goto cleanup);
	clike_print("\thigh priority task booted: %p\n", high_task);

	// Wait for execution - high priority should execute first
	clike_print("\twaiting for task execution (high priority should execute first)\n");
	clike_delay_ms(100);
	clike_print("\thigh task count: %d, low task count: %d\n", task_high.count, task_low.count);

	// Test no-duplicate flag (MD_TASK_FLAG_ND)
	clike_print("test: no-duplicate flag\n");
	_task_demo_t task_dup = {0};
	task_dup.name = "dup";

	// Boot first task without ND flag
	MdTask *dup_task1 = MdTask_Boot(_task_route_a_, (unv)&task_dup, 300, 1, "dup_task1");
	clike_assert(!dup_task1, goto cleanup);
	clike_print("\tfirst duplicate task booted: %p\n", dup_task1);

	// Boot second task with same route without ND flag - should succeed
	MdTask *dup_task2 = MdTask_Boot(_task_route_a_, (unv)&task_dup, 400, 1, "dup_task2");
	clike_assert(!dup_task2, goto cleanup);
	clike_print("\tsecond duplicate task booted: %p\n", dup_task2);

	// Wait for both tasks to execute
	clike_delay_ms(100);
	clike_print("\tboth tasks executed, count: %d (should be 2)\n", task_dup.count);

	// Boot third task with ND flag - should find existing or not create duplicate
	MdTask *dup_task3 = MdTask_Boot(_task_route_a_, (unv)&task_dup, 500, MD_TASK_FLAG_ND | 1, "dup_task3");
	clike_print("\tthird task with ND flag: %p\n", dup_task3);

	// Wait a bit more
	clike_delay_ms(100);
	clike_print("\tfinal dup task count: %d\n", task_dup.count);

	// Test task find functionality
	clike_print("test: task find functionality\n");
	_task_demo_t task_find = {0};
	task_find.name = "find";
	MdTask *find_task = MdTask_Boot(_task_route_b_, (unv)&task_find, 600, 1, "find_task");
	clike_assert(!find_task, goto cleanup);

	MdTask *found_task = MdTask_Find(_task_route_b_);
	clike_print("\tfind task result: %p (should match %p or be valid)\n", found_task, find_task);

	// Wait for task execution
	clike_delay_ms(100);
	clike_print("\tfound task executed, count: %d\n", task_find.count);

	// Test task kill functionality
	clike_print("test: task kill functionality\n");
	_task_demo_t task_kill = {0};
	task_kill.name = "kill";
	MdTask *kill_task = MdTask_Boot(_task_route_c_, (unv)&task_kill, 700, 1, "kill_task");
	clike_assert(!kill_task, goto cleanup);
	clike_print("\ttask to kill booted: %p\n", kill_task);

	// Kill the task immediately before it can execute
	int kill_count = MdTask_Kill(kill_task);
	clike_print("\tkill result: %d (should be 1)\n", kill_count);

	// Wait and check - should not execute killed task
	clike_delay_ms(100);
	clike_print("\tkilled task count: %d (should be 0)\n", task_kill.count);

	// Test kill by route functionality
	clike_print("test: kill by route functionality\n");
	_task_demo_t task_route_kill[2] = {0};
	task_route_kill[0].name = "route_kill_1";
	task_route_kill[1].name = "route_kill_2";

	// Boot multiple tasks with same route
	MdTask *route_task1 = MdTask_Boot(_task_route_a_, (unv)&task_route_kill[0], 800, 1, "route_task1");
	MdTask *route_task2 = MdTask_Boot(_task_route_a_, (unv)&task_route_kill[1], 900, 1, "route_task2");
	clike_assert(!route_task1, goto cleanup);
	clike_assert(!route_task2, goto cleanup);
	clike_print("\ttwo tasks with same route booted: %p, %p\n", route_task1, route_task2);

	// Kill by route immediately
	int route_kill_count = MdTask_KillByRoute(_task_route_a_);
	clike_print("\tkill by route result: %d (should be 2)\n", route_kill_count);

	// Wait and check - should not execute any killed tasks
	clike_delay_ms(100);
	clike_print(
		"\troute killed task counts: %d, %d (both should be 0)\n", task_route_kill[0].count, task_route_kill[1].count);

	// Test multiple priority levels
	clike_print("test: multiple priority levels\n");
	_task_demo_t task_multi[3] = {0};
	task_multi[0].name = "prio0";
	task_multi[1].name = "prio1";
	task_multi[2].name = "prio2";

	// Boot tasks with different priorities (reverse order)
	MdTask_Boot(_task_route_a_, (unv)&task_multi[2], 1000, 2, "prio2_task");
	MdTask_Boot(_task_route_b_, (unv)&task_multi[1], 1100, 1, "prio1_task");
	MdTask_Boot(_task_route_c_, (unv)&task_multi[0], 1200, 0, "prio0_task");

	clike_print("\tshould execute in priority order: 0, 1, 2\n");
	// Wait for all tasks to execute
	clike_delay_ms(200);

	// Check execution results
	clike_print("\texecution verification:\n");
	for (int i = 0; i < 3; i++) { clike_print("\t\tprio%d count: %d\n", i, task_multi[i].count); }

	// Test stress scenario for expansion
	clike_print("test: stress scenario for expansion\n");
	clike_print("\tcreating many tasks rapidly to stress-test expansion\n");

	_task_demo_t stress_tasks[15] = {0};
	int stress_created = 0;

	for (int i = 0; i < 15; i++)
	{
		stress_tasks[i].name = "stress";
		MdTask *stress_task = MdTask_Boot(_task_route_a_, (unv)&stress_tasks[i], 2000 + i, 1, "stress");
		if (stress_task)
		{
			stress_created++;
			clike_print("\t\tstress task %d created\n", i);
		}
		else { clike_print("\t\tstress task %d failed to create\n", i); }
	}

	clike_print("\tstress test: created %d out of 15 tasks\n", stress_created);

	// Wait for stress tasks to complete
	clike_delay_ms(300);

	// Check stress results
	int stress_executed = 0;
	for (int i = 0; i < 15; i++) { stress_executed += stress_tasks[i].count; }
	clike_print("\tstress test: %d tasks executed\n", stress_executed);

	// Final memory pool state
	clike_print("\tfinal memory pool state after stress test:\n");
	MdTask_Dump(clike_print);

	// Test task dump functionality
	clike_print("test: task dump functionality\n");
	_task_demo_t task_dump = {0};
	task_dump.name = "dump";
	MdTask_Boot(_task_route_b_, (unv)&task_dump, 1400, 1, "dump_task");

	// Give it a moment, then dump before it executes
	clike_delay_ms(50);
	clike_print("\tdumping task information:\n");
	int dump_count = MdTask_Dump(clike_print);
	clike_print("\tdump returned count: %d\n", dump_count);

	// Wait for dump task to execute
	clike_delay_ms(100);

	// Test edge cases
	clike_print("test: edge cases\n");

	// Test invalid priority (should fail) - MdTaskServer uses 3 priorities (0,1,2)
	MdTask *invalid_task = MdTask_Boot(_task_route_a_, 0, 0, 5, "invalid_priority"); // priority 5 > max 2
	clike_print("\tinvalid priority task: %p (should be NULL)\n", invalid_task);

	// Test kill non-existent task - use a valid but fake pointer
	MdTask *fake_task_ptr = (MdTask *)0x12345678;
	int fake_kill_count = MdTask_Kill(fake_task_ptr);
	clike_print("\tkill non-existent task: %d (should be 0)\n", fake_kill_count);

	// Test find non-existent route
	MdTask *not_found = MdTask_Find(_non_existent_route_);
	clike_print("\tfind non-existent route: %p (should be NULL)\n", not_found);

	// Test results
	clike_print("test results:\n");
	clike_print("\tbasic execution       %s\n", task_basic.count >= 1 ? "pass" : "fail");
	clike_print("\tauto expansion        %s\n", total_executed >= 8 ? "pass" : "fail");
	clike_print("\tpriority execution    %s\n", (task_high.count >= 1 && task_low.count >= 1) ? "pass" : "fail");
	clike_print("\tno-duplicate flag     %s\n", task_dup.count >= 2 ? "pass" : "fail");
	clike_print("\ttask find             %s\n", (found_task && task_find.count >= 1) ? "pass" : "fail");
	clike_print("\ttask kill             %s\n", (kill_count == 1 && task_kill.count == 0) ? "pass" : "fail");
	clike_print("\tkill by route         %s\n",
		(route_kill_count >= 1 && task_route_kill[0].count == 0 && task_route_kill[1].count == 0) ? "pass" : "fail");
	clike_print("\tmultiple priorities   %s\n",
		(task_multi[0].count >= 1 && task_multi[1].count >= 1 && task_multi[2].count >= 1) ? "pass" : "fail");
	clike_print("\tstress expansion      %s\n", stress_executed >= 10 ? "pass" : "fail");
	clike_print(
		"\tedge cases            %s\n", (!invalid_task && fake_kill_count == 0 && !not_found) ? "pass" : "fail");

cleanup:
	// Clean up task server (this will stop thread and free task system)
	if (task_server)
	{
		clike_print("cleaning up task server\n");
		MdTaskServer_Del(task_server);
	}
	return 0;
}
