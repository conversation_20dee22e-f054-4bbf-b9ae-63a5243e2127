//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: test_waiter.c
// Author		: DongDL
// Created On	: 2025/04/09
// Description	: test_waiter.c
//
// History
// 1. V1.0, Created by DongDL. 2025/04/09
//=============================================================================

#include "MdRunner.h"
#include "autocmd.h"
#include "libcustom.h"

static any _sub_thread_(any obj)
{
	clike_print("\tthread enter\n");
	clike_delay_ms(1000);
	clike_print("\tthread exit\n");
	*((u32 *)obj) = 1;
	return 0;
}

CMD int test_thread_detach(void)	/* test thread detach*/
{
	u32 thread_exit = 0;

	clike_print("create sub thread\n");
	MdThread *thread = MdThread_Boot(_sub_thread_, &thread_exit, "sub thread");
	MdThread_Detach(thread);

	clike_delay_ms(2000);
	if (thread_exit) clike_print("sub thread operating normal, test succeed\n");
	else clike_print("sub thread abort, test failed\n");
	return 0;
}

struct waiter_mate
{
	MdWaiter *waiter;
	MdMutex *mutex;
};

static any _waiter_route_(any arg)
{
	struct waiter_mate *mate = (struct waiter_mate *)arg;
	clike_delay_ms(100);
	MdWaiter_Wake(mate->waiter);
	while(1)
	{
		u32 start = clike_time_ms();
		MdMutex_Lock(mate->mutex);
		int ret = MdWaiter_Wait(mate->waiter, mate->mutex, 5000);
		MdMutex_Unlock(mate->mutex);
		if (ret)
		{
			clike_print("\tWaiter timeout, sleep %u ms, ret = %d\n", clike_time_ms() - start, ret);
			break;
		}
		else
		{
			clike_print("\tWaiter wake up, sleep %u ms, ret = %d\n", clike_time_ms() - start, ret);
		}
	}
	return 0;
}

CMD int test_waiter(void)	/* test waiter */
{
	struct waiter_mate mate;
	mate.waiter = MdWaiter_Create();
	mate.mutex = MdMutex_Create();

	MdThread *thread = MdThread_Boot(_waiter_route_, (any)&mate, "waiter");
	clike_print("test1 : waike up thread in 1000ms\n");
	MdMutex_Lock(mate.mutex);
	MdWaiter_Wait(mate.waiter, mate.mutex, -1);
	MdMutex_Unlock(mate.mutex);

	clike_delay_ms(1000);
	MdWaiter_Wake(mate.waiter);
	clike_print("\twake up\n");

	clike_delay_ms(100);
	clike_print("test2 : wait waker timeout (5000ms)\n");
	MdThread_Wait(thread);
	MdMutex_Delete(mate.mutex);
	MdWaiter_Delete(mate.waiter);

	return 0;
}

struct event_mate
{
	MdEvent *event1;
	MdEvent *event2;
};

static any _event_route_1_(any arg)
{
	u32 ret = 0;
	struct event_mate *mate = (struct event_mate *)arg;
	ret = MdEvent_Get(mate->event1, 1, 0, -1);
	clike_print("\tThread1: get event %d\n", ret);
	ret = MdEvent_Get(mate->event1, 3, 0, -1);
	clike_print("\tThread1: get event %d\n", ret);
	ret = MdEvent_Get(mate->event1, 4, MD_EVENT_FLAG_NC, -1);
	clike_print("\tThread1: get event %d\n", ret);
	ret = MdEvent_Get(mate->event1, 8, 0, -1);
	clike_print("\tThread1: get event %d\n", ret);
	u32 start = clike_time_ms();
	ret = MdEvent_Get(mate->event1, 16, 0, 500);
	clike_print("\tThread1: get event %d, timeout = %u\n", ret, clike_time_ms() - start);
	return 0;
}

static any _event_route_2_(any arg)
{
	u32 ret = 0;
	struct event_mate *mate = (struct event_mate *)arg;
	ret = MdEvent_Get(mate->event1, 2, 0, -1);
	clike_print("\tThread2: get event %d\n", ret);
	ret = MdEvent_Get(mate->event1, 4, MD_EVENT_FLAG_NC, -1);
	clike_print("\tThread2: get event %d\n", ret);
	ret = MdEvent_Get(mate->event2, 8, 0, -1);
	clike_print("\tThread2: get event %d\n", ret);
	u32 start = clike_time_ms();
	ret = MdEvent_Get(mate->event1, 16, 0, 500);
	clike_print("\tThread2: get event %d, timeout = %u\n", ret, clike_time_ms() - start);
	return 0;
}

CMD int test_events(void)	/* test events */
{
	struct event_mate mate;
	mate.event1 = MdEvent_Create();
	mate.event2 = MdEvent_Create();

	MdThread *thread1 = MdThread_Boot(_event_route_1_, (any)&mate, "event1");
	MdThread *thread2 = MdThread_Boot(_event_route_2_, (any)&mate, "event2");

	clike_print("Events test\n");
	clike_delay_ms(100);
	clike_print("Event 1 should occur in thread1\n");
	MdEvent_Put(mate.event1, 1);
	clike_delay_ms(100);
	clike_print("Event 2 should occur in thread2\n");
	MdEvent_Put(mate.event1, 2);
	clike_delay_ms(100);
	clike_print("No Event should occur in both thread1 and thread2\n");
	MdEvent_Put(mate.event1, 1);
	clike_delay_ms(100);
	clike_print("Event 3 should occur in thread1\n");
	MdEvent_Put(mate.event1, 2);
	clike_delay_ms(100);
	clike_print("Event 4 should occur in both thread1 and thread2\n");
	MdEvent_Put(mate.event1, 4);
	clike_delay_ms(100);
	MdEvent_Clr(mate.event1, 4);
	clike_print("Event 8 should occur in thread1\n");
	MdEvent_Put(mate.event1, 8);
	clike_delay_ms(100);
	clike_print("Event 8 should occur in thread2\n");
	MdEvent_Put(mate.event2, 8);
	clike_delay_ms(100);
	MdEvent_Clr(mate.event1, ~0u);
	MdEvent_Clr(mate.event2, ~0u);
	clike_print("Event 0 should occur in both thread1 and thread2 after 500ms, because timeout\n");

	MdThread_Wait(thread1);
	MdThread_Wait(thread2);
	MdEvent_Delete(mate.event1);
	MdEvent_Delete(mate.event2);
	return 0;
}


