#!python
from typing import Callable
import struct
import socket
import threading
import time
import abc


class Cproxy(abc.ABC):
    """Abstract base class for the C-proxy functionality"""

    def __init__(self):
        self.callbacks = {}  # Dictionary to store callbacks, keyed by unique ID
        self.next_id = 0  # Auto-increment ID for generating unique identifiers
        self.callbacks_lock = threading.Lock()  # Lock to protect callbacks dictionary
        self.running = True
        # Start receiving thread
        self.receive_thread = threading.Thread(target=self._receive_loop)
        self.receive_thread.daemon = True
        self.receive_thread.start()

    def __enter__(self):
        """Supports 'with' statement"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Clean up resources when exiting 'with' block"""
        self.running = False
        self._close_connection()
        self.receive_thread.join(timeout=1.0)  # Wait for receive thread to end
        return False  # Do not suppress exceptions

    @abc.abstractmethod
    def _close_connection(self):
        """Close the underlying connection"""
        pass

    @abc.abstractmethod
    def _receive_data(self, buffer, buffer_size):
        """Try to receive data and return new buffer size
        
        Args:
            buffer: The buffer to store received data
            buffer_size: Current size of data in buffer
            
        Returns:
            New buffer size after receiving data, or -1 if connection closed
        """
        pass

    @abc.abstractmethod
    def _send_data(self, data, length):
        """Send data over the connection
        
        Args:
            data: The data to send
            length: The length of data to send
            
        Returns:
            Number of bytes sent
        """
        pass
    
    def _process_buffer(self, buffer, buffer_size):
        """Process data in buffer and return new buffer size"""
        processed_size = 0
        
        # Process complete data packets
        while processed_size < buffer_size and buffer_size - processed_size >= 16:
            # Parse packet length
            packet_len = struct.unpack_from('I', buffer, processed_size + 4)[0]
            
            # Check if we have a complete packet
            if packet_len > buffer_size - processed_size:
                break  # Incomplete packet, wait for more data
                
            # Extract complete packet
            packet = bytes(buffer[processed_size:processed_size + packet_len])
            
            # Process packet
            self._process_packet(packet)
            
            # Move past this packet
            processed_size += packet_len
            
        # If we processed anything, move remaining data to start of buffer
        if processed_size > 0:
            remaining = buffer_size - processed_size
            if remaining > 0:
                buffer[:remaining] = buffer[processed_size:buffer_size]
            return remaining
        
        return buffer_size

    def _receive_loop(self):
        """Thread loop for receiving data"""
        buffer = bytearray(1024)
        buffer_size = 0
        
        while self.running:
            # Try to receive data
            buffer_size = self._receive_data(buffer, buffer_size)
            if buffer_size < 0:  # Connection closed
                break
                
            # Process data in buffer
            buffer_size = self._process_buffer(buffer, buffer_size)
                
    def _process_packet(self, data):
        """Process received data packet"""
        if len(data) < 16:
            return
            
        # Get function index and callback ID
        func_idx = struct.unpack_from('H', data, 2)[0]  # Command ID
        callback_id = struct.unpack_from('B', data, 1)[0]  # Callback ID
        
        # Find corresponding callback function
        with self.callbacks_lock:
            callback = self.callbacks.get(callback_id, None)
            if callback:
                # Call the appropriate hook function
                if 0 <= func_idx < len(self._autosdk_func_tb_):
                    hook_func = self._autosdk_func_tb_[func_idx]
                    hook_func(self, data, callback)
                
                # Remove used callback
                del self.callbacks[callback_id]

    def block(self, timeout=None):
        """Wait for all pending callbacks to complete
        
        Args:
            timeout: Maximum time to wait in seconds, None for no timeout
            
        Returns:
            True if all callbacks completed, False if timed out
        """
        start_time = time.time()
        
        while self.running:
            # Check if there are any pending callbacks
            with self.callbacks_lock:
                if not self.callbacks:
                    return True
            
            # Check timeout
            if timeout is not None and time.time() - start_time > timeout:
                return False
                
            # Wait a bit before checking again
            time.sleep(0.01)
        
        return False  # Not running anymore

    def alloc(self, callback: Callable) -> int:
        """Allocate a unique ID and store callback function"""
        with self.callbacks_lock:
            callback_id = self.next_id
            self.next_id = (self.next_id + 1) & 0xFF  # Ensure ID cycles within 0-255 range
            self.callbacks[callback_id] = callback
            return callback_id

    def prior(self) -> None:
        """Prior prepare for write"""
        pass

    def write(self, data: bytes, length: int) -> None:
        """Write data to the connection"""
        self._send_data(data, length)

    def flush(self) -> None:
        """Flush data - implementation depends on connection type"""
        pass

    # functions-py >>
    def cproxy_delay(self, ms: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(20)  # Allocate fixed size buffer
        struct.pack_into('i', _data, 16, ms)  # Pack ms
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 0)  # Command ID
        struct.pack_into('I', _data, 4, 20)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _cproxy_delay_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    _autosdk_func_tb_ = [
        _cproxy_delay_hook_,
]
    # functions-py <<


# TCP implementation of Cproxy
class CproxyTcp(Cproxy):
    """TCP socket implementation of Cproxy"""
    
    def __init__(self, server_address=('localhost', 9999)):
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.sock.connect(server_address)
        super().__init__()
    
    def _close_connection(self):
        """Close the TCP socket"""
        self.sock.close()
    
    def _receive_data(self, buffer, buffer_size):
        """Receive data from TCP socket"""
        try:
            chunk = self.sock.recv(1024 - buffer_size)
            if not chunk:  # Connection closed
                return -1
                
            # Add to buffer
            buffer[buffer_size:buffer_size+len(chunk)] = chunk
            return buffer_size + len(chunk)
        except socket.error:
            time.sleep(0.01)  # Short wait on error
            return buffer_size
    
    def _send_data(self, data, length):
        """Send data over TCP socket"""
        if length > len(data):
            self.sock.sendall(data + bytes(length - len(data)))
        else:
            self.sock.sendall(data[:length])
        return length


def _demo_(hand):
    """Demo program: can submit multiple calls simultaneously"""
    results = []
    
    def _delay_callback_1(ret):
        print(f"Delay 1 result: {ret}ms")
        results.append(ret)
    hand.cproxy_delay(1000, _delay_callback_1)
    
    def _delay_callback_2(ret):
        print(f"Delay 2 result: {ret}ms")
        results.append(ret)
    hand.cproxy_delay(500, _delay_callback_2)
    
    def _delay_callback_3(ret):
        print(f"Delay 3 result: {ret}ms")
        results.append(ret)
    hand.cproxy_delay(100, _delay_callback_3)
    
    print("Submitted 3 calls")
    
    # Wait for all callbacks to complete using the new block method
    hand.block(timeout=5.0)  # Wait up to 5 seconds


if __name__ == "__main__":
    with CproxyTcp() as hand:  # Use CproxyTcp instead of Cproxy
        _demo_(hand)
