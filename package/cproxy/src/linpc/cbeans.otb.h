// Auto generated, don't edit.
#include "libcustom.h"
typedef struct
{
	any poller; // Md<PERSON>oller
} cproxy_t;
typedef struct
{
	any _obj0; // MdNotice
	any _obj1; // MdTaskServer
	any poller; // MdPoller
	any allocator; // MdAllocator
	any notice; // MdNoticeNode
	any _obj2; // MdReadHelper
	any booter; // MdNoticeNode
	any stream; // MdTcpServer
	any ticker; // MdStreamDaemon
	any autosdk; // CpAutoSdkServer
} CproxyServer;
non cproxy_new(cproxy_t *thiz);
non CproxyServer_Init(CproxyServer *thiz);
int cproxy_del(cproxy_t *thiz);
int CproxyServer_Free(CproxyServer *thiz);
#define BEAN_POLLER 0
#define BEAN_AUTOSDK 1
#define BEAN_EXPORT_CNT 2
