// Auto generated, don't edit.
#include "cbeans.h"
any CpAutoSdkServer_New(any notice, any writer);
any MdNotice_New(non);
any MdNoticeNode_New(non);
any MdTaskServer_New(int count);
any MdAllocator_New(int size, int count);
any MdReadHelper_New(any allocator, any notice);
any MdStreamDaemon_New(any notice, any stream, int heartbeat);
any MdPoller_New(non);
any MdTcpServer_New(any helper, int port, any booter);
non CpAutoSdkServer_Del(any obj);
non MdNotice_Del(any obj);
non MdNoticeNode_Del(any node);
non MdTaskServer_Del(any obj);
non MdAllocator_Del(any thiz);
non MdReadHelper_Del(any obj);
non MdStreamDaemon_Del(any obj);
non Md<PERSON>oll<PERSON>_<PERSON>(any obj);
non MdTcpServer_Del(any obj);
non cproxy_new(cproxy_t *thiz)
{
	if (!thiz) return;
	thiz->poller = MdPoller_New();
	cbeans_fill(thiz->poller, BEAN_POLLER);
}
non CproxyServer_Init(CproxyServer *thiz)
{
	if (!thiz) return;
	thiz->_obj0 = MdNotice_New();
	thiz->_obj1 = MdTaskServer_New(64);
	thiz->poller = MdPoller_New();
	cbeans_fill(thiz->poller, BEAN_POLLER);
	thiz->allocator = MdAllocator_New(128, 4);
	thiz->notice = MdNoticeNode_New();
	thiz->_obj2 = MdReadHelper_New(thiz->allocator, thiz->notice);
	thiz->booter = MdNoticeNode_New();
	thiz->stream = MdTcpServer_New(thiz->_obj2, 9999, thiz->booter);
	thiz->ticker = MdStreamDaemon_New(thiz->notice, thiz->stream, 1);
	thiz->autosdk = CpAutoSdkServer_New(thiz->notice, thiz->ticker);
	cbeans_fill(thiz->autosdk, BEAN_AUTOSDK);
}
int cproxy_del(cproxy_t *thiz)
{
	if (!thiz) return 0;
	if (thiz->poller)
	{
		MdPoller_Del(thiz->poller);
		thiz->poller = 0;
		cbeans_fill(0, BEAN_POLLER);
	}
	return 0;
}
int CproxyServer_Free(CproxyServer *thiz)
{
	if (!thiz) return 0;
	if (thiz->autosdk)
	{
		CpAutoSdkServer_Del(thiz->autosdk);
		thiz->autosdk = 0;
		cbeans_fill(0, BEAN_AUTOSDK);
	}
	if (thiz->ticker)
	{
		MdStreamDaemon_Del(thiz->ticker);
		thiz->ticker = 0;
	}
	if (thiz->stream)
	{
		MdTcpServer_Del(thiz->stream);
		thiz->stream = 0;
	}
	if (thiz->booter)
	{
		MdNoticeNode_Del(thiz->booter);
		thiz->booter = 0;
	}
	if (thiz->_obj2)
	{
		MdReadHelper_Del(thiz->_obj2);
		thiz->_obj2 = 0;
	}
	if (thiz->notice)
	{
		MdNoticeNode_Del(thiz->notice);
		thiz->notice = 0;
	}
	if (thiz->allocator)
	{
		MdAllocator_Del(thiz->allocator);
		thiz->allocator = 0;
	}
	if (thiz->poller)
	{
		MdPoller_Del(thiz->poller);
		thiz->poller = 0;
		cbeans_fill(0, BEAN_POLLER);
	}
	if (thiz->_obj1)
	{
		MdTaskServer_Del(thiz->_obj1);
		thiz->_obj1 = 0;
	}
	if (thiz->_obj0)
	{
		MdNotice_Del(thiz->_obj0);
		thiz->_obj0 = 0;
	}
	return 0;
}
