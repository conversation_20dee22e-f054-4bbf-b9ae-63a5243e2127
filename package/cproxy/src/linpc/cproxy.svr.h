// Auto generated, don't edit
// cproxy_delay I.S32
#include "libcustom.h"
#include "autosdk_protoc.h"
#include "autosdk_server.h"
int cproxy_delay(s32 ms);
static int _cproxy_delay_svr_(any _hand, any _data)
{
	s32 ms = *(s32 *)(_data + 16);
	int _ret = cproxy_delay(ms);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
static const any _autosdk_func_tb_[] = {
	_cproxy_delay_svr_,
};
static const any _autosdk_hand_tb_[] = {
	cproxy_delay,
};
const char *_autosdk_version_ = "2e37f9ba";
