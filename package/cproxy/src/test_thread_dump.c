//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: Main.c
// Author		: XuCF
// Created On	: 2024/09/11
// Description	: Main.c
//
// History
// 1. V1.0, Created by XuCF. 2024/09/11
//=============================================================================

#include "MdRunner.h"
#include "autocmd.h"

static void _delay_ms_(int ms)
{
	unsigned long start = clike_time_ms_full();
	while (clike_time_ms_full() < start + ms);
}

static any _thread_route_(any obj)
{
	for (int i = 0; i < 10; i++)
	{
		clike_print("%zu\n", (unv)obj);
		_delay_ms_(500);
	}
	return 0;
}

static any _ticker_route_(any obj)
{
	clike_print("ticker %u\n", clike_time_ms());
	MdThread_List();
	return 0;
}

CMD int demo_thread_dump(void) /* dump cpu rate. */
{
	MdThread_List();
	MdThread *thread1 = MdThread_Boot(_thread_route_, (any)1, "thread1");
	MdThread *thread2 = MdThread_Boot(_thread_route_, (any)2, "thread2");
	MdTicker *ticker1 = MdTicker_Boot(_ticker_route_, (any)0, MD_TICKER_FLAG_CY | MD_TICKER_FLAG_RE, 1000);
	MdThread_Wait(thread1);
	MdThread_Wait(thread2);

	// kill ticker
	while (!MdTicker_Kill(ticker1))
	{
		clike_print("ticker killing\n");
		clike_delay_us(100);
	}
	clike_print("ticker killed\n");
	// destroy ticker server
	int MdTicker_Exit(void);
	MdTicker_Exit();

	MdThread_Main();
	return 0;
}
