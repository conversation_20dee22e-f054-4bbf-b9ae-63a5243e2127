//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_mdinfrared_demo.c
// Author		: LiJZ
// Created On	: 2024/01/15
// Description	: hal_mdinfrared_demo.c
//
// History
// 1. V1.0, Created by LiJZ. 2024/01/15
//=============================================================================

// Code head Top
#define MACR_LOG_LEVEL 5
#include "autocmd.h"
#include "autosdk_server.h"
#include "hal_infrared.h"
// Code head Bot

// Code static Top

CMD int demo_infrared(void) /* infrared demo */
{
	s32 count = 0;
	s32 state = 0;
	s32 last_state = 0;
	Infrared *irf = HAL_Device_Find(MD_DEVICE_IRF);
	clike_assert(!irf, return -1);
	HAL_Infrared_Ctrl(irf, MD_INFRARED_STAT_WORK);
	last_state = state = HAL_Infrared_Read(irf);
	while (1)
	{
		clike_delay_ms(100);
		state = HAL_Infrared_Read(irf);
		if (last_state != state)
		{
			if (count++ >= 5)
			{
				clike_print("object %s detect area\n", state ? "enter" : "leave");
				last_state = state;
				count = 0;
			}
		}
	}
	HAL_Infrared_Ctrl(irf, MD_INFRARED_STAT_STOP);
	return 0;
}

API int infrared_sta(	/* get infrared state */
	S32(O) state		/* infrared state */
)
{
	Infrared *irf = HAL_Device_Find(MD_DEVICE_IRF);
	clike_assert(!irf, return -1);
	HAL_Infrared_Ctrl(irf, MD_INFRARED_STAT_WORK);
	*state = HAL_Infrared_Read(irf);
	HAL_Infrared_Ctrl(irf, MD_INFRARED_STAT_STOP);
	return AUTOSDK_STAT_DONE;
}

// Code static Bot
