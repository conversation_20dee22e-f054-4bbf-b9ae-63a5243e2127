//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: demo_network.c
// Author		: DongDL
// Created On	: 2024/11/25
// Description	: demo_network.c
//
// History
// 1. V1.0, Created by DongDL. 2024/11/25
//=============================================================================

#include "MdGvcp.h"
#include "autocmd.h"

CMD int demo_gvcp(void) /* gvcp. */
{
	gvcp_meta meta = 
	{
		.ip_cfg_opt = 0x7,
		.model_name = "This is model name",
		.device_version = "This is device version",
		.vendor_info = "This is vendor info",
		.serial_num = "Serial number",
		.user_define_name = "User name",
	};

	MdGvcp_Init(&meta);
	// wait forever
	while (1) clike_delay_ms(1000);
	return 0;
}
