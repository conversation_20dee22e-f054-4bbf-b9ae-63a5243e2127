//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_basichw_demo.c
// Author		: LiJZ
// Created On	: 2023/08/24
// Description	: hal_basichw_demo.c
//
// History
// 1. V1.0, Created by LiJZ. 2023/08/24
//=============================================================================

#include "autocmd.h"
#include "autosdk_server.h"
#include "hal_device.h"
#include "hal_switch.h"
#include "product_resource.h"

/**********************************************************************************************************************/
#define SWITCH_GLED 0
#define SWITCH_BLED 1
#define SWITCH_RLED 2
#define SWITCH_ALED 3
#define SWITCH_EASA 4
#define SWITCH_EASB 5
#define SWITCH_VIBR 6
#define SWITCH_INDC 7
#define SWITCH_CLIM 8
#define SWITCH_PLED 9
#define SWITCH_LPWR 10
#define SWITCH_NUMB 11

static Switch *_basic_hardware_switch_table_(u32 id)
{
	switch (id)
	{
		case SWITCH_GLED: return HAL_Device_Find(MD_DEVICE_LED_G);
		case SWITCH_BLED: return HAL_Device_Find(MD_DEVICE_LED_B);
		case SWITCH_RLED: return HAL_Device_Find(MD_DEVICE_LED_R);
		case SWITCH_ALED: return HAL_Device_Find(MD_DEVICE_AIM);
		case SWITCH_EASA: return HAL_Device_Find(MD_DEVICE_EAS_A);
		case SWITCH_EASB: return HAL_Device_Find(MD_DEVICE_EAS_B);
		case SWITCH_VIBR: return HAL_Device_Find(MD_DEVICE_VIBRATOR);
		case SWITCH_INDC: return HAL_Device_Find(MD_DEVICE_PWR_ON_IND);
		case SWITCH_CLIM: return HAL_Device_Find(MD_DEVICE_CURR_LIM);
		case SWITCH_PLED: return HAL_Device_Find(MD_DEVICE_PWR_LED);
		case SWITCH_LPWR: return HAL_Device_Find(MD_DEVICE_LUM_PWR_SET);
	}
	return MACR_NULL;
}

CMD int switch_ctrl(/* Switch state ctrl. */
	NUMB(0) leds /* switchs state */
)
{
	for (int i = 0; i < SWITCH_NUMB; i++)
	{
		Switch *pswitch = _basic_hardware_switch_table_(i);
		if (!pswitch) continue;

		// ON
		if ((leds >> i) & 1) { HAL_Switch_On(pswitch); }
		// OFF
		else { HAL_Switch_Off(pswitch); }
	}
	LOGI("leds:%lx", leds);
	return leds;
}

API int api_switch_ctrl(/* Switch state ctrl. */
	U32(I) leds			/* switchs state */
)
{
	switch_ctrl(leds);
	return AUTOSDK_STAT_DONE;
}

/**********************************************************************************************************************/
static Beeper *pbeeper = 0;

API int beeper_init(void) /* Beeper init. */
{
	if (pbeeper) return AUTOSDK_STAT_DONE;

	void PRD_Beeper_Build(void);
	PRD_Beeper_Build();
	if (DEV_BEEPER)
	{
		pbeeper = DEV_BEEPER;
		HAL_Beeper_Init(pbeeper);
	}

	return AUTOSDK_STAT_DONE;
}

API int beeper_cfg(/* Beeper config. */
	U32(I) tone,   /* beper tone */
	U32(I) volume  /* beeper volume */
)
{
	if (!pbeeper) return AUTOSDK_STAT_DONE;

	HAL_Beeper_Set(pbeeper, tone, volume);

	return AUTOSDK_STAT_DONE;
}

API int beeper_ctrl(/* beeper state ctrl. */
	U32(I) stat		/* beeper state */
)
{
	if (!pbeeper) return AUTOSDK_STAT_DONE;

	if (stat) { HAL_Beeper_ON(pbeeper); }
	else { HAL_Beeper_OFF(pbeeper); }

	return AUTOSDK_STAT_DONE;
}

/**********************************************************************************************************************/

#define LUMIN_NOR 0
#define LUMIN_IR  1
#define LUMIN_NUM 2

static Lumin *_basic_hardware_lumin_table_(u32 id)
{
	switch (id)
	{
		case LUMIN_NOR: return HAL_Device_Find(MD_DEVICE_LUMIN);
		case LUMIN_IR: return HAL_Device_Find(MD_DEVICE_IRLUM);
	}
	return MACR_NULL;
}

API int lum_pwr(/* Lumin power state ctrl */
	U32(I) stat /* lumin power state */
)
{
	Lumin *plumin = 0;

	for (int i = 0; i < LUMIN_NUM; i++)
	{
		plumin = _basic_hardware_lumin_table_(i);
		if (!plumin) continue;

		if ((stat >> i) & 0x1) { HAL_Lumin_PowerUp(plumin); }
		else { HAL_Lumin_PowerDown(plumin); }
	}
	return AUTOSDK_STAT_DONE;
}

API int lum_cfg(  /* Lumin config. */
	U32(I) lumin, /* lumin */
	U32(I) light, /* lumin level */
	U32(I) pos	  /* lumin pos */
)
{
	Lumin *plumin = 0;

	for (int i = 0; i < LUMIN_NUM; i++)
	{
		plumin = _basic_hardware_lumin_table_(i);
		if (!plumin) continue;

		HAL_Lumin_Luminance(plumin, light);
		HAL_Lumin_Position(plumin, pos);
	}
	return AUTOSDK_STAT_DONE;
}

API int lum_ctrl(/* Lumin state ctrl. */
	U32(I) stat	 /* lumin state */
)
{
	Lumin *plumin = 0;

	for (int i = 0; i < LUMIN_NUM; i++)
	{
		plumin = _basic_hardware_lumin_table_(i);
		if (!plumin) continue;
		if ((stat >> i) & 0x1) { HAL_Lumin_Enable(plumin); }
		else { HAL_Lumin_Disable(plumin); }
	}
	return AUTOSDK_STAT_DONE;
}
