// Auto generated by cpropr.py, DO NOT EDIT!

#ifndef __CPROPR_CTX_H__
#define __CPROPR_CTX_H__

#include "cpropr_types.h"
#include "cpropr.cfg.h"

// Hook function declarations
void _aadv_set_deco_time_ms_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ethernet_ip_enable_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _modbus_server_enable_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _profinet_enable_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _profinet_station_name_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _tcp_client_enable_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _tcp_target_server_ip_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _tcp_target_server_port_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _tcp_client_output_format_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _tcp_client_output_prefix_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _tcp_client_output_suffix_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _tcp_server_enable_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _tcp_server_output_format_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _tcp_server_output_prefix_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _tcp_server_output_suffix_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _tcp_server_service_port_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _uart_enable_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _usb_ethernet_composite_enable_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _usb_ethernet_composite_type_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_active_cmd_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_inactive_cmd_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_mode_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_incount_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_debounce_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_revent_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_rdelay_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_fevent_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_fdelay_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_active_cond_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_inactive_cond_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_active_delay_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_inactive_timer_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _netled_prop_hook_(int id, cpropr_hook_type_t type, void *value);

// Options for USBEthernetCompositeType
static const char *_usbethernetcompositetype_names_[] = {
    "Keyboard",
    "VCOM",
    0
};
// Options for CustomIOModeMain
static const char *_customiomodemain_names_[] = {
    "Close",
    "InputHigh",
    "InputLow",
    "OutputHigh",
    "OutputLow",
    0
};
// Options for CustomIORisingAction
static const char *_customiorisingaction_names_[] = {
    "None",
    "StartDecode",
    "EndDecode",
    0
};
// Options for CustomIOFallingAction
static const char *_customiofallingaction_names_[] = {
    "None",
    "StartDecode",
    "EndDecode",
    0
};
// Options for CustomIOActiveCondition
static const char *_customioactivecondition_names_[] = {
    "None",
    "DecodeSuccess",
    "NoRead",
    "DecodeStarted",
    "DecodeStopped",
    "Command",
    "Timer",
    "Input1High",
    "Input2High",
    "Input3High",
    "Input4High",
    "Input1Low",
    "Input2Low",
    "Input3Low",
    "Input4Low",
    0
};
// Options for CustomIOInactiveCondition
static const char *_customioinactivecondition_names_[] = {
    "None",
    "DecodeSuccess",
    "NoRead",
    "DecodeStarted",
    "DecodeStopped",
    "Command",
    "Timer",
    "Input1High",
    "Input2High",
    "Input3High",
    "Input4High",
    "Input1Low",
    "Input2Low",
    "Input3Low",
    "Input4Low",
    0
};
// Options for NetLed
static const char *_netled_names_[] = {
    "LINK",
    "TX",
    "RX",
    0
};
static cpropr_item_t *_cpropr_items_[] = {
    &(cpropr_item_t){
        .id = PROP_AIMDECOVIDEOSETDECOTIMEMS,
        .name = "AimDecoVideoSetDecoTimeMs",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 3600000 } },
        .defval = { .i = 500 },
        .flags = CPROPR_FLAG_WRONLY,
        .children = 0,
        .hook = _aadv_set_deco_time_ms_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_ETHERNETIPOUTPUTEN,
        .name = "EthernetIpOutputEn",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_REBOOT,
        .children = 0,
        .hook = _ethernet_ip_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MODBUSSVROUTPUTEN,
        .name = "ModbusSvrOutputEn",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_REBOOT,
        .children = 0,
        .hook = _modbus_server_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_PROFINETOUTPUTENMAIN,
        .name = "ProfinetOutputEnMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_REBOOT,
        .children = (short[]){4, -1},
        .hook = _profinet_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_PROFINETSTATIONNAME,
        .name = "ProfinetStationName",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 32 },
        .defval = { .p = (cpropr_bytes_t*)"\x0b""ad-irseries" },
        .flags = CPROPR_FLAG_REBOOT,
        .children = 0,
        .hook = _profinet_station_name_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TCPCLIOUTPUTENMAIN,
        .name = "TCPCliOutputEnMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NONE,
        .children = (short[]){6, 7, 8, 9, 10, -1},
        .hook = _tcp_client_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TCPCLITARGETSVRIP,
        .name = "TCPCliTargetSvrIP",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 16 },
        .defval = { .p = (cpropr_bytes_t*)"\x07""0.0.0.0" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _tcp_target_server_ip_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TCPCLITARGETSVRPORT,
        .name = "TCPCliTargetSvrPort",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 65535 } },
        .defval = { .i = 5000 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _tcp_target_server_port_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TCPCLIOUTPUTFMT,
        .name = "TCPCliOutputFmt",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 200 },
        .defval = { .p = (cpropr_bytes_t*)"\x0e""<code_content>" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _tcp_client_output_format_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TCPCLIOUTPUTPREFIX,
        .name = "TCPCliOutputPrefix",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 32 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _tcp_client_output_prefix_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TCPCLIOUTPUTSUFFIX,
        .name = "TCPCliOutputSuffix",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 32 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _tcp_client_output_suffix_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TCPSVROUTPUTENABLEMAIN,
        .name = "TCPSvrOutputEnableMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NONE,
        .children = (short[]){12, 13, 14, 15, -1},
        .hook = _tcp_server_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TCPSVROUTPUTFMT,
        .name = "TCPSvrOutputFmt",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 200 },
        .defval = { .p = (cpropr_bytes_t*)"\x0e""<code_content>" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _tcp_server_output_format_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TCPSVROUTPUTPREFIX,
        .name = "TCPSvrOutputPrefix",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 32 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _tcp_server_output_prefix_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TCPSVROUTPUTSUFFIX,
        .name = "TCPSvrOutputSuffix",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 32 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _tcp_server_output_suffix_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TCPSVRSERVICEPORT,
        .name = "TCPSvrServicePort",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 65535 } },
        .defval = { .i = 2002 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _tcp_server_service_port_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UARTENABLE,
        .name = "UartEnable",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _uart_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_USBETHERNETCOMPOSITEENMAIN,
        .name = "USBEthernetCompositeEnMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_REBOOT,
        .children = (short[]){18, -1},
        .hook = _usb_ethernet_composite_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_USBETHERNETCOMPOSITETYPE,
        .name = "USBEthernetCompositeType",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _usbethernetcompositetype_names_ } },
        .defval = { .i = USBETHERNETCOMPOSITETYPE_E_Keyboard },
        .flags = CPROPR_FLAG_REBOOT,
        .children = 0,
        .hook = _usb_ethernet_composite_type_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1ACTIVECMD,
        .name = "CustomIO1ActiveCmd",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 64 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_active_cmd_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2ACTIVECMD,
        .name = "CustomIO2ActiveCmd",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 64 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_active_cmd_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3ACTIVECMD,
        .name = "CustomIO3ActiveCmd",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 64 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_active_cmd_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4ACTIVECMD,
        .name = "CustomIO4ActiveCmd",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 64 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_active_cmd_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1INACTIVECMD,
        .name = "CustomIO1InactiveCmd",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 64 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_inactive_cmd_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2INACTIVECMD,
        .name = "CustomIO2InactiveCmd",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 64 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_inactive_cmd_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3INACTIVECMD,
        .name = "CustomIO3InactiveCmd",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 64 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_inactive_cmd_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4INACTIVECMD,
        .name = "CustomIO4InactiveCmd",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 64 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_inactive_cmd_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1MODEMAIN,
        .name = "CustomIO1ModeMain",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customiomodemain_names_ } },
        .defval = { .i = CUSTOMIOMODEMAIN_E_Close },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_mode_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2MODEMAIN,
        .name = "CustomIO2ModeMain",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customiomodemain_names_ } },
        .defval = { .i = CUSTOMIOMODEMAIN_E_Close },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_mode_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3MODEMAIN,
        .name = "CustomIO3ModeMain",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customiomodemain_names_ } },
        .defval = { .i = CUSTOMIOMODEMAIN_E_Close },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_mode_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4MODEMAIN,
        .name = "CustomIO4ModeMain",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customiomodemain_names_ } },
        .defval = { .i = CUSTOMIOMODEMAIN_E_Close },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_mode_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1INPUTCOUNT,
        .name = "CustomIO1InputCount",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_incount_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2INPUTCOUNT,
        .name = "CustomIO2InputCount",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_incount_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3INPUTCOUNT,
        .name = "CustomIO3InputCount",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_incount_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4INPUTCOUNT,
        .name = "CustomIO4InputCount",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_incount_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1DEBOUNCE,
        .name = "CustomIO1Debounce",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 1000 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_debounce_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2DEBOUNCE,
        .name = "CustomIO2Debounce",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 1000 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_debounce_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3DEBOUNCE,
        .name = "CustomIO3Debounce",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 1000 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_debounce_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4DEBOUNCE,
        .name = "CustomIO4Debounce",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 1000 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_debounce_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1RISINGACTION,
        .name = "CustomIO1RisingAction",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customiorisingaction_names_ } },
        .defval = { .i = CUSTOMIORISINGACTION_E_None },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_revent_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2RISINGACTION,
        .name = "CustomIO2RisingAction",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customiorisingaction_names_ } },
        .defval = { .i = CUSTOMIORISINGACTION_E_None },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_revent_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3RISINGACTION,
        .name = "CustomIO3RisingAction",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customiorisingaction_names_ } },
        .defval = { .i = CUSTOMIORISINGACTION_E_None },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_revent_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4RISINGACTION,
        .name = "CustomIO4RisingAction",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customiorisingaction_names_ } },
        .defval = { .i = CUSTOMIORISINGACTION_E_None },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_revent_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1RISINGDELAY,
        .name = "CustomIO1RisingDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_rdelay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2RISINGDELAY,
        .name = "CustomIO2RisingDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_rdelay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3RISINGDELAY,
        .name = "CustomIO3RisingDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_rdelay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4RISINGDELAY,
        .name = "CustomIO4RisingDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_rdelay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1FALLINGACTION,
        .name = "CustomIO1FallingAction",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customiofallingaction_names_ } },
        .defval = { .i = CUSTOMIOFALLINGACTION_E_None },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_fevent_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2FALLINGACTION,
        .name = "CustomIO2FallingAction",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customiofallingaction_names_ } },
        .defval = { .i = CUSTOMIOFALLINGACTION_E_None },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_fevent_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3FALLINGACTION,
        .name = "CustomIO3FallingAction",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customiofallingaction_names_ } },
        .defval = { .i = CUSTOMIOFALLINGACTION_E_None },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_fevent_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4FALLINGACTION,
        .name = "CustomIO4FallingAction",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customiofallingaction_names_ } },
        .defval = { .i = CUSTOMIOFALLINGACTION_E_None },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_fevent_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1FALLINGDELAY,
        .name = "CustomIO1FallingDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_fdelay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2FALLINGDELAY,
        .name = "CustomIO2FallingDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_fdelay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3FALLINGDELAY,
        .name = "CustomIO3FallingDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_fdelay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4FALLINGDELAY,
        .name = "CustomIO4FallingDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_fdelay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1ACTIVECONDITION,
        .name = "CustomIO1ActiveCondition",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customioactivecondition_names_ } },
        .defval = { .i = CUSTOMIOACTIVECONDITION_E_None },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_active_cond_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2ACTIVECONDITION,
        .name = "CustomIO2ActiveCondition",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customioactivecondition_names_ } },
        .defval = { .i = CUSTOMIOACTIVECONDITION_E_None },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_active_cond_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3ACTIVECONDITION,
        .name = "CustomIO3ActiveCondition",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customioactivecondition_names_ } },
        .defval = { .i = CUSTOMIOACTIVECONDITION_E_None },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_active_cond_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4ACTIVECONDITION,
        .name = "CustomIO4ActiveCondition",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customioactivecondition_names_ } },
        .defval = { .i = CUSTOMIOACTIVECONDITION_E_None },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_active_cond_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1INACTIVECONDITION,
        .name = "CustomIO1InactiveCondition",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customioinactivecondition_names_ } },
        .defval = { .i = CUSTOMIOINACTIVECONDITION_E_Timer },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_inactive_cond_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2INACTIVECONDITION,
        .name = "CustomIO2InactiveCondition",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customioinactivecondition_names_ } },
        .defval = { .i = CUSTOMIOINACTIVECONDITION_E_Timer },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_inactive_cond_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3INACTIVECONDITION,
        .name = "CustomIO3InactiveCondition",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customioinactivecondition_names_ } },
        .defval = { .i = CUSTOMIOINACTIVECONDITION_E_Timer },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_inactive_cond_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4INACTIVECONDITION,
        .name = "CustomIO4InactiveCondition",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customioinactivecondition_names_ } },
        .defval = { .i = CUSTOMIOINACTIVECONDITION_E_Timer },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_inactive_cond_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1ACTIVEDELAY,
        .name = "CustomIO1ActiveDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_active_delay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2ACTIVEDELAY,
        .name = "CustomIO2ActiveDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_active_delay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3ACTIVEDELAY,
        .name = "CustomIO3ActiveDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_active_delay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4ACTIVEDELAY,
        .name = "CustomIO4ActiveDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_active_delay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1INACTIVETIMER,
        .name = "CustomIO1InactiveTimer",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 100 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_inactive_timer_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2INACTIVETIMER,
        .name = "CustomIO2InactiveTimer",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 100 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_inactive_timer_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3INACTIVETIMER,
        .name = "CustomIO3InactiveTimer",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 100 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_inactive_timer_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4INACTIVETIMER,
        .name = "CustomIO4InactiveTimer",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 100 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_inactive_timer_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_NETLED,
        .name = "NetLed",
        .type = CPROPR_TYPE_COMB,
        .range = { .e = { .names = _netled_names_ } },
        .defval = { .i = NETLED_E_LINK | NETLED_E_TX | NETLED_E_RX },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _netled_prop_hook_,
    },
};

#define _cpropr_count_ (sizeof(_cpropr_items_)/sizeof(_cpropr_items_[0]))

#endif // __CPROPR_CTX_H__
