//=============================================================================
// Copyright (C), 2004-2021, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: knl_events_demo.c
// Author		: ChenCM
// Created On	: 2021/07/12
// Description	: knl_events_demo.c
//
// History
// 1. V1.0, Created by ChenCM. 2021/07/12
// 2. V2.0, Updated by XuCF. 2021/12/16
//=============================================================================

// Code impo >>
// Code impo <<

#include "object.h"
#include "hal/kernel/hal_thread.h"

// Code head >>
#include "libcustom.h"
#include "hal/kernel/hal_events.h"
#include "autocmd.h"
// Code head <<

ClassDeclare(MdEventsWaiterDemo);
ClassDeclare(MdEventsSenderDemo);

// [MdEventsWaiterDemo] <= [] <- [ThreadCall]
ClassRealize(MdEventsWaiterDemo)
{
	VTable super;
	// Code MdEventsWaiterDemo-class >>
	Thread thread;
	// Code MdEventsWaiterDemo-class <<
};
// [MdEventsSenderDemo] <= [] <- [ThreadCall]
ClassRealize(MdEventsSenderDemo)
{
	VTable super;
	// Code MdEventsSenderDemo-class >>
	Thread thread;
	// Code MdEventsSenderDemo-class <<
};

s32 MdEventsWaiterDemo_OnLoop_Impl(MdEventsWaiterDemo *thiz);
s32 MdEventsSenderDemo_OnLoop_Impl(MdEventsSenderDemo *thiz);

static void *__MdEventsWaiterDemo_VTable__(int id)
{
	switch (id)
	{
		case ApiOnLoop: return MdEventsWaiterDemo_OnLoop_Impl;
		default: return __DefaultImpl__;
	}
}
static void *__MdEventsSenderDemo_VTable__(int id)
{
	switch (id)
	{
		case ApiOnLoop: return MdEventsSenderDemo_OnLoop_Impl;
		default: return __DefaultImpl__;
	}
}

// Code static >>
static Events demo_events;
// Code static <<

s32 MdEventsWaiterDemo_Run(MdEventsWaiterDemo *thiz)
{
	// Code MdEventsWaiterDemo_Run >>
	s32 ret = 0;
	VTableBuild(MdEventsWaiterDemo, thiz);
	HAL_Events_Construct(&demo_events, "demo");
	HAL_Events_Setup(&demo_events, 0);
	HAL_Thread_InitV2(&thiz->thread, (ThreadCall *)thiz, "waiter");
	HAL_Thread_Boot(&thiz->thread);
	return ret;
	// Code MdEventsWaiterDemo_Run <<
}

s32 MdEventsWaiterDemo_OnLoop_Impl(MdEventsWaiterDemo *thiz)
{
	// Code MdEventsWaiterDemo_OnLoop_Impl >>
	s32 ret = 0;
	clike_print("Demo event wait\n");
	HAL_Events_WaitOr(&demo_events, 1, -1);
	HAL_Events_ClrEvent(&demo_events, 1);
	clike_print("Demo event occur\n");
	return ret;
	// Code MdEventsWaiterDemo_OnLoop_Impl <<
}

s32 MdEventsSenderDemo_Run(MdEventsSenderDemo *thiz)
{
	// Code MdEventsSenderDemo_Run >>
	s32 ret = 0;
	VTableBuild(MdEventsSenderDemo, thiz);
	HAL_Thread_InitV2(&thiz->thread, (ThreadCall *)thiz, "sender");
	HAL_Thread_Boot(&thiz->thread);
	return ret;
	// Code MdEventsSenderDemo_Run <<
}

s32 MdEventsSenderDemo_OnLoop_Impl(MdEventsSenderDemo *thiz)
{
	// Code MdEventsSenderDemo_OnLoop_Impl >>
	s32 ret = 0;
	clike_delay_ms(1000);
	clike_print("Demo event set\n");
	HAL_Events_SetEvent(&demo_events, 1);
	return ret;
	// Code MdEventsSenderDemo_OnLoop_Impl <<
}

// Code tail >>
CMD int demo_event(void) 	/* event demo */
{
	clike_print("MdEventsDemo+\n");
	static MdEventsWaiterDemo waiter;
	static MdEventsSenderDemo sendor;
	MdEventsWaiterDemo_Run(&waiter);
	MdEventsSenderDemo_Run(&sendor);
	clike_delay_ms(10000);
	HAL_Thread_Kill(&waiter.thread);
	HAL_Thread_Kill(&sendor.thread);
	clike_print("MdEventsDemo-\n");
	return 0;
}
// Code tail <<
