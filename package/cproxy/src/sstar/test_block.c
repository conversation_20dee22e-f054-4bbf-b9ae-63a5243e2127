//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_block_demo.c
// Author		: XuCF
// Created On	: 2023/07/31
// Description	: hal_block_demo.c
//
// History
// 1. V1.0, Created by XuCF. 2023/07/31
//=============================================================================

#define MACR_LOG_LEVEL 5

#include "libcustom.h"
#include "autocmd.h"
#include "autosdk_server.h"
#include "device/hal_block.h"

Class(MdBlkDemo);
Class(MdBlkDemo)
{
	Inherit(Super);
	Block *blk;
};

static MdBlkDemo demo_obj, *demo = &demo_obj;

static u8 *string_zero2space(u8 *data, u32 len)
{
	for (u32 i = 0; i < len; i++) data[i] = (data[i] == 0) ? ' ' : data[i];
	return data;
}

static void _block_init_(MdBlkDemo *thiz)
{
	s32 SSC377BMEM_Init(void);
	SSC377BMEM_Init();
	thiz->blk = HAL_Device_Find(MD_DEVICE_BLK_0);
}

CMD int demo_block(void) /* Block demo. */
{
	s32 ret = MACR_NONE;
	_block_init_(demo);

	clike_print("%p", demo->blk);

	BlockInfo info;
	HAL_Block_Dump(demo->blk, &info);
	clike_print("page_size(%d) * page_numb(%d) = size(%d)\n", info.page_size, info.page_numb, info.page_size * info.page_numb);

	HAL_Block_Wipe(demo->blk, 0, 32);
	HAL_Block_Seek(demo->blk, 0);
	HAL_Block_Write(demo->blk, "hello block", 12);

	u8 buffer[33] = {0};
	HAL_Block_Seek(demo->blk, 0);
	HAL_Block_Read(demo->blk, buffer, 6);
	clike_print("buffer = \"%s\"\n", string_zero2space(buffer, 32)); // buffer = "hello                           "
	HAL_Block_Read(demo->blk, buffer, 6);
	clike_print("buffer = \"%s\"\n", string_zero2space(buffer, 32)); // buffer = "block                           "

	HAL_Block_Seek(demo->blk, 0);
	HAL_Block_Read(demo->blk, buffer, 12);
	clike_print("buffer = \"%s\"\n", string_zero2space(buffer, 32)); // buffer = "hello block                     "

	HAL_Block_Wipe(demo->blk, 4, 4);
	HAL_Block_Seek(demo->blk, 0);
	HAL_Block_Read(demo->blk, buffer, 12);
	clike_print("buffer = \"%s\"\n", string_zero2space(buffer, 32)); // buffer = "hell    ock                     "

	HAL_Block_Seek(demo->blk, 16);
	HAL_Block_Write(demo->blk, "hello block", 12);
	HAL_Block_Seek(demo->blk, 0);
	HAL_Block_Read(demo->blk, buffer, 32);
	clike_print("buffer = \"%s\"\n", string_zero2space(buffer, 32)); // buffer = "hell    ock     hello block     "

	HAL_Block_Seek(demo->blk, info.page_numb * info.page_size - 16);
	HAL_Block_Write(demo->blk, "11112222333344445555666677778888", 32);
	HAL_Block_Seek(demo->blk, info.page_numb * info.page_size - 16);
	HAL_Block_Read(demo->blk, buffer, 32);
	clike_print("buffer = \"%s\"\n", string_zero2space(buffer, 32)); // buffer = "1111222233334444hello block     "

	return ret;
}

API int block_init(void)
{
	_block_init_(demo);
	return AUTOSDK_STAT_DONE;
}

API int block_dump(	/* dump block info */
	ARR(O) out
)
{
	BlockInfo info;
	HAL_Block_Dump(demo->blk, &info);
	out->len = snprintf(out->ptr, out->len, "page_size(%d) * page_numb(%d) = size(%d)", info.page_size, info.page_numb, info.page_size * info.page_numb);
	return AUTOSDK_STAT_DONE;
}

API int block_wipe(	/* wipe block */
	U32(I) addr,
	U32(I) size
)
{
	HAL_Block_Wipe(demo->blk, addr, size);
	return AUTOSDK_STAT_DONE;
}

API int block_seek(	/* seek block */
	U32(I) addr
)
{
	HAL_Block_Seek(demo->blk, addr);
	return AUTOSDK_STAT_DONE;
}

API int block_write(/* write block */
	ARR(I) in
)
{
	HAL_Block_Write(demo->blk, (any)in->ptr, in->len);
	return AUTOSDK_STAT_DONE;
}

API int block_read(	/* read block */
	ARR(O) out
)
{
	HAL_Block_Read(demo->blk, (any)out->ptr, out->len);
	return AUTOSDK_STAT_DONE;
}
