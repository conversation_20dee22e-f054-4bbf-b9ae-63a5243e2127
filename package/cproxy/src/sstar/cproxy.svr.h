// Auto generated, don't edit
// MdScanner_AppAimDecoVideo_GetInfo O.ARR
// MdScanner_AppAimDecoVideo_Start I.ARR
// MdScanner_AppAimDecoVideo_Stop
// MdScanner_ClearUCIPList
// MdScanner_CusROI_CapROIVideo
// MdScanner_CusROI_StpROIVideo
// MdScanner_CusROI_SttROIVideo
// MdScanner_GetCodeResult O.ARR
// MdScanner_GetImage I.S32 O.ARR
// MdScanner_GetImageSizeInfo O.ARR
// MdScanner_GetROISuccDecoInfo O.ARR
// MdScanner_GetUCIPList O.ARR
// MdScanner_GetUCIPVersion O.ARR
// MdScanner_SetUCIPList I.ARR
// MdScanner_StartAdjustImagingParaMode O.S32
// MdScanner_StartDeco
// MdScanner_StopAdjustImagingParaMode
// MdScanner_StopDeco
// api_switch_ctrl I.U32
// beeper_cfg I.U32 I.U32
// beeper_ctrl I.U32
// beeper_init
// block_dump
// block_read
// block_seek
// block_wipe
// block_write
// button_wait I.U32 I.U32 O.U32
// cable_sta O.U32
// cam_capt I.U32 O.ARR
// cam_capt_gray I.U32 O.F32
// cam_exit
// cam_flash_ctrl I.U32 I.U32 I.U32 I.U32 I.U32 I.U32 I.U32 I.U32 I.U32 I.U32
// cam_info O.U32 O.U32 O.U32
// cam_init I.S32
// cam_para_load I.U32 I.U32 I.U32 I.U32
// cproxy_delay I.S32
// domelight_cfg I.U32
// domelight_ctrl I.U32
// domelight_init
// infrared_sta O.S32
// lum_cfg I.U32 I.U32 I.U32
// lum_ctrl I.U32
// lum_pwr I.U32
// vibrate_ctrl I.S32
// vibrate_read O.S32
#include "libcustom.h"
#include "autosdk_protoc.h"
#include "autosdk_server.h"
int MdScanner_AppAimDecoVideo_GetInfo(autosdk_array_t *out);
static int _MdScanner_AppAimDecoVideo_GetInfo_svr_(any _hand, any _data)
{
	any _wptr = _data + 20;
	autosdk_array_t out = {_wptr, *(int *)(_data + 16)};
	_wptr += out.len;
	int _ret = MdScanner_AppAimDecoVideo_GetInfo(&out);
	if (_ret > 0) return _ret;
	*(int *)(_data + 16) = out.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20 + out.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_write(_hand, out.ptr, out.len);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_AppAimDecoVideo_Start(autosdk_array_t *in);
static int _MdScanner_AppAimDecoVideo_Start_svr_(any _hand, any _data)
{
	any _rptr = _data + 20;
	autosdk_array_t in = {_rptr, *(int *)(_data + 16)};
	int _ret = MdScanner_AppAimDecoVideo_Start(&in);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_AppAimDecoVideo_Stop(void);
static int _MdScanner_AppAimDecoVideo_Stop_svr_(any _hand, any _data)
{
	int _ret = MdScanner_AppAimDecoVideo_Stop();
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_ClearUCIPList(void);
static int _MdScanner_ClearUCIPList_svr_(any _hand, any _data)
{
	int _ret = MdScanner_ClearUCIPList();
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_CusROI_CapROIVideo(void);
static int _MdScanner_CusROI_CapROIVideo_svr_(any _hand, any _data)
{
	int _ret = MdScanner_CusROI_CapROIVideo();
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_CusROI_StpROIVideo(void);
static int _MdScanner_CusROI_StpROIVideo_svr_(any _hand, any _data)
{
	int _ret = MdScanner_CusROI_StpROIVideo();
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_CusROI_SttROIVideo(void);
static int _MdScanner_CusROI_SttROIVideo_svr_(any _hand, any _data)
{
	int _ret = MdScanner_CusROI_SttROIVideo();
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_GetCodeResult(autosdk_array_t *out);
static int _MdScanner_GetCodeResult_svr_(any _hand, any _data)
{
	any _wptr = _data + 20;
	autosdk_array_t out = {_wptr, *(int *)(_data + 16)};
	_wptr += out.len;
	int _ret = MdScanner_GetCodeResult(&out);
	if (_ret > 0) return _ret;
	*(int *)(_data + 16) = out.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20 + out.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_write(_hand, out.ptr, out.len);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_GetImage(s32 type, autosdk_array_t *out);
static int _MdScanner_GetImage_svr_(any _hand, any _data)
{
	s32 type = *(s32 *)(_data + 16);
	any _wptr = _data + 20;
	autosdk_array_t out = {_wptr, *(int *)(_data + 20)};
	_wptr += out.len;
	int _ret = MdScanner_GetImage(type, &out);
	if (_ret > 0) return _ret;
	*(int *)(_data + 16) = out.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20 + out.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_write(_hand, out.ptr, out.len);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_GetImageSizeInfo(autosdk_array_t *out);
static int _MdScanner_GetImageSizeInfo_svr_(any _hand, any _data)
{
	any _wptr = _data + 20;
	autosdk_array_t out = {_wptr, *(int *)(_data + 16)};
	_wptr += out.len;
	int _ret = MdScanner_GetImageSizeInfo(&out);
	if (_ret > 0) return _ret;
	*(int *)(_data + 16) = out.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20 + out.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_write(_hand, out.ptr, out.len);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_GetROISuccDecoInfo(autosdk_array_t *out);
static int _MdScanner_GetROISuccDecoInfo_svr_(any _hand, any _data)
{
	any _wptr = _data + 20;
	autosdk_array_t out = {_wptr, *(int *)(_data + 16)};
	_wptr += out.len;
	int _ret = MdScanner_GetROISuccDecoInfo(&out);
	if (_ret > 0) return _ret;
	*(int *)(_data + 16) = out.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20 + out.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_write(_hand, out.ptr, out.len);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_GetUCIPList(autosdk_array_t *out);
static int _MdScanner_GetUCIPList_svr_(any _hand, any _data)
{
	any _wptr = _data + 20;
	autosdk_array_t out = {_wptr, *(int *)(_data + 16)};
	_wptr += out.len;
	int _ret = MdScanner_GetUCIPList(&out);
	if (_ret > 0) return _ret;
	*(int *)(_data + 16) = out.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20 + out.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_write(_hand, out.ptr, out.len);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_GetUCIPVersion(autosdk_array_t *out);
static int _MdScanner_GetUCIPVersion_svr_(any _hand, any _data)
{
	any _wptr = _data + 20;
	autosdk_array_t out = {_wptr, *(int *)(_data + 16)};
	_wptr += out.len;
	int _ret = MdScanner_GetUCIPVersion(&out);
	if (_ret > 0) return _ret;
	*(int *)(_data + 16) = out.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20 + out.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_write(_hand, out.ptr, out.len);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_SetUCIPList(autosdk_array_t *pl);
static int _MdScanner_SetUCIPList_svr_(any _hand, any _data)
{
	any _rptr = _data + 20;
	autosdk_array_t pl = {_rptr, *(int *)(_data + 16)};
	int _ret = MdScanner_SetUCIPList(&pl);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_StartAdjustImagingParaMode(s32 *out);
static int _MdScanner_StartAdjustImagingParaMode_svr_(any _hand, any _data)
{
	s32 *out = (s32 *)(_data + 16);
	int _ret = MdScanner_StartAdjustImagingParaMode(out);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_StartDeco(void);
static int _MdScanner_StartDeco_svr_(any _hand, any _data)
{
	int _ret = MdScanner_StartDeco();
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_StopAdjustImagingParaMode(void);
static int _MdScanner_StopAdjustImagingParaMode_svr_(any _hand, any _data)
{
	int _ret = MdScanner_StopAdjustImagingParaMode();
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_StopDeco(void);
static int _MdScanner_StopDeco_svr_(any _hand, any _data)
{
	int _ret = MdScanner_StopDeco();
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int api_switch_ctrl(u32 leds);
static int _api_switch_ctrl_svr_(any _hand, any _data)
{
	u32 leds = *(u32 *)(_data + 16);
	int _ret = api_switch_ctrl(leds);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int beeper_cfg(u32 tone, u32 volume);
static int _beeper_cfg_svr_(any _hand, any _data)
{
	u32 tone = *(u32 *)(_data + 16);
	u32 volume = *(u32 *)(_data + 20);
	int _ret = beeper_cfg(tone, volume);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int beeper_ctrl(u32 stat);
static int _beeper_ctrl_svr_(any _hand, any _data)
{
	u32 stat = *(u32 *)(_data + 16);
	int _ret = beeper_ctrl(stat);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int beeper_init(void);
static int _beeper_init_svr_(any _hand, any _data)
{
	int _ret = beeper_init();
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int block_dump(void);
static int _block_dump_svr_(any _hand, any _data)
{
	int _ret = block_dump();
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int block_read(void);
static int _block_read_svr_(any _hand, any _data)
{
	int _ret = block_read();
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int block_seek(void);
static int _block_seek_svr_(any _hand, any _data)
{
	int _ret = block_seek();
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int block_wipe(void);
static int _block_wipe_svr_(any _hand, any _data)
{
	int _ret = block_wipe();
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int block_write(void);
static int _block_write_svr_(any _hand, any _data)
{
	int _ret = block_write();
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int button_wait(u32 btn, u32 timeout, u32 *event);
static int _button_wait_svr_(any _hand, any _data)
{
	u32 btn = *(u32 *)(_data + 16);
	u32 timeout = *(u32 *)(_data + 20);
	u32 *event = (u32 *)(_data + 16);
	int _ret = button_wait(btn, timeout, event);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_flush(_hand);
	return _ret;
}
int cable_sta(u32 *type);
static int _cable_sta_svr_(any _hand, any _data)
{
	u32 *type = (u32 *)(_data + 16);
	int _ret = cable_sta(type);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_flush(_hand);
	return _ret;
}
int cam_capt(u32 requ, autosdk_array_t *out);
static int _cam_capt_svr_(any _hand, any _data)
{
	u32 requ = *(u32 *)(_data + 16);
	any _wptr = _data + 20;
	autosdk_array_t out = {_wptr, *(int *)(_data + 20)};
	_wptr += out.len;
	int _ret = cam_capt(requ, &out);
	if (_ret > 0) return _ret;
	*(int *)(_data + 16) = out.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20 + out.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_write(_hand, out.ptr, out.len);
	autosdk_portme_flush(_hand);
	return _ret;
}
int cam_capt_gray(u32 requ, f32 *gray);
static int _cam_capt_gray_svr_(any _hand, any _data)
{
	u32 requ = *(u32 *)(_data + 16);
	f32 *gray = (f32 *)(_data + 16);
	int _ret = cam_capt_gray(requ, gray);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_flush(_hand);
	return _ret;
}
int cam_exit(void);
static int _cam_exit_svr_(any _hand, any _data)
{
	int _ret = cam_exit();
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int cam_flash_ctrl(u32 seq, u32 aim, u32 lum, u32 irf, u32 cycl, u32 glow, u32 ldly, u32 ddly, u32 cap_cycl, u32 aim_line);
static int _cam_flash_ctrl_svr_(any _hand, any _data)
{
	u32 seq = *(u32 *)(_data + 16);
	u32 aim = *(u32 *)(_data + 20);
	u32 lum = *(u32 *)(_data + 24);
	u32 irf = *(u32 *)(_data + 28);
	u32 cycl = *(u32 *)(_data + 32);
	u32 glow = *(u32 *)(_data + 36);
	u32 ldly = *(u32 *)(_data + 40);
	u32 ddly = *(u32 *)(_data + 44);
	u32 cap_cycl = *(u32 *)(_data + 48);
	u32 aim_line = *(u32 *)(_data + 52);
	int _ret = cam_flash_ctrl(seq, aim, lum, irf, cycl, glow, ldly, ddly, cap_cycl, aim_line);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int cam_info(u32 *width, u32 *height, u32 *format);
static int _cam_info_svr_(any _hand, any _data)
{
	u32 *width = (u32 *)(_data + 16);
	u32 *height = (u32 *)(_data + 20);
	u32 *format = (u32 *)(_data + 24);
	int _ret = cam_info(width, height, format);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 28;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 28);
	autosdk_portme_flush(_hand);
	return _ret;
}
int cam_init(s32 isp_on);
static int _cam_init_svr_(any _hand, any _data)
{
	s32 isp_on = *(s32 *)(_data + 16);
	int _ret = cam_init(isp_on);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int cam_para_load(u32 expo, u32 gain, u32 gamma, u32 denoise);
static int _cam_para_load_svr_(any _hand, any _data)
{
	u32 expo = *(u32 *)(_data + 16);
	u32 gain = *(u32 *)(_data + 20);
	u32 gamma = *(u32 *)(_data + 24);
	u32 denoise = *(u32 *)(_data + 28);
	int _ret = cam_para_load(expo, gain, gamma, denoise);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int cproxy_delay(s32 ms);
static int _cproxy_delay_svr_(any _hand, any _data)
{
	s32 ms = *(s32 *)(_data + 16);
	int _ret = cproxy_delay(ms);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int domelight_cfg(u32 color);
static int _domelight_cfg_svr_(any _hand, any _data)
{
	u32 color = *(u32 *)(_data + 16);
	int _ret = domelight_cfg(color);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int domelight_ctrl(u32 sta);
static int _domelight_ctrl_svr_(any _hand, any _data)
{
	u32 sta = *(u32 *)(_data + 16);
	int _ret = domelight_ctrl(sta);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int domelight_init(void);
static int _domelight_init_svr_(any _hand, any _data)
{
	int _ret = domelight_init();
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int infrared_sta(s32 *state);
static int _infrared_sta_svr_(any _hand, any _data)
{
	s32 *state = (s32 *)(_data + 16);
	int _ret = infrared_sta(state);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_flush(_hand);
	return _ret;
}
int lum_cfg(u32 lumin, u32 light, u32 pos);
static int _lum_cfg_svr_(any _hand, any _data)
{
	u32 lumin = *(u32 *)(_data + 16);
	u32 light = *(u32 *)(_data + 20);
	u32 pos = *(u32 *)(_data + 24);
	int _ret = lum_cfg(lumin, light, pos);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int lum_ctrl(u32 stat);
static int _lum_ctrl_svr_(any _hand, any _data)
{
	u32 stat = *(u32 *)(_data + 16);
	int _ret = lum_ctrl(stat);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int lum_pwr(u32 stat);
static int _lum_pwr_svr_(any _hand, any _data)
{
	u32 stat = *(u32 *)(_data + 16);
	int _ret = lum_pwr(stat);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int vibrate_ctrl(s32 stat);
static int _vibrate_ctrl_svr_(any _hand, any _data)
{
	s32 stat = *(s32 *)(_data + 16);
	int _ret = vibrate_ctrl(stat);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int vibrate_read(s32 *cnt);
static int _vibrate_read_svr_(any _hand, any _data)
{
	s32 *cnt = (s32 *)(_data + 16);
	int _ret = vibrate_read(cnt);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_flush(_hand);
	return _ret;
}
static const any _autosdk_func_tb_[] = {
	_MdScanner_AppAimDecoVideo_GetInfo_svr_,
	_MdScanner_AppAimDecoVideo_Start_svr_,
	_MdScanner_AppAimDecoVideo_Stop_svr_,
	_MdScanner_ClearUCIPList_svr_,
	_MdScanner_CusROI_CapROIVideo_svr_,
	_MdScanner_CusROI_StpROIVideo_svr_,
	_MdScanner_CusROI_SttROIVideo_svr_,
	_MdScanner_GetCodeResult_svr_,
	_MdScanner_GetImage_svr_,
	_MdScanner_GetImageSizeInfo_svr_,
	_MdScanner_GetROISuccDecoInfo_svr_,
	_MdScanner_GetUCIPList_svr_,
	_MdScanner_GetUCIPVersion_svr_,
	_MdScanner_SetUCIPList_svr_,
	_MdScanner_StartAdjustImagingParaMode_svr_,
	_MdScanner_StartDeco_svr_,
	_MdScanner_StopAdjustImagingParaMode_svr_,
	_MdScanner_StopDeco_svr_,
	_api_switch_ctrl_svr_,
	_beeper_cfg_svr_,
	_beeper_ctrl_svr_,
	_beeper_init_svr_,
	_block_dump_svr_,
	_block_read_svr_,
	_block_seek_svr_,
	_block_wipe_svr_,
	_block_write_svr_,
	_button_wait_svr_,
	_cable_sta_svr_,
	_cam_capt_svr_,
	_cam_capt_gray_svr_,
	_cam_exit_svr_,
	_cam_flash_ctrl_svr_,
	_cam_info_svr_,
	_cam_init_svr_,
	_cam_para_load_svr_,
	_cproxy_delay_svr_,
	_domelight_cfg_svr_,
	_domelight_ctrl_svr_,
	_domelight_init_svr_,
	_infrared_sta_svr_,
	_lum_cfg_svr_,
	_lum_ctrl_svr_,
	_lum_pwr_svr_,
	_vibrate_ctrl_svr_,
	_vibrate_read_svr_,
};
static const any _autosdk_hand_tb_[] = {
	MdScanner_AppAimDecoVideo_GetInfo,
	MdScanner_AppAimDecoVideo_Start,
	MdScanner_AppAimDecoVideo_Stop,
	MdScanner_ClearUCIPList,
	MdScanner_CusROI_CapROIVideo,
	MdScanner_CusROI_StpROIVideo,
	MdScanner_CusROI_SttROIVideo,
	MdScanner_GetCodeResult,
	MdScanner_GetImage,
	MdScanner_GetImageSizeInfo,
	MdScanner_GetROISuccDecoInfo,
	MdScanner_GetUCIPList,
	MdScanner_GetUCIPVersion,
	MdScanner_SetUCIPList,
	MdScanner_StartAdjustImagingParaMode,
	MdScanner_StartDeco,
	MdScanner_StopAdjustImagingParaMode,
	MdScanner_StopDeco,
	api_switch_ctrl,
	beeper_cfg,
	beeper_ctrl,
	beeper_init,
	block_dump,
	block_read,
	block_seek,
	block_wipe,
	block_write,
	button_wait,
	cable_sta,
	cam_capt,
	cam_capt_gray,
	cam_exit,
	cam_flash_ctrl,
	cam_info,
	cam_init,
	cam_para_load,
	cproxy_delay,
	domelight_cfg,
	domelight_ctrl,
	domelight_init,
	infrared_sta,
	lum_cfg,
	lum_ctrl,
	lum_pwr,
	vibrate_ctrl,
	vibrate_read,
};
const char *_autosdk_version_ = "b9847ff9";
