#!python
from typing import Callable
import struct
import socket
import threading
import time
import abc


class Cproxy(abc.ABC):
    """Abstract base class for the C-proxy functionality"""

    def __init__(self):
        self.callbacks = {}  # Dictionary to store callbacks, keyed by unique ID
        self.next_id = 0  # Auto-increment ID for generating unique identifiers
        self.callbacks_lock = threading.Lock()  # Lock to protect callbacks dictionary
        self.running = True
        # Start receiving thread
        self.receive_thread = threading.Thread(target=self._receive_loop)
        self.receive_thread.daemon = True
        self.receive_thread.start()

    def __enter__(self):
        """Supports 'with' statement"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Clean up resources when exiting 'with' block"""
        self.running = False
        self._close_connection()
        self.receive_thread.join(timeout=1.0)  # Wait for receive thread to end
        return False  # Do not suppress exceptions

    @abc.abstractmethod
    def _close_connection(self):
        """Close the underlying connection"""
        pass

    @abc.abstractmethod
    def _receive_data(self, length):
        """Try to receive a chunk of data

        Args:
            length: Maximum number of bytes to receive
        
        Returns:
            Bytes received, or None if connection closed
        """
        pass

    @abc.abstractmethod
    def _receive_view(self, view):
        """Receive data directly into a memoryview
        
        Args:
            view: memoryview to receive data into
            
        Returns:
            Number of bytes received, or -1 if connection closed
        """
        pass

    @abc.abstractmethod
    def _send_data(self, data):
        """Send data over the connection
        
        Args:
            data: The data to send
            
        Returns:
            Number of bytes sent
        """
        pass
    
    def _receive_packet(self, data):
        """Receive one complete packet from connection
        
        Returns:
            Received packet as bytes if successful, None if connection closed
        """
        # First, get the header (at least 16 bytes)
        while len(data) < 16:
            data += self._receive_data(128)
        
        # Parse packet length from header
        packet_len = max(struct.unpack_from('I', data, 4)[0], 16)
        
        # If we already have the full packet, return it
        if len(data) >= packet_len:
            return data[:packet_len], data[packet_len:]
        
        # Need more data - create a buffer of exact size needed
        full_packet = bytearray(packet_len)
        
        # Copy what we already have
        full_packet[:len(data)] = data
        
        # Create a view for receiving the rest
        view = memoryview(full_packet)[len(data):]
        remaining = packet_len - len(data)
        received = 0
        
        # Receive remaining data directly into the buffer
        while received < remaining:
            bytes_read = self._receive_view(view[received:])
            if bytes_read < 0:  # Connection closed
                return None
            received += bytes_read
        
        # Return the complete packet
        return bytes(full_packet), b''
    
    def _process_packet(self, data):
        """Process received data packet"""
        if len(data) < 16:
            return
        if data[0] != 0xfe:
            return
        # Get function index and callback ID
        func_idx = struct.unpack_from('H', data, 2)[0]  # Command ID
        callback_id = struct.unpack_from('B', data, 1)[0]  # Callback ID
        
        # Find corresponding callback function
        with self.callbacks_lock:
            callback = self.callbacks.get(callback_id, None)
            if callback:
                # Call the appropriate hook function
                if 0 <= func_idx < len(self._autosdk_func_tb_):
                    hook_func = self._autosdk_func_tb_[func_idx]
                    hook_func(self, data, callback)
                
                # Remove used callback
                del self.callbacks[callback_id]

    def _receive_loop(self):
        """Thread loop for receiving data"""
        extra_data = b''
        # Main receive loop
        while self.running:
            try:
                # Receive one packet
                packet, extra_data = self._receive_packet(extra_data)
                # Process the received packet
                self._process_packet(packet)
            except:
                break

    def block(self, timeout=None):
        """Wait for all pending callbacks to complete
        
        Args:
            timeout: Maximum time to wait in seconds, None for no timeout
            
        Returns:
            True if all callbacks completed, False if timed out
        """
        start_time = time.time()
        
        while self.running:
            # Check if there are any pending callbacks
            with self.callbacks_lock:
                if not self.callbacks:
                    return True
            
            # Check timeout
            if timeout is not None and time.time() - start_time > timeout:
                return False
                
            # Wait a bit before checking again
            time.sleep(0.01)
        
        return False  # Not running anymore

    def alloc(self, callback: Callable) -> int:
        """Allocate a unique ID and store callback function"""
        with self.callbacks_lock:
            callback_id = self.next_id
            self.next_id = (self.next_id + 1) & 0xFF  # Ensure ID cycles within 0-255 range
            self.callbacks[callback_id] = callback
            return callback_id

    def prior(self) -> None:
        """Prior prepare for write"""
        pass

    def write(self, data: bytes) -> None:
        """Write data to the connection"""
        self._send_data(data)

    def flush(self) -> None:
        """Flush data - implementation depends on connection type"""
        pass

# functions-py >>
    def MdScanner_AppAimDecoVideo_GetInfo(self, out_len: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(20)  # Allocate fixed size buffer
        struct.pack_into('i', _data, 16, out_len)  # out buffer size
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 0)  # Command ID
        struct.pack_into('I', _data, 4, 20)  # Request total length
        struct.pack_into('I', _data, 8, 20 + out_len)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _MdScanner_AppAimDecoVideo_GetInfo_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ptr = 20  # Start index for variable data section
        out_len = struct.unpack_from('i', _data, 16)[0]  # out length
        out = _data[_ptr:_ptr+out_len]  # Extract out bytes
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(out, _ret)
        return _ret

    def MdScanner_AppAimDecoVideo_Start(self, _in: bytes, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(20)  # Allocate fixed size buffer
        _in_len = len(_in)  # Get array length
        struct.pack_into('i', _data, 16, _in_len)  # _in length
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 1)  # Command ID
        struct.pack_into('I', _data, 4, 20 + _in_len)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.write(_in)  # Write array directly
        self.flush()  # Flush all data
        return _idx

    def _MdScanner_AppAimDecoVideo_Start_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def MdScanner_AppAimDecoVideo_Stop(self, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(16)  # Allocate fixed size buffer
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 2)  # Command ID
        struct.pack_into('I', _data, 4, 16)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _MdScanner_AppAimDecoVideo_Stop_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def MdScanner_ClearUCIPList(self, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(16)  # Allocate fixed size buffer
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 3)  # Command ID
        struct.pack_into('I', _data, 4, 16)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _MdScanner_ClearUCIPList_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def MdScanner_CusROI_CapROIVideo(self, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(16)  # Allocate fixed size buffer
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 4)  # Command ID
        struct.pack_into('I', _data, 4, 16)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _MdScanner_CusROI_CapROIVideo_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def MdScanner_CusROI_StpROIVideo(self, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(16)  # Allocate fixed size buffer
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 5)  # Command ID
        struct.pack_into('I', _data, 4, 16)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _MdScanner_CusROI_StpROIVideo_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def MdScanner_CusROI_SttROIVideo(self, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(16)  # Allocate fixed size buffer
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 6)  # Command ID
        struct.pack_into('I', _data, 4, 16)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _MdScanner_CusROI_SttROIVideo_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def MdScanner_GetCodeResult(self, out_len: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(20)  # Allocate fixed size buffer
        struct.pack_into('i', _data, 16, out_len)  # out buffer size
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 7)  # Command ID
        struct.pack_into('I', _data, 4, 20)  # Request total length
        struct.pack_into('I', _data, 8, 20 + out_len)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _MdScanner_GetCodeResult_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ptr = 20  # Start index for variable data section
        out_len = struct.unpack_from('i', _data, 16)[0]  # out length
        out = _data[_ptr:_ptr+out_len]  # Extract out bytes
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(out, _ret)
        return _ret

    def MdScanner_GetImage(self, type: int, out_len: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(24)  # Allocate fixed size buffer
        struct.pack_into('i', _data, 16, type)  # Pack type
        struct.pack_into('i', _data, 20, out_len)  # out buffer size
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 8)  # Command ID
        struct.pack_into('I', _data, 4, 24)  # Request total length
        struct.pack_into('I', _data, 8, 20 + out_len)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _MdScanner_GetImage_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ptr = 20  # Start index for variable data section
        out_len = struct.unpack_from('i', _data, 16)[0]  # out length
        out = _data[_ptr:_ptr+out_len]  # Extract out bytes
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(out, _ret)
        return _ret

    def MdScanner_GetImageSizeInfo(self, out_len: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(20)  # Allocate fixed size buffer
        struct.pack_into('i', _data, 16, out_len)  # out buffer size
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 9)  # Command ID
        struct.pack_into('I', _data, 4, 20)  # Request total length
        struct.pack_into('I', _data, 8, 20 + out_len)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _MdScanner_GetImageSizeInfo_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ptr = 20  # Start index for variable data section
        out_len = struct.unpack_from('i', _data, 16)[0]  # out length
        out = _data[_ptr:_ptr+out_len]  # Extract out bytes
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(out, _ret)
        return _ret

    def MdScanner_GetROISuccDecoInfo(self, out_len: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(20)  # Allocate fixed size buffer
        struct.pack_into('i', _data, 16, out_len)  # out buffer size
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 10)  # Command ID
        struct.pack_into('I', _data, 4, 20)  # Request total length
        struct.pack_into('I', _data, 8, 20 + out_len)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _MdScanner_GetROISuccDecoInfo_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ptr = 20  # Start index for variable data section
        out_len = struct.unpack_from('i', _data, 16)[0]  # out length
        out = _data[_ptr:_ptr+out_len]  # Extract out bytes
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(out, _ret)
        return _ret

    def MdScanner_GetUCIPList(self, out_len: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(20)  # Allocate fixed size buffer
        struct.pack_into('i', _data, 16, out_len)  # out buffer size
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 11)  # Command ID
        struct.pack_into('I', _data, 4, 20)  # Request total length
        struct.pack_into('I', _data, 8, 20 + out_len)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _MdScanner_GetUCIPList_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ptr = 20  # Start index for variable data section
        out_len = struct.unpack_from('i', _data, 16)[0]  # out length
        out = _data[_ptr:_ptr+out_len]  # Extract out bytes
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(out, _ret)
        return _ret

    def MdScanner_GetUCIPVersion(self, out_len: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(20)  # Allocate fixed size buffer
        struct.pack_into('i', _data, 16, out_len)  # out buffer size
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 12)  # Command ID
        struct.pack_into('I', _data, 4, 20)  # Request total length
        struct.pack_into('I', _data, 8, 20 + out_len)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _MdScanner_GetUCIPVersion_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ptr = 20  # Start index for variable data section
        out_len = struct.unpack_from('i', _data, 16)[0]  # out length
        out = _data[_ptr:_ptr+out_len]  # Extract out bytes
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(out, _ret)
        return _ret

    def MdScanner_SetUCIPList(self, pl: bytes, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(20)  # Allocate fixed size buffer
        pl_len = len(pl)  # Get array length
        struct.pack_into('i', _data, 16, pl_len)  # pl length
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 13)  # Command ID
        struct.pack_into('I', _data, 4, 20 + pl_len)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.write(pl)  # Write array directly
        self.flush()  # Flush all data
        return _idx

    def _MdScanner_SetUCIPList_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def MdScanner_StartAdjustImagingParaMode(self, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(16)  # Allocate fixed size buffer
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 14)  # Command ID
        struct.pack_into('I', _data, 4, 16)  # Request total length
        struct.pack_into('I', _data, 8, 20)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _MdScanner_StartAdjustImagingParaMode_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        out = struct.unpack_from('i', _data, 16)[0]  # Read out
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(out, _ret)
        return _ret

    def MdScanner_StartDeco(self, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(16)  # Allocate fixed size buffer
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 15)  # Command ID
        struct.pack_into('I', _data, 4, 16)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _MdScanner_StartDeco_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def MdScanner_StopAdjustImagingParaMode(self, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(16)  # Allocate fixed size buffer
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 16)  # Command ID
        struct.pack_into('I', _data, 4, 16)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _MdScanner_StopAdjustImagingParaMode_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def MdScanner_StopDeco(self, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(16)  # Allocate fixed size buffer
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 17)  # Command ID
        struct.pack_into('I', _data, 4, 16)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _MdScanner_StopDeco_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def api_switch_ctrl(self, leds: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(20)  # Allocate fixed size buffer
        struct.pack_into('I', _data, 16, leds)  # Pack leds
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 18)  # Command ID
        struct.pack_into('I', _data, 4, 20)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _api_switch_ctrl_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def beeper_cfg(self, tone: int, volume: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(24)  # Allocate fixed size buffer
        struct.pack_into('I', _data, 16, tone)  # Pack tone
        struct.pack_into('I', _data, 20, volume)  # Pack volume
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 19)  # Command ID
        struct.pack_into('I', _data, 4, 24)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _beeper_cfg_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def beeper_ctrl(self, stat: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(20)  # Allocate fixed size buffer
        struct.pack_into('I', _data, 16, stat)  # Pack stat
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 20)  # Command ID
        struct.pack_into('I', _data, 4, 20)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _beeper_ctrl_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def beeper_init(self, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(16)  # Allocate fixed size buffer
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 21)  # Command ID
        struct.pack_into('I', _data, 4, 16)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _beeper_init_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def block_dump(self, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(16)  # Allocate fixed size buffer
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 22)  # Command ID
        struct.pack_into('I', _data, 4, 16)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _block_dump_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def block_read(self, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(16)  # Allocate fixed size buffer
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 23)  # Command ID
        struct.pack_into('I', _data, 4, 16)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _block_read_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def block_seek(self, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(16)  # Allocate fixed size buffer
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 24)  # Command ID
        struct.pack_into('I', _data, 4, 16)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _block_seek_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def block_wipe(self, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(16)  # Allocate fixed size buffer
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 25)  # Command ID
        struct.pack_into('I', _data, 4, 16)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _block_wipe_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def block_write(self, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(16)  # Allocate fixed size buffer
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 26)  # Command ID
        struct.pack_into('I', _data, 4, 16)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _block_write_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def button_wait(self, btn: int, timeout: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(24)  # Allocate fixed size buffer
        struct.pack_into('I', _data, 16, btn)  # Pack btn
        struct.pack_into('I', _data, 20, timeout)  # Pack timeout
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 27)  # Command ID
        struct.pack_into('I', _data, 4, 24)  # Request total length
        struct.pack_into('I', _data, 8, 20)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _button_wait_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        event = struct.unpack_from('I', _data, 16)[0]  # Read event
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(event, _ret)
        return _ret

    def cable_sta(self, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(16)  # Allocate fixed size buffer
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 28)  # Command ID
        struct.pack_into('I', _data, 4, 16)  # Request total length
        struct.pack_into('I', _data, 8, 20)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _cable_sta_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        type = struct.unpack_from('I', _data, 16)[0]  # Read type
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(type, _ret)
        return _ret

    def cam_capt(self, requ: int, out_len: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(24)  # Allocate fixed size buffer
        struct.pack_into('I', _data, 16, requ)  # Pack requ
        struct.pack_into('i', _data, 20, out_len)  # out buffer size
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 29)  # Command ID
        struct.pack_into('I', _data, 4, 24)  # Request total length
        struct.pack_into('I', _data, 8, 20 + out_len)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _cam_capt_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ptr = 20  # Start index for variable data section
        out_len = struct.unpack_from('i', _data, 16)[0]  # out length
        out = _data[_ptr:_ptr+out_len]  # Extract out bytes
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(out, _ret)
        return _ret

    def cam_capt_gray(self, requ: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(20)  # Allocate fixed size buffer
        struct.pack_into('I', _data, 16, requ)  # Pack requ
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 30)  # Command ID
        struct.pack_into('I', _data, 4, 20)  # Request total length
        struct.pack_into('I', _data, 8, 20)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _cam_capt_gray_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        gray = struct.unpack_from('f', _data, 16)[0]  # Read gray
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(gray, _ret)
        return _ret

    def cam_exit(self, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(16)  # Allocate fixed size buffer
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 31)  # Command ID
        struct.pack_into('I', _data, 4, 16)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _cam_exit_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def cam_flash_ctrl(self, seq: int, aim: int, lum: int, irf: int, cycl: int, glow: int, ldly: int, ddly: int, cap_cycl: int, aim_line: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(56)  # Allocate fixed size buffer
        struct.pack_into('I', _data, 16, seq)  # Pack seq
        struct.pack_into('I', _data, 20, aim)  # Pack aim
        struct.pack_into('I', _data, 24, lum)  # Pack lum
        struct.pack_into('I', _data, 28, irf)  # Pack irf
        struct.pack_into('I', _data, 32, cycl)  # Pack cycl
        struct.pack_into('I', _data, 36, glow)  # Pack glow
        struct.pack_into('I', _data, 40, ldly)  # Pack ldly
        struct.pack_into('I', _data, 44, ddly)  # Pack ddly
        struct.pack_into('I', _data, 48, cap_cycl)  # Pack cap_cycl
        struct.pack_into('I', _data, 52, aim_line)  # Pack aim_line
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 32)  # Command ID
        struct.pack_into('I', _data, 4, 56)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _cam_flash_ctrl_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def cam_info(self, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(16)  # Allocate fixed size buffer
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 33)  # Command ID
        struct.pack_into('I', _data, 4, 16)  # Request total length
        struct.pack_into('I', _data, 8, 28)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _cam_info_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        width = struct.unpack_from('I', _data, 16)[0]  # Read width
        height = struct.unpack_from('I', _data, 20)[0]  # Read height
        format = struct.unpack_from('I', _data, 24)[0]  # Read format
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(width, height, format, _ret)
        return _ret

    def cam_init(self, isp_on: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(20)  # Allocate fixed size buffer
        struct.pack_into('i', _data, 16, isp_on)  # Pack isp_on
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 34)  # Command ID
        struct.pack_into('I', _data, 4, 20)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _cam_init_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def cam_para_load(self, expo: int, gain: int, gamma: int, denoise: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(32)  # Allocate fixed size buffer
        struct.pack_into('I', _data, 16, expo)  # Pack expo
        struct.pack_into('I', _data, 20, gain)  # Pack gain
        struct.pack_into('I', _data, 24, gamma)  # Pack gamma
        struct.pack_into('I', _data, 28, denoise)  # Pack denoise
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 35)  # Command ID
        struct.pack_into('I', _data, 4, 32)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _cam_para_load_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def cproxy_delay(self, ms: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(20)  # Allocate fixed size buffer
        struct.pack_into('i', _data, 16, ms)  # Pack ms
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 36)  # Command ID
        struct.pack_into('I', _data, 4, 20)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _cproxy_delay_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def domelight_cfg(self, color: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(20)  # Allocate fixed size buffer
        struct.pack_into('I', _data, 16, color)  # Pack color
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 37)  # Command ID
        struct.pack_into('I', _data, 4, 20)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _domelight_cfg_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def domelight_ctrl(self, sta: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(20)  # Allocate fixed size buffer
        struct.pack_into('I', _data, 16, sta)  # Pack sta
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 38)  # Command ID
        struct.pack_into('I', _data, 4, 20)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _domelight_ctrl_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def domelight_init(self, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(16)  # Allocate fixed size buffer
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 39)  # Command ID
        struct.pack_into('I', _data, 4, 16)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _domelight_init_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def infrared_sta(self, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(16)  # Allocate fixed size buffer
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 40)  # Command ID
        struct.pack_into('I', _data, 4, 16)  # Request total length
        struct.pack_into('I', _data, 8, 20)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _infrared_sta_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        state = struct.unpack_from('i', _data, 16)[0]  # Read state
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(state, _ret)
        return _ret

    def lum_cfg(self, lumin: int, light: int, pos: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(28)  # Allocate fixed size buffer
        struct.pack_into('I', _data, 16, lumin)  # Pack lumin
        struct.pack_into('I', _data, 20, light)  # Pack light
        struct.pack_into('I', _data, 24, pos)  # Pack pos
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 41)  # Command ID
        struct.pack_into('I', _data, 4, 28)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _lum_cfg_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def lum_ctrl(self, stat: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(20)  # Allocate fixed size buffer
        struct.pack_into('I', _data, 16, stat)  # Pack stat
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 42)  # Command ID
        struct.pack_into('I', _data, 4, 20)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _lum_ctrl_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def lum_pwr(self, stat: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(20)  # Allocate fixed size buffer
        struct.pack_into('I', _data, 16, stat)  # Pack stat
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 43)  # Command ID
        struct.pack_into('I', _data, 4, 20)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _lum_pwr_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def vibrate_ctrl(self, stat: int, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(20)  # Allocate fixed size buffer
        struct.pack_into('i', _data, 16, stat)  # Pack stat
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 44)  # Command ID
        struct.pack_into('I', _data, 4, 20)  # Request total length
        struct.pack_into('I', _data, 8, 16)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _vibrate_ctrl_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(_ret)
        return _ret

    def vibrate_read(self, _callback: Callable) -> int:
        _idx = self.alloc(_callback)
        if _idx < 0:
            return _idx
        _data = bytearray(16)  # Allocate fixed size buffer
        # Pack protocol header
        struct.pack_into('B', _data, 0, 0xff)  # Type: REQ
        struct.pack_into('B', _data, 1, _idx)  # Index
        struct.pack_into('H', _data, 2, 45)  # Command ID
        struct.pack_into('I', _data, 4, 16)  # Request total length
        struct.pack_into('I', _data, 8, 20)  # Expected response length
        struct.pack_into('I', _data, 12, 0)  # Checksum
        self.prior()  # Prior prepare for write
        self.write(_data)  # Write header and fixed data
        self.flush()  # Flush all data
        return _idx

    def _vibrate_read_hook_(self, _data: bytes, _hook: Callable) -> int:
        if not _data:
            return -1
        cnt = struct.unpack_from('i', _data, 16)[0]  # Read cnt
        _ret = struct.unpack_from('i', _data, 8)[0]  # Request result
        _hook(cnt, _ret)
        return _ret

    _autosdk_func_tb_ = [
        _MdScanner_AppAimDecoVideo_GetInfo_hook_,
        _MdScanner_AppAimDecoVideo_Start_hook_,
        _MdScanner_AppAimDecoVideo_Stop_hook_,
        _MdScanner_ClearUCIPList_hook_,
        _MdScanner_CusROI_CapROIVideo_hook_,
        _MdScanner_CusROI_StpROIVideo_hook_,
        _MdScanner_CusROI_SttROIVideo_hook_,
        _MdScanner_GetCodeResult_hook_,
        _MdScanner_GetImage_hook_,
        _MdScanner_GetImageSizeInfo_hook_,
        _MdScanner_GetROISuccDecoInfo_hook_,
        _MdScanner_GetUCIPList_hook_,
        _MdScanner_GetUCIPVersion_hook_,
        _MdScanner_SetUCIPList_hook_,
        _MdScanner_StartAdjustImagingParaMode_hook_,
        _MdScanner_StartDeco_hook_,
        _MdScanner_StopAdjustImagingParaMode_hook_,
        _MdScanner_StopDeco_hook_,
        _api_switch_ctrl_hook_,
        _beeper_cfg_hook_,
        _beeper_ctrl_hook_,
        _beeper_init_hook_,
        _block_dump_hook_,
        _block_read_hook_,
        _block_seek_hook_,
        _block_wipe_hook_,
        _block_write_hook_,
        _button_wait_hook_,
        _cable_sta_hook_,
        _cam_capt_hook_,
        _cam_capt_gray_hook_,
        _cam_exit_hook_,
        _cam_flash_ctrl_hook_,
        _cam_info_hook_,
        _cam_init_hook_,
        _cam_para_load_hook_,
        _cproxy_delay_hook_,
        _domelight_cfg_hook_,
        _domelight_ctrl_hook_,
        _domelight_init_hook_,
        _infrared_sta_hook_,
        _lum_cfg_hook_,
        _lum_ctrl_hook_,
        _lum_pwr_hook_,
        _vibrate_ctrl_hook_,
        _vibrate_read_hook_,
]
# functions-py <<


# TCP implementation of Cproxy
class CproxyTcp(Cproxy):
    def __init__(self, server_address=('************', 9999)):
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.sock.connect(server_address)
        self.sock.settimeout(1.5)
        self.heartbeat = threading.Timer(1.0, self._heartbeat)
        self.heartbeat.start()
        super().__init__()

    
    def _close_connection(self):
        self.heartbeat.cancel()
        self.sock.close()
    
    def _receive_data(self, length):
        chunk = self.sock.recv(length)  # Receive a chunk of reasonable size
        if not chunk:  # Connection closed
            raise socket.error("Connection closed")
        return chunk
  
    def _receive_view(self, view):
        bytes_read = self.sock.recv_into(view)
        if bytes_read == 0:  # Connection closed
            raise socket.error("Connection closed")
        return bytes_read

    def _send_data(self, data):
        self.heartbeat.cancel()
        ret = self.sock.send(data)
        self.heartbeat = threading.Timer(1.0, self._heartbeat)
        self.heartbeat.start()
        return ret

    def _heartbeat(self):
        try:
            self.sock.sendall(b'\x00'*16)
        except:
            return
        self.heartbeat = threading.Timer(1.0, self._heartbeat)
        self.heartbeat.start()


def _demo_(hand):
    """Demo program: can submit multiple calls simultaneously"""
    results = []
    
    def _delay_callback_1(ret):
        print(f"Delay 1 result: {ret}ms")
        results.append(ret)
    hand.cproxy_delay(1000, _delay_callback_1)
    
    def _delay_callback_2(ret):
        print(f"Delay 2 result: {ret}ms")
        results.append(ret)
    hand.cproxy_delay(500, _delay_callback_2)
    
    def _delay_callback_3(ret):
        print(f"Delay 3 result: {ret}ms")
        results.append(ret)
    hand.cproxy_delay(100, _delay_callback_3)
    
    print("Submitted 3 calls")
    
    # Wait for all callbacks to complete using the new block method
    hand.block(timeout=5.0)  # Wait up to 5 seconds


if __name__ == "__main__":
    with CproxyTcp() as hand:  # Use CproxyTcp instead of Cproxy
        _demo_(hand)
