//=============================================================================
// Copyright (C), 2004-2023, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_version_demo.c
// Author		: LiJZ
// Created On	: 2023/09/14
// Description	: hal_version_demo.c
//
// History
// 1. V1.0, Created by LiJZ. 2023/09/14
//=============================================================================

#include "autocmd.h"
#include "hal_board.h"
#include "product_info.h"

CMD int version(			  /* show version of all components. */
	FOBJ("hello.txt") out /* version output file. */
)
{
	const char *comp_tb[] = {
		"BIOS",					// MD_BOARD_INFO_BIOS_VER
		"LOADER",				// MD_BOARD_INFO_LOADER_VER
		"HARDWARE",				// MD_BOARD_INFO_HARDWARE_VER
		"SERIAL",				// MD_BOARD_INFO_SERIAL_NUM
		"AIROI160x320",			// MD_BOARD_INFO_AIROI160x320_VER
		"AIROI320x160",			// MD_BOARD_INFO_AIROI320x160_VER
		"AIROI320x320",			// MD_BOARD_INFO_AIROI320x320_VER
		"AIROI512x640",			// MD_BOARD_INFO_AIROI512x640_VER
		"AIROI640x512",			// MD_BOARD_INFO_AIROI640x512_VER
		"AIROILEGACY320x320",	// MD_BOARD_INFO_AIROILEGACY320x320_VER
		"AIFINDER320",			// MD_BOARD_INFO_AIFINDER320_VER
		"AIFINDER640",			// MD_BOARD_INFO_AIFINDER640_VER
		"AIDOT320",				// MD_BOARD_INFO_AIDOT320_VER
		"AIOCRDET320",			// MD_BOARD_INFO_AIOCRDET320_VER
		"AIOCRREC960",			// MD_BOARD_INFO_AIOCRREC960_48_VER
		"AICAPDET",				// MD_BOARD_INFO_AICAPDET_VER
		"AIRESTORER",			// MD_BOARD_INFO_AIRESTORER_VER
		"KERNEL",				// MD_BOARD_INFO_KERNEL_VER
		"ROOTFS",				// MD_BOARD_INFO_ROOTFS_VER
		"DRIVERCIS",			// MD_BOARD_INFO_DRIVERCIS_VER
		"DRIVERMAIN",			// MD_BOARD_INFO_DRIVERMAIN_VER
		"UGTOOL",				// MD_BOARD_INFO_UGTOOL_VER
		"STARTUP",				// MD_BOARD_INFO_STARTUP_VER
		"PACKER",				// MD_BOARD_INFO_APP_PACK_VER
		"SCANOS",				// MD_BOARD_INFO_SCANOS_VER
	};
	ProductVerFormat_t info;
	for (int i = 0; i < MD_BOARD_INFO_SCANOS_VER; i++)
	{
		HAL_Board_Info(&info, i);
		if (info.digit_1 == -1)
		{
			// version not found
			fobj_print(out, "%-12s: unknown %s\n", comp_tb[i], info.orign_ver);
		}
		else
		{
			// version found
			fobj_print(out, "%-12s: V%d.%d.%d.%d %s\n", comp_tb[i], info.digit_1, info.digit_2, info.digit_3,
				info.digit_4, info.orign_ver);
		}
	}
	extern u8 MD_H_Version_Info[];
	fobj_print(out, "SCANOS      : %s\n", MD_H_Version_Info);
	return 0;
}
