#!python
import sys
import time
import numpy as np
from PIL import Image
from cproxy import CproxyTcp

class MEcho:
    RED = '\033[31m'
    GREEN = '\033[32m'
    YELLOW = '\033[33m'
    BLUE = '\033[34m'
    PURPLE = '\033[35m'
    GRAY = '\033[37m'
    RESET = '\033[0m'

    ERROR = 0x01
    WARNING = 0x02
    INFO = 0x04
    DEBUG = 0x08
    VERBOSE = 0x10
    LINE = 0x20
    RAW = 0x40

    level = ERROR | WARNING | INFO | LINE

    @staticmethod
    def _print_with_color(color, message, level):
        if MEcho.level & level:
            print(color + message + MEcho.RESET)

    @staticmethod
    def _stdout_with_color(color, data, level):
        if MEcho.level & level:
            sys.stdout.write(color + data + MEcho.RESET)
            sys.stdout.flush()

    @staticmethod
    def e(message):
        MEcho._print_with_color(MEcho.RED, "[E]" + message, MEcho.ERROR)

    @staticmethod
    def w(message):
        MEcho._print_with_color(MEcho.YELLOW, "[W]" + message, MEcho.WARNING)

    @staticmethod
    def i(message):
        MEcho._print_with_color(MEcho.GREEN, "[I]" + message, MEcho.INFO)

    @staticmethod
    def d(message):
        MEcho._print_with_color(MEcho.BLUE, "[D]" + message, MEcho.DEBUG)

    @staticmethod
    def v(message):
        MEcho._print_with_color(MEcho.GRAY, "[V]" + message, MEcho.VERBOSE)

    @staticmethod
    def l(size):
        MEcho._stdout_with_color(MEcho.GREEN, "-" * size + "\n", MEcho.LINE)

    @staticmethod
    def r(data):
        MEcho._stdout_with_color(MEcho.PURPLE, data, MEcho.RAW)


def _confirm_(question):
    yn = input(f"{question}[Y/n]")
    return yn.lower() != "n"


def _show_image_(prefix, num, width=1024, height=1280, raw=10):
    raw = raw - 8
    result_image = Image.new("RGB", (width * num, height))
    for i in range(num):
        with open(f"{prefix}{i}.raw", "rb") as f:
            raw_data = np.frombuffer(f.read(), dtype=np.uint16)
        image_array = raw_data.reshape((height, width)) >> raw
        image = Image.fromarray(image_array.astype(np.uint8))
        result_image.paste(image, (width * i, 0))
    result_image.show()


################################################################################
def _tcase_switch_(func):
    def wrapper(hand: CproxyTcp):
        tb = [
                ("on", 1,),
                ("off", 0,),
        ]
        def _callback_(ret):
            nonlocal okay
            okay = _confirm_(f"{name} {sta_str}?")
        pos, name = func()
        for sta_str, sta in tb:
            okay = False
            hand.api_switch_ctrl(pos if sta else 0, _callback_)
            assert hand.block(timeout=1.0)
            assert okay
    wrapper.__name__ = func.__name__
    return wrapper


@_tcase_switch_
def tcase_blink():
    return 0x80, "blink"


@_tcase_switch_
def tcase_gled():
    return 0x01, "green led"


@_tcase_switch_
def tcase_bled():
    return 0x02, "blue led"


@_tcase_switch_
def tcase_rled():
    return 0x04, "red led"


@_tcase_switch_
def tcase_aim():
    return 0x08, "aim"


@_tcase_switch_
def tcase_eas_a():
    return 0x10, "eas-a"


@_tcase_switch_
def tcase_eas_b():
    return 0x20, "eas-b"


@_tcase_switch_
def tcase_vibrator():
    return 0x40, "vibrator"


@_tcase_switch_
def tcase_curr_limit():
    return 0x100, "curr_limit"


@_tcase_switch_
def tcase_pwr_led():
    return 0x200, "power led"


def tcase_beeper(hand: CproxyTcp):
    def _callback_(ret):
        pass
    hand.beeper_init(_callback_)
    hand.block(timeout=1.0)
    hand.beeper_cfg(2400, 0, _callback_)
    hand.block(timeout=1.0)
    for i in range(0, 100, 10):
        hand.beeper_cfg(2400, i, _callback_)
        hand.block(timeout=1.0)
        for _ in range(2):
            hand.beeper_ctrl(1, _callback_)
            hand.block(timeout=1.0)
            time.sleep(0.1)
            hand.beeper_ctrl(0, _callback_)
            time.sleep(0.1)
        time.sleep(0.5)
    hand.beeper_ctrl(0, _callback_)
    assert _confirm_("the volume of the beeper swept correctly?")
    hand.block(timeout=1.0)

    hand.beeper_cfg(20, 10, _callback_)
    hand.block(timeout=1.0)
    hand.beeper_ctrl(1, _callback_)
    hand.block(timeout=1.0)
    for i in range(100):
        hand.beeper_cfg(50 * i + 20, 10, _callback_)
        time.sleep(0.01)
    hand.beeper_ctrl(0, _callback_)
    assert _confirm_("the tone of the beeper swept correctly?")
    hand.block(timeout=1.0)


def _tcase_lumin_(func):
    def wrapper(hand: CproxyTcp):
        def _callback_(ret):
            pass
        lumin, pos, name = func()
        hand.lum_pwr(lumin, _callback_)
        hand.lum_cfg(lumin, 0, pos, _callback_)
        hand.block(timeout=1.0)
        for _ in range(3):
            for i in list(range(16)) + list(reversed(range(16))):
                hand.lum_cfg(lumin, i, pos, _callback_)
                hand.block(timeout=1.0)
                hand.lum_ctrl(1, _callback_)
                hand.block(timeout=1.0)
                time.sleep(0.001)
                hand.lum_ctrl(0, _callback_)
                hand.block(timeout=1.0)
                time.sleep(0.009)
        hand.lum_pwr(0, _callback_)
        assert _confirm_(f"{name} lumin breathing correctly?")
        hand.block(timeout=1.0)
    wrapper.__name__ = func.__name__
    return wrapper


@_tcase_lumin_
def tcase_lumin_red():
    return 0x1, 0x3, "red"


@_tcase_lumin_
def tcase_lumin_white():
    return 0x1, 0xC, "white"

@_tcase_lumin_
def tcase_lumin_ir():
    return 0x2, 0x0, "ir"


def _tcase_camera_(func):
    def wrapper(hand: CproxyTcp):
        cam = {
            "width": 0,
            "height": 0,
            "format": 0,
        }
        def _callback_info_(width, height, format, ret):
            cam["width"] = width
            cam["height"] = height
            cam["format"] = format
        def _callback_(ret): pass
        hand.cam_init(0, _callback_)
        assert hand.block(timeout=1.0)
        hand.lum_pwr(1, _callback_)
        assert hand.block(timeout=1.0)
        hand.lum_cfg(1, 20, 0x3, _callback_)
        assert hand.block(timeout=1.0)
        hand.cam_info(_callback_info_)
        assert hand.block(timeout=1.0)
        func(hand, cam)
        hand.lum_ctrl(0, _callback_)
        assert hand.block(timeout=1.0)
        hand.lum_pwr(0, _callback_)
        assert hand.block(timeout=1.0)
    wrapper.__name__ = func.__name__
    return wrapper


@_tcase_camera_
def tcase_camera_expo(hand: CproxyTcp, cam: dict):
    def _callback_(ret):
        pass
    def _callback_capt_(out, ret):
        with open(f"expo{i}.raw", "wb") as f:
            f.write(out)
    hand.cam_flash_ctrl(
        seq=0,
        aim=1,
        lum=1,
        irf=0,
        cycl=16000,
        glow=2100,
        ldly=100,
        ddly=0,
        cap_cycl=0,
        aim_line=0,
        _callback=_callback_,
    )
    assert hand.block(timeout=1.0)
    for i in range(10):
        hand.cam_para_load(100 * i, 3, 0, 0, _callback_)
        assert hand.block(timeout=1.0)
        image_size = cam["width"] * cam["height"] * (2 if cam["format"] > 8 else 1)
        hand.cam_capt(1, image_size, _callback_capt_)
        assert hand.block(timeout=1.0)
    hand.cam_exit(_callback_)
    _show_image_("expo", 10, cam["width"], cam["height"], cam["format"])
    assert _confirm_("the images are getting brighter?")
    hand.block(timeout=1.0)


@_tcase_camera_
def tcase_camera_gain(hand: CproxyTcp, cam: dict):
    def _callback_(ret): pass
    def _callback_capt_(out, ret): pass
    def _callback_save_capt_(out, ret):
        if ret < 0: return
        with open(f"gain{i}.raw", "wb") as f:
            f.write(out)
    hand.cam_flash_ctrl(
        seq=0,
        aim=1,
        lum=1,
        irf=0,
        cycl=16000,
        glow=300,
        ldly=100,
        ddly=0,
        cap_cycl=0,
        aim_line=0,
        _callback=_callback_,
    )
    assert hand.block(timeout=1.0)
    image_size = cam["width"] * cam["height"] * (2 if cam["format"] > 8 else 1)
    for i in range(8):
        hand.cam_para_load(200, i, 0, 0, _callback_)
        assert hand.block(timeout=1.0)
        hand.cam_capt(1, image_size, _callback_capt_)
        assert hand.block(timeout=1.0)
        hand.cam_capt(1, image_size, _callback_save_capt_)
        assert hand.block(timeout=1.0)
    hand.cam_exit(_callback_)
    _show_image_("gain", 8, cam["width"], cam["height"], cam["format"])
    assert _confirm_("the images are getting brighter?")
    hand.block(timeout=1.0)


def _tcase_button_(func):
    def wrapper(hand: CproxyTcp):
        okay = False
        tb = [
            (2, "press"),
            (1, "release"),
        ]
        def _callback_(event, ret):
            nonlocal okay
            okay = (ret == 0 and event == ev)
        idx, name = func()
        for ev, ev_name in tb:
            okay = False
            print(f"please {ev_name} {name} within 10s.")
            hand.button_wait(idx, 10000, _callback_)
            hand.block(timeout=10)
            assert okay
    wrapper.__name__ = func.__name__
    return wrapper


@_tcase_button_
def tcase_button_main():
    return 0, "main button"


@_tcase_button_
def tcase_button_auxi():
    return 1, "auxi button"


def tcase_cable(hand: CproxyTcp):
    tb = [
        (0, "usb custom"),
        (2, "usb hid"),
        (3, "rs232"),
    ]
    def _callback_(type, ret):
        nonlocal okay
        okay = (ret == 0 and type == typ)
    for typ, name in tb:
        okay = False
        input(f"please switch to {name} cable manually, then press enter to continue.")
        hand.cable_sta(_callback_)
        hand.block(timeout=1.0)
        assert okay


def tcase_domelight(hand: CproxyTcp):
    def _callback_(ret):
        pass
    hand.domelight_init(_callback_)
    hand.block(timeout=1.0)
    for i in range(3):
        for j in list(range(64)) + list(reversed(range(64))):
            hand.domelight_cfg(j<<(i*8), _callback_)
            for _ in range(5):
                hand.domelight_ctrl(1, _callback_)
                assert hand.block(timeout=1.0)
                time.sleep(0.001)
                hand.domelight_ctrl(0, _callback_)
                assert hand.block(timeout=1.0)
                time.sleep(0.004)
    hand.domelight_cfg(0, _callback_)
    hand.block(timeout=1.0)
    assert _confirm_("domelight breathing correctly?")


def tcase_infrared(hand: CproxyTcp):
    tb = [
        (1, "Please place the object in the detection area, then press enter to continue."),
        (0, "Please remove the object from the detection area, then press enter to continue."),
    ]
    def _callback_(state, ret):
        nonlocal okay
        okay = (ret == 0 and state == sta)
    for sta, desc in tb:
        okay = False
        input(desc)
        hand.infrared_sta(_callback_)
        hand.block(timeout=1.0)
        assert okay


def tcase_vibrate(hand: CproxyTcp):
    tb = [
        (False, "Please keep the device stationary, test start after press enter."),
        (True, "Please shake the device, test start after press enter."),
    ]
    def _callback_ctrl_(ret): pass
    def _callback_read_(cnt, ret):
        nonlocal okay
        okay = (ret == 0 and ((cnt != 0) == exp))
    for exp, desc in tb:
        okay = False
        input(desc)
        hand.vibrate_ctrl(1, _callback_ctrl_)
        hand.block(timeout=1.0)
        time.sleep(1.0)
        hand.vibrate_read(_callback_read_)
        hand.block(timeout=1.0)
        hand.vibrate_ctrl(0, _callback_ctrl_)
        hand.block(timeout=1.0)
        assert okay


################################################################################

def main():
    context = {
        name: obj
        for name, obj in globals().items()
        if callable(obj) and name.startswith("tcase_")
    }
    if len(sys.argv) > 1 and "--list" in sys.argv:
        for name, func in context.items():
            print(f"{name[6::]}")
        return
    with CproxyTcp() as hand:
        results = []
        for name, func in context.items():
            if len(sys.argv) > 1 and not name[6::] in sys.argv:
                if not name[6::] in sys.argv:
                    if not name[6::].split("_")[0] in sys.argv:
                        continue
            try:
                skip = input(f"{name}, skip?[y/N]")
                if skip.lower() == "y":
                    results.append((name, "skip"))
                    continue
                func(hand)
                results.append((name, "pass"))
            except KeyboardInterrupt:
                break
            except AssertionError as e:
                results.append((name, "fail"))
        nfail, npass, nskip = 0, 0, 0
        print()
        for idx, (name, ok) in enumerate(results):
            if ok == "pass":
                MEcho.i(f"{idx:03}:{name:.<60}pass")
                npass += 1
            elif ok == "fail":
                MEcho.e(f"{idx:03}:{name:.<60}fail")
                nfail += 1
            else:
                MEcho.w(f"{idx:03}:{name:.<60}skip")
                nskip += 1
        MEcho.l(80)
        MEcho.i(f"total {len(results)}, pass {npass}, fail {nfail}, skip {nskip}")


if __name__ == '__main__':
    main()