// Auto generated by cpropr.py, DO NOT EDIT!

#ifndef __CPROPR_CFG_H__
#define __CPROPR_CFG_H__

// Property IDs
#define PROP_AIMDECOVIDEOSETDECOTIMEMS 0 // AimDecoVideoSetDecoTimeMs
#define PROP_ETHERNETIPOUTPUTEN 1 // EthernetIpOutputEn
#define PROP_MODBUSSVROUTPUTEN 2 // ModbusSvrOutputEn
#define PROP_PROFINETOUTPUTENMAIN 3 // ProfinetOutputEnMain
#define PROP_PROFINETSTATIONNAME 4 // ProfinetStationName
#define PROP_TCPCLIOUTPUTENMAIN 5 // TCPCliOutputEnMain
#define PROP_TCPCLITARGETSVRIP 6 // TCPCliTargetSvrIP
#define PROP_TCPCLITARGETSVRPORT 7 // TCPCliTargetSvrPort
#define PROP_TCPCLIOUTPUTFMT 8 // TCPCliOutputFmt
#define PROP_TCPCLIOUTPUTPREFIX 9 // TCPCliOutputPrefix
#define PROP_TCPCLIOUTPUTSUFFIX 10 // TCPCliOutputSuffix
#define PROP_TCPSVROUTPUTENABLEMAIN 11 // TCPSvrOutputEnableMain
#define PROP_TCPSVROUTPUTFMT 12 // TCPSvrOutputFmt
#define PROP_TCPSVROUTPUTPREFIX 13 // TCPSvrOutputPrefix
#define PROP_TCPSVROUTPUTSUFFIX 14 // TCPSvrOutputSuffix
#define PROP_TCPSVRSERVICEPORT 15 // TCPSvrServicePort
#define PROP_UARTENABLE 16 // UartEnable
#define PROP_USBETHERNETCOMPOSITEENMAIN 17 // USBEthernetCompositeEnMain
#define PROP_USBETHERNETCOMPOSITETYPE 18 // USBEthernetCompositeType
#define PROP_CUSTOMIO1ACTIVECMD 19 // CustomIO1ActiveCmd
#define PROP_CUSTOMIO2ACTIVECMD 20 // CustomIO2ActiveCmd
#define PROP_CUSTOMIO3ACTIVECMD 21 // CustomIO3ActiveCmd
#define PROP_CUSTOMIO4ACTIVECMD 22 // CustomIO4ActiveCmd
#define PROP_CUSTOMIO1INACTIVECMD 23 // CustomIO1InactiveCmd
#define PROP_CUSTOMIO2INACTIVECMD 24 // CustomIO2InactiveCmd
#define PROP_CUSTOMIO3INACTIVECMD 25 // CustomIO3InactiveCmd
#define PROP_CUSTOMIO4INACTIVECMD 26 // CustomIO4InactiveCmd
#define PROP_CUSTOMIO1MODEMAIN 27 // CustomIO1ModeMain
#define PROP_CUSTOMIO2MODEMAIN 28 // CustomIO2ModeMain
#define PROP_CUSTOMIO3MODEMAIN 29 // CustomIO3ModeMain
#define PROP_CUSTOMIO4MODEMAIN 30 // CustomIO4ModeMain
#define PROP_CUSTOMIO1INPUTCOUNT 31 // CustomIO1InputCount
#define PROP_CUSTOMIO2INPUTCOUNT 32 // CustomIO2InputCount
#define PROP_CUSTOMIO3INPUTCOUNT 33 // CustomIO3InputCount
#define PROP_CUSTOMIO4INPUTCOUNT 34 // CustomIO4InputCount
#define PROP_CUSTOMIO1DEBOUNCE 35 // CustomIO1Debounce
#define PROP_CUSTOMIO2DEBOUNCE 36 // CustomIO2Debounce
#define PROP_CUSTOMIO3DEBOUNCE 37 // CustomIO3Debounce
#define PROP_CUSTOMIO4DEBOUNCE 38 // CustomIO4Debounce
#define PROP_CUSTOMIO1RISINGACTION 39 // CustomIO1RisingAction
#define PROP_CUSTOMIO2RISINGACTION 40 // CustomIO2RisingAction
#define PROP_CUSTOMIO3RISINGACTION 41 // CustomIO3RisingAction
#define PROP_CUSTOMIO4RISINGACTION 42 // CustomIO4RisingAction
#define PROP_CUSTOMIO1RISINGDELAY 43 // CustomIO1RisingDelay
#define PROP_CUSTOMIO2RISINGDELAY 44 // CustomIO2RisingDelay
#define PROP_CUSTOMIO3RISINGDELAY 45 // CustomIO3RisingDelay
#define PROP_CUSTOMIO4RISINGDELAY 46 // CustomIO4RisingDelay
#define PROP_CUSTOMIO1FALLINGACTION 47 // CustomIO1FallingAction
#define PROP_CUSTOMIO2FALLINGACTION 48 // CustomIO2FallingAction
#define PROP_CUSTOMIO3FALLINGACTION 49 // CustomIO3FallingAction
#define PROP_CUSTOMIO4FALLINGACTION 50 // CustomIO4FallingAction
#define PROP_CUSTOMIO1FALLINGDELAY 51 // CustomIO1FallingDelay
#define PROP_CUSTOMIO2FALLINGDELAY 52 // CustomIO2FallingDelay
#define PROP_CUSTOMIO3FALLINGDELAY 53 // CustomIO3FallingDelay
#define PROP_CUSTOMIO4FALLINGDELAY 54 // CustomIO4FallingDelay
#define PROP_CUSTOMIO1ACTIVECONDITION 55 // CustomIO1ActiveCondition
#define PROP_CUSTOMIO2ACTIVECONDITION 56 // CustomIO2ActiveCondition
#define PROP_CUSTOMIO3ACTIVECONDITION 57 // CustomIO3ActiveCondition
#define PROP_CUSTOMIO4ACTIVECONDITION 58 // CustomIO4ActiveCondition
#define PROP_CUSTOMIO1INACTIVECONDITION 59 // CustomIO1InactiveCondition
#define PROP_CUSTOMIO2INACTIVECONDITION 60 // CustomIO2InactiveCondition
#define PROP_CUSTOMIO3INACTIVECONDITION 61 // CustomIO3InactiveCondition
#define PROP_CUSTOMIO4INACTIVECONDITION 62 // CustomIO4InactiveCondition
#define PROP_CUSTOMIO1ACTIVEDELAY 63 // CustomIO1ActiveDelay
#define PROP_CUSTOMIO2ACTIVEDELAY 64 // CustomIO2ActiveDelay
#define PROP_CUSTOMIO3ACTIVEDELAY 65 // CustomIO3ActiveDelay
#define PROP_CUSTOMIO4ACTIVEDELAY 66 // CustomIO4ActiveDelay
#define PROP_CUSTOMIO1INACTIVETIMER 67 // CustomIO1InactiveTimer
#define PROP_CUSTOMIO2INACTIVETIMER 68 // CustomIO2InactiveTimer
#define PROP_CUSTOMIO3INACTIVETIMER 69 // CustomIO3InactiveTimer
#define PROP_CUSTOMIO4INACTIVETIMER 70 // CustomIO4InactiveTimer
#define PROP_NETLED 71 // NetLed

// Enum values for USBEthernetCompositeType
typedef enum {
    USBETHERNETCOMPOSITETYPE_E_Keyboard = 0,
    USBETHERNETCOMPOSITETYPE_E_VCOM = 1,
} USBETHERNETCOMPOSITETYPE_E;

// Enum values for CustomIOModeMain
typedef enum {
    CUSTOMIOMODEMAIN_E_Close = 0,
    CUSTOMIOMODEMAIN_E_InputHigh = 1,
    CUSTOMIOMODEMAIN_E_InputLow = 2,
    CUSTOMIOMODEMAIN_E_OutputHigh = 3,
    CUSTOMIOMODEMAIN_E_OutputLow = 4,
} CUSTOMIOMODEMAIN_E;

// Enum values for CustomIORisingAction
typedef enum {
    CUSTOMIORISINGACTION_E_None = 0,
    CUSTOMIORISINGACTION_E_StartDecode = 1,
    CUSTOMIORISINGACTION_E_EndDecode = 2,
} CUSTOMIORISINGACTION_E;

// Enum values for CustomIOFallingAction
typedef enum {
    CUSTOMIOFALLINGACTION_E_None = 0,
    CUSTOMIOFALLINGACTION_E_StartDecode = 1,
    CUSTOMIOFALLINGACTION_E_EndDecode = 2,
} CUSTOMIOFALLINGACTION_E;

// Enum values for CustomIOActiveCondition
typedef enum {
    CUSTOMIOACTIVECONDITION_E_None = 0,
    CUSTOMIOACTIVECONDITION_E_DecodeSuccess = 1,
    CUSTOMIOACTIVECONDITION_E_NoRead = 2,
    CUSTOMIOACTIVECONDITION_E_DecodeStarted = 3,
    CUSTOMIOACTIVECONDITION_E_DecodeStopped = 4,
    CUSTOMIOACTIVECONDITION_E_Command = 5,
    CUSTOMIOACTIVECONDITION_E_Timer = 6,
    CUSTOMIOACTIVECONDITION_E_Input1High = 7,
    CUSTOMIOACTIVECONDITION_E_Input2High = 8,
    CUSTOMIOACTIVECONDITION_E_Input3High = 9,
    CUSTOMIOACTIVECONDITION_E_Input4High = 10,
    CUSTOMIOACTIVECONDITION_E_Input1Low = 11,
    CUSTOMIOACTIVECONDITION_E_Input2Low = 12,
    CUSTOMIOACTIVECONDITION_E_Input3Low = 13,
    CUSTOMIOACTIVECONDITION_E_Input4Low = 14,
} CUSTOMIOACTIVECONDITION_E;

// Enum values for CustomIOInactiveCondition
typedef enum {
    CUSTOMIOINACTIVECONDITION_E_None = 0,
    CUSTOMIOINACTIVECONDITION_E_DecodeSuccess = 1,
    CUSTOMIOINACTIVECONDITION_E_NoRead = 2,
    CUSTOMIOINACTIVECONDITION_E_DecodeStarted = 3,
    CUSTOMIOINACTIVECONDITION_E_DecodeStopped = 4,
    CUSTOMIOINACTIVECONDITION_E_Command = 5,
    CUSTOMIOINACTIVECONDITION_E_Timer = 6,
    CUSTOMIOINACTIVECONDITION_E_Input1High = 7,
    CUSTOMIOINACTIVECONDITION_E_Input2High = 8,
    CUSTOMIOINACTIVECONDITION_E_Input3High = 9,
    CUSTOMIOINACTIVECONDITION_E_Input4High = 10,
    CUSTOMIOINACTIVECONDITION_E_Input1Low = 11,
    CUSTOMIOINACTIVECONDITION_E_Input2Low = 12,
    CUSTOMIOINACTIVECONDITION_E_Input3Low = 13,
    CUSTOMIOINACTIVECONDITION_E_Input4Low = 14,
} CUSTOMIOINACTIVECONDITION_E;

// Enum values for NetLed
typedef enum {
    NETLED_E_LINK = 1,
    NETLED_E_TX = 2,
    NETLED_E_RX = 4,
} NETLED_E;

#endif // __CPROPR_CFG_H__
