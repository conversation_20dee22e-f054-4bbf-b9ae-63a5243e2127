//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_mdcable_demo.c
// Author		: LiJZ
// Created On	: 2024/01/12
// Description	: hal_mdcable_demo.c
//
// History
// 1. V1.0, Created by XuCF. 2024/01/12
//=============================================================================
#define MACR_LOG_LEVEL 5

// Code head Top
#include "autocmd.h"
#include "autosdk_server.h"
#include "device/hal_cable.h"
// Code head Bot

// Code static Top
static str _cable_name_(int type)
{
	str name;
	switch (type)
	{
		case MACR_CABLE_UART: name = "uart"; break;
		case MACR_CABLE_HID_USB: name = "usb-fs"; break;
		case MACR_CABLE_PS2: name = "ps2"; break;
		case MACR_CABLE_CUSTOM_USB: name = "usb-hs"; break;
		default: name = "unkown"; break;
	}
	return name;
}

CMD int demo_cable(void) /* Cable detect. */
{
	s32 err = 0, type = MACR_CABLE_NONE;
	Cable *cable = HAL_Device_Find(MD_DEVICE_CABLE);
	Assert(!cable, return -1);
	type = HAL_Cable_DetectAndFit(cable);
	clike_print("cable type = %s\n", _cable_name_(type));
	return err;
}

API int cable_sta( /* Cable detect. */
	U32(O) type	/* cable type */
)
{
	*type = HAL_Cable_DetectAndFit(HAL_Device_Find(MD_DEVICE_CABLE));
	LOGD("cable type = %s\n", _cable_name_(*type));
	return AUTOSDK_STAT_DONE;
}

// Code static Bot
