// Auto generated by autocmd.py, don't edit
#include "autocmd.h"
code_t cam_stability_test(void);
code_t cam_take_cont(numb_t expo, numb_t gain, numb_t count);
code_t cam_take_norm(numb_t expo, numb_t gain, numb_t count);
code_t cam_take_roll(numb_t expo, numb_t gain, numb_t count);
code_t demo_battery(void);
code_t demo_block(void);
code_t demo_cable(void);
code_t demo_delay(void);
code_t demo_event(void);
code_t demo_filesys(text_t src, text_t dst);
code_t demo_gvcp(void);
code_t demo_hrtime(void);
code_t demo_infrared(void);
code_t demo_mqueue(void);
code_t demo_net_monitor(void);
code_t demo_pcre(text_t text, text_t pattern);
code_t demo_reset(void);
code_t demo_service(void);
code_t demo_set_gateway(text_t gw);
code_t demo_set_mac_addr(text_t mac);
code_t demo_set_mask(text_t mask);
code_t demo_setup_ip(text_t ip);
code_t demo_simple_notice(void);
code_t demo_systick(code_t cycle, code_t timeout);
code_t demo_task_loop(void);
code_t demo_thread(void);
code_t demo_thread_dump(void);
code_t demo_ticker(void);
code_t demo_tmr(void);
code_t demo_wdt(numb_t in, numb_t ex);
code_t domelight_breath(numb_t time);
code_t pin_get(code_t pin_id);
code_t pin_irq(code_t pin_id, numb_t irq, numb_t time);
code_t pin_set(code_t pin_id, numb_t val, numb_t timeout);
code_t speaker_play(text_t wav);
code_t switch_ctrl(numb_t leds);
code_t test_cblock_advanced(void);
code_t test_cblock_basic(void);
code_t test_events(void);
code_t test_notice_auto_detach(void);
code_t test_notice_basic(void);
code_t test_notice_error_handling(void);
code_t test_notice_external_detach(void);
code_t test_poller_delete(void);
code_t test_recycler_advanced(void);
code_t test_recycler_basic(void);
code_t test_recycler_stress(void);
code_t test_task(void);
code_t test_thread_detach(void);
code_t test_ticker_kill(void);
code_t test_waiter(void);
code_t uart_recv(numb_t uart, numb_t baud, numb_t bits, numb_t pari, numb_t timeout);
code_t uart_send(numb_t uart, text_t info, numb_t baud, numb_t bits, numb_t pari);
code_t usb_cancel(numb_t ch, numb_t pid, numb_t vid, numb_t rev, text_t name, text_t serial);
code_t usb_enum(numb_t ch, numb_t pid, numb_t vid, numb_t rev, text_t name, text_t serial);
code_t usb_read(numb_t ch, numb_t pid, numb_t vid, numb_t rev, text_t name, text_t serial);
code_t usb_write(numb_t ch, text_t info, numb_t pid, numb_t vid, numb_t rev, text_t name, text_t serial);
code_t version(fobj_t out);
static code_t _cam_stability_test_cwrp_(void)
{
	code_t ret;
	ret = cam_stability_test();
	return ret;
}
static code_t _cam_take_cont_cwrp_(void)
{
	code_t ret;
	numb_t expo = cmdparser_get_num("expo", 300);
	numb_t gain = cmdparser_get_num("gain", 4);
	numb_t count = cmdparser_get_num("count", 5);
	ret = cam_take_cont(expo, gain, count);
	return ret;
}
static code_t _cam_take_norm_cwrp_(void)
{
	code_t ret;
	numb_t expo = cmdparser_get_num("expo", 300);
	numb_t gain = cmdparser_get_num("gain", 4);
	numb_t count = cmdparser_get_num("count", 5);
	ret = cam_take_norm(expo, gain, count);
	return ret;
}
static code_t _cam_take_roll_cwrp_(void)
{
	code_t ret;
	numb_t expo = cmdparser_get_num("expo", 300);
	numb_t gain = cmdparser_get_num("gain", 4);
	numb_t count = cmdparser_get_num("count", 5);
	ret = cam_take_roll(expo, gain, count);
	return ret;
}
static code_t _demo_battery_cwrp_(void)
{
	code_t ret;
	ret = demo_battery();
	return ret;
}
static code_t _demo_block_cwrp_(void)
{
	code_t ret;
	ret = demo_block();
	return ret;
}
static code_t _demo_cable_cwrp_(void)
{
	code_t ret;
	ret = demo_cable();
	return ret;
}
static code_t _demo_delay_cwrp_(void)
{
	code_t ret;
	ret = demo_delay();
	return ret;
}
static code_t _demo_event_cwrp_(void)
{
	code_t ret;
	ret = demo_event();
	return ret;
}
static code_t _demo_filesys_cwrp_(void)
{
	code_t ret;
	text_t src = cmdparser_get_txt("src", "hello-src.txt");
	text_t dst = cmdparser_get_txt("dst", "hello-dst.txt");
	ret = demo_filesys(src, dst);
	return ret;
}
static code_t _demo_gvcp_cwrp_(void)
{
	code_t ret;
	ret = demo_gvcp();
	return ret;
}
static code_t _demo_hrtime_cwrp_(void)
{
	code_t ret;
	ret = demo_hrtime();
	return ret;
}
static code_t _demo_infrared_cwrp_(void)
{
	code_t ret;
	ret = demo_infrared();
	return ret;
}
static code_t _demo_mqueue_cwrp_(void)
{
	code_t ret;
	ret = demo_mqueue();
	return ret;
}
static code_t _demo_net_monitor_cwrp_(void)
{
	code_t ret;
	ret = demo_net_monitor();
	return ret;
}
static code_t _demo_pcre_cwrp_(void)
{
	code_t ret;
	text_t text = cmdparser_get_txt("text", "This is a test string.");
	text_t pattern = cmdparser_get_txt("pattern", "(\\w+\\s+\\w+)\\s+(\\w+\\s+\\w+)\\s+(\\w+)");
	ret = demo_pcre(text, pattern);
	return ret;
}
static code_t _demo_reset_cwrp_(void)
{
	code_t ret;
	ret = demo_reset();
	return ret;
}
static code_t _demo_service_cwrp_(void)
{
	code_t ret;
	ret = demo_service();
	return ret;
}
static code_t _demo_set_gateway_cwrp_(void)
{
	code_t ret;
	text_t gw = cmdparser_get_txt("gw", "0.0.0.0");
	ret = demo_set_gateway(gw);
	return ret;
}
static code_t _demo_set_mac_addr_cwrp_(void)
{
	code_t ret;
	text_t mac = cmdparser_get_txt("mac", "00:30:1B:BA:02:DB");
	ret = demo_set_mac_addr(mac);
	return ret;
}
static code_t _demo_set_mask_cwrp_(void)
{
	code_t ret;
	text_t mask = cmdparser_get_txt("mask", "*************");
	ret = demo_set_mask(mask);
	return ret;
}
static code_t _demo_setup_ip_cwrp_(void)
{
	code_t ret;
	text_t ip = cmdparser_get_txt("ip", "auto");
	ret = demo_setup_ip(ip);
	return ret;
}
static code_t _demo_simple_notice_cwrp_(void)
{
	code_t ret;
	ret = demo_simple_notice();
	return ret;
}
static code_t _demo_systick_cwrp_(void)
{
	code_t ret;
	code_t cycle = cmdparser_get_num("cycle", 1000);
	code_t timeout = cmdparser_get_num("timeout", 1500);
	ret = demo_systick(cycle, timeout);
	return ret;
}
static code_t _demo_task_loop_cwrp_(void)
{
	code_t ret;
	ret = demo_task_loop();
	return ret;
}
static code_t _demo_thread_cwrp_(void)
{
	code_t ret;
	ret = demo_thread();
	return ret;
}
static code_t _demo_thread_dump_cwrp_(void)
{
	code_t ret;
	ret = demo_thread_dump();
	return ret;
}
static code_t _demo_ticker_cwrp_(void)
{
	code_t ret;
	ret = demo_ticker();
	return ret;
}
static code_t _demo_tmr_cwrp_(void)
{
	code_t ret;
	ret = demo_tmr();
	return ret;
}
static code_t _demo_wdt_cwrp_(void)
{
	code_t ret;
	numb_t in = cmdparser_get_num("in", 1000);
	numb_t ex = cmdparser_get_num("ex", 500);
	ret = demo_wdt(in, ex);
	return ret;
}
static code_t _domelight_breath_cwrp_(void)
{
	code_t ret;
	numb_t time = cmdparser_get_num("time", 10000);
	ret = domelight_breath(time);
	return ret;
}
static code_t _pin_get_cwrp_(void)
{
	code_t ret;
	code_t pin_id = cmdparser_get_num("pin_id", -1);
	ret = pin_get(pin_id);
	return ret;
}
static code_t _pin_irq_cwrp_(void)
{
	code_t ret;
	code_t pin_id = cmdparser_get_num("pin_id", -1);
	numb_t irq = cmdparser_get_num("irq", 6);
	numb_t time = cmdparser_get_num("time", 5);
	ret = pin_irq(pin_id, irq, time);
	return ret;
}
static code_t _pin_set_cwrp_(void)
{
	code_t ret;
	code_t pin_id = cmdparser_get_num("pin_id", -1);
	numb_t val = cmdparser_get_num("val", 1);
	numb_t timeout = cmdparser_get_num("timeout", 1);
	ret = pin_set(pin_id, val, timeout);
	return ret;
}
static code_t _speaker_play_cwrp_(void)
{
	code_t ret;
	text_t wav = cmdparser_get_txt("wav", "mindeo.wav");
	ret = speaker_play(wav);
	return ret;
}
static code_t _switch_ctrl_cwrp_(void)
{
	code_t ret;
	numb_t leds = cmdparser_get_num("leds", 0);
	ret = switch_ctrl(leds);
	return ret;
}
static code_t _test_cblock_advanced_cwrp_(void)
{
	code_t ret;
	ret = test_cblock_advanced();
	return ret;
}
static code_t _test_cblock_basic_cwrp_(void)
{
	code_t ret;
	ret = test_cblock_basic();
	return ret;
}
static code_t _test_events_cwrp_(void)
{
	code_t ret;
	ret = test_events();
	return ret;
}
static code_t _test_notice_auto_detach_cwrp_(void)
{
	code_t ret;
	ret = test_notice_auto_detach();
	return ret;
}
static code_t _test_notice_basic_cwrp_(void)
{
	code_t ret;
	ret = test_notice_basic();
	return ret;
}
static code_t _test_notice_error_handling_cwrp_(void)
{
	code_t ret;
	ret = test_notice_error_handling();
	return ret;
}
static code_t _test_notice_external_detach_cwrp_(void)
{
	code_t ret;
	ret = test_notice_external_detach();
	return ret;
}
static code_t _test_poller_delete_cwrp_(void)
{
	code_t ret;
	ret = test_poller_delete();
	return ret;
}
static code_t _test_recycler_advanced_cwrp_(void)
{
	code_t ret;
	ret = test_recycler_advanced();
	return ret;
}
static code_t _test_recycler_basic_cwrp_(void)
{
	code_t ret;
	ret = test_recycler_basic();
	return ret;
}
static code_t _test_recycler_stress_cwrp_(void)
{
	code_t ret;
	ret = test_recycler_stress();
	return ret;
}
static code_t _test_task_cwrp_(void)
{
	code_t ret;
	ret = test_task();
	return ret;
}
static code_t _test_thread_detach_cwrp_(void)
{
	code_t ret;
	ret = test_thread_detach();
	return ret;
}
static code_t _test_ticker_kill_cwrp_(void)
{
	code_t ret;
	ret = test_ticker_kill();
	return ret;
}
static code_t _test_waiter_cwrp_(void)
{
	code_t ret;
	ret = test_waiter();
	return ret;
}
static code_t _uart_recv_cwrp_(void)
{
	code_t ret;
	numb_t uart = cmdparser_get_num("uart", 1);
	numb_t baud = cmdparser_get_num("baud", 115200);
	numb_t bits = cmdparser_get_num("bits", 8);
	numb_t pari = cmdparser_get_num("pari", 0);
	numb_t timeout = cmdparser_get_num("timeout", 1000);
	ret = uart_recv(uart, baud, bits, pari, timeout);
	return ret;
}
static code_t _uart_send_cwrp_(void)
{
	code_t ret;
	numb_t uart = cmdparser_get_num("uart", 1);
	text_t info = cmdparser_get_txt("info", "hello");
	numb_t baud = cmdparser_get_num("baud", 115200);
	numb_t bits = cmdparser_get_num("bits", 8);
	numb_t pari = cmdparser_get_num("pari", 0);
	ret = uart_send(uart, info, baud, bits, pari);
	return ret;
}
static code_t _usb_cancel_cwrp_(void)
{
	code_t ret;
	numb_t ch = cmdparser_get_num("ch", 1);
	numb_t pid = cmdparser_get_num("pid", 0);
	numb_t vid = cmdparser_get_num("vid", 0);
	numb_t rev = cmdparser_get_num("rev", 0);
	text_t name = cmdparser_get_txt("name", "Mindeo Scanner");
	text_t serial = cmdparser_get_txt("serial", "0123456789");
	ret = usb_cancel(ch, pid, vid, rev, name, serial);
	return ret;
}
static code_t _usb_enum_cwrp_(void)
{
	code_t ret;
	numb_t ch = cmdparser_get_num("ch", 1);
	numb_t pid = cmdparser_get_num("pid", 0);
	numb_t vid = cmdparser_get_num("vid", 0);
	numb_t rev = cmdparser_get_num("rev", 0);
	text_t name = cmdparser_get_txt("name", "Mindeo Scanner");
	text_t serial = cmdparser_get_txt("serial", "0123456789");
	ret = usb_enum(ch, pid, vid, rev, name, serial);
	return ret;
}
static code_t _usb_read_cwrp_(void)
{
	code_t ret;
	numb_t ch = cmdparser_get_num("ch", 1);
	numb_t pid = cmdparser_get_num("pid", 0);
	numb_t vid = cmdparser_get_num("vid", 0);
	numb_t rev = cmdparser_get_num("rev", 0);
	text_t name = cmdparser_get_txt("name", "Mindeo Scanner");
	text_t serial = cmdparser_get_txt("serial", "0123456789");
	ret = usb_read(ch, pid, vid, rev, name, serial);
	return ret;
}
static code_t _usb_write_cwrp_(void)
{
	code_t ret;
	numb_t ch = cmdparser_get_num("ch", 1);
	text_t info = cmdparser_get_txt("info", "Hello");
	numb_t pid = cmdparser_get_num("pid", 0);
	numb_t vid = cmdparser_get_num("vid", 0);
	numb_t rev = cmdparser_get_num("rev", 0);
	text_t name = cmdparser_get_txt("name", "Mindeo Scanner");
	text_t serial = cmdparser_get_txt("serial", "0123456789");
	ret = usb_write(ch, info, pid, vid, rev, name, serial);
	return ret;
}
static code_t _version_cwrp_(void)
{
	code_t ret;
	fobj_t out = fobj_create(cmdparser_get_txt("out", "hello.txt"));
	ret = version(out);
	fobj_delete(out);
	return ret;
}
static argv_t _cam_stability_test_args_[] = {
};
static func_t _cam_stability_test_func_ = {
	// command data >>
	.cwrp = _cam_stability_test_cwrp_,
	.name = "cam_stability_test",
	.desc = "camera stability test",
	.args = _cam_stability_test_args_,
	.argn = 0
	// command data <<
};
static argv_t _cam_take_cont_args_[] = {
	{"expo", "expo in us", "300"},
	{"gain", "analog gain 1-8", "4"},
	{"count", "number of images", "5"},
};
static func_t _cam_take_cont_func_ = {
	// command data >>
	.cwrp = _cam_take_cont_cwrp_,
	.name = "cam_take_cont",
	.desc = "take images by cont mode",
	.args = _cam_take_cont_args_,
	.argn = 3
	// command data <<
};
static argv_t _cam_take_norm_args_[] = {
	{"expo", "expo in us", "300"},
	{"gain", "analog gain 1-8", "4"},
	{"count", "number of images", "5"},
};
static func_t _cam_take_norm_func_ = {
	// command data >>
	.cwrp = _cam_take_norm_cwrp_,
	.name = "cam_take_norm",
	.desc = "take images by norm mode",
	.args = _cam_take_norm_args_,
	.argn = 3
	// command data <<
};
static argv_t _cam_take_roll_args_[] = {
	{"expo", "expo in us", "300"},
	{"gain", "analog gain 1-8", "4"},
	{"count", "number of images", "5"},
};
static func_t _cam_take_roll_func_ = {
	// command data >>
	.cwrp = _cam_take_roll_cwrp_,
	.name = "cam_take_roll",
	.desc = "take images by roll mode",
	.args = _cam_take_roll_args_,
	.argn = 3
	// command data <<
};
static argv_t _demo_battery_args_[] = {
};
static func_t _demo_battery_func_ = {
	// command data >>
	.cwrp = _demo_battery_cwrp_,
	.name = "demo_battery",
	.desc = "Battery demo.",
	.args = _demo_battery_args_,
	.argn = 0
	// command data <<
};
static argv_t _demo_block_args_[] = {
};
static func_t _demo_block_func_ = {
	// command data >>
	.cwrp = _demo_block_cwrp_,
	.name = "demo_block",
	.desc = "Block demo.",
	.args = _demo_block_args_,
	.argn = 0
	// command data <<
};
static argv_t _demo_cable_args_[] = {
};
static func_t _demo_cable_func_ = {
	// command data >>
	.cwrp = _demo_cable_cwrp_,
	.name = "demo_cable",
	.desc = "Cable detect.",
	.args = _demo_cable_args_,
	.argn = 0
	// command data <<
};
static argv_t _demo_delay_args_[] = {
};
static func_t _demo_delay_func_ = {
	// command data >>
	.cwrp = _demo_delay_cwrp_,
	.name = "demo_delay",
	.desc = "hrtime demo",
	.args = _demo_delay_args_,
	.argn = 0
	// command data <<
};
static argv_t _demo_event_args_[] = {
};
static func_t _demo_event_func_ = {
	// command data >>
	.cwrp = _demo_event_cwrp_,
	.name = "demo_event",
	.desc = "event demo",
	.args = _demo_event_args_,
	.argn = 0
	// command data <<
};
static argv_t _demo_filesys_args_[] = {
	{"src", "copy from", "hello-src.txt"},
	{"dst", "copy to", "hello-dst.txt"},
};
static func_t _demo_filesys_func_ = {
	// command data >>
	.cwrp = _demo_filesys_cwrp_,
	.name = "demo_filesys",
	.desc = "copy a file",
	.args = _demo_filesys_args_,
	.argn = 2
	// command data <<
};
static argv_t _demo_gvcp_args_[] = {
};
static func_t _demo_gvcp_func_ = {
	// command data >>
	.cwrp = _demo_gvcp_cwrp_,
	.name = "demo_gvcp",
	.desc = "gvcp.",
	.args = _demo_gvcp_args_,
	.argn = 0
	// command data <<
};
static argv_t _demo_hrtime_args_[] = {
};
static func_t _demo_hrtime_func_ = {
	// command data >>
	.cwrp = _demo_hrtime_cwrp_,
	.name = "demo_hrtime",
	.desc = "hrtime demo",
	.args = _demo_hrtime_args_,
	.argn = 0
	// command data <<
};
static argv_t _demo_infrared_args_[] = {
};
static func_t _demo_infrared_func_ = {
	// command data >>
	.cwrp = _demo_infrared_cwrp_,
	.name = "demo_infrared",
	.desc = "infrared demo",
	.args = _demo_infrared_args_,
	.argn = 0
	// command data <<
};
static argv_t _demo_mqueue_args_[] = {
};
static func_t _demo_mqueue_func_ = {
	// command data >>
	.cwrp = _demo_mqueue_cwrp_,
	.name = "demo_mqueue",
	.desc = "mqueue demo",
	.args = _demo_mqueue_args_,
	.argn = 0
	// command data <<
};
static argv_t _demo_net_monitor_args_[] = {
};
static func_t _demo_net_monitor_func_ = {
	// command data >>
	.cwrp = _demo_net_monitor_cwrp_,
	.name = "demo_net_monitor",
	.desc = "network monitor.",
	.args = _demo_net_monitor_args_,
	.argn = 0
	// command data <<
};
static argv_t _demo_pcre_args_[] = {
	{"text", "text", "This is a test string."},
	{"pattern", "pattern", "(\\w+\\s+\\w+)\\s+(\\w+\\s+\\w+)\\s+(\\w+)"},
};
static func_t _demo_pcre_func_ = {
	// command data >>
	.cwrp = _demo_pcre_cwrp_,
	.name = "demo_pcre",
	.desc = "pcre.",
	.args = _demo_pcre_args_,
	.argn = 2
	// command data <<
};
static argv_t _demo_reset_args_[] = {
};
static func_t _demo_reset_func_ = {
	// command data >>
	.cwrp = _demo_reset_cwrp_,
	.name = "demo_reset",
	.desc = "reset test demo",
	.args = _demo_reset_args_,
	.argn = 0
	// command data <<
};
static argv_t _demo_service_args_[] = {
};
static func_t _demo_service_func_ = {
	// command data >>
	.cwrp = _demo_service_cwrp_,
	.name = "demo_service",
	.desc = "service test demo.",
	.args = _demo_service_args_,
	.argn = 0
	// command data <<
};
static argv_t _demo_set_gateway_args_[] = {
	{"gw", "gateway", "0.0.0.0"},
};
static func_t _demo_set_gateway_func_ = {
	// command data >>
	.cwrp = _demo_set_gateway_cwrp_,
	.name = "demo_set_gateway",
	.desc = "network gateway set.",
	.args = _demo_set_gateway_args_,
	.argn = 1
	// command data <<
};
static argv_t _demo_set_mac_addr_args_[] = {
	{"mac", "network IP", "00:30:1B:BA:02:DB"},
};
static func_t _demo_set_mac_addr_func_ = {
	// command data >>
	.cwrp = _demo_set_mac_addr_cwrp_,
	.name = "demo_set_mac_addr",
	.desc = "network ip set.",
	.args = _demo_set_mac_addr_args_,
	.argn = 1
	// command data <<
};
static argv_t _demo_set_mask_args_[] = {
	{"mask", "subnet mask", "*************"},
};
static func_t _demo_set_mask_func_ = {
	// command data >>
	.cwrp = _demo_set_mask_cwrp_,
	.name = "demo_set_mask",
	.desc = "network subnet mask set.",
	.args = _demo_set_mask_args_,
	.argn = 1
	// command data <<
};
static argv_t _demo_setup_ip_args_[] = {
	{"ip", "network IP", "auto"},
};
static func_t _demo_setup_ip_func_ = {
	// command data >>
	.cwrp = _demo_setup_ip_cwrp_,
	.name = "demo_setup_ip",
	.desc = "network ip set.",
	.args = _demo_setup_ip_args_,
	.argn = 1
	// command data <<
};
static argv_t _demo_simple_notice_args_[] = {
};
static func_t _demo_simple_notice_func_ = {
	// command data >>
	.cwrp = _demo_simple_notice_cwrp_,
	.name = "demo_simple_notice",
	.desc = "simple notice demo.",
	.args = _demo_simple_notice_args_,
	.argn = 0
	// command data <<
};
static argv_t _demo_systick_args_[] = {
	{"cycle", "tick time", "1000"},
	{"timeout", "demo time", "1500"},
};
static func_t _demo_systick_func_ = {
	// command data >>
	.cwrp = _demo_systick_cwrp_,
	.name = "demo_systick",
	.desc = "systemtick test demo",
	.args = _demo_systick_args_,
	.argn = 2
	// command data <<
};
static argv_t _demo_task_loop_args_[] = {
};
static func_t _demo_task_loop_func_ = {
	// command data >>
	.cwrp = _demo_task_loop_cwrp_,
	.name = "demo_task_loop",
	.desc = "auto task loop.",
	.args = _demo_task_loop_args_,
	.argn = 0
	// command data <<
};
static argv_t _demo_thread_args_[] = {
};
static func_t _demo_thread_func_ = {
	// command data >>
	.cwrp = _demo_thread_cwrp_,
	.name = "demo_thread",
	.desc = "thread test demo.",
	.args = _demo_thread_args_,
	.argn = 0
	// command data <<
};
static argv_t _demo_thread_dump_args_[] = {
};
static func_t _demo_thread_dump_func_ = {
	// command data >>
	.cwrp = _demo_thread_dump_cwrp_,
	.name = "demo_thread_dump",
	.desc = "dump cpu rate.",
	.args = _demo_thread_dump_args_,
	.argn = 0
	// command data <<
};
static argv_t _demo_ticker_args_[] = {
};
static func_t _demo_ticker_func_ = {
	// command data >>
	.cwrp = _demo_ticker_cwrp_,
	.name = "demo_ticker",
	.desc = "ticker test demo.",
	.args = _demo_ticker_args_,
	.argn = 0
	// command data <<
};
static argv_t _demo_tmr_args_[] = {
};
static func_t _demo_tmr_func_ = {
	// command data >>
	.cwrp = _demo_tmr_cwrp_,
	.name = "demo_tmr",
	.desc = "tmr test demo.",
	.args = _demo_tmr_args_,
	.argn = 0
	// command data <<
};
static argv_t _demo_wdt_args_[] = {
	{"in", "in wdt feed cycle", "1000"},
	{"ex", "ex wdt feed cycle", "500"},
};
static func_t _demo_wdt_func_ = {
	// command data >>
	.cwrp = _demo_wdt_cwrp_,
	.name = "demo_wdt",
	.desc = "wdt test demo",
	.args = _demo_wdt_args_,
	.argn = 2
	// command data <<
};
static argv_t _domelight_breath_args_[] = {
	{"time", "keep time", "10000"},
};
static func_t _domelight_breath_func_ = {
	// command data >>
	.cwrp = _domelight_breath_cwrp_,
	.name = "domelight_breath",
	.desc = "breathing light",
	.args = _domelight_breath_args_,
	.argn = 1
	// command data <<
};
static argv_t _pin_get_args_[] = {
	{"pin_id", "pin id", "-1"},
};
static func_t _pin_get_func_ = {
	// command data >>
	.cwrp = _pin_get_cwrp_,
	.name = "pin_get",
	.desc = "get pin value",
	.args = _pin_get_args_,
	.argn = 1
	// command data <<
};
static argv_t _pin_irq_args_[] = {
	{"pin_id", "pin id", "-1"},
	{"irq", "interrupt type", "6"},
	{"time", "test time", "5"},
};
static func_t _pin_irq_func_ = {
	// command data >>
	.cwrp = _pin_irq_cwrp_,
	.name = "pin_irq",
	.desc = "set pin interrupt",
	.args = _pin_irq_args_,
	.argn = 3
	// command data <<
};
static argv_t _pin_set_args_[] = {
	{"pin_id", "pin id", "-1"},
	{"val", "value", "1"},
	{"timeout", "keep time", "1"},
};
static func_t _pin_set_func_ = {
	// command data >>
	.cwrp = _pin_set_cwrp_,
	.name = "pin_set",
	.desc = "set pin value",
	.args = _pin_set_args_,
	.argn = 3
	// command data <<
};
static argv_t _speaker_play_args_[] = {
	{"wav", "path of wav file", "mindeo.wav"},
};
static func_t _speaker_play_func_ = {
	// command data >>
	.cwrp = _speaker_play_cwrp_,
	.name = "speaker_play",
	.desc = "play music",
	.args = _speaker_play_args_,
	.argn = 1
	// command data <<
};
static argv_t _switch_ctrl_args_[] = {
	{"leds", "switchs state", "0"},
};
static func_t _switch_ctrl_func_ = {
	// command data >>
	.cwrp = _switch_ctrl_cwrp_,
	.name = "switch_ctrl",
	.desc = "Switch state ctrl.",
	.args = _switch_ctrl_args_,
	.argn = 1
	// command data <<
};
static argv_t _test_cblock_advanced_args_[] = {
};
static func_t _test_cblock_advanced_func_ = {
	// command data >>
	.cwrp = _test_cblock_advanced_cwrp_,
	.name = "test_cblock_advanced",
	.desc = "test cblock with init/free functions",
	.args = _test_cblock_advanced_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_cblock_basic_args_[] = {
};
static func_t _test_cblock_basic_func_ = {
	// command data >>
	.cwrp = _test_cblock_basic_cwrp_,
	.name = "test_cblock_basic",
	.desc = "test cblock basic functionality",
	.args = _test_cblock_basic_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_events_args_[] = {
};
static func_t _test_events_func_ = {
	// command data >>
	.cwrp = _test_events_cwrp_,
	.name = "test_events",
	.desc = "test events",
	.args = _test_events_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_notice_auto_detach_args_[] = {
};
static func_t _test_notice_auto_detach_func_ = {
	// command data >>
	.cwrp = _test_notice_auto_detach_cwrp_,
	.name = "test_notice_auto_detach",
	.desc = "test MD_NOTICE_FLAG_DT one-time detach behavior",
	.args = _test_notice_auto_detach_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_notice_basic_args_[] = {
};
static func_t _test_notice_basic_func_ = {
	// command data >>
	.cwrp = _test_notice_basic_cwrp_,
	.name = "test_notice_basic",
	.desc = "test basic Notice functionality",
	.args = _test_notice_basic_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_notice_error_handling_args_[] = {
};
static func_t _test_notice_error_handling_func_ = {
	// command data >>
	.cwrp = _test_notice_error_handling_cwrp_,
	.name = "test_notice_error_handling",
	.desc = "test Notice error handling",
	.args = _test_notice_error_handling_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_notice_external_detach_args_[] = {
};
static func_t _test_notice_external_detach_func_ = {
	// command data >>
	.cwrp = _test_notice_external_detach_cwrp_,
	.name = "test_notice_external_detach",
	.desc = "test external thread detach loop",
	.args = _test_notice_external_detach_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_poller_delete_args_[] = {
};
static func_t _test_poller_delete_func_ = {
	// command data >>
	.cwrp = _test_poller_delete_cwrp_,
	.name = "test_poller_delete",
	.desc = "test poller delete.",
	.args = _test_poller_delete_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_recycler_advanced_args_[] = {
};
static func_t _test_recycler_advanced_func_ = {
	// command data >>
	.cwrp = _test_recycler_advanced_cwrp_,
	.name = "test_recycler_advanced",
	.desc = "test MdRecycler with init/free functions",
	.args = _test_recycler_advanced_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_recycler_basic_args_[] = {
};
static func_t _test_recycler_basic_func_ = {
	// command data >>
	.cwrp = _test_recycler_basic_cwrp_,
	.name = "test_recycler_basic",
	.desc = "test MdRecycler basic functionality",
	.args = _test_recycler_basic_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_recycler_stress_args_[] = {
};
static func_t _test_recycler_stress_func_ = {
	// command data >>
	.cwrp = _test_recycler_stress_cwrp_,
	.name = "test_recycler_stress",
	.desc = "test MdRecycler thread safety",
	.args = _test_recycler_stress_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_task_args_[] = {
};
static func_t _test_task_func_ = {
	// command data >>
	.cwrp = _test_task_cwrp_,
	.name = "test_task",
	.desc = "test task module.",
	.args = _test_task_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_thread_detach_args_[] = {
};
static func_t _test_thread_detach_func_ = {
	// command data >>
	.cwrp = _test_thread_detach_cwrp_,
	.name = "test_thread_detach",
	.desc = "test thread detach",
	.args = _test_thread_detach_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_ticker_kill_args_[] = {
};
static func_t _test_ticker_kill_func_ = {
	// command data >>
	.cwrp = _test_ticker_kill_cwrp_,
	.name = "test_ticker_kill",
	.desc = "test ticker kill.",
	.args = _test_ticker_kill_args_,
	.argn = 0
	// command data <<
};
static argv_t _test_waiter_args_[] = {
};
static func_t _test_waiter_func_ = {
	// command data >>
	.cwrp = _test_waiter_cwrp_,
	.name = "test_waiter",
	.desc = "test waiter",
	.args = _test_waiter_args_,
	.argn = 0
	// command data <<
};
static argv_t _uart_recv_args_[] = {
	{"uart", "uart id", "1"},
	{"baud", "baud rate", "115200"},
	{"bits", "data bits", "8"},
	{"pari", "parity", "0"},
	{"timeout", "timeout", "1000"},
};
static func_t _uart_recv_func_ = {
	// command data >>
	.cwrp = _uart_recv_cwrp_,
	.name = "uart_recv",
	.desc = "uart recv_data",
	.args = _uart_recv_args_,
	.argn = 5
	// command data <<
};
static argv_t _uart_send_args_[] = {
	{"uart", "uart id", "1"},
	{"info", "send data", "hello"},
	{"baud", "baud rate", "115200"},
	{"bits", "data bits", "8"},
	{"pari", "parity", "0"},
};
static func_t _uart_send_func_ = {
	// command data >>
	.cwrp = _uart_send_cwrp_,
	.name = "uart_send",
	.desc = "uart send_data",
	.args = _uart_send_args_,
	.argn = 5
	// command data <<
};
static argv_t _usb_cancel_args_[] = {
	{"ch", "Usb channel.", "1"},
	{"pid", "Product id.", "0"},
	{"vid", "Vendor id.", "0"},
	{"rev", "Device rev.", "0"},
	{"name", "Device name.", "Mindeo Scanner"},
	{"serial", "Device serial.", "0123456789"},
};
static func_t _usb_cancel_func_ = {
	// command data >>
	.cwrp = _usb_cancel_cwrp_,
	.name = "usb_cancel",
	.desc = "Usb send cancel test.",
	.args = _usb_cancel_args_,
	.argn = 6
	// command data <<
};
static argv_t _usb_enum_args_[] = {
	{"ch", "Usb channel.", "1"},
	{"pid", "Product id.", "0"},
	{"vid", "Vendor id.", "0"},
	{"rev", "Device rev.", "0"},
	{"name", "Device name.", "Mindeo Scanner"},
	{"serial", "Device serial.", "0123456789"},
};
static func_t _usb_enum_func_ = {
	// command data >>
	.cwrp = _usb_enum_cwrp_,
	.name = "usb_enum",
	.desc = "Usb enum test.",
	.args = _usb_enum_args_,
	.argn = 6
	// command data <<
};
static argv_t _usb_read_args_[] = {
	{"ch", "Usb channel.", "1"},
	{"pid", "Product id.", "0"},
	{"vid", "Vendor id.", "0"},
	{"rev", "Device rev.", "0"},
	{"name", "Device name.", "Mindeo Scanner"},
	{"serial", "Device serial.", "0123456789"},
};
static func_t _usb_read_func_ = {
	// command data >>
	.cwrp = _usb_read_cwrp_,
	.name = "usb_read",
	.desc = "Usb read test.",
	.args = _usb_read_args_,
	.argn = 6
	// command data <<
};
static argv_t _usb_write_args_[] = {
	{"ch", "Usb channel.", "1"},
	{"info", "data.", "Hello"},
	{"pid", "Product id.", "0"},
	{"vid", "Vendor id.", "0"},
	{"rev", "Device rev.", "0"},
	{"name", "Device name.", "Mindeo Scanner"},
	{"serial", "Device serial.", "0123456789"},
};
static func_t _usb_write_func_ = {
	// command data >>
	.cwrp = _usb_write_cwrp_,
	.name = "usb_write",
	.desc = "Usb write test.",
	.args = _usb_write_args_,
	.argn = 7
	// command data <<
};
static argv_t _version_args_[] = {
	{"out", "version output file.", "hello.txt"},
};
static func_t _version_func_ = {
	// command data >>
	.cwrp = _version_cwrp_,
	.name = "version",
	.desc = "show version of all components.",
	.args = _version_args_,
	.argn = 1
	// command data <<
};
func_t *_funcs_tb_[] = {
	&_cam_stability_test_func_,
	&_cam_take_cont_func_,
	&_cam_take_norm_func_,
	&_cam_take_roll_func_,
	&_demo_battery_func_,
	&_demo_block_func_,
	&_demo_cable_func_,
	&_demo_delay_func_,
	&_demo_event_func_,
	&_demo_filesys_func_,
	&_demo_gvcp_func_,
	&_demo_hrtime_func_,
	&_demo_infrared_func_,
	&_demo_mqueue_func_,
	&_demo_net_monitor_func_,
	&_demo_pcre_func_,
	&_demo_reset_func_,
	&_demo_service_func_,
	&_demo_set_gateway_func_,
	&_demo_set_mac_addr_func_,
	&_demo_set_mask_func_,
	&_demo_setup_ip_func_,
	&_demo_simple_notice_func_,
	&_demo_systick_func_,
	&_demo_task_loop_func_,
	&_demo_thread_func_,
	&_demo_thread_dump_func_,
	&_demo_ticker_func_,
	&_demo_tmr_func_,
	&_demo_wdt_func_,
	&_domelight_breath_func_,
	&_pin_get_func_,
	&_pin_irq_func_,
	&_pin_set_func_,
	&_speaker_play_func_,
	&_switch_ctrl_func_,
	&_test_cblock_advanced_func_,
	&_test_cblock_basic_func_,
	&_test_events_func_,
	&_test_notice_auto_detach_func_,
	&_test_notice_basic_func_,
	&_test_notice_error_handling_func_,
	&_test_notice_external_detach_func_,
	&_test_poller_delete_func_,
	&_test_recycler_advanced_func_,
	&_test_recycler_basic_func_,
	&_test_recycler_stress_func_,
	&_test_task_func_,
	&_test_thread_detach_func_,
	&_test_ticker_kill_func_,
	&_test_waiter_func_,
	&_uart_recv_func_,
	&_uart_send_func_,
	&_usb_cancel_func_,
	&_usb_enum_func_,
	&_usb_read_func_,
	&_usb_write_func_,
	&_version_func_,
	0,
};
