//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_tmr_demo.c
// Author		: XuCF
// Created On	: 2023/04/12
// Description	: hal_tmr_demo.c
//
// History
// 1. V1.0, Created by XuCF. 2023/04/12
//=============================================================================

// Code impo >>
// Code impo <<

#include "object.h"
#include "hal/driver/hal_tmr.h"

// Code head Top
#include "autocmd.h"
// Code head Bot

ClassDeclare(MdTmrDemo);

// [MdTmrDemo] <= [] <- [MdTmrCall]
ClassRealize(MdTmrDemo)
{
	VTable super;
	// Code MdTmrDemo-class >>
	MdTmr *tmr;
	// Code MdTmrDemo-class <<
};

s32 HAL_MdTmrDemo_OnTimeout_Impl(MdTmrDemo *thiz);

static void *__MdTmrDemo_VTable__(int id)
{
	switch (id)
	{
		case ApiOnTimeout: return HAL_MdTmrDemo_OnTimeout_Impl;
		default: return __DefaultImpl__;
	}
}

// Code static Top
s32 HAL_MdTmrDemo_Init(MdTmrDemo *thiz);
CMD int demo_tmr(void)	/* tmr test demo. */
{
	static Allocate(MdTmrDemo, demo);
	MdTmr *LinuxTmr_Instance(void);
	LinuxTmr_Instance();
	HAL_MdTmrDemo_Init(demo);
	HAL_MdTmr_Cycl(demo->tmr, (MdTmrCall *)demo, 500000);
	clike_delay_us(3000000);
	HAL_MdTmr_Free(demo->tmr);
	return MACR_NONE;
}
// Code static Bot

s32 HAL_MdTmrDemo_Init(MdTmrDemo *thiz)
{
	// Code HAL_MdTmrDemo_Init Top
	VTableBuild(MdTmrDemo, thiz);
	thiz->tmr = HAL_Device_Find(MD_DEVICE_TMR_0);
	return MACR_NONE;
	// Code HAL_MdTmrDemo_Init Bot
}

s32 HAL_MdTmrDemo_OnTimeout_Impl(MdTmrDemo *thiz)
{
	// Code HAL_MdTmrDemo_OnTimeout_Impl Top
	clike_print("OnTimout %llu\n", clike_time_us_full());
	HAL_MdTmr_Stop(thiz->tmr);
	HAL_MdTmr_Once(thiz->tmr, (MdTmrCall *)thiz, 1000000);
	return MACR_NONE;
	// Code HAL_MdTmrDemo_OnTimeout_Impl Bot
}

// Code tail >>
// Code tail <<
