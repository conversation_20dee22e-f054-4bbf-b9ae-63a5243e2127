//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: demo_network.c
// Author		: DongDL
// Created On	: 2024/11/25
// Description	: demo_network.c
//
// History
// 1. V1.0, Created by DongDL. 2024/11/25
//=============================================================================

#include "MdNotice.h"
#include "MdNetwork.h"
#include "autocmd.h"

int _monitor_notice_(any thiz, unv what, any ptr, unv num)
{
	clike_print("%d %s: %s\n", (int)num, (char *)ptr, what ? "up" : "down");
	return 0;
}

CMD int demo_net_monitor(void) /* network monitor. */
{
	// init notice
	MdNotice_Init(8);
	// init network
	MdNetwork_Init();
	// attach notice
	MdNotice_AttachUser(MdNotice_FindNode(MdNoticeId_NetworkMonitor), _monitor_notice_, 0, 0);
	// wait forever
	while (1) clike_delay_ms(1000);
	return 0;
}

CMD int demo_setup_ip( /* network ip set. */
	TEXT("auto") ip /* network IP */
)
{
	s32 ret = -1;
	u32 ip_bin = 0;
	bin ip_str[16] = {0};
	u32 ip_cfg_curr = 0;
	str cfg_opt_str[] = {"Unknow", "FORCE", "UDHCP", "LLA"};

	if (!MdNetwork_IPGet(&ip_bin, &ip_cfg_curr))
	{
		MdNetwork_IPHex2Str(ip_bin, ip_str, sizeof(ip_str));
		clike_print("Current IP: %s, Cfg Opt: %s\n", ip_str, cfg_opt_str[ip_cfg_curr]);
	}
	if (memcmp(ip, "auto", 4) == 0) { ret = MdNetwork_IPSet(0, MDNetwork_IPSet_DHCP | MDNetwork_IPSet_LLA); }
	else
	{
		Assert(MdNetwork_IPStr2Hex(ip, &ip_bin), return -1);
		ret = MdNetwork_IPSet(ip_bin, MDNetwork_IPSet_FORCE);
	}
	if (ret < 0) clike_print("IP set: failed\n");

	Assert(MdNetwork_IPGet(&ip_bin, &ip_cfg_curr), return -1);
	Assert(MdNetwork_IPHex2Str(ip_bin, ip_str, sizeof(ip_str)), return -1);
	clike_print("Current IP: %s, Cfg Opt: %s\n", ip_str, cfg_opt_str[ip_cfg_curr]);

	return 0;
}

static s32 hex2num(bin c)
{
	if (c >= '0' && c <= '9') return c - '0';
	if (c >= 'a' && c <= 'f') return c - 'a' + 10;
	if (c >= 'A' && c <= 'F') return c - 'A' + 10;
	return -1;
}

static s32 hexstr2mac(byt *dst, str src)
{
	s32 i = 0;
	s32 v = 0;
	while (i < 6)
	{
		if (' ' == *src || ':' == *src)
		{
			src++;
			continue;
		}
		v = (hex2num(*src) << 4) | hex2num(*(src + 1));
		Assert(v == -1, return -1);
		*(dst + i) = v;
		i++;
		src += 2;
	}
	return 0;
}

CMD int demo_set_mac_addr(			  /* network ip set. */
	TEXT("00:30:1B:BA:02:DB") mac /* network IP */
)
{
	s32 ret = -1;
	byt mac_bin[6] = {0};

	Assert(MdNetwork_MacGet(mac_bin), return -1);
	clike_print("Current mac addr:%02x:%02x:%02x:%02x:%02x:%02x\n", mac_bin[0], mac_bin[1], mac_bin[2], mac_bin[3],
		mac_bin[4], mac_bin[5]);

	if (hexstr2mac(mac_bin, mac))
	{
		clike_print("Invalid mac addr\n");
		return -1;
	}

	ret = MdNetwork_MacSet(mac_bin);

	if (!ret) { clike_print("Mac addr set: success\n"); }
	else { clike_print("Mac addr set: failed\n"); }

	Assert(MdNetwork_MacGet(mac_bin), return -1);
	clike_print("Current mac addr:%02x:%02x:%02x:%02x:%02x:%02x\n", mac_bin[0], mac_bin[1], mac_bin[2], mac_bin[3],
		mac_bin[4], mac_bin[5]);

	return 0;
}

CMD int demo_set_mask(			   /* network subnet mask set. */
	TEXT("*************") mask /* subnet mask */
)
{
	s32 ret = -1;
	u32 mask_bin = 0;
	bin mask_str[16] = {0};

	if (!MdNetwork_NetMaskGet(&mask_bin))
	{
		MdNetwork_IPHex2Str(mask_bin, mask_str, sizeof(mask_str));
		clike_print("Current mask %s\n", mask_str);
	}
	Assert(MdNetwork_IPStr2Hex(mask, &mask_bin), return -1);
	ret = MdNetwork_NetMaskSet(mask_bin);

	if (!ret) { clike_print("Mask set: success\n"); }
	else { clike_print("Mask set: failed\n"); }

	Assert(MdNetwork_NetMaskGet(&mask_bin), return -1);
	Assert(MdNetwork_IPHex2Str(mask_bin, mask_str, sizeof(mask_str)), return -1);
	clike_print("Current mask %s\n", mask_str);

	return 0;
}

CMD int demo_set_gateway( /* network gateway set. */
	TEXT("0.0.0.0") gw /* gateway */
)
{
	s32 ret = -1;
	u32 gw_bin = 0;
	bin gw_str[16] = {0};

	if (!MdNetwork_GwGet(&gw_bin))
	{
		MdNetwork_IPHex2Str(gw_bin, gw_str, sizeof(gw_str));
		clike_print("Current gateway ip %s\n", gw_str);
	}
	Assert(MdNetwork_IPStr2Hex(gw, &gw_bin), return -1);
	ret = MdNetwork_GwSet(gw_bin);

	if (!ret) { clike_print("Gateway ip set: success\n"); }
	else { clike_print("Gateway ip set: failed\n"); }

	Assert(MdNetwork_GwGet(&gw_bin), return -1);
	Assert(MdNetwork_IPHex2Str(gw_bin, gw_str, sizeof(gw_str)), return -1);
	clike_print("Current Gateway ip %s\n", gw_str);

	return 0;
}