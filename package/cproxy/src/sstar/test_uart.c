//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_mduart_demo.c
// Author		: XuCF
// Created On	: 2023/07/25
// Description	: hal_mduart_demo.c
//
// History
// 1. V1.0, Created by XuCF. 2023/07/25
//=============================================================================

// Code impo >>
// Code impo <<

#include "object.h"
#include "hal/driver/hal_uart.h"

// Code head Top
#include "autocmd.h"
#include "autosdk_server.h"
#include "kernel/hal_events.h"
// Code head Bot

ClassDeclare(MdUartDemo);

// [MdUartDemo] <= [] <- [UartBack]
ClassRealize(MdUartDemo)
{
	VTable super;
	// Code MdUartDemo-class >>
	MdUart *uart;
	Events reve;
	any buffer;
	u32 recv;
	// Code MdUartDemo-class <<
};

s32 HAL_MdUartDemo_OnRecv_Impl(MdUartDemo *thiz, UartCbkMeta *meta);

static void *__MdUartDemo_VTable__(int id)
{
	switch (id)
	{
		case ApiOnRecv: return HAL_MdUartDemo_OnRecv_Impl;
		default: return __DefaultImpl__;
	}
}

// Code static Top
s32 HAL_MdUartDemo_Load(MdUartDemo *thiz, u32 baud, u8 bits, u8 pari)
{
	s32 err = 0;
	UartCfgMeta meta;
	meta.back = (UartBack *)thiz;
	meta.baud = baud;
	meta.bits = bits;
	meta.pari = pari;
	meta.compat = 0;
	HAL_MdUart_Ctrl(thiz->uart, MD_UART_STAT_OFF);
	HAL_MdUart_Load(thiz->uart, &meta);
	HAL_MdUart_Ctrl(thiz->uart, MD_UART_STAT_READY);
	return err;
}

s32 HAL_MdUartDemo_Send(MdUartDemo *thiz, any data, u32 size)
{
	s32 len = 0;
	len = HAL_MdUart_Send(thiz->uart, data, size);
	HAL_MdUart_Wait(thiz->uart, -1);
	return len;
}

s32 HAL_MdUartDemo_Recv(MdUartDemo *thiz, any data, u32 size, u32 timeout)
{
	thiz->buffer = data, thiz->recv = size > 0x1000 || size == 0 ? 0x1000 : size;
	HAL_Events_WaitOr(&thiz->reve, 1, timeout);
	HAL_Events_ClrEvent(&thiz->reve, 1);
	return thiz->recv;
}

s32 HAL_MdUartDemo_Init(MdUartDemo *thiz, u32 uart)
{
	s32 err = 0;
	VTableBuild(MdUartDemo, thiz);
	thiz->uart = HAL_Device_Find(MD_DEVICE_UART_0 + uart);
	Assert(!thiz->uart, goto label_exit);
	HAL_Events_Construct(&thiz->reve, "uartdemo");
	HAL_Events_Setup(&thiz->reve, 0);
label_exit:
	return err;
}

s32 HAL_MdUartDemo_Close(MdUartDemo *thiz)
{
	s32 err = 0;
	HAL_MdUart_Ctrl(thiz->uart, MD_UART_STAT_OFF);
	return err;
}

u16 _crc_(u8 *data, u32 length)
{
	u16 crc = 0;
	for (int i = 0; i < length; i++)
	{
		crc ^= (u16)data[i] << 8;
		for (int j = 0; j < 8; j++)
		{
			if (crc & 0x8000) { crc = (crc << 1) ^ 0x1021; }
			else { crc <<= 1; }
		}
	}
	return crc;
}

static MdUartDemo obj, *demo = &obj;
CMD int uart_send(			/* uart send_data */
	NUMB(1) uart,		/* uart id */
	TEXT("hello") info,	/* send data */
	NUMB(115200) baud,	/* baud rate */
	NUMB(8) bits,		/* data bits */
	NUMB(0) pari		/* parity */
)
{
	HAL_MdUartDemo_Init(demo, uart);
	HAL_MdUartDemo_Load(demo, baud, bits, pari);
	s32 len = HAL_MdUartDemo_Send(demo, (any)info, strlen(info));
	clike_assert(len < 0, return len);
	HAL_MdUartDemo_Close(demo);
	return len;
}

CMD int uart_recv(			/* uart recv_data */
	NUMB(1) uart,		/* uart id*/
	NUMB(115200) baud,	/* baud rate */
	NUMB(8) bits,		/* data bits */
	NUMB(0) pari,		/* parity */
	NUMB(1000) timeout	/* timeout */
)
{
	u8 buffer[1024] = { 0 };
	HAL_MdUartDemo_Init(demo, uart);
	HAL_MdUartDemo_Load(demo, baud, bits, pari);
	s32 len = HAL_MdUartDemo_Recv(demo, (any)buffer, 0, timeout);
	clike_assert(len < 0, return len);
	HAL_MdUartDemo_Close(demo);
	clike_print("recv data[%d]:%.*s\n", len, len, buffer);
	return len;
}

// Code static Bot

s32 HAL_MdUartDemo_OnRecv_Impl(MdUartDemo *thiz, UartCbkMeta *meta)
{
	// Code HAL_MdUartDemo_OnRecv_Impl Top
	s32 err = 0;
	thiz->recv = HAL_MdUart_Recv(thiz->uart, thiz->buffer, thiz->recv);
	HAL_Events_SetEvent(&thiz->reve, 1);
	return err;
	// Code HAL_MdUartDemo_OnRecv_Impl Bot
}

// Code tail >>
// Code tail <<
