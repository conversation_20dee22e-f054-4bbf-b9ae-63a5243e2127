//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_mdfile_demo.c
// Author		: XuCF
// Created On	: 2023/09/21
// Description	: hal_mdfile_demo.c
//
// History
// 1. V1.0, Created by XuCF. 2023/09/21
//=============================================================================

#include "hal_fskits.h"
#include "autocmd.h"

CMD int demo_filesys(			   /* copy a file */
	TEXT("hello-src.txt") src, /* copy from  */
	TEXT("hello-dst.txt") dst  /* copy to */
)
{
	s32 err = 0;

	// Find filesys
	MdFileSys *filesys = HAL_Device_Find(MD_DEVICE_FS_USR);
	Assert(!filesys, return -1);

	// Read
	MdFile *fpi = HAL_MdFileSys_Open(filesys, src, "r");
	int size = HAL_MdFile_Size(fpi);
	unsigned char *buffer = clike_malloc(size);
	HAL_MdFile_Read(fpi, buffer, size);
	HAL_MdFile_Close(fpi);

	// Write
	MdFile *fpo = HAL_MdFileSys_Open(filesys, dst, "w");
	HAL_MdFile_Write(fpo, buffer, size);
	HAL_MdFile_Close(fpo);

	return err;
}
