//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_button_demo.c
// Author		: XuCF
// Created On	: 2023/09/26
// Description	: hal_button_demo.c
//
// History
// 1. V1.0, Created by XuCF. 2023/09/26
//=============================================================================
#define MACR_LOG_LEVEL 5

// Code impo >>
// Code impo <<

#include "object.h"
#include "hal/device/hal_button.h"

// Code head Top
#include "autocmd.h"
#include "autosdk_server.h"
// Code head Bot

ClassDeclare(MdBtnDemo);

// [MdBtnDemo] <= [] <- [BtnBack]
ClassRealize(MdBtnDemo)
{
	VTable super;
	// Code MdBtnDemo-class >>
	Button *btn;
	s8 pending;
	// Code MdBtnDemo-class <<
};

s32 HAL_MdBtnDemo_OnExcited_Impl(MdBtnDemo *thiz, BtnCbkMeta *meta);

static void *__MdBtnDemo_VTable__(int id)
{
	switch (id)
	{
		case ApiOnExcited: return HAL_MdBtnDemo_OnExcited_Impl;
		default: return __DefaultImpl__;
	}
}

// Code static Top
static s32 _demo_init_(MdBtnDemo *thiz, s32 ibtn)
{
	s32 err = 0;
	VTableBuild(MdBtnDemo, thiz);
	thiz->pending = 0;
	thiz->btn = HAL_Device_Find(MD_DEVICE_TRIG_MAIN + ibtn);
	Assert(!thiz->btn, return -1);
	BtnCfgMeta meta;
	meta.user = (BtnBack *)thiz;
	err = HAL_Button_Load(thiz->btn, &meta);
	Assert(err, return err);
	HAL_Button_Scan(thiz->btn, 0);
	return err;
}

static s32 _demo_wait_(MdBtnDemo *thiz, u32 timeout)
{
	thiz->pending = 0;
	while (!thiz->pending && timeout--) clike_delay_ms(1);
	return thiz->pending;
}

// Code static Bot

s32 HAL_MdBtnDemo_OnExcited_Impl(MdBtnDemo *thiz, BtnCbkMeta *meta)
{
	// Code HAL_MdBtnDemo_OnExcited_Impl Top
	s32 err = 0;
	s32 event = HAL_Button_Scan(thiz->btn, 0);
	LOGI("button %d event %d", thiz->btn->super.uuid, event);
	if (event) thiz->pending = event;
	return err;
	// Code HAL_MdBtnDemo_OnExcited_Impl Bot
}

// Code tail >>
API int button_wait(/* wait button event. */
	U32(I) btn,		/* 0:main 1:auxi */
	U32(I) timeout,	/* timeout in ms */
	U32(O) event	/* event 2:press 1:release */
)
{
	static MdBtnDemo demo[2] = {0};
	Assert(btn >= 2, return -1);
	if (!demo[btn].super.__vtb__)
	{
		Assert(_demo_init_(demo + btn, btn), return -1);
	}
	*event = _demo_wait_(demo + btn, timeout);
	return AUTOSDK_STAT_DONE;
}
// Code tail <<
