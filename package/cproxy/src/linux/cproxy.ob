cproxy(non)
{
    Md<PERSON>oll<PERSON>#poller();
}

CproxyServer_Init(non)
{
    MdNotice();
    MdTaskServer(64);
    MdPoller#poller();

    MdTcpServer@stream(
        MdReadHelper(
            MdAllocator@allocator(128, 4),
            MdNoticeNode@notice()
        ),
        9999,
        MdNoticeNode@booter()
    );
    MdStreamDaemon@ticker(notice, stream, 1);
    CpAutoSdkServer#autosdk(notice, ticker);
}
