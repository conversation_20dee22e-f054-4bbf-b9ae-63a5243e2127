#include "autocmd.h"
#include "MdPoller.h"
#include "cbeans.otb.h"
#include <sys/eventfd.h>

static void _event_callback_(any obj)
{
	int efd = (int)obj;
	uint64_t value;
	clike_delay_ms(50);
	read(efd, &value, sizeof(value));
	clike_print("Read value: %llu\n", value);
}

CMD int test_poller_delete(void) /* test poller delete. */
{
	cproxy_t cproxy;
	cproxy_new(&cproxy);
	MdPoller *poller = cproxy.poller;
	Assert(!poller, return -1);
	int efd = eventfd(0, EFD_NONBLOCK);

	// Add eventfd to poller
	MdPollee *pollee = MdPollee_New(efd, _event_callback_, (any)efd);
	Assert(!pollee, goto label_exit);
	MdPollee_Attach(pollee, poller, POLLIN);

	// Trigger event
	uint64_t val = 9;
	write(efd, &val, sizeof(val));
	clike_delay_ms(100); // Wait for 1000ms

	// Trigger event again and detach pollee
	write(efd, &val, sizeof(val));
	clike_delay_ms(10);
	while (MdPollee_Detach(pollee, 0) != 0)
	{
		clike_print("try to detach pollee...\n");
		clike_delay_ms(5);
	}
	MdPollee_Del(pollee);

	clike_delay_ms(1000);

label_exit:
	close(efd);
	// Clean up resources
	MdPoller_Del(poller);
	return 0;
}