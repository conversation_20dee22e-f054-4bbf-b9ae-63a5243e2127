//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: Main.c
// Author		: XuCF
// Created On	: 2024/09/04
// Description	: Main.c
//
// History
// 1. V1.0, Created by XuCF. 2024/09/04
//=============================================================================

#include "MdNotice.h"
#include "autocmd.h"

int hello(any obj, int what, any ptr, unv num)
{
	int *pval = ptr;
	clike_print("p=%p,a=%d,v=%d\n", obj, *pval, (int)num);
	*pval += 1;
	return 0;
}

CMD int demo_simple_notice(void) /* simple notice demo. */
{
	MdNotice_Init(8);
	MdNoticeNode *node = MdNotice_CreateNode(MdNoticeId_Invisible);
	MdNotice_AttachUser(node, hello, node, 0);
	MdNotice_AttachUser(node, hello, node, 0);
	int i = 10;
	MdNotice_Invoke(node, 0, &i, i);
	MdNotice_DeleteNode(node);
	return 0;
}
