//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: test_notice.c
// Author		: XuCF
// Created On	: 2024/12/19
// Description	: Test for refactored MdNotice using MdRecycler
//
// History
// 1. V1.0, Created by XuCF. 2024/12/19
//=============================================================================

#include "MdNotice.h"
#include "MdRunner.h"
#include "autocmd.h"
#include "libcustom.h"

static int _callback_count_ = 0;
static int _callback_data_[10];

// Test callback function
static int _test_callback_(any pobj, unv what, any ptr, unv num)
{
	int *counter = (int *)pobj;
	(*counter)++;
	_callback_count_++;
	clike_print(
		"  Callback called: pobj=%p, what=%d, ptr=%p, num=%d, counter=%d\n", pobj, (int)what, ptr, (int)num, *counter);
	return 0;
}

// Test callback that returns error
static int _test_callback_error_(any pobj, unv what, any ptr, unv num)
{
	int *counter = (int *)pobj;
	(*counter)++;
	_callback_count_++;
	clike_print("  Error callback called: pobj=%p, counter=%d\n", pobj, *counter);
	return -1; // Return error
}

// Test callback with always invoke flag
static int _test_callback_always_(any pobj, unv what, any ptr, unv num)
{
	int *counter = (int *)pobj;
	(*counter)++;
	_callback_count_++;
	clike_print("  Always callback called: pobj=%p, counter=%d\n", pobj, *counter);
	return 0;
}

CMD int test_notice_basic(void) /* test basic Notice functionality */
{
	clike_print("=== Basic Notice test ===\n");

	// Initialize Notice
	MdNotice_Init(10);

	// Create a notice node
	MdNoticeNode *node = MdNotice_CreateNode(MdNoticeId_Invisible);
	clike_assert(!node, return -1);

	// Reset counters
	_callback_count_ = 0;
	memset(_callback_data_, 0, sizeof(_callback_data_));

	// Attach some users
	MdNoticeUser *user1 = MdNotice_AttachUser(node, _test_callback_, &_callback_data_[0], 0);
	MdNoticeUser *user2 = MdNotice_AttachUser(node, _test_callback_, &_callback_data_[1], 0);
	MdNoticeUser *user3 = MdNotice_AttachUser(node, _test_callback_, &_callback_data_[2], 0);

	clike_assert(!user1 || !user2 || !user3, goto cleanup);

	clike_print("Attached 3 users\n");

	// Invoke notifications
	int ret = MdNotice_Invoke(node, 100, (any)0x1234, 200);
	clike_print("First invoke returned: %d, total callbacks: %d\n", ret, _callback_count_);

	if (3 != _callback_count_ || 1 != _callback_data_[0] || 1 != _callback_data_[1] || 1 != _callback_data_[2])
	{
		clike_print("Callback counts incorrect\n");
		goto cleanup;
	}

	// Detach one user
	int detach_result = MdNotice_DetachUser(user2);
	clike_print("Detach user2 result: %d (1=success)\n", detach_result);
	if (1 != detach_result)
	{
		clike_print("Detach should succeed immediately\n");
		goto cleanup;
	}

	// Invoke again
	_callback_count_ = 0;
	ret = MdNotice_Invoke(node, 101, (any)0x5678, 201);
	clike_print("Second invoke returned: %d, total callbacks: %d\n", ret, _callback_count_);

	if (2 != _callback_count_ || 2 != _callback_data_[0] || 1 != _callback_data_[1] || 2 != _callback_data_[2])
	{
		clike_print("Second callback counts incorrect\n");
		goto cleanup;
	}

	// Cleanup
	MdNotice_DetachUser(user1);
	MdNotice_DetachUser(user3);
	MdNotice_DeleteNode(node);
	MdNotice_Exit();

	clike_print("Basic test passed\n");
	return 0;

cleanup:
	MdNotice_DeleteNode(node);
	MdNotice_Exit();
	clike_print("Basic test failed\n");
	return -1;
}

CMD int test_notice_error_handling(void) /* test Notice error handling */
{
	clike_print("=== Notice error handling test ===\n");

	MdNotice_Init(10);
	MdNoticeNode *node = MdNotice_CreateNode(MdNoticeId_Invisible);
	clike_assert(!node, return -1);

	_callback_count_ = 0;
	memset(_callback_data_, 0, sizeof(_callback_data_));

	// Attach users: normal, error, always
	MdNoticeUser *user1 = MdNotice_AttachUser(node, _test_callback_, &_callback_data_[0], 0);
	MdNoticeUser *user2 = MdNotice_AttachUser(node, _test_callback_error_, &_callback_data_[1], 0);
	MdNoticeUser *user3 = MdNotice_AttachUser(node, _test_callback_always_, &_callback_data_[2], MD_NOTICE_FLAG_AL);

	clike_assert(!user1 || !user2 || !user3, goto cleanup);

	clike_print("Attached 3 users (normal, error, always)\n");

	// Invoke - should stop after error but always callback should still be called
	int ret = MdNotice_Invoke(node, 300, (any)0xABCD, 400);
	clike_print("Invoke with error returned: %d, total callbacks: %d\n", ret, _callback_count_);

	// Should be: user1 called, user2 called (returns error), user3 called (always flag)
	if (3 != _callback_count_ || -1 != ret)
	{
		clike_print("Error handling incorrect: count=%d, ret=%d\n", _callback_count_, ret);
		goto cleanup;
	}

	if (1 != _callback_data_[0] || 1 != _callback_data_[1] || 1 != _callback_data_[2])
	{
		clike_print(
			"Callback data incorrect: %d, %d, %d\n", _callback_data_[0], _callback_data_[1], _callback_data_[2]);
		goto cleanup;
	}

	// Cleanup
	MdNotice_DetachUser(user1);
	MdNotice_DetachUser(user2);
	MdNotice_DetachUser(user3);
	MdNotice_DeleteNode(node);
	MdNotice_Exit();

	clike_print("Error handling test passed\n");
	return 0;

cleanup:
	MdNotice_DeleteNode(node);
	MdNotice_Exit();
	clike_print("Error handling test failed\n");
	return -1;
}

// Context for concurrent detach test
typedef struct
{
	MdNoticeNode *node;
	MdNoticeUser *user;
	int callback_count;
	int detach_attempts;
	int detach_success;
} _detach_context_t;

// Callback that takes some time
static int _slow_callback_(any pobj, unv what, any ptr, unv num)
{
	_detach_context_t *ctx = (_detach_context_t *)pobj;
	ctx->callback_count++;
	clike_print("  Slow callback start: count=%d\n", ctx->callback_count);

	// Simulate some work
	clike_delay_ms(100);

	clike_print("  Slow callback end: count=%d\n", ctx->callback_count);
	return 0;
}

// Context for self-detach test
typedef struct
{
	MdNoticeUser *user;
	int detach_called;
	int detach_result;
	int callback_count;
} _auto_detach_context_t;

// Callback that tries to detach itself
static int _self_detach_callback_(any pobj, unv what, any ptr, unv num)
{
	_auto_detach_context_t *ctx = (_auto_detach_context_t *)pobj;
	ctx->callback_count++;
	clike_print("  Self-detach callback called: count=%d\n", ctx->callback_count);

	// Try to detach self (should fail because we're in callback)
	if (!ctx->detach_called)
	{
		ctx->detach_called = 1;
		ctx->detach_result = MdNotice_DetachUser(ctx->user);
		clike_print("  Self-detach attempt result: %d (should be 0)\n", ctx->detach_result);
	}

	return 0;
}

// Test for MD_NOTICE_FLAG_DT functionality
CMD int test_notice_auto_detach(void) /* test MD_NOTICE_FLAG_DT one-time detach behavior */
{
	clike_print("=== Notice MD_NOTICE_FLAG_DT test ===\n");

	MdNotice_Init(10);
	MdNoticeNode *node = MdNotice_CreateNode(MdNoticeId_Invisible);
	clike_assert(!node, return -1);

	_callback_count_ = 0;
	memset(_callback_data_, 0, sizeof(_callback_data_));

	// Test scenario 1: Detach succeeds immediately (not during callback)
	clike_print("--- Test 1: Immediate detach success ---\n");
	MdNoticeUser *user1 = MdNotice_AttachUser(node, _test_callback_, &_callback_data_[0], MD_NOTICE_FLAG_DT);
	clike_assert(!user1, goto cleanup);

	// Detach immediately (should succeed)
	int detach_result = MdNotice_DetachUser(user1);
	clike_print("Immediate detach result: %d (should be 1)\n", detach_result);
	if (1 != detach_result)
	{
		clike_print("Immediate detach should succeed\n");
		goto cleanup;
	}

	// Test scenario 2: Detach during callback (should fail, then auto-delete)
	clike_print("--- Test 2: Detach during callback ---\n");

	_auto_detach_context_t ctx = {0};

	ctx.user = MdNotice_AttachUser(node, _self_detach_callback_, &ctx, MD_NOTICE_FLAG_DT);
	clike_assert(!ctx.user, goto cleanup);

	// Invoke notification - callback will try to detach itself
	int ret = MdNotice_Invoke(node, 100, (any)0x1234, 200);
	clike_print("Invoke result: %d, callback_count: %d\n", ret, ctx.callback_count);

	// Check results
	if (1 != ctx.callback_count)
	{
		clike_print("Callback should be called once: %d\n", ctx.callback_count);
		goto cleanup;
	}

	if (!ctx.detach_called)
	{
		clike_print("Detach should have been called in callback\n");
		goto cleanup;
	}

	if (0 != ctx.detach_result)
	{
		clike_print("Self-detach should fail (return 0) during callback: %d\n", ctx.detach_result);
		goto cleanup;
	}

	// Try to invoke again - user should be gone (auto-deleted after callback)
	_callback_count_ = 0;
	ret = MdNotice_Invoke(node, 101, (any)0x5678, 201);
	clike_print("Second invoke result: %d, callback_count: %d\n", ret, _callback_count_);

	if (0 != _callback_count_)
	{
		clike_print("Second invoke should not call any callbacks (user auto-deleted): %d\n", _callback_count_);
		goto cleanup;
	}

	clike_print("MD_NOTICE_FLAG_DT test passed\n");
	MdNotice_DeleteNode(node);
	MdNotice_Exit();
	return 0;

cleanup:
	if (node) MdNotice_DeleteNode(node);
	MdNotice_Exit();
	clike_print("MD_NOTICE_FLAG_DT test failed\n");
	return -1;
}

// External detach thread function
static any _external_detach_thread_(any arg)
{
	_detach_context_t *ctx = (_detach_context_t *)arg;

	clike_print("External detach thread started\n");

	// Wait a bit to ensure invoke is running
	clike_delay_ms(50);

	// Loop until detach succeeds (this is allowed without MD_NOTICE_FLAG_DT)
	while (ctx->detach_attempts < 20)
	{
		ctx->detach_attempts++;
		int result = MdNotice_DetachUser(ctx->user);
		clike_print("  External detach attempt %d: result=%d\n", ctx->detach_attempts, result);

		if (result)
		{
			ctx->detach_success = 1;
			clike_print("  External detach succeeded on attempt %d\n", ctx->detach_attempts);
			break;
		}

		// Wait a bit before retrying
		clike_delay_ms(20);
	}

	clike_print("External detach thread finished\n");
	return 0;
}

// Test for external thread detach without MD_NOTICE_FLAG_DT
CMD int test_notice_external_detach(void) /* test external thread detach loop */
{
	clike_print("=== Notice external detach test ===\n");

	MdNotice_Init(10);
	MdNoticeNode *node = MdNotice_CreateNode(MdNoticeId_Invisible);
	clike_assert(!node, return -1);

	_detach_context_t ctx = {0};
	ctx.node = node;
	ctx.callback_count = 0;
	ctx.detach_attempts = 0;
	ctx.detach_success = 0;

	// Attach a slow callback WITHOUT MD_NOTICE_FLAG_DT
	ctx.user = MdNotice_AttachUser(node, _slow_callback_, &ctx, 0);
	clike_assert(!ctx.user, goto cleanup);

	clike_print("Attached slow callback user (no MD_NOTICE_FLAG_DT)\n");

	// Start external detach thread
	MdThread *detach_thread = MdThread_Boot(_external_detach_thread_, &ctx, "external_detach");
	clike_assert(!detach_thread, goto cleanup);

	// Invoke notification (this will take time)
	clike_print("Starting invoke (will take ~100ms)\n");
	int ret = MdNotice_Invoke(node, 500, (any)0xDEAD, 600);
	clike_print("Invoke completed: ret=%d, callback_count=%d\n", ret, ctx.callback_count);

	// Wait for detach thread
	clike_print("Waiting for external detach thread to complete...\n");
	MdThread_Wait(detach_thread);

	// Check results
	if (1 != ctx.callback_count)
	{
		clike_print("Callback should be called once: %d\n", ctx.callback_count);
		goto cleanup;
	}

	if (!ctx.detach_success)
	{
		clike_print("External detach should eventually succeed: attempts=%d\n", ctx.detach_attempts);
		goto cleanup;
	}

	clike_print("External detach test passed: detach succeeded after %d attempts\n", ctx.detach_attempts);

	// Cleanup
	MdNotice_DeleteNode(node);
	MdNotice_Exit();
	return 0;

cleanup:
	if (node) MdNotice_DeleteNode(node);
	MdNotice_Exit();
	clike_print("External detach test failed\n");
	return -1;
}
