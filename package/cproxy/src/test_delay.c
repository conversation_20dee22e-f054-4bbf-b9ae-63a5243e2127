//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: test_delay.c
// Author		: XuCF
// Created On	: 2025/03/18
// Description	: test_delay.c
//
// History
// 1. V1.0, Created by XuCF. 2025/03/18
//=============================================================================

#include "libcustom.h"
#include "autosdk_server.h"

API int cproxy_delay(/* delay for a while */
	S32(I) ms		 /* delay time in milliseconds */
)
{
	clike_print("delay %dms\n", ms);
	clike_delay_ms(ms);
	return AUTOSDK_STAT_DONE;
}
