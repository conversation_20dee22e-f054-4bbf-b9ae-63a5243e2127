//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: cproxy_main.c
// Author		: XuCF
// Created On	: 2024/10/25
// Description	: cproxy_main.c
//
// History
// 1. V1.0, Created by XuCF. 2024/10/25
//=============================================================================

#include "autocmd.h"
#include "cbeans.cst.h"
#include "cproxy.svr.h"
#include "MdRunner.h"
#include "MdNotice.h"
#include "cproxy.cmd.h"

any CpAutoSdkServer_New(any notice, any writer)
{
	any AutoSdkServer_New(any notice, any writer, const any *func_tb, const any *hand_tb, int func_cnt);
	return AutoSdkServer_New(notice, writer, _autosdk_func_tb_, _autosdk_hand_tb_, ARRAY_CNT(_autosdk_func_tb_));
}

non CpAutoSdkServer_Del(any obj)
{
	void AutoSdkServer_Del(any obj);
	AutoSdkServer_Del(obj);
}

int main(int argc, const char **args)
{
#ifdef _SSTAR_
	s32 KNL_Time_Init(void);
	s32 HAL_Boot(void);
	s32 HAL_MdCmdTest_Run(void);
	KNL_Time_Init();
	HAL_Boot();
#endif
#ifndef _WINPC_
	if (argc < 2)
	{
		// return HAL_MdCmdTest_Run();
		CproxyServer cproxy_server;
		CproxyServer_Init(&cproxy_server);
		MdNotice_Invoke(cproxy_server.booter, 0, Null, 0);
		MdThread_Main();
		return 0;
	}
#endif
	return autocmd_entry(argc, args, _funcs_tb_);
}
