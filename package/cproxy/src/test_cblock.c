//=============================================================================
// Copyright (C), 2004-2025, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: test_cblock.c
// Author		: XuCF
// Created On	: 2025/06/27
// Description	: test for cblock and MdRecycler
//
// History
// 1. V1.0, Created by XuCF. 2025/06/27
//=============================================================================

#include "cblock.h"
#include "MdRecycler.h"
#include "MdRunner.h"
#include "autocmd.h"
#include "libcustom.h"
#include "clink.h"

#define _TEST_INIT_CAPACITY_ 10
#define _TEST_ITERATIONS_	 10000
#define _TEST_THREAD_COUNT_	 4

// Test data structure
typedef struct
{
	slink_t super;
	char data[256];
	int magic;
	int init_count; // Track initialization calls
} test_block_t;

// Block initialization function
static non _test_block_init_(any block)
{
	test_block_t *tb = (test_block_t *)block;
	memset(tb->data, 0, sizeof(tb->data));
	tb->magic = 0xDEADBEEF;
	tb->init_count++;
	clike_print("    Block initialized at %p: init_count=%d\n", tb, tb->init_count);
}

// Block cleanup function
static non _test_block_free_(any block)
{
	test_block_t *tb = (test_block_t *)block;
	clike_print("    Block cleanup at %p: magic=0x%x -> 0xFEEDFACE\n", tb, tb->magic);
	// Clear sensitive data
	memset(tb->data, 0xFF, sizeof(tb->data));
	tb->magic = 0xFEEDFACE;
}

// Basic functionality test
CMD int test_cblock_basic(void) /* test cblock basic functionality */
{
	clike_print("=== Basic cblock test ===\n");

	// Create pool using type-safe macro
	cblock_t *pool = cblock_new(test_block_t, _TEST_INIT_CAPACITY_);
	clike_assert(!pool, return -1);

	clike_print("Pool created successfully\n");
	cblock_dump(pool);

	// Test allocation and deallocation
	test_block_t *blocks[20];
	for (int i = 0; i < 20; i++)
	{
		blocks[i] = (test_block_t *)cblock_get(pool);
		clike_assert(!blocks[i], goto cleanup);
		// Write pattern to verify memory integrity
		memset(blocks[i]->data, i + 1, sizeof(blocks[i]->data));
		blocks[i]->magic = i + 1000;
	}

	clike_print("Allocated 20 blocks (should trigger expansion)\n");
	cblock_dump(pool);

	// Verify data integrity
	for (int i = 0; i < 20; i++)
	{
		for (int j = 0; j < sizeof(blocks[i]->data); j++)
		{
			if (blocks[i]->data[j] != (char)(i + 1))
			{
				clike_print("Data corruption detected in block %d at offset %d\n", i, j);
				goto cleanup;
			}
		}
		if (blocks[i]->magic != i + 1000)
		{
			clike_print("Magic corruption detected in block %d\n", i);
			goto cleanup;
		}
	}

	clike_print("Data integrity verified\n");

	// Return half blocks
	for (int i = 0; i < 10; i++) { cblock_put(pool, blocks[i]); }

	clike_print("Returned 10 blocks\n");
	cblock_dump(pool);

	// Return remaining blocks
	for (int i = 10; i < 20; i++) { cblock_put(pool, blocks[i]); }

	clike_print("All blocks returned\n");
	cblock_dump(pool);

	cblock_del(pool);
	clike_print("Basic test passed\n");
	return 0;

cleanup:
	cblock_del(pool);
	clike_print("Basic test failed\n");
	return -1;
}

// Test with initialization and cleanup functions
CMD int test_cblock_advanced(void) /* test cblock with init/free functions */
{
	clike_print("=== Advanced cblock test (with init/free functions) ===\n");

	// Create pool with initialization and cleanup functions
	cblock_t *pool = cblock_new_adv(test_block_t, 3, _test_block_init_, _test_block_free_);
	clike_assert(!pool, return -1);

	clike_print("Advanced pool created successfully (should see 3 init calls)\n");
	cblock_dump(pool);

	// Get some blocks and verify they were initialized
	test_block_t *blocks[5];
	for (int i = 0; i < 5; i++)
	{
		clike_print("Getting block %d...\n", i);
		blocks[i] = (test_block_t *)cblock_get(pool);
		clike_assert(!blocks[i], goto cleanup);

		// Verify initialization (should be initialized when chunk was created)
		if (0xDEADBEEF != blocks[i]->magic || blocks[i]->init_count < 1)
		{
			clike_print("Block %d not properly initialized\n", i);
			goto cleanup;
		}

		clike_print("Block %d: magic=0x%x, init_count=%d\n", i, blocks[i]->magic, blocks[i]->init_count);

		// Modify the block
		snprintf(blocks[i]->data, sizeof(blocks[i]->data), "Block %d data", i);
		blocks[i]->magic = 0x12345678 + i;
	}

	clike_print("Got 5 blocks (should trigger expansion and see more init calls)\n");
	cblock_dump(pool);

	// Return blocks (should NOT trigger cleanup function)
	for (int i = 0; i < 5; i++)
	{
		clike_print("Returning block %d (no cleanup should happen)\n", i);
		cblock_put(pool, blocks[i]);

		// Verify cleanup was NOT called during put
		if (0xFEEDFACE == blocks[i]->magic)
		{
			clike_print("ERROR: Block %d cleanup called during put!\n", i);
			goto cleanup;
		}
	}

	clike_print("Advanced test completed, destroying pool (should see cleanup calls)\n");
	cblock_dump(pool);

	// This should trigger cleanup for all blocks
	cblock_del(pool);
	clike_print("Advanced test passed\n");
	return 0;

cleanup:
	cblock_del(pool);
	clike_print("Advanced test failed\n");
	return -1;
}

// Stress test structure
typedef struct
{
	cblock_t *pool;
	int thread_id;
	int iterations;
	int success_count;
	int error_count;
} _stress_context_t;

// MdRecycler basic test
CMD int test_recycler_basic(void) /* test MdRecycler basic functionality */
{
	clike_print("=== Basic MdRecycler test ===\n");

	// Create recycler using type-safe macro
	any recycler = MdRecycler_New(test_block_t, _TEST_INIT_CAPACITY_);
	clike_assert(!recycler, return -1);

	// Normal allocation and deallocation
	test_block_t *block1 = (test_block_t *)MdRecycler_Get(recycler);
	test_block_t *block2 = (test_block_t *)MdRecycler_Get(recycler);
	clike_assert(!block1 || !block2, goto cleanup);

	clike_print("Allocated 2 blocks\n");
	MdRecycler_Dump(recycler);

	// Test data integrity
	memset(block1->data, 0xAA, sizeof(block1->data));
	block1->magic = 0xDEAD;
	memset(block2->data, 0xBB, sizeof(block2->data));
	block2->magic = 0xBEEF;

	MdRecycler_Put(recycler, block1);
	clike_print("Returned block1\n");

	MdRecycler_Put(recycler, block2);
	clike_print("Returned block2\n");

	MdRecycler_Dump(recycler);

	MdRecycler_Del(recycler);
	clike_print("Basic MdRecycler test passed\n");
	return 0;

cleanup:
	MdRecycler_Del(recycler);
	clike_print("Basic MdRecycler test failed\n");
	return -1;
}

// MdRecycler advanced test
CMD int test_recycler_advanced(void) /* test MdRecycler with init/free functions */
{
	clike_print("=== Advanced MdRecycler test ===\n");

	// Create recycler with initialization and cleanup functions
	any recycler = MdRecycler_NewAdv(test_block_t, 2, _test_block_init_, _test_block_free_);
	clike_assert(!recycler, return -1);

	clike_print("Advanced recycler created (should see 2 init calls)\n");

	// Get blocks and verify initialization
	test_block_t *blocks[3];
	for (int i = 0; i < 3; i++)
	{
		clike_print("Getting block %d from recycler...\n", i);
		blocks[i] = (test_block_t *)MdRecycler_Get(recycler);
		clike_assert(!blocks[i], goto cleanup);

		if (0xDEADBEEF != blocks[i]->magic)
		{
			clike_print("Block %d not initialized properly\n", i);
			goto cleanup;
		}

		clike_print("Block %d initialized: magic=0x%x, init_count=%d\n", i, blocks[i]->magic, blocks[i]->init_count);

		// Modify block
		blocks[i]->magic = 0xABCD0000 + i;
	}

	clike_print("Got 3 blocks (should trigger expansion and more init calls)\n");
	MdRecycler_Dump(recycler);

	// Return blocks (no cleanup should happen)
	for (int i = 0; i < 3; i++)
	{
		clike_print("Returning block %d to recycler (no cleanup should happen)\n", i);
		MdRecycler_Put(recycler, blocks[i]);

		// Verify cleanup was NOT called
		if (0xFEEDFACE == blocks[i]->magic)
		{
			clike_print("ERROR: Block %d cleanup called during put!\n", i);
			goto cleanup;
		}
	}

	clike_print("Advanced MdRecycler test completed, destroying recycler (should see cleanup calls)\n");
	MdRecycler_Dump(recycler);

	MdRecycler_Del(recycler);
	clike_print("Advanced MdRecycler test passed\n");
	return 0;

cleanup:
	MdRecycler_Del(recycler);
	clike_print("Advanced MdRecycler test failed\n");
	return -1;
}

// MdRecycler stress test context
typedef struct
{
	any recycler;
	int thread_id;
	int iterations;
	int success_count;
	int error_count;
} _recycler_stress_context_t;

// MdRecycler stress test thread
static any _recycler_stress_thread_(any arg)
{
	_recycler_stress_context_t *ctx = (_recycler_stress_context_t *)arg;
	test_block_t *blocks[50];
	int allocated = 0;

	clike_print("\tRecycler stress thread %d started\n", ctx->thread_id);

	for (int i = 0; i < ctx->iterations; i++)
	{
		if (allocated < 50 && (rand() % 2))
		{
			// Allocate block
			blocks[allocated] = (test_block_t *)MdRecycler_Get(ctx->recycler);
			if (blocks[allocated])
			{
				// Write pattern
				memset(blocks[allocated]->data, ctx->thread_id, sizeof(blocks[allocated]->data));
				blocks[allocated]->magic = ctx->thread_id + 2000;
				allocated++;
				ctx->success_count++;
			}
			else { ctx->error_count++; }
		}
		else if (allocated > 0)
		{
			// Free random block
			int idx = rand() % allocated;

			// Verify pattern
			for (int j = 0; j < sizeof(blocks[idx]->data); j++)
			{
				if (blocks[idx]->data[j] != (char)ctx->thread_id)
				{
					ctx->error_count++;
					break;
				}
			}
			if (blocks[idx]->magic != ctx->thread_id + 2000) { ctx->error_count++; }

			MdRecycler_Put(ctx->recycler, blocks[idx]);
			blocks[idx] = blocks[allocated - 1];
			allocated--;
		}

		if (0 == i % 500) { clike_delay_ms(1); }
	}

	// Free remaining blocks
	for (int i = 0; i < allocated; i++) { MdRecycler_Put(ctx->recycler, blocks[i]); }

	clike_print("\tRecycler stress thread %d finished: %d success, %d errors\n", ctx->thread_id, ctx->success_count,
		ctx->error_count);
	return 0;
}

CMD int test_recycler_stress(void) /* test MdRecycler thread safety */
{
	clike_print("=== MdRecycler stress test ===\n");

	any recycler = MdRecycler_New(test_block_t, _TEST_INIT_CAPACITY_);
	clike_assert(!recycler, return -1);

	_recycler_stress_context_t contexts[_TEST_THREAD_COUNT_];
	MdThread *threads[_TEST_THREAD_COUNT_];

	// Create stress threads
	for (int i = 0; i < _TEST_THREAD_COUNT_; i++)
	{
		contexts[i].recycler = recycler;
		contexts[i].thread_id = i;
		contexts[i].iterations = _TEST_ITERATIONS_ / 2; // Less iterations for safety overhead
		contexts[i].success_count = 0;
		contexts[i].error_count = 0;

		char thread_name[32];
		snprintf(thread_name, sizeof(thread_name), "recycler_%d", i);
		threads[i] = MdThread_Boot(_recycler_stress_thread_, &contexts[i], thread_name);
	}

	clike_print("Started %d recycler stress threads\n", _TEST_THREAD_COUNT_);

	// Wait for all threads
	for (int i = 0; i < _TEST_THREAD_COUNT_; i++) { MdThread_Wait(threads[i]); }

	// Collect statistics
	int total_success = 0, total_errors = 0;
	for (int i = 0; i < _TEST_THREAD_COUNT_; i++)
	{
		total_success += contexts[i].success_count;
		total_errors += contexts[i].error_count;
	}

	clike_print("Recycler stress test completed\n");
	clike_print("Total operations: %d success, %d errors\n", total_success, total_errors);
	MdRecycler_Dump(recycler);

	MdRecycler_Del(recycler);

	if (total_errors > 0)
	{
		clike_print("Recycler stress test failed with %d errors\n", total_errors);
		return -1;
	}

	clike_print("Recycler stress test passed\n");
	return 0;
}