# @sstar
[cproxy-test]
srcs = [
    "../../test/*.c",
    "src/linux/*.c|*.ob",
    "src/sstar/*.c|*.ob",
    "src/sstar/cbeans.cst.h",
    "src/sstar/cbeans.otb.h",
    "src/sstar/cproxy.cmd.h",
    "../../test/cproxy.py",
    "src/sstar/cproxy.py:cli",
    "src/sstar/cproxy.svr.h",
    "src/sstar/cpropr.ctx.h",
    "src/sstar/cpropr.cfg.h",
]
incp = ["src/linux", "src/sstar"]
copt = ["-D_SSTAR_"]
deps = ["appmisc", "cpropr", "network", "gvcp"]
