//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: cmdexample.c
// Author		: XuCF
// Created On	: 2024/05/21
// Description	: cmdexample.c
//
// History
// 1. V1.0, Created by XuCF. 2024/05/21
//=============================================================================

#include "autocmd.h"

CMD int example(					/* Autocmdmaker example. */
	TEXT("default-value") arg1, /* TEXT argument example. */
	CODE(0) arg2,				/* CODE argument example. */
	NUMB(0) arg3,				/* NUMB argument example. */
	FOBJ("hello.txt") arg4		/* Output file example. */
)
{
	clike_print("Arg1=%s, Arg2=%d, Arg3=0x%lx\n", arg1, arg2, arg3);
	fobj_write(arg4, "hello you\n", 10);
	return 0;
}
