// Auto generated by autocmd.py, don't edit
#include "autocmd.h"
code_t example(text_t arg1, code_t arg2, numb_t arg3, fobj_t arg4);
static code_t _example_cwrp_(void)
{
	code_t ret;
	text_t arg1 = cmdparser_get_txt("arg1", "default-value");
	code_t arg2 = cmdparser_get_num("arg2", 0);
	numb_t arg3 = cmdparser_get_num("arg3", 0);
	fobj_t arg4 = fobj_create(cmdparser_get_txt("arg4", "hello.txt"));
	ret = example(arg1, arg2, arg3, arg4);
	fobj_delete(arg4);
	return ret;
}
static argv_t _example_args_[] = {
	{"arg1", "TEXT argument example.", "default-value"},
	{"arg2", "CODE argument example.", "0"},
	{"arg3", "NUMB argument example.", "0"},
	{"arg4", "Output file example.", "hello.txt"},
};
static func_t _example_func_ = {
	// command data >>
	.cwrp = _example_cwrp_,
	.name = "example",
	.desc = "Autocmdmaker example.",
	.args = _example_args_,
	.argn = 4
	// command data <<
};
func_t *_funcs_tb_[] = {
	&_example_func_,
	0,
};
