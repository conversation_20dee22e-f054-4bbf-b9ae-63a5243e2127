#!/usr/bin/env python

import os
import re
import sys
from dutil import MEcho


PTFUN = re.compile(r"CMD int\s+(\w+)\((void\))?\s*(/\*)([\s\S]*)?(\*/)")
PTARG = re.compile(r"\s*(\w+)(\()(.*?)(\))\s+(\w+)(,?)\s*(/\*)([\s\S]*)?(\*/)")


def _hex_(numb, size):
    out = []
    for i in range(0, size << 3, 8):
        byte = (numb >> i) & 0xff
        out.append(f"\\x{byte:02x}")
    return "".join(out)


class MyArgv:
    def __init__(self, name, desc, typx, defv):
        self.name = name
        self.desc = desc
        self.typx = typx.lower() + "_t"
        self.defv = defv


class MyFunc:
    def __init__(self):
        self.iden = 0
        self.name = None
        self.desc = None
        self.args = []


def _find_commands_in_src_(psrc):

    funcs, func = [], MyFunc()

    def _state_start_(line):
        nonlocal funcs, func
        m = PTFUN.match(line)
        if not m:
            return 0
        func.name = m.group(1)
        func.desc = m.group(4).strip()
        if m.group(2):  # void func
            funcs.append(func)
            func = MyFunc()
            return 0
        return 1

    def _state_args_(line):
        nonlocal funcs, func
        m = PTARG.match(line)
        if not m:
            return 0
        typx = m.group(1)
        defv = m.group(3)
        name = m.group(5)
        desc = m.group(8).strip()
        argv = MyArgv(name, desc, typx, defv)
        func.args.append(argv)
        ends = m.group(6)
        if ends == "":
            funcs.append(func)
            func = MyFunc()
            return 0
        return 1

    state_tb = [
        _state_start_,
        _state_args_,
    ]

    with open(psrc, "r", errors="ignore") as fp:
        stat = 0
        for line in fp:
            if stat == -1:
                break
            stat = state_tb[stat](line)
    return funcs


def _find_commands_all_(psrcs):
    funcs = []
    for item in psrcs:
        if os.path.isfile(item):
            funcs += _find_commands_in_src_(item)
        elif os.path.isdir(item):
            for src in os.listdir(item):
                src = os.path.join(item, src)
                if os.path.isfile(src) and src.endswith(".c"):
                    funcs += _find_commands_in_src_(src)
    return funcs


def _update_(psrcs, ccout):
    def _write_func_decl_(func, fp):
        arg_list = []
        for arg in func.args:
            arg = f"{arg.typx} {arg.name}"
            arg_list.append(arg)
        arg_list_str = ", ".join(arg_list)
        if arg_list_str == "":
            arg_list_str = "void"
        declare = f"code_t {func.name}({arg_list_str});\n"
        fp.write(declare)

    def _write_func_cwrp_(func, fp):
        arg_list = []
        for arg in func.args:
            arg_list.append(arg.name)
        arg_list_str = ", ".join(arg_list)
        fp.write(f"static code_t _{func.name}_cwrp_(void)\n")
        fp.write("{\n")
        fp.write("\tcode_t ret;\n")
        for arg in func.args:
            if "fobj_t" == arg.typx:
                expr = f"cmdparser_get_txt(\"{arg.name}\", {arg.defv})"
                expr = f"fobj_create({expr})"
            elif "text_t" == arg.typx:
                expr = f"cmdparser_get_txt(\"{arg.name}\", {arg.defv})"
            else:
                expr = f"cmdparser_get_num(\"{arg.name}\", {arg.defv})"
            fp.write(f"\t{arg.typx} {arg.name} = {expr};\n")
        fp.write(f"\tret = {func.name}({arg_list_str});\n")
        for arg in reversed(func.args):
            if "fobj_t" == arg.typx:
                fp.write(f"\tfobj_delete({arg.name});\n")
        fp.write("\treturn ret;\n")
        fp.write("}\n")

    def _write_func_body_(func, fp):
        fp.write(f"static argv_t _{func.name}_args_[] = {{\n")
        for arg in func.args:
            defv = arg.defv if arg.defv.startswith("\"") else f"\"{arg.defv}\""
            fp.write(f"\t{{\"{arg.name}\", \"{arg.desc}\", {defv}}},\n")
        fp.write("};\n")

        fp.write(f"static func_t _{func.name}_func_ = {{\n")
        fp.write("\t// command data >>\n")
        fp.write(f"\t.cwrp = _{func.name}_cwrp_,\n")
        fp.write(f"\t.name = \"{func.name}\",\n")
        fp.write(f"\t.desc = \"{func.desc}\",\n")
        fp.write(f"\t.args = _{func.name}_args_,\n")
        fp.write(f"\t.argn = {len(func.args)}\n")
        fp.write("\t// command data <<\n")
        fp.write("};\n")

    def _write_func_body_tb_(func, fp):
        fp.write(f"\t&_{func.name}_func_,\n")

    funcs = _find_commands_all_(psrcs)
    funcs.sort(key=lambda n: n.name)
    for idx, func in enumerate(funcs):
        func.iden = idx

    with open(ccout, "w") as fp:
        fp.write("// Auto generated by autocmd.py, don't edit\n")
        fp.write("#include \"autocmd.h\"\n")
        for func in funcs:
            _write_func_decl_(func, fp)
        for func in funcs:
            _write_func_cwrp_(func, fp)
        for func in funcs:
            _write_func_body_(func, fp)
        fp.write("func_t *_funcs_tb_[] = {\n")
        for func in funcs:
            _write_func_body_tb_(func, fp)
        fp.write("\t0,\n};\n")


def check_param_with_key(inputs):
    vkey = None
    args = []
    opts = {}
    for idx, arg in enumerate(inputs):
        if arg.startswith("--"):
            if "=" in arg:
                key, val = arg[2::].split("=", 1)
                opts[key] = val
            else:
                opts[arg[2::]] = True
        elif idx == 0:
            continue
        elif vkey is None:
            vkey = arg
        else:
            args.append(arg)
    return vkey, args, opts


def mkcmd(application):
    sources, entry_cc = [], None
    for item in application["SRCS"]:
        if entry_cc is None and item.endswith(".cmd.h"):
            entry_cc = item
        elif (item.endswith(".c") or item.endswith(".cpp")) and os.access(item, os.R_OK):
            sources.append(item)
    if entry_cc:
        MEcho.i(f"[---][MC] {entry_cc}")
        _update_(sources, entry_cc)


def main():
    vkey, args, opts = check_param_with_key(sys.argv)
    if "update" == vkey:
        spath = os.path.normpath(os.path.dirname(__file__))
        if len(args) == 0:
            args.append(spath)
        entry_cc = opts.get("cc", os.path.join(spath, "cmdentry.c"))
        _update_(args, entry_cc)


if __name__ == '__main__':
    main()
