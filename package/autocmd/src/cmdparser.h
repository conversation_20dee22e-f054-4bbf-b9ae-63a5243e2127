//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: cmdparser.h
// Author		: XuCF
// Created On	: 2024/05/15
// Description	: cmdparser.h
//
// History
// 1. V1.0, Created by XuCF. 2024/05/15
//=============================================================================

#ifndef _CMDPARSER_H
#define _CMDPARSER_H

#include "libcustom.h"

typedef char char_t;
typedef const char *text_t;
typedef int code_t;
typedef unsigned long numb_t;
typedef void void_t;

void_t cmdparser_init(code_t argn, text_t *args);
void_t cmdparser_free(void_t);

text_t cmdparser_key(void_t);
code_t cmdparser_cnt(void_t);
text_t cmdparser_arg(code_t idx);
text_t cmdparser_opt(text_t key);

code_t cmdparser_chk_key(text_t val);
code_t cmdparser_fzy_key(text_t val);
code_t cmdparser_chk_opt(text_t key, text_t val);
numb_t cmdparser_get_num(text_t key, numb_t def);
text_t cmdparser_get_txt(text_t key, text_t def);

#endif //_CMDPARSER_H
