//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: autocmd.h
// Author		: XuCF
// Created On	: 2024/05/21
// Description	: autocmd.h
//
// History
// 1. V1.0, Created by XuCF. 2024/05/21
//=============================================================================

#ifndef _AUTOCMD_H
#define _AUTOCMD_H

#ifdef __cplusplus
extern "C"
{
#endif

#include "cmdparser.h"
#include "fobj.h"

	typedef code_t (*cwrp_t)(void);

	typedef struct
	{
		text_t name;
		text_t desc;
		text_t defv;
	} argv_t;

	typedef struct
	{
		cwrp_t cwrp;
		text_t name;
		text_t desc;
		argv_t *args;
		code_t argn;
	} func_t;

	code_t autocmd_entry(code_t argc, text_t *args, func_t **ftb);

#ifdef __cplusplus
}
#endif

#define TEXT(...) text_t
#define CODE(...) code_t
#define NUMB(...) numb_t
#define FOBJ(...) fobj_t

#ifdef __cplusplus
#define CMD extern "C"
#else
#define CMD
#endif

#endif //_AUTOCMD_H
