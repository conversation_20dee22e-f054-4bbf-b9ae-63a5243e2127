//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: fobj.h
// Author		: XuCF
// Created On	: 2024/05/28
// Description	: fobj.h
//
// History
// 1. V1.0, Created by XuCF. 2024/05/28
//=============================================================================

#ifndef _FOBJ_H
#define _FOBJ_H

typedef struct fobj_s *fobj_t;

fobj_t fobj_create(const char *path);
void fobj_delete(fobj_t thiz);
int fobj_read(fobj_t thiz, void *data, int size);
int fobj_write(fobj_t thiz, void *data, int size);
int fobj_print(fobj_t thiz, const char *fmt, ...);

#endif //_FOBJ_H
