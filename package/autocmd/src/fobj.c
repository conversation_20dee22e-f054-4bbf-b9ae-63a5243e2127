//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: fobj.c
// Author		: XuCF
// Created On	: 2024/05/28
// Description	: fobj.c
//
// History
// 1. V1.0, Created by XuCF. 2024/05/28
//=============================================================================

#include "fobj.h"
#include "libcustom.h"

struct fobj_s
{
	FILE *fp;
};

fobj_t fobj_create(const char *path)
{
	fobj_t thiz = clike_new(struct fobj_s);
	if (0 == strcmp(path, "stdout")) thiz->fp = stdout;
	else
	{
		thiz->fp = fopen(path, "w"); // Open file
	}
	return thiz;
}

void fobj_delete(fobj_t thiz)
{
	if (thiz->fp) fclose(thiz->fp);
	clike_free(thiz);
}

int fobj_read(fobj_t thiz, void *data, int size)
{
	return 0;
}

int fobj_write(fobj_t thiz, void *data, int size)
{
	return fwrite(data, size, 1, thiz->fp);
}

int fobj_print(fobj_t thiz, const char *fmt, ...)
{
	int ret = 0;
	char data[1024] = {0};
	unsigned int size = 0;
	va_list ap;

	va_start(ap, fmt);
	size = vsnprintf((char *)data, sizeof(data), (const char *)fmt, ap);
	va_end(ap);
	Assert((size > sizeof(data) - 1), size = sizeof(data) - 1);
	ret = fobj_write(thiz, data, size);

	return ret;
}
