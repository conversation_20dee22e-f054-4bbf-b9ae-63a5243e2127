//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: autocmd.c
// Author		: XuCF
// Created On	: 2024/05/21
// Description	: autocmd.c
//
// History
// 1. V1.0, Created by XuCF. 2024/05/21
//=============================================================================

#include "libcustom.h"
#include "autocmd.h"
#include "cmdparser.h"

code_t autocmd_entry(code_t argc, text_t *args, func_t **ftb)
{
	int i;
	func_t *func;
	cmdparser_init(argc, args);
	if (cmdparser_opt("help"))
	{
		if (cmdparser_key())
		{
			clike_print("usage: [options]\n");
			clike_print("options:\n");
			for (i = 0; (func = ftb[i]); i++)
			{
				if (cmdparser_chk_key(func->name))
				{
					argv_t *argv;
					for (i = 0; i < func->argn; i++)
					{
						argv = func->args + i;
						clike_print("    --%-20s%s\n", argv->name, argv->desc);
					}
					break;
				}
			}
		}
		else
		{
			clike_print("usage: [options] [command]\n");
			clike_print("commands:\n");
			for (i = 0; (func = ftb[i]); i++)
			{
				// description
				clike_print("    %-40s%s\n", func->name, func->desc);
			}
			clike_print("options:\n");
			clike_print("    --help              show help info\n");
		}
	}
	else if (cmdparser_opt("auto"))
	{
		for (i = 0; (func = ftb[i]); i++) func->cwrp();
	}
	else if (cmdparser_opt("fuzz"))
	{
		for (i = 0; (func = ftb[i]); i++)
		{
			if (cmdparser_fzy_key(func->name))
			{
				clike_print("-----> %s\n", func->name);
				func->cwrp();
				clike_print("\n");
			}
		}
	}
	else
	{
		for (i = 0; (func = ftb[i]); i++)
		{
			if (cmdparser_chk_key(func->name))
			{
				func->cwrp();
				break;
			}
		}
	}
	cmdparser_free();
	return 0;
}
