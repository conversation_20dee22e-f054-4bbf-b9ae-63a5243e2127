//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: cmdparser.c
// Author		: XuCF
// Created On	: 2024/05/15
// Description	: cmdparser.c
//
// History
// 1. V1.0, Created by XuCF. 2024/05/15
//=============================================================================

#include "cmdparser.h"
#include "libcustom.h"

typedef struct
{
	text_t *args;
	code_t argn;
} cmdparser_t;

static cmdparser_t cmdparserobj, *thiz = &cmdparserobj;

void_t cmdparser_init(code_t argn, text_t *args)
{
	thiz->argn = argn;
	thiz->args = (text_t *)args;
}

void_t cmdparser_free(void_t)
{
}

static code_t _letter_chk_(char_t letter)
{
	if ('a' <= letter && 'z' >= letter) return 1;
	if ('A' <= letter && 'Z' >= letter) return 1;
	return 0;
}

static code_t _bin_chk_(char_t letter)
{
	if ('0' <= letter && '1' >= letter) return letter - '0';
	return -1;
}

static code_t _oct_chk_(char_t letter)
{
	if ('0' <= letter && '7' >= letter) return letter - '0';
	return -1;
}

static code_t _digital_chk_(char_t letter)
{
	if ('0' <= letter && '9' >= letter) return letter - '0';
	return -1;
}

static code_t _hex_chk_(char_t letter)
{
	if ('0' <= letter && '9' >= letter) return letter - '0';
	if ('a' <= letter && 'f' >= letter) return letter - ('a' - 10);
	if ('A' <= letter && 'F' >= letter) return letter - ('A' - 10);
	return -1;
}

static code_t _compare_chr_(char_t one, char_t two)
{
	if (one == two) return 1;
	if ('_' != one && '-' != one) return 0;
	if ('_' != two && '-' != two) return 0;
	return 1;
}

static code_t _compare_str_(text_t one, text_t two)
{
	code_t i = 0;
	for (i = 0;; i++)
	{
		if (!_compare_chr_(one[i], two[i])) return 0;
		if (one[i] == 0) break;
	}
	return 1;
}

static code_t _compare_fzy_(text_t one, text_t two)
{
	code_t i = 0;
	for (i = 0;; i++)
	{
		if (one[i] == 0) break;
		if (!_compare_chr_(one[i], two[i])) return 0;
	}
	return 1;
}

static numb_t _text_to_numb_(text_t text)
{
	numb_t value = 0;
	code_t minus = 0, shift = 0, curr;
	code_t (*chk)(char_t letter);
	curr = *(text++);
	if ('0' == curr)
	{
		switch (*(text++))
		{
			case 'x': shift = 4, chk = _hex_chk_; break;
			case 'o': shift = 3, chk = _oct_chk_; break;
			case 'b': shift = 1, chk = _bin_chk_; break;
			default: return 0;
		}
		while ((curr = chk(*(text++))) >= 0) value = (value << shift) + curr;
	}
	else
	{
		if ('-' == curr) minus = 1;
		while ((curr = _digital_chk_(curr)) >= 0)
		{
			value = value * 10 + curr;
			curr = *(text++);
		}
		if (minus) value = -value;
	}
	return value;
}

static code_t _opt_chk_(text_t clip)
{
	if (clip[0] != '-') return 0;
	if (clip[1] != '-') return 0;
	if (!_letter_chk_(clip[2])) return 0;
	return 1;
}

static text_t _opt_get_(text_t clip, text_t key)
{
	code_t i;
	if (*(clip++) != '-') return 0;
	if (*(clip++) != '-') return 0;
	for (i = 0;; i++)
	{
		if (key[i] == 0) break;
		if (clip[i] != key[i]) return 0;
	}
	if (clip[i] != '=') return clip + i;
	return clip + i + 1;
}

text_t cmdparser_key(void_t)
{
	code_t i;
	text_t tmp;
	for (i = 1; i < thiz->argn; i++)
	{
		tmp = thiz->args[i];
		if (!_opt_chk_(tmp)) return tmp;
	}
	return 0;
}

code_t cmdparser_cnt(void_t)
{
	code_t i, cnt = 0;
	text_t tmp;
	for (i = 1; i < thiz->argn; i++)
	{
		tmp = thiz->args[i];
		if (!_opt_chk_(tmp)) cnt++;
	}
	return cnt > 0 ? cnt - 1 : 0;
}

text_t cmdparser_arg(code_t idx)
{
	code_t i;
	idx++;
	text_t tmp;
	for (i = 1; i < thiz->argn; i++)
	{
		tmp = thiz->args[i];
		if (!_opt_chk_(tmp))
		{
			if (0 == idx) return tmp;
			idx--;
		}
	}
	return 0;
}

text_t cmdparser_opt(text_t key)
{
	code_t i;
	text_t tmp;
	for (i = 1; i < thiz->argn; i++)
	{
		tmp = thiz->args[i];
		tmp = _opt_get_(tmp, key);
		if (tmp) return tmp;
	}
	return 0;
}

code_t cmdparser_chk_key(text_t val)
{
	text_t tmp = cmdparser_key();
	if (!tmp) return 0;
	return _compare_str_(tmp, val);
}

code_t cmdparser_fzy_key(text_t val)
{
	text_t tmp = cmdparser_key();
	if (!tmp) return 0;
	return _compare_fzy_(tmp, val);
}

code_t cmdparser_chk_opt(text_t key, text_t val)
{
	text_t tmp = cmdparser_opt(key);
	if (!tmp) return 0;
	return _compare_str_(tmp, val);
}

numb_t cmdparser_get_num(text_t key, numb_t de)
{
	text_t value_str = cmdparser_opt(key);
	if (!value_str) return de;
	return _text_to_numb_(value_str);
}

text_t cmdparser_get_txt(text_t key, text_t de)
{
	text_t value_str = cmdparser_opt(key);
	if (!value_str) return de;
	return value_str;
}
