[PathKeys]
ImagingCtr = "ImagingCtr"
UCIPGroup1 = "UCIPGroup1"
UCIPGroup2 = "UCIPGroup2"
UCIPGroup3 = "UCIPGroup3"
UCIPGroup4 = "UCIPGroup4"
UCIPGroup5 = "UCIPGroup5"
UCIPGroup6 = "UCIPGroup6"
UCIPGroup7 = "UCIPGroup7"
UCIPGroup8 = "UCIPGroup8"
IllControl = "IllControl"
AimControl = "AimControl"
DataProcessing = "DataProcessing"
DataFilter = "DataFilter"
Rule1 = "Rule1"
Rule2 = "Rule2"
Rule3 = "Rule3"
Rule4 = "Rule4"
Rule5 = "Rule5"
Rule6 = "Rule6"
Rule7 = "Rule7"
Rule8 = "Rule8"
Rule9 = "Rule9"
Rule10 = "Rule10"
AlgorithmCtr = "AlgorithmCtr"
Code = "Code"
Code39 = "Code39"
Code128 = "Code128"
Codabar = "Codabar"
Interleaved25 = "Interleaved25"
Code93 = "Code93"
EAN-8 = "EAN-8"
EAN-13 = "EAN-13"
UPCA = "UPCA"
UPCE = "UPCE"
Code11 = "Code11"
GS1EXP = "GS1EXP"
GS1DataBar = "GS1DataBar"
GS1DataBarLimited = "GS1DataBarLimited"
ISBT128 = "ISBT128"
MSIPL = "MSIPL"
UCCEAN128 = "UCCEAN128"
UKPL = "UKPL"
UPCE1 = "UPCE1"
CHNPST = "CHNPST"
Industrial25 = "Industrial25"
Matrix25 = "Matrix25"
QRCode = "QRCode"
DM = "DM"
Aztec = "Aztec"
CodaBlockF = "CodaBlockF"
GM = "GM"
MaxiCode = "MaxiCode"
MICPDF417 = "MICPDF417"
MicroQR = "MicroQR"
PDF417 = "PDF417"
BinaryCode = "BinaryCode"
CSCode = "CSCode"
DotCode = "DotCode"
CHNFNC = "CHNFNC"
ROI = "ROI"
DeviceCtr = "Device Control"
NetworkCtr = "Network Control"
InputOutputCtr = "InputOutputCtr"
Transmission = "Transmission"
Modes = "Modes"
Indicators = "Indicators"
CustomIO1 = "CustomIO1"
CustomIO2 = "CustomIO2"
CustomIO3 = "CustomIO3"
CustomIO4 = "CustomIO4"
Input = "Input"
Output = "Output"
Encoding = "Encoding"
OutputFormat = "OutputFormat"
InsertStr = "InsertStr"
Communication = "Communication"
Interface = "Interface"
USB = "USB"
IndustrialProtocol = "IndustrialProtocol"
Profinet = "Profinet"
TCPClient = "TCPClient"
TCPServer = "TCPServer"
RS232 = "RS232"
FocalCtrValue = "FocalCtrValue"
Reserved = "Reserved"
TriggerConfig = "TriggerConfig"
Delays = "Delays"
ImageControl = "ImageControl"
[Temperature]
LABEL = "Temperature"
HELP = "Device temperature in celsius"
[UCIPGroupFinishPUB]
LABEL = "UCIPGroup Finish"
HELP = "Finish custom imaging parameters group configuration"
[UCIPGroup1Enable]
LABEL = "Enable"
HELP = "Enable custom imaging parameters group 1"
[UCIPGroup2Enable]
LABEL = "Enable"
HELP = "Enable custom imaging parameters group 2"
[UCIPGroup3Enable]
LABEL = "Enable"
HELP = "Enable custom imaging parameters group 3"
[UCIPGroup4Enable]
LABEL = "Enable"
HELP = "Enable custom imaging parameters group 4"
[UCIPGroup5Enable]
LABEL = "Enable"
HELP = "Enable custom imaging parameters group 5"
[UCIPGroup6Enable]
LABEL = "Enable"
HELP = "Enable custom imaging parameters group 6"
[UCIPGroup7Enable]
LABEL = "Enable"
HELP = "Enable custom imaging parameters group 7"
[UCIPGroup8Enable]
LABEL = "Enable"
HELP = "Enable custom imaging parameters group 8"
[UCIPGroup1Expo]
LABEL = "Exposure Time(us)"
HELP = "Set custom imaging parameters group 1 exposure time in microseconds"
[UCIPGroup2Expo]
LABEL = "Exposure Time(us)"
HELP = "Set custom imaging parameters group 2 exposure time in microseconds"
[UCIPGroup3Expo]
LABEL = "Exposure Time(us)"
HELP = "Set custom imaging parameters group 3 exposure time in microseconds"
[UCIPGroup4Expo]
LABEL = "Exposure Time(us)"
HELP = "Set custom imaging parameters group 4 exposure time in microseconds"
[UCIPGroup5Expo]
LABEL = "Exposure Time(us)"
HELP = "Set custom imaging parameters group 5 exposure time in microseconds"
[UCIPGroup6Expo]
LABEL = "Exposure Time(us)"
HELP = "Set custom imaging parameters group 6 exposure time in microseconds"
[UCIPGroup7Expo]
LABEL = "Exposure Time(us)"
HELP = "Set custom imaging parameters group 7 exposure time in microseconds"
[UCIPGroup8Expo]
LABEL = "Exposure Time(us)"
HELP = "Set custom imaging parameters group 8 exposure time in microseconds"
[UCIPGroup1Gain]
LABEL = "Gain"
HELP = "Set custom imaging parameters group 1 gain value"
[UCIPGroup2Gain]
LABEL = "Gain"
HELP = "Set custom imaging parameters group 2 gain value"
[UCIPGroup3Gain]
LABEL = "Gain"
HELP = "Set custom imaging parameters group 3 gain value"
[UCIPGroup4Gain]
LABEL = "Gain"
HELP = "Set custom imaging parameters group 4 gain value"
[UCIPGroup5Gain]
LABEL = "Gain"
HELP = "Set custom imaging parameters group 5 gain value"
[UCIPGroup6Gain]
LABEL = "Gain"
HELP = "Set custom imaging parameters group 6 gain value"
[UCIPGroup7Gain]
LABEL = "Gain"
HELP = "Set custom imaging parameters group 7 gain value"
[UCIPGroup8Gain]
LABEL = "Gain"
HELP = "Set custom imaging parameters group 8 gain value"
[UCIPGroup1LuminLevel]
LABEL = "Luminance Level"
HELP = "Set custom imaging parameters group 1 fill light brightness level"
[UCIPGroup2LuminLevel]
LABEL = "Luminance Level"
HELP = "Set custom imaging parameters group 2 fill light brightness level"
[UCIPGroup3LuminLevel]
LABEL = "Luminance Level"
HELP = "Set custom imaging parameters group 3 fill light brightness level"
[UCIPGroup4LuminLevel]
LABEL = "Luminance Level"
HELP = "Set custom imaging parameters group 4 fill light brightness level"
[UCIPGroup5LuminLevel]
LABEL = "Luminance Level"
HELP = "Set custom imaging parameters group 5 fill light brightness level"
[UCIPGroup6LuminLevel]
LABEL = "Luminance Level"
HELP = "Set custom imaging parameters group 6 fill light brightness level"
[UCIPGroup7LuminLevel]
LABEL = "Luminance Level"
HELP = "Set custom imaging parameters group 7 fill light brightness level"
[UCIPGroup8LuminLevel]
LABEL = "Luminance Level"
HELP = "Set custom imaging parameters group 8 fill light brightness level"
[UCIPGroup1HoldTime]
LABEL = "Hold Time(ms)"
HELP = "Set custom imaging parameters group 1 hold time in milliseconds"
[UCIPGroup2HoldTime]
LABEL = "Hold Time(ms)"
HELP = "Set custom imaging parameters group 2 hold time in milliseconds"
[UCIPGroup3HoldTime]
LABEL = "Hold Time(ms)"
HELP = "Set custom imaging parameters group 3 hold time in milliseconds"
[UCIPGroup4HoldTime]
LABEL = "Hold Time(ms)"
HELP = "Set custom imaging parameters group 4 hold time in milliseconds"
[UCIPGroup5HoldTime]
LABEL = "Hold Time(ms)"
HELP = "Set custom imaging parameters group 5 hold time in milliseconds"
[UCIPGroup6HoldTime]
LABEL = "Hold Time(ms)"
HELP = "Set custom imaging parameters group 6 hold time in milliseconds"
[UCIPGroup7HoldTime]
LABEL = "Hold Time(ms)"
HELP = "Set custom imaging parameters group 7 hold time in milliseconds"
[UCIPGroup8HoldTime]
LABEL = "Hold Time(ms)"
HELP = "Set custom imaging parameters group 8 hold time in milliseconds"
[IlluminationMode]
LABEL = "Illumination Mode"
HELP = "Set illumination mode for decoding"
[IlluminationMode.Options]
ALWAYS_OFF = "Always off"
ALWAYS_ON = "Always on"
FLASH = "Flash mode"
ON_WHEN_READING = "On when reading"
[IlluminationLevel]
LABEL = "Illumination Level"
HELP = "Set illumination brightness level"
[IlluminationLevel.Options]
LOW = "Low brightness"
MID = "Medium brightness"
HIGH = "High brightness"
[LightGroupSelect]
LABEL = "Light Group Select"
HELP = "Select light source group for dual-color illumination"
[LightGroupSelect.Options]
GROUP1 = "Group 1"
GROUP2 = "Group 2"
[AimMode]
LABEL = "Aim Mode"
HELP = "Set aim mode for decoding"
[DataFilterMode]
LABEL = "Data Filter Mode"
HELP = "Set data filter mode"
[DataFilterMode.Options]
Disable = "Disable"
Normal = "Normal"
Advanced = "Advanced"
[DataFilterEnable1]
LABEL = "Data Filter Enable 1"
HELP = "Set normal data filter enable"
[DataFilterEnable2]
LABEL = "Data Filter Enable 2"
HELP = "Set normal data filter enable"
[DataFilterEnable3]
LABEL = "Data Filter Enable 3"
HELP = "Set normal data filter enable"
[DataFilterEnable4]
LABEL = "Data Filter Enable 4"
HELP = "Set normal data filter enable"
[DataFilterFunc1]
LABEL = "Match Function 1"
HELP = "Set match function for regex 1"
[DataFilterFunc1.Options]
FILTER = "Filter"
DELETE = "Delete"
[DataFilterFunc2]
LABEL = "Match Function 2"
HELP = "Set match function for regex 2"
[DataFilterFunc3]
LABEL = "Match Function 3"
HELP = "Set match function for regex 3"
[DataFilterFunc4]
LABEL = "Match Function 4"
HELP = "Set match function for regex 4"
[DataFilterFunc5]
LABEL = "Match Function 5"
HELP = "Set match function for regex 5"
[DataFilterFunc6]
LABEL = "Match Function 6"
HELP = "Set match function for regex 6"
[DataFilterFunc7]
LABEL = "Match Function 7"
HELP = "Set match function for regex 7"
[DataFilterFunc8]
LABEL = "Match Function 8"
HELP = "Set match function for regex 8"
[DataFilterFunc9]
LABEL = "Match Function 9"
HELP = "Set match function for regex 9"
[DataFilterFunc10]
LABEL = "Match Function 10"
HELP = "Set match function for regex 10"
[DataFilterRegex1]
LABEL = "Regex Pattern 1"
HELP = "Set scanning regex pattern 1 for data formatting and validation"
[DataFilterRegex2]
LABEL = "Regex Pattern 2"
HELP = "Set scanning regex pattern 2 for data formatting and validation"
[DataFilterRegex3]
LABEL = "Regex Pattern 3"
HELP = "Set scanning regex pattern 3 for data formatting and validation"
[DataFilterRegex4]
LABEL = "Regex Pattern 4"
HELP = "Set scanning regex pattern 4 for data formatting and validation"
[DataFilterRegex5]
LABEL = "Regex Pattern 5"
HELP = "Set scanning regex pattern 5 for data formatting and validation"
[DataFilterRegex6]
LABEL = "Regex Pattern 6"
HELP = "Set scanning regex pattern 6 for data formatting and validation"
[DataFilterRegex7]
LABEL = "Regex Pattern 7"
HELP = "Set scanning regex pattern 7 for data formatting and validation"
[DataFilterRegex8]
LABEL = "Regex Pattern 8"
HELP = "Set scanning regex pattern 8 for data formatting and validation"
[DataFilterRegex9]
LABEL = "Regex Pattern 9"
HELP = "Set scanning regex pattern 9 for data formatting and validation"
[DataFilterRegex10]
LABEL = "Regex Pattern 10"
HELP = "Set scanning regex pattern 10 for data formatting and validation"
[AimDecoVideoSetDecoTimeMs]
LABEL = "Single Decoding Time Limit (ms)"
HELP = "Set the single decoding time limit"
[DoubleConfirm]
LABEL = "Double Confirm(times)"
HELP = "Number of confirmations required (0-9)"
[SameBarDelay1D]
LABEL = "1D Same Code Delay(ms)"
HELP = "1D barcode same code delay time in 50ms units"
[SameBarDelay2D]
LABEL = "2D Same Code Delay(ms)"
HELP = "2D barcode same code delay time in 50ms units"
[GoodReadDelay]
LABEL = "Good Read Delay(ms)"
HELP = "Delay after successful decode in ms units"
[CodeSymb1D]
LABEL = "1D Code Symbologies Read"
HELP = "Control which 1D code types can be read"
[CodeSymb1D.Options]
AS_PARA = "Follow custom settings for each 1D code type"
DISABLE_ALL = "Disable reading of all 1D codes"
ENABLE_ALL = "Enable reading of all 1D codes"
[CodeSymb2D]
LABEL = "2D Code Symbologies Read"
HELP = "Control which 2D code types can be read"
[CodeSymb2D.Options]
AS_PARA = "Follow custom settings for each 2D code type"
DISABLE_ALL = "Disable reading of all 2D codes"
ENABLE_ALL = "Enable reading of all 2D codes"
PDF417_ONLY = "Enable PDF417 only"
QRCODE_ONLY = "Enable QR code only"
DATAMATRIX_ONLY = "Enable DataMatrix only"
MAXICODE_ONLY = "Enable MaxiCode only"
AZTECCODE_ONLY = "Enable AztecCode only"
CSCODE_ONLY = "Enable Chinese Sensible code only"
[MultiSymbRead]
LABEL = "Multi-Symbol Read"
HELP = "Control multi-symbol read mode"
[MultiSymbRead.Options]
DISABLE = "Disable multi-symbol read"
ONLY_1D = "Read multiple 1D codes only"
ONLY_2D = "Read multiple 2D codes only"
BOTH_1D_2D = "Read both 1D and 2D codes"
[CenteringRead]
LABEL = "Centering Read Mode"
HELP = "Control centering decode mode"
[CenteringRead.Options]
NONE = "Disable centering read mode"
VERTICAL = "Enable vertical centering mode"
NEAR = "Enable near center mode"
[ScreenRead]
LABEL = "Screen Code Read"
HELP = "Enable/Disable reading codes from mobile screens"
[Code39ReadMain]
LABEL = "Code 39 Read"
HELP = "Enable/Disable reading of Code 39 codes"
[Code39ChkDigVer]
LABEL = "Code 39 Check Digit Verification"
HELP = "Enable/Disable Code 39 check digit verification"
[Code39ChkDigTx]
LABEL = "Code 39 Check Digit Transmission"
HELP = "Enable/Disable Code 39 check digit transmission"
[Code39MaxLen]
LABEL = "Code 39 Maximum Length"
HELP = "Set maximum length for Code 39 codes"
[Code39MinLen]
LABEL = "Code 39 Minimum Length"
HELP = "Set minimum length for Code 39 codes"
[Code39CodeID]
LABEL = "Code 39 Code ID"
HELP = "Set custom code identifier for Code 39"
[Code39InsertStr]
LABEL = "Code 39 Insert String"
HELP = "Select the string group to be inserted for Code 39"
[Code39Format]
LABEL = "Code 39 Format"
HELP = "Set data transmission format for Code 39"
[Code39Format.Options]
STANDARD = "Standard format"
FULL_ASCII = "Full ASCII format"
[Code39StEdTrans]
LABEL = "Code 39 Start/Stop Character Transmission"
HELP = "Enable/Disable transmission of start/stop characters for Code 39"
[Code39AsteAsChar]
LABEL = "Code 39 Asterisk as Data Character"
HELP = "Enable/Disable asterisk as data character for Code 39"
[Code39Code32]
LABEL = "Code 39 to Code 32 Conversion"
HELP = "Enable/Disable Code 39 to Code 32 conversion"
[Code39Code32Pre]
LABEL = "Code 39 Code 32 Prefix"
HELP = "Enable/Disable transmission of prefix 'A' for Code 32"
[Code39Trioptic]
LABEL = "Code 39 Trioptic"
HELP = "Enable/Disable reading of Trioptic Code 39"
[Code39TOStEdTrans]
LABEL = "Code 39 Trioptic Start/Stop Transmission"
HELP = "Enable/Disable transmission of Trioptic Code 39 start/stop characters"
[Code39AimID]
LABEL = "Code 39 Aim ID"
HELP = "Set Code 39 Aim ID"
[Code128ReadMain]
LABEL = "Code 128 Read"
HELP = "Enable/Disable reading of Code 128 codes"
[Code128ChkDigVer]
LABEL = "Code 128 Check Digit Verification"
HELP = "Enable/Disable Code 128 check digit verification"
[Code128ChkDigTx]
LABEL = "Code 128 Check Digit Transmission"
HELP = "Enable/Disable Code 128 check digit transmission"
[Code128MaxLen]
LABEL = "Code 128 Maximum Length"
HELP = "Set maximum length for Code 128 codes"
[Code128MinLen]
LABEL = "Code 128 Minimum Length"
HELP = "Set minimum length for Code 128 codes"
[Code128CodeID]
LABEL = "Code 128 Code ID"
HELP = "Set custom code identifier for Code 128"
[Code128InsertStr]
LABEL = "Code 128 Insert String Group"
HELP = "Select the string group to be inserted for Code 128"
[Code128TruZero]
LABEL = "Code 128 Truncate Leading Zeros"
HELP = "Control truncation of leading zeros for Code 128"
[Code128TruZero.Options]
DISABLE = "Do not truncate leading zeros"
ALL = "Truncate all leading zeros"
FIRST = "Truncate only first leading zero"
[Code128AimID]
LABEL = "Code 128 Aim ID"
HELP = "Set Code 128 Aim ID"
[CodabarReadMain]
LABEL = "Codabar Read"
HELP = "Enable/Disable reading of Codabar codes"
[CodabarChkDigVer]
LABEL = "Codabar Check Digit Verify"
HELP = "Enable/Disable check digit verification for Codabar"
[CodabarChkDigTx]
LABEL = "Codabar Check Digit Transmit"
HELP = "Enable/Disable check digit transmission for Codabar"
[CodabarMaxLen]
LABEL = "Codabar Maximum Length"
HELP = "Set maximum length for Codabar codes"
[CodabarMinLen]
LABEL = "Codabar Minimum Length"
HELP = "Set minimum length for Codabar codes"
[CodabarCodeID]
LABEL = "Codabar Code ID"
HELP = "Set custom code identifier for Codabar"
[CodabarInsertStr]
LABEL = "Codabar Insert String"
HELP = "Select the string group to be inserted for Codabar"
[CodabarStartStopType]
LABEL = "Codabar Start/Stop Type"
HELP = "Set start/stop character type for Codabar"
[CodabarStartStopType.Options]
ABCD = "ABCD type"
abcd = "abcd type"
TNE = "ABCD/TN*E type"
tne = "abcd/tn*e type"
[CodabarStartStopTx]
LABEL = "Codabar Start/Stop Transmit"
HELP = "Enable/Disable start/stop character transmission for Codabar"
[CodabarStartEndCharEq]
LABEL = "Codabar Start/End Character Equal"
HELP = "Enable/Disable requirement for equal start/end characters in Codabar"
[CodabarAimID]
LABEL = "Codabar Aim ID"
HELP = "Set Codabar Aim ID"
[Intl25ReadMain]
LABEL = "Interleaved 2 of 5 Read"
HELP = "Enable/Disable reading of Interleaved 2 of 5 codes"
[Intl25ChkDigVer]
LABEL = "Interleaved 2 of 5 Check Digit Verification"
HELP = "Enable/Disable Interleaved 2 of 5 check digit verification"
[Intl25ChkDigVer.Options]
DISABLE = "Disable"
USS = "USS"
OPCC = "OPCC"
[Intl25ChkDigTx]
LABEL = "Interleaved 2 of 5 Check Digit Transmission"
HELP = "Enable/Disable Interleaved 2 of 5 check digit transmission"
[Intl25MaxLen]
LABEL = "Interleaved 2 of 5 Maximum Length"
HELP = "Set maximum length for Interleaved 2 of 5 codes"
[Intl25MinLen]
LABEL = "Interleaved 2 of 5 Minimum Length"
HELP = "Set minimum length for Interleaved 2 of 5 codes"
[Intl25CodeID]
LABEL = "Interleaved 2 of 5 Code ID"
HELP = "Set custom code identifier for Interleaved 2 of 5"
[Intl25InsertStr]
LABEL = "Interleaved 2 of 5 Insert String Group"
HELP = "Select the string group to be inserted for Interleaved 2 of 5"
[Intl25LenRestr]
LABEL = "Interleaved 2 of 5 Length Restriction"
HELP = "Control length restriction for Interleaved 2 of 5"
[Intl25LenRestr.Options]
ANY = "No length restriction"
ONLY_44 = "Only 44 characters"
[Intl25BankFormatConv]
LABEL = "Interleaved 2 of 5 Bank Format Conversion"
HELP = "Enable/Disable bank format conversion for Interleaved 2 of 5"
[Intl25BankFormatType]
LABEL = "Interleaved 2 of 5 Bank Format Type"
HELP = "Set bank format type for Interleaved 2 of 5"
[Intl25BankFormatType.Options]
NO_TAB = "No Tab insertion"
ALGUNS = "ALGUNS rule Tab insertion"
TODOS = "TODOS OS rule Tab insertion"
[Intl25AimID]
LABEL = "Interleaved 2 of 5 Aim ID"
HELP = "Set Interleaved 2 of 5 Aim ID"
[Code93ReadMain]
LABEL = "Code 93 Read"
HELP = "Enable/Disable reading of Code 93 codes"
[Code93ChkDigVer]
LABEL = "Code 93 Check Digit Verify"
HELP = "Enable/Disable check digit verification for Code 93"
[Code93ChkDigTx]
LABEL = "Code 93 Check Digit Transmit"
HELP = "Enable/Disable check digit transmission for Code 93"
[Code93MaxLen]
LABEL = "Code 93 Maximum Length"
HELP = "Set maximum length for Code 93 codes"
[Code93MinLen]
LABEL = "Code 93 Minimum Length"
HELP = "Set minimum length for Code 93 codes"
[Code93CodeID]
LABEL = "Code 93 Code ID"
HELP = "Set custom code identifier for Code 93"
[Code93InsertStr]
LABEL = "Code 93 Insert String Group"
HELP = "Select the string group to be inserted for Code 93"
[Code93AimID]
LABEL = "Code 93 Aim ID"
HELP = "Set Code 93 Aim ID"
[EAN8ReadMain]
LABEL = "EAN-8 Read"
HELP = "Enable/Disable reading of EAN-8 codes"
[EAN8ChkDigVer]
LABEL = "EAN-8 Check Digit Verification"
HELP = "Enable/Disable EAN-8 check digit verification"
[EAN8ChkDigTx]
LABEL = "EAN-8 Check Digit Transmission"
HELP = "Enable/Disable EAN-8 check digit transmission"
[EAN8CodeID]
LABEL = "EAN-8 Code ID"
HELP = "Set custom code identifier for EAN-8"
[EAN8InsertStr]
LABEL = "EAN-8 Insert String Group"
HELP = "Select the string group to be inserted for EAN-8"
[EAN8SuplDig]
LABEL = "EAN-8 Supplemental Digits"
HELP = "Control EAN-8 supplemental digits options"
[EAN8SuplDig.Options]
NONE = "No supplemental digits"
TWO_DIGITS = "Two supplemental digits"
FIVE_DIGITS = "Five supplemental digits"
TWO_OR_FIVE = "Two or five supplemental digits"
ONLY_WITH_SUPPLEMENTAL = "Only decode with supplemental digits"
[EAN8TrunExp]
LABEL = "EAN-8 Truncation/Expansion"
HELP = "Control EAN-8 truncation and expansion options"
[EAN8TrunExp.Options]
NONE = "No truncation or expansion"
TRUNCATE = "Truncate leading zeros"
EXPAND = "Expand to EAN-13"
[EAN8AimID]
LABEL = "EAN-8 Aim ID"
HELP = "Set EAN-8 Aim ID"
[EAN13ReadMain]
LABEL = "EAN-13 Read"
HELP = "Enable/Disable reading of EAN-13 codes"
[EAN13ChkDigVer]
LABEL = "EAN-13 Check Digit Verification"
HELP = "Enable/Disable EAN-13 check digit verification"
[EAN13ChkDigTx]
LABEL = "EAN-13 Check Digit Transmission"
HELP = "Enable/Disable EAN-13 check digit transmission"
[EAN13CodeID]
LABEL = "EAN-13 Code ID"
HELP = "Set custom code identifier for EAN-13"
[EAN13InsertStr]
LABEL = "EAN-13 Insert String Group"
HELP = "Select the string group to be inserted for EAN-13"
[EAN13SuplDig]
LABEL = "EAN-13 Supplemental Digits"
HELP = "Control EAN-13 supplemental digits options"
[EAN13SuplDig.Options]
NONE = "No supplemental digits"
TWO_DIGITS = "Two supplemental digits"
FIVE_DIGITS = "Five supplemental digits"
TWO_OR_FIVE = "Two or five supplemental digits"
ONLY_WITH_SUPPLEMENTAL = "Only decode with supplemental digits"
[EAN13ISBSN]
LABEL = "EAN13 ISBN/ISSN Conversion"
HELP = "Enable/Disable ISBN/ISSN conversion for EAN-13"
[EAN13ISSNISBNID]
LABEL = "EAN-13 ISBN/ISSN ID"
HELP = "Set custom code identifier for EAN-13 ISBN/ISSN"
[EAN13AimID]
LABEL = "EAN-13 Aim ID"
HELP = "Set EAN-13 Aim ID"
[UPCAReadMain]
LABEL = "UPC-A Read"
HELP = "Enable/Disable reading of UPC-A codes"
[UPCAChkDigVer]
LABEL = "UPC-A Check Digit Verification"
HELP = "Enable/Disable UPC-A check digit verification"
[UPCAChkDigTx]
LABEL = "UPC-A Check Digit Transmission"
HELP = "Enable/Disable UPC-A check digit transmission"
[UPCACodeID]
LABEL = "UPC-A Code ID"
HELP = "Set custom code identifier for UPC-A"
[UPCAInsertStr]
LABEL = "UPC-A Insert String Group"
HELP = "Select the string group to be inserted for UPC-A"
[UPCASuplDig]
LABEL = "UPC-A Supplemental Digits"
HELP = "Control UPC-A supplemental digits options"
[UPCASuplDig.Options]
NONE = "No supplemental digits"
TWO_DIGITS = "Two supplemental digits"
FIVE_DIGITS = "Five supplemental digits"
TWO_OR_FIVE = "Two or five supplemental digits"
ONLY_WITH_SUPPLEMENTAL = "Only decode with supplemental digits"
[UPCATrunExp]
LABEL = "UPC-A Truncation/Expansion"
HELP = "Control UPC-A truncation and expansion options"
[UPCATrunExp.Options]
NONE = "No truncation or expansion"
TRUNCATE = "Truncate leading zeros"
EXPAND = "Expand to EAN-13"
[UPCAAimID]
LABEL = "UPC-A Aim ID"
HELP = "Set UPC-A Aim ID"
[UPCEReadMain]
LABEL = "UPC-E Read"
HELP = "Enable/Disable reading of UPC-E codes"
[UPCEChkDigVer]
LABEL = "UPC-E Check Digit Verification"
HELP = "Enable/Disable UPC-E check digit verification"
[UPCEChkDigTx]
LABEL = "UPC-E Check Digit Transmission"
HELP = "Enable/Disable UPC-E check digit transmission"
[UPCECodeID]
LABEL = "UPC-E Code ID"
HELP = "Set custom code identifier for UPC-E"
[UPCEInsertStr]
LABEL = "UPC-E Insert String Group"
HELP = "Select the string group to be inserted for UPC-E"
[UPCESuplDig]
LABEL = "UPC-E Supplemental Digits"
HELP = "Control UPC-E supplemental digits options"
[UPCESuplDig.Options]
NONE = "No supplemental digits"
TWO_DIGITS = "Two supplemental digits"
FIVE_DIGITS = "Five supplemental digits"
TWO_OR_FIVE = "Two or five supplemental digits"
ONLY_WITH_SUPPLEMENTAL = "Only decode with supplemental digits"
[UPCETrunExp]
LABEL = "UPC-E Truncation/Expansion"
HELP = "Control UPC-E truncation and expansion options"
[UPCETrunExp.Options]
NONE = "No truncation or expansion"
TRUNCATE = "Truncate leading zeros"
EXPAND_EAN13 = "Expand to EAN-13"
EXPAND_UPCA = "Expand to UPC-A"
[UPCEAimID]
LABEL = "UPC-E Aim ID"
HELP = "Set UPC-E Aim ID"
[Code11ReadMain]
LABEL = "Code 11 Read"
HELP = "Enable/Disable reading of Code 11 codes"
[Code11ChkDigVer]
LABEL = "Code 11 Check Digit Verify"
HELP = "Set check digit verification for Code 11"
[Code11ChkDigVer.Options]
NONE = "None"
ONE = "One check digit"
TWO = "Two check digits"
RESERVED = "Reserved"
[Code11ChkDigTx]
LABEL = "Code 11 Check Digit Transmit"
HELP = "Enable/Disable check digit transmission for Code 11"
[Code11MaxLen]
LABEL = "Code 11 Maximum Length"
HELP = "Set maximum length for Code 11 codes"
[Code11MinLen]
LABEL = "Code 11 Minimum Length"
HELP = "Set minimum length for Code 11 codes"
[Code11CodeID]
LABEL = "Code 11 Code ID"
HELP = "Set custom code identifier for Code 11"
[Code11InsertStr]
LABEL = "Code 11 Insert String"
HELP = "Select the string group to be inserted for Code 11"
[Code11AimID]
LABEL = "Code 11 Aim ID"
HELP = "Set Code 11 Aim ID"
[GS1EXPAReadMain]
LABEL = "GS1 DataBar Expanded Read"
HELP = "Enable/Disable reading of GS1 DataBar Expanded codes"
[GS1EXPAMaxLen]
LABEL = "GS1 DataBar Expanded Maximum Length"
HELP = "Set maximum length for GS1 DataBar Expanded codes"
[GS1EXPAMinLen]
LABEL = "GS1 DataBar Expanded Minimum Length"
HELP = "Set minimum length for GS1 DataBar Expanded codes"
[GS1EXPACodeID]
LABEL = "GS1 DataBar Expanded Code ID"
HELP = "Set custom code identifier for GS1 DataBar Expanded"
[GS1EXPAInsertStr]
LABEL = "GS1 DataBar Expanded Insert String Group"
HELP = "Select the string group to be inserted for GS1 DataBar Expanded"
[GS1EXPAConversion]
LABEL = "GS1 DataBar Expanded Conversion"
HELP = "Control conversion mode for GS1 DataBar Expanded"
[GS1EXPAConversion.Options]
NONE = "No conversion"
UCC_EAN128 = "Convert to UCC/EAN-128"
[GS1EXPAimID]
LABEL = "GS1 DataBar Expanded Aim ID"
HELP = "Set GS1 DataBar Expanded Aim ID"
[GS1DBReadMain]
LABEL = "GS1 DataBar Read"
HELP = "Enable/Disable reading of GS1 DataBar codes"
[GS1DBCodeID]
LABEL = "GS1 DataBar Code ID"
HELP = "Set custom code identifier for GS1 DataBar"
[GS1DBInsertStr]
LABEL = "GS1 DataBar Insert String Group"
HELP = "Select the string group to be inserted for GS1 DataBar"
[GS1DBConversion]
LABEL = "GS1 DataBar Conversion"
HELP = "Control conversion mode for GS1 DataBar"
[GS1DBConversion.Options]
NONE = "No conversion"
UCC_EAN128 = "Convert to UCC/EAN-128"
UPC_EAN13 = "Convert to UPC/EAN-13"
[GS1DBOutputBrackets]
LABEL = "GS1 DataBar Output Brackets"
HELP = "Enable/Disable output brackets for GS1 DataBar"
[GS1DBAimID]
LABEL = "GS1 DataBar Aim ID"
HELP = "Set GS1 DataBar Aim ID"
[GS1LIMIReadMain]
LABEL = "GS1 DataBar Limited Read"
HELP = "Enable/Disable reading of GS1 DataBar Limited codes"
[GS1LIMICodeID]
LABEL = "GS1 DataBar Limited Code ID"
HELP = "Set custom code identifier for GS1 DataBar Limited"
[GS1LIMIInsertStr]
LABEL = "GS1 DataBar Limited Insert String Group"
HELP = "Select the string group to be inserted for GS1 DataBar Limited"
[GS1LIMIConversion]
LABEL = "GS1 DataBar Limited Conversion"
HELP = "Control conversion mode for GS1 DataBar Limited"
[GS1LIMIConversion.Options]
NONE = "No conversion"
UCC_EAN128 = "Convert to UCC/EAN-128"
UPC_EAN13 = "Convert to UPC/EAN-13"
[GS1LIMIAimID]
LABEL = "GS1 DataBar Limited Aim ID"
HELP = "Set GS1 DataBar Limited Aim ID"
[ISBT128ReadMain]
LABEL = "ISBT128 Read"
HELP = "Enable/Disable reading of ISBT128 codes"
[ISBT128CkDigiVer]
LABEL = "ISBT128 Check Digit Verify"
HELP = "Enable/Disable check digit verification for ISBT128 codes"
[ISBT128ChkDigTrans]
LABEL = "ISBT128 Check Digit Transmit"
HELP = "Enable/Disable check digit transmission for ISBT128 codes"
[ISBT128MaxLen]
LABEL = "ISBT128 Maximum Length"
HELP = "Set maximum length for ISBT128 codes"
[ISBT128MinLen]
LABEL = "ISBT128 Minimum Length"
HELP = "Set minimum length for ISBT128 codes"
[ISBT128CodeID]
LABEL = "ISBT128 Code ID"
HELP = "Set custom code identifier for ISBT128 codes"
[ISBT128InsertStr]
LABEL = "ISBT128 Insert String"
HELP = "Select the string group to be inserted for ISBT128 codes"
[ISBT128AimID]
LABEL = "ISBT 128 Aim ID"
HELP = "Set ISBT 128 Aim ID"
[MSIPLReadMain]
LABEL = "MSI/Plessey Read"
HELP = "Enable/Disable reading of MSI/Plessey codes"
[MSIPLChkDigVer]
LABEL = "MSI/Plessey Check Digit Verification"
HELP = "Control check digit verification for MSI/Plessey"
[MSIPLChkDigVer.Options]
DISABLE = "Disable check digit verification"
ONE_MOD10 = "One MOD10 check digit"
TWO_MOD10 = "Two MOD10 check digits"
MOD10_MOD11 = "MOD10/MOD11 check digits"
[MSIPLChkDigTx]
LABEL = "MSI/Plessey Check Digit Transmission"
HELP = "Enable/Disable MSI/Plessey check digit transmission"
[MSIPLMaxLen]
LABEL = "MSI/Plessey Maximum Length"
HELP = "Set maximum length for MSI/Plessey codes"
[MSIPLMinLen]
LABEL = "MSI/Plessey Minimum Length"
HELP = "Set minimum length for MSI/Plessey codes"
[MSIPLCodeID]
LABEL = "MSI/Plessey Code ID"
HELP = "Set custom code identifier for MSI/Plessey"
[MSIPLInsertStr]
LABEL = "MSI/Plessey Insert String Group"
HELP = "Select the string group to be inserted for MSI/Plessey"
[MSIPLAimID]
LABEL = "MSI/Plessey Aim ID"
HELP = "Set MSI/Plessey Aim ID"
[UCCEAN128ReadMain]
LABEL = "UCC/EAN 128 Read"
HELP = "Enable/Disable reading of UCC/EAN 128 codes"
[UCCEAN128ChkDigVer]
LABEL = "UCC/EAN 128 Check Digit Verification"
HELP = "Enable/Disable UCC/EAN 128 check digit verification"
[UCCEAN128ChkDigTx]
LABEL = "UCC/EAN 128 Check Digit Transmission"
HELP = "Enable/Disable UCC/EAN 128 check digit transmission"
[UCCEAN128MaxLen]
LABEL = "UCC/EAN 128 Maximum Length"
HELP = "Set maximum length for UCC/EAN 128 codes"
[UCCEAN128MinLen]
LABEL = "UCC/EAN 128 Minimum Length"
HELP = "Set minimum length for UCC/EAN 128 codes"
[UCCEAN128CodeID]
LABEL = "UCC/EAN 128 Code ID"
HELP = "Set custom code identifier for UCC/EAN 128"
[UCCEAN128InsertStr]
LABEL = "UCC/EAN 128 Insert String Group"
HELP = "Select the string group to be inserted for UCC/EAN 128"
[UCCEAN128TruZero]
LABEL = "UCC/EAN 128 Truncate Leading Zeros"
HELP = "Control truncation of leading zeros for UCC/EAN 128"
[UCCEAN128TruZero.Options]
DISABLE = "Do not truncate leading zeros"
ALL = "Truncate all leading zeros"
FIRST = "Truncate only first leading zero"
[UCCEAN128AimID]
LABEL = "UCC/EAN 128 Aim ID"
HELP = "Set UCC/EAN 128 Aim ID"
[UKPLReadMain]
LABEL = "UK/Plessey Read"
HELP = "Enable/Disable reading of UK/Plessey codes"
[UKPLChkDigVer]
LABEL = "UK/Plessey Check Digit Verification"
HELP = "Enable/Disable UK/Plessey check digit verification"
[UKPLChkDigTx]
LABEL = "UK/Plessey Check Digit Transmission"
HELP = "Enable/Disable UK/Plessey check digit transmission"
[UKPLMaxLen]
LABEL = "UK/Plessey Maximum Length"
HELP = "Set maximum length for UK/Plessey codes"
[UKPLMinLen]
LABEL = "UK/Plessey Minimum Length"
HELP = "Set minimum length for UK/Plessey codes"
[UKPLCodeID]
LABEL = "UK/Plessey Code ID"
HELP = "Set custom code identifier for UK/Plessey"
[UKPLInsertStr]
LABEL = "UK/Plessey Insert String Group"
HELP = "Select the string group to be inserted for UK/Plessey"
[UKPLAimID]
LABEL = "UK/Plessey Aim ID"
HELP = "Set UK/Plessey Aim ID"
[UPCE1ReadMain]
LABEL = "UPC-E1 Read"
HELP = "Enable/Disable reading of UPC-E1 codes"
[UPCE1ChkDigVer]
LABEL = "UPC-E1 Check Digit Verification"
HELP = "Enable/Disable check digit verification for UPC-E1"
[UPCE1ChkDigTrans]
LABEL = "UPC-E1 Check Digit Transmission"
HELP = "Enable/Disable transmission of check digit for UPC-E1"
[UPCE1CodeID]
LABEL = "UPC-E1 Code ID"
HELP = "Set custom code identifier for UPC-E1"
[UPCE1InsertStr]
LABEL = "UPC-E1 Insert String"
HELP = "Select the string group to be inserted for UPC-E1"
[UPCE1SuplDig]
LABEL = "UPC-E1 Supplemental Digits"
HELP = "Set supplemental digits option for UPC-E1"
[UPCE1SuplDig.Options]
NONE = "No supplemental digits"
TWO = "Two supplemental digits"
FIVE = "Five supplemental digits"
TWO_FIVE = "Two or five supplemental digits"
ONLY = "Only supplemental digits"
[UPCE1TrunExp]
LABEL = "UPC-E1 Truncate/Expand"
HELP = "Set truncation/expansion option for UPC-E1"
[UPCE1TrunExp.Options]
NONE = "No truncation or expansion"
TRUNCATE = "Truncate leading zeros"
EXPAND_EAN13 = "Expand to EAN-13"
EXPAND_UPCA = "Expand to UPC-A"
[UPCE1AimID]
LABEL = "UPC-E1 Aim ID"
HELP = "Set UPC-E1 Aim ID"
[CHNPSTReadMain]
LABEL = "China Post Read"
HELP = "Enable/Disable reading of China Post codes"
[CHNPSTChkDigVer]
LABEL = "China Post Check Digit Verification"
HELP = "Enable/Disable China Post check digit verification"
[CHNPSTChkDigTx]
LABEL = "China Post Check Digit Transmission"
HELP = "Enable/Disable China Post check digit transmission"
[CHNPSTMaxLen]
LABEL = "China Post Maximum Length"
HELP = "Set maximum length for China Post codes"
[CHNPSTMinLen]
LABEL = "China Post Minimum Length"
HELP = "Set minimum length for China Post codes"
[CHNPSTCodeID]
LABEL = "China Post Code ID"
HELP = "Set custom code identifier for China Post"
[CHNPSTInsertStr]
LABEL = "China Post Insert String Group"
HELP = "Select the string group to be inserted for China Post"
[CHNPSTAimID]
LABEL = "China Post Aim ID"
HELP = "Set China Post Aim ID"
[Industrial25ReadMain]
LABEL = "Industrial 25 Read"
HELP = "Enable/Disable reading of Industrial 25 codes"
[Industrial25MaxLen]
LABEL = "Industrial 25 Maximum Length"
HELP = "Set maximum length for Industrial 25 codes"
[Industrial25MinLen]
LABEL = "Industrial 25 Minimum Length"
HELP = "Set minimum length for Industrial 25 codes"
[Industrial25CodeID]
LABEL = "Industrial 25 Code ID"
HELP = "Set custom code identifier for Industrial 25"
[Industrial25InsertStr]
LABEL = "Industrial 2 of 5 Insert String"
HELP = "Select the string group to be inserted for Industrial 2 of 5"
[Industrial25AimID]
LABEL = "Industrial 2 of 5 Aim ID"
HELP = "Set Industrial 2 of 5 Aim ID"
[Matrix25ReadMain]
LABEL = "Matrix 25 Read"
HELP = "Enable/Disable reading of Matrix 25 codes"
[Matrix25ChkDigVer]
LABEL = "Matrix 25 Check Digit Verify"
HELP = "Enable/Disable check digit verification for Matrix 25"
[Matrix25ChkDigTrans]
LABEL = "Matrix 25 Check Digit Transmit"
HELP = "Enable/Disable check digit transmission for Matrix 25"
[Matrix25MaxLen]
LABEL = "Matrix 25 Maximum Length"
HELP = "Set maximum length for Matrix 25 codes"
[Matrix25MinLen]
LABEL = "Matrix 25 Minimum Length"
HELP = "Set minimum length for Matrix 25 codes"
[Matrix25CodeID]
LABEL = "Matrix 25 Code ID"
HELP = "Set custom code identifier for Matrix 25"
[Matrix25InsertStr]
LABEL = "Matrix 2 of 5 Insert String"
HELP = "Select the string group to be inserted for Matrix 2 of 5"
[Matrix25AimID]
LABEL = "Matrix 2 of 5 Aim ID"
HELP = "Set Matrix 2 of 5 Aim ID"
[QRCodeReadMain]
LABEL = "QR Code Read"
HELP = "Enable/Disable reading of QR codes"
[QRCodeCodeID]
LABEL = "QR Code ID"
HELP = "Set custom code identifier for QR Code"
[QRCodeWebCodeForbid]
LABEL = "QR Code Web Code Access"
HELP = "Control access to websites in QR codes"
[QRCodeWebCodeForbid.Options]
ALLOW = "Allow access to websites"
FORBID = "Forbid access to websites"
DOUYIN_ONLY = "Allow access to Douyin websites only"
[QRCodeMaxLen]
LABEL = "QR Code Maximum Length"
HELP = "Set maximum length for QR codes"
[QRCodeMinLen]
LABEL = "QR Code Minimum Length"
HELP = "Set minimum length for QR codes"
[QRCodeAimID]
LABEL = "QR Code Aim ID"
HELP = "Set QR Code Aim ID"
[DMReadMain]
LABEL = "DataMatrix Read"
HELP = "Enable/Disable reading of DataMatrix codes"
[DMCodeID]
LABEL = "DataMatrix Code ID"
HELP = "Set custom code identifier for DataMatrix"
[DMMaxLen]
LABEL = "DataMatrix Maximum Length"
HELP = "Set maximum length for DataMatrix codes"
[DMMinLen]
LABEL = "DataMatrix Minimum Length"
HELP = "Set minimum length for DataMatrix codes"
[DMPPNCODE]
LABEL = "DataMatrix PPN Code"
HELP = "Enable/Disable output of PPN (Pharmacy Product Number) code"
[DMAimID]
LABEL = "DataMatrix Aim ID"
HELP = "Set DataMatrix Aim ID"
[AztecReadMain]
LABEL = "Aztec Read"
HELP = "Enable/Disable reading of Aztec codes"
[AztecCodeID]
LABEL = "Aztec Code ID"
HELP = "Set custom code identifier for Aztec"
[AztecMaxLen]
LABEL = "Aztec Maximum Length"
HELP = "Set maximum length for Aztec codes"
[AztecMinLen]
LABEL = "Aztec Minimum Length"
HELP = "Set minimum length for Aztec codes"
[AztecAimID]
LABEL = "Aztec Code Aim ID"
HELP = "Set Aztec Code Aim ID"
[CodaBlockFReadMain]
LABEL = "CodaBlock F Read"
HELP = "Enable/Disable CodaBlock F code reading"
[CodaBlockFCodeID]
LABEL = "CodaBlock F Code ID"
HELP = "Set code ID for CodaBlock F code"
[CodaBlockFMaxLen]
LABEL = "CodaBlock F Maximum Length"
HELP = "Set maximum length for CodaBlock F code"
[CodaBlockFMinLen]
LABEL = "CodaBlock F Minimum Length"
HELP = "Set minimum length for CodaBlock F code"
[CodaBlockFAimID]
LABEL = "CodaBlock F Aim ID"
HELP = "Set CodaBlock F Aim ID"
[GMCodeReadMain]
LABEL = "GM Code Read"
HELP = "Enable/Disable GM code reading"
[GMCodeCodeID]
LABEL = "GM Code ID"
HELP = "Set code ID for GM code"
[GMCodeMaxLen]
LABEL = "GM Code Maximum Length"
HELP = "Set maximum length for GM code"
[GMCodeMinLen]
LABEL = "GM Code Minimum Length"
HELP = "Set minimum length for GM code"
[MaxiCodeReadMain]
LABEL = "Maxicode Read"
HELP = "Enable/Disable reading of Maxicode codes"
[MaxiCodeMaxLen]
LABEL = "Maxicode Maximum Length"
HELP = "Set maximum length for Maxicode codes"
[MaxiCodeMinLen]
LABEL = "Maxicode Minimum Length"
HELP = "Set minimum length for Maxicode codes"
[MaxiCodeAimID]
LABEL = "MaxiCode Aim ID"
HELP = "Set MaxiCode Aim ID"
[MICPDF417ReadMain]
LABEL = "MicroPDF417 Read"
HELP = "Enable/Disable reading of MicroPDF417 codes"
[MICPDF417CodeID]
LABEL = "MicroPDF417 Code ID"
HELP = "Set custom code identifier for MicroPDF417"
[MICPDF417InsertStr]
LABEL = "MicroPDF417 Insert String"
HELP = "Select the string group to be inserted for MicroPDF417"
[MICPDF417Conversion]
LABEL = "MicroPDF417 Conversion"
HELP = "Set conversion mode for MicroPDF417"
[MICPDF417Conversion.Options]
OFF = "No conversion"
UCCEAN128 = "Convert to UCC/EAN-128"
UPCA_EAN13 = "Convert to UPC-A/EAN-13"
[MICPDF417MaxLen]
LABEL = "MicroPDF417 Maximum Length"
HELP = "Set maximum length for MicroPDF417 codes"
[MICPDF417MinLen]
LABEL = "MicroPDF417 Minimum Length"
HELP = "Set minimum length for MicroPDF417 codes"
[MICPDF417AimID]
LABEL = "MicroPDF417 Aim ID"
HELP = "Set MicroPDF417 Aim ID"
[MicroQRReadMain]
LABEL = "Micro QR Read"
HELP = "Enable/Disable Micro QR code reading"
[MicroQRCodeID]
LABEL = "Micro QR Code ID"
HELP = "Set code ID for Micro QR code"
[MicroQRMaxLen]
LABEL = "Micro QR Maximum Length"
HELP = "Set maximum length for Micro QR code"
[MicroQRMinLen]
LABEL = "Micro QR Minimum Length"
HELP = "Set minimum length for Micro QR code"
[MicroQRAimID]
LABEL = "MicroQR Code Aim ID"
HELP = "Set MicroQR Code Aim ID"
[PDF417ReadMain]
LABEL = "PDF417 Read"
HELP = "Enable/Disable reading of PDF417 codes"
[PDF417CodeID]
LABEL = "PDF417 Code ID"
HELP = "Set custom code identifier for PDF417"
[PDF417InsertStr]
LABEL = "PDF417 Insert String Group"
HELP = "Select the string group to be inserted for PDF417"
[PDF417Conversion]
LABEL = "PDF417 Conversion"
HELP = "Control conversion mode for PDF417"
[PDF417Conversion.Options]
NONE = "No conversion"
UCC_EAN128 = "Convert to UCC/EAN-128"
UPC_EAN13 = "Convert to UPC/EAN-13"
[PDF417MaxLen]
LABEL = "PDF417 Maximum Length"
HELP = "Set maximum length for PDF417 codes"
[PDF417MinLen]
LABEL = "PDF417 Minimum Length"
HELP = "Set minimum length for PDF417 codes"
[PDF417AimID]
LABEL = "PDF417 Aim ID"
HELP = "Set PDF417 Aim ID"
[BinaryCodeReadMain]
LABEL = "Binary Code Read"
HELP = "Enable/Disable reading of Binary codes"
[BinaryCodeCodeID]
LABEL = "Binary Code ID"
HELP = "Set custom code identifier for Binary code"
[BinaryCodeMaxLen]
LABEL = "Binary Code Maximum Length"
HELP = "Set maximum length for Binary codes"
[BinaryCodeMinLen]
LABEL = "Binary Code Minimum Length"
HELP = "Set minimum length for Binary codes"
[BinaryCodeAimID]
LABEL = "Binary Code Aim ID"
HELP = "Set Binary Code Aim ID"
[CSCodeReadMain]
LABEL = "Chinese Sensible Code Read"
HELP = "Enable/Disable reading of Chinese Sensible code"
[CSCodeCodeID]
LABEL = "Chinese Sensible Code ID"
HELP = "Set custom code identifier for Chinese Sensible code"
[CSCodeMaxLen]
LABEL = "Chinese Sensible Code Maximum Length"
HELP = "Set maximum length for Chinese Sensible code"
[CSCodeMinLen]
LABEL = "Chinese Sensible Code Minimum Length"
HELP = "Set minimum length for Chinese Sensible code"
[CSCodeAimID]
LABEL = "Chinese Sensible Code Aim ID"
HELP = "Set Chinese Sensible Code Aim ID"
[DotCodeReadMain]
LABEL = "Dot Code Read"
HELP = "Enable/Disable reading of Dot codes"
[DotCodeCodeID]
LABEL = "Dot Code ID"
HELP = "Set custom code identifier for Dot code"
[DotCodeMaxLen]
LABEL = "Dot Code Maximum Length"
HELP = "Set maximum length for Dot codes"
[DotCodeMinLen]
LABEL = "Dot Code Minimum Length"
HELP = "Set minimum length for Dot codes"
[DotCodeAimID]
LABEL = "Dot Code Aim ID"
HELP = "Set Dot Code Aim ID"
[CHNFNCReadMain]
LABEL = "China Finance Read"
HELP = "Enable/Disable reading of China Finance codes"
[CHNFNCMaxLen]
LABEL = "China Finance Maximum Length"
HELP = "Set maximum length for China Finance codes"
[CHNFNCMinLen]
LABEL = "China Finance Minimum Length"
HELP = "Set minimum length for China Finance codes"
[CHNFNCChkDigVer]
LABEL = "China Finance Check Digit Verify"
HELP = "Enable/Disable check digit verification for China Finance codes"
[CHNFNCFrtCharConv]
LABEL = "China Finance First Character Conversion"
HELP = "Set first character conversion mode for China Finance codes"
[CHNFNCFrtCharConv.Options]
DISABLE = "Disable conversion"
ENABLE = "Enable all conversions"
FIVE_TO_A = "Only 5 to A"
SIX_TO_B = "Only 6 to B"
SEVEN_TO_C = "Only 7 to C"
EIGHT_TO_D = "Only 8 to D"
NINE_TO_E = "Only 9 to E"
[CHNFNCFrtCharSpec]
LABEL = "China Finance First Character Specification"
HELP = "Set first character specification for China Finance codes"
[CHNFNCFrtCharSpec.Options]
DISABLE = "Disable specification"
ZERO = "Specify as 0"
FIVE_OR_A = "Specify as 5 or A"
SIX_OR_B = "Specify as 6 or B"
SEVEN_OR_C = "Specify as 7 or C"
EIGHT_OR_D = "Specify as 8 or D"
NINE_OR_E = "Specify as 9 or E"
ONE = "Specify as 1"
TWO = "Specify as 2"
THREE = "Specify as 3"
FOUR = "Specify as 4"
[CHNFNCCodeID]
LABEL = "China Finance Code ID"
HELP = "Set custom code identifier for China Finance codes"
[CHNFNCInsertStr]
LABEL = "China Finance Insert String"
HELP = "Select the string group to be inserted for China Finance codes"
[CHNFNCAimID]
LABEL = "China Finance Aim ID"
HELP = "Set China Finance Aim ID"
[Custom_Decode_ROI_1]
LABEL = "Decode ROI 1"
HELP = "Set Decode ROI 1"
[Custom_Decode_ROI_2]
LABEL = "Decode ROI 2"
HELP = "Set Decode ROI 2"
[Custom_Decode_ROI_3]
LABEL = "Decode ROI 3"
HELP = "Set Decode ROI 3"
[Custom_Decode_ROI_4]
LABEL = "Decode ROI 4"
HELP = "Set Decode ROI 4"
[Custom_Decode_ROI_5]
LABEL = "Decode ROI 5"
HELP = "Set Decode ROI 5"
[Custom_Decode_ROI_6]
LABEL = "Decode ROI 6"
HELP = "Set Decode ROI 6"
[Custom_Decode_ROI_7]
LABEL = "Decode ROI 7"
HELP = "Set Decode ROI 7"
[Custom_Decode_ROI_8]
LABEL = "Decode ROI 8"
HELP = "Set Decode ROI 8"
[Custom_Decode_ROI_9]
LABEL = "Decode ROI 9"
HELP = "Set Decode ROI 9"
[Custom_Decode_ROI_10]
LABEL = "Decode ROI 10"
HELP = "Set Decode ROI 10"
[Custom_Decode_ROI_11]
LABEL = "Decode ROI 11"
HELP = "Set Decode ROI 11"
[Custom_Decode_ROI_12]
LABEL = "Decode ROI 12"
HELP = "Set Decode ROI 12"
[NetLed]
LABEL = "Network LED Mode"
HELP = "Network status LED mode"
[NetLed.Options]
LINK = "Connection on"
TX = "Send blink"
RX = "Receive blink"
[InsertStr]
LABEL = "Insert String"
HELP = "Select the string group to be inserted globally"
[BackTx]
LABEL = "Back Transmission"
HELP = "Back transmission mode for decode data"
[NoReadOutput]
LABEL = "No Read Output"
HELP = "Enable/Disable output of NR when no code is read"
[DataBatch]
LABEL = "Data Batch"
HELP = "Control wireless data batch processing"
[DataBatch.Options]
NONE = "Disabled"
OUT_RANGE = "Out of range storage"
STANDARD = "Standard storage"
STANDARD_MANUAL = "Standard storage (manual clear)"
[ScanMode]
LABEL = "Scan Mode"
HELP = "Scanner working mode selection"
[ScanMode.Options]
GoodReadOff = "Single Press Trigger Mode"
Mome = "Press Hold Mode"
Alter = "Switch Hold Mode"
Cont = "Continuous Mode"
GoodReadOn = "Single Press Hold Mode"
SciTrigger = "Command Control Trigger Mode"
AutoDete = "Auto Sense - Single Press Hold Mode"
AutoDeteIMG = "Auto Sense - Single Press Trigger Mode"
AutoDeteIR_GoodReadOn = "IR Sense - Single Press Hold"
AutoDeteIR_GoodReadOff = "IR Sense - Single Press Trigger"
AutoDeteIR_Mome = "IR Sense - Press Hold"
Desktop = "Desktop Mode"
ButtonCont = "Button Continuous Mode"
MultiSymbolsCont = "Multi-Signal Hold Mode"
[ScanKeepTime]
LABEL = "Scan Keep Time(seconds)"
HELP = "Scanner hold duration in seconds"
[ScanKeepTimeAdvanced]
LABEL = "Scan Keep Time(milliseconds)"
HELP = "Scanner hold duration in milliseconds"
[MovementScanMode]
LABEL = "Movement Scan Mode"
HELP = "Scanning mode for moving objects"
[MovementScanMode.Options]
Standard = "Standard Mode"
Enhance = "Enhanced Mode"
[PowerIndication]
LABEL = "Power Indication"
HELP = "Enable/Disable power-on indication"
[LedIndication]
LABEL = "LED Indication"
HELP = "Enable/Disable LED indication on successful decode"
[BeepIndication]
LABEL = "Beep Indication"
HELP = "Enable/Disable beep indication on successful decode"
[BeepTime]
LABEL = "Decode Indication Time(ms)"
HELP = "Set decode indication time in 10ms units"
[BeepVolume]
LABEL = "Beep Volume"
HELP = "Set beeper volume level"
[BeepVolume.Options]
LOW = "Low volume"
MID = "Medium volume"
HIGH = "High volume"
LOW2 = "Lower volume (15%)"
LOW3 = "Lower volume (10%)"
LOW4 = "Lower volume (8%)"
LOW5 = "Lower volume (6%)"
LOW6 = "Lower volume (4%)"
LOW7 = "Lower volume (2%)"
LOW8 = "Lower volume (1%)"
[BeepTone]
LABEL = "Beep Tone"
HELP = "Set beeper tone level"
[BeepTone.Options]
LEVEL0 = "Level 0 tone"
LEVEL1 = "Level 1 tone"
LEVEL2 = "Level 2 tone"
LEVEL3 = "Level 3 tone"
[PowerLEDCtrl]
LABEL = "Power LED Control"
HELP = "Enable/Disable power LED control"
[CustomIO1ModeMain]
LABEL = "CustomIO Mode"
HELP = "Select CustomIO working mode"
[CustomIO1ModeMain.Options]
Close = "Close"
InputHigh = "Input, default pull up"
InputLow = "Input, default pull down"
OutputHigh = "Output, default high"
OutputLow = "Output, default low"
[CustomIO2ModeMain]
LABEL = "CustomIO Mode"
HELP = "Select CustomIO working mode"
[CustomIO3ModeMain]
LABEL = "CustomIO Mode"
HELP = "Select CustomIO working mode"
[CustomIO4ModeMain]
LABEL = "CustomIO Mode"
HELP = "Select CustomIO working mode"
[CustomIO1InputCount]
LABEL = "CustomIO Input Edge Count"
HELP = "CustomIO input edge count"
[CustomIO2InputCount]
LABEL = "CustomIO Input Edge Count"
HELP = "CustomIO input edge count"
[CustomIO3InputCount]
LABEL = "CustomIO Input Edge Count"
HELP = "CustomIO input edge count"
[CustomIO4InputCount]
LABEL = "CustomIO Input Edge Count"
HELP = "CustomIO input edge count"
[CustomIO1Debounce]
LABEL = "CustomIO Debounce Time (ms)"
HELP = "Set input debounce time(ms)"
[CustomIO2Debounce]
LABEL = "CustomIO Debounce Time (ms)"
HELP = "Set input debounce time(ms)"
[CustomIO3Debounce]
LABEL = "CustomIO Debounce Time (ms)"
HELP = "Set input debounce time(ms)"
[CustomIO4Debounce]
LABEL = "CustomIO Debounce Time (ms)"
HELP = "Set input debounce time(ms)"
[CustomIO1RisingAction]
LABEL = "CustomIO Rising trigger action"
HELP = "Configure rising edge trigger action"
[CustomIO1RisingAction.Options]
None = "None"
StartDecode = "Start Decode"
EndDecode = "End Decode"
[CustomIO2RisingAction]
LABEL = "CustomIO Rising trigger action"
HELP = "Configure rising edge trigger action"
[CustomIO3RisingAction]
LABEL = "CustomIO Rising trigger action"
HELP = "Configure rising edge trigger action"
[CustomIO4RisingAction]
LABEL = "CustomIO Rising trigger action"
HELP = "Configure rising edge trigger action"
[CustomIO1RisingDelay]
LABEL = "CustomIO Rising Delay Time (ms)"
HELP = "Set rising edge delay(ms)"
[CustomIO2RisingDelay]
LABEL = "CustomIO Rising Delay Time (ms)"
HELP = "Set rising edge delay(ms)"
[CustomIO3RisingDelay]
LABEL = "CustomIO Rising Delay Time (ms)"
HELP = "Set rising edge delay(ms)"
[CustomIO4RisingDelay]
LABEL = "CustomIO Rising Delay Time (ms)"
HELP = "Set rising edge delay(ms)"
[CustomIO1FallingAction]
LABEL = "CustomIO Falling trigger action"
HELP = "Configure falling edge trigger action"
[CustomIO1FallingAction.Options]
None = "None"
StartDecode = "Start Decode"
EndDecode = "End Decode"
[CustomIO2FallingAction]
LABEL = "CustomIO Falling trigger action"
HELP = "Configure falling edge trigger action"
[CustomIO3FallingAction]
LABEL = "CustomIO Falling trigger action"
HELP = "Configure falling edge trigger action"
[CustomIO4FallingAction]
LABEL = "CustomIO Falling trigger action"
HELP = "Configure falling edge trigger action"
[CustomIO1FallingDelay]
LABEL = "CustomIO Falling Delay Time (ms)"
HELP = "Set falling edge delay(ms)"
[CustomIO2FallingDelay]
LABEL = "CustomIO Falling Delay Time (ms)"
HELP = "Set falling edge delay(ms)"
[CustomIO3FallingDelay]
LABEL = "CustomIO Falling Delay Time (ms)"
HELP = "Set falling edge delay(ms)"
[CustomIO4FallingDelay]
LABEL = "CustomIO Falling Delay Time (ms)"
HELP = "Set falling edge delay(ms)"
[CustomIO1ActiveCondition]
LABEL = "Active Condition"
HELP = "Configure active condition"
[CustomIO1ActiveCondition.Options]
None = "None"
DecodeSuccess = "Decode Success"
NoRead = "No Read"
DecodeStarted = "Decode Started"
DecodeStopped = "Decode Stopped"
Command = "Command"
Timer = "Timer"
Input1High = "Input1 High"
Input2High = "Input2 High"
Input3High = "Input3 High"
Input4High = "Input4 High"
Input1Low = "Input1 Low"
Input2Low = "Input2 Low"
Input3Low = "Input3 Low"
Input4Low = "Input4 Low"
[CustomIO2ActiveCondition]
LABEL = "Active Condition"
HELP = "Configure active condition"
[CustomIO3ActiveCondition]
LABEL = "Active Condition"
HELP = "Configure active condition"
[CustomIO4ActiveCondition]
LABEL = "Active Condition"
HELP = "Configure active condition"
[CustomIO1InactiveCondition]
LABEL = "Inactive Condition"
HELP = "Configure inactive condition"
[CustomIO1InactiveCondition.Options]
None = "None"
DecodeSuccess = "Decode Success"
NoRead = "No Read"
DecodeStarted = "Decode Started"
DecodeStopped = "Decode Stopped"
Command = "Command"
Timer = "Timer"
Input1High = "Input1 High"
Input2High = "Input2 High"
Input3High = "Input3 High"
Input4High = "Input4 High"
Input1Low = "Input1 Low"
Input2Low = "Input2 Low"
Input3Low = "Input3 Low"
Input4Low = "Input4 Low"
[CustomIO2InactiveCondition]
LABEL = "Inactive Condition"
HELP = "Configure inactive condition"
[CustomIO3InactiveCondition]
LABEL = "Inactive Condition"
HELP = "Configure inactive condition"
[CustomIO4InactiveCondition]
LABEL = "Inactive Condition"
HELP = "Configure inactive condition"
[CustomIO1ActiveDelay]
LABEL = "Active Delay (ms)"
HELP = "Set active delay(ms)"
[CustomIO2ActiveDelay]
LABEL = "Active Delay (ms)"
HELP = "Set active delay(ms)"
[CustomIO3ActiveDelay]
LABEL = "Active Delay (ms)"
HELP = "Set active delay(ms)"
[CustomIO4ActiveDelay]
LABEL = "Active Delay (ms)"
HELP = "Set active delay(ms)"
[CustomIO1InactiveTimer]
LABEL = "Inactive Timer (ms)"
HELP = "Set auto inactive timer(ms), 0 means no auto inactive"
[CustomIO2InactiveTimer]
LABEL = "Inactive Timer (ms)"
HELP = "Set auto inactive timer(ms), 0 means no auto inactive"
[CustomIO3InactiveTimer]
LABEL = "Inactive Timer (ms)"
HELP = "Set auto inactive timer(ms), 0 means no auto inactive"
[CustomIO4InactiveTimer]
LABEL = "Inactive Timer (ms)"
HELP = "Set auto inactive timer(ms), 0 means no auto inactive"
[CustomIO1ActiveCmd]
LABEL = "Active Command"
HELP = "Set the command to active custom IO"
[CustomIO2ActiveCmd]
LABEL = "Active Command"
HELP = "Set the command to active custom IO"
[CustomIO3ActiveCmd]
LABEL = "Active Command"
HELP = "Set the command to active custom IO"
[CustomIO4ActiveCmd]
LABEL = "Active Command"
HELP = "Set the command to active custom IO"
[CustomIO1InactiveCmd]
LABEL = "Inactive Command"
HELP = "Set the command to inactivate custom IO"
[CustomIO2InactiveCmd]
LABEL = "Inactive Command"
HELP = "Set the command to inactivate custom IO"
[CustomIO3InactiveCmd]
LABEL = "Inactive Command"
HELP = "Set the command to inactivate custom IO"
[CustomIO4InactiveCmd]
LABEL = "Inactive Command"
HELP = "Set the command to inactivate custom IO"
[CharEncode]
LABEL = "Character Encoding"
HELP = "Character encoding system"
[CharEncode.Options]
ASCII = "ASCII"
UTF8 = "UTF-8"
Windows1251 = "Windows-1251"
SimpChinese = "Simplified Chinese"
TraChinese = "Traditional Chinese"
Windows1250 = "Windows-1250"
KOI8R = "KOI8-R"
Japanese = "Japanese"
[CharPrintCtr]
LABEL = "Character Print Control"
HELP = "Control which characters are output"
[CharPrintCtr.Options]
Disable = "No Control"
Printable = "Printable Characters Only"
Alphanumeric = "Letters and Numbers Only"
[DecodeResultMaxNum]
LABEL = "Maximum Number of Decode Results"
HELP = "Set maximum number of decode results in one scan (range: 1-30)"
[FirstOnly]
LABEL = "First N Characters Only"
HELP = "Set to transmit only first N characters"
[LastOnly]
LABEL = "Last N Characters Only"
HELP = "Set to transmit only last N characters"
[SingReplaC1]
LABEL = "Single Replace C1"
HELP = "Set the single character C1 replacement"
[SingReplaC2]
LABEL = "Single Replace C2"
HELP = "Set the single character C2 replacement"
[SingReplaC3]
LABEL = "Single Replace C3"
HELP = "Set the single character C3 replacement"
[ReplaChar2Str]
LABEL = "Replace Character to String"
HELP = "Set the character to string replacement"
[CaseConv]
LABEL = "Case Conversion"
HELP = "Set case conversion mode"
[CaseConv.Options]
Disable = "Disable"
UpperDatOnly = "Upper Data Only"
LowerDatOnly = "Lower Data Only"
UpperWholeStr = "Upper Whole String"
LowWholeStr = "Lower Whole String"
[FN1SubstuTextEnable]
LABEL = "Enable FN1 Substitution Text"
HELP = "Enable/Disable FN1 substitution text transmission"
[CharactersToStrings]
LABEL = "Show Special Characters as text"
HELP = "Enable/Disable control characters to strings conversion"
[DecoRsltCheck]
LABEL = "Decode Result Check"
HELP = "Set decode result check mode"
[DecoRsltCheck.Options]
Disable = "Disable"
EnableAll = "Enable All"
HeadCheckOnly = "Head Check Only"
TailCheckOnly = "Tail Check Only"
[DecoRsltCheck1HeadStr]
LABEL = "Decode Result Check Head String 1"
HELP = "Set decode result check head string 1"
[DecoRsltCheck1TailStr]
LABEL = "Decode Result Check Tail String 1"
HELP = "Set decode result check tail string 1"
[DecoResultCheckFailIndication]
LABEL = "Decode Result Check Fail Indication"
HELP = "Enable/Disable decode result check fail indication"
[Group1Text]
LABEL = "Text 1"
HELP = "Set the group 1 text"
[Group2Text]
LABEL = "Text 2"
HELP = "Set the group 2 text"
[Group3Text]
LABEL = "Text 3"
HELP = "Set the group 3 text"
[Group4Text]
LABEL = "Text 4"
HELP = "Set the group 4 text"
[Group1Pos]
LABEL = "String 1 Position"
HELP = "Set the group 1 position"
[Group2Pos]
LABEL = "String 2 Position"
HELP = "Set the group 2 position"
[Group3Pos]
LABEL = "String 3 Position"
HELP = "Set the group 3 position"
[Group4Pos]
LABEL = "String 4 Position"
HELP = "Set the group 4 position"
[USBEthernetCompositeEnMain]
LABEL = "USB Ethernet Composite Enable"
HELP = "Enable USB Ethernet Composite Enable"
[USBEthernetCompositeType]
LABEL = "USB Ethernet Composite Type"
HELP = "USB Ethernet Composite Type"
[USBEthernetCompositeType.Options]
Keyboard = "Keyboard"
VCOM = "VCOM"
[EthernetIpOutputEn]
LABEL = "EthernetIp Output Enable"
HELP = "Enable EthernetIp Output"
[ModbusSvrOutputEn]
LABEL = "ModbusServer Output Enable"
HELP = "Enable ModbusServer Output"
[UartEnable]
LABEL = "Uart Output Enable"
HELP = "Enable Uart Output"
[ProfinetOutputEnMain]
LABEL = "Profinet Output Enable"
HELP = "Enable Profinet Output"
[ProfinetStationName]
LABEL = "Profinet Station Name"
HELP = "Profinet Station Name"
[TCPCliOutputEnMain]
LABEL = "TCP Client Output Enable"
HELP = "Enable TCP Client Output"
[TCPCliTargetSvrIP]
LABEL = "TCP Client Target Server IP Address"
HELP = "TCP Client Target Server IP Address"
[TCPCliTargetSvrPort]
LABEL = "TCP Client Target Server Port"
HELP = "TCP Client Target Server Port"
[TCPCliOutputFmt]
LABEL = "TCP Client Output Format"
HELP = "TCP Client Output Format"
[TCPCliOutputPrefix]
LABEL = "TCP Client Output Prefix"
HELP = "TCP Client Output Prefix"
[TCPCliOutputSuffix]
LABEL = "TCP Client Output Suffix"
HELP = "TCP Client Output Suffix"
[TCPSvrOutputEnableMain]
LABEL = "TCP Server Output Enable"
HELP = "Enable TCP Server Output"
[TCPSvrOutputFmt]
LABEL = "TCP Server Output Format"
HELP = "TCP Server Output Format"
[TCPSvrOutputPrefix]
LABEL = "TCP Server Output Prefix"
HELP = "TCP Server Output Prefix"
[TCPSvrOutputSuffix]
LABEL = "TCP Server Output Suffix"
HELP = "TCP Server Output Suffix"
[TCPSvrServicePort]
LABEL = "TCP Server Service Port"
HELP = "TCP Server Service Port"
[RS232FlowControl]
LABEL = "RS232 Flow Control"
HELP = "RS232 flow control mode"
[RS232FlowControl.Options]
None = "No Flow Control"
RtsCtsLow = "RTS/CTS (Low)"
RtsCtsHigh = "RTS/CTS (High)"
XonXoff = "XON/XOFF"
AckNak = "ACK/NAK"
RtsLow = "RTS Low (ES Only)"
AckNakRtsCtsHigh = "ACK/NAK+RTS/CTS High (uE Only)"
CtsScan = "CTS Scan Control"
[RS232CharDelay]
LABEL = "RS232 Character Delay"
HELP = "Inter-character delay for RS232 communication"
[RS232CharDelay.Options]
Forbid = "No Delay"
Delay5ms = "5ms Delay"
Delay10ms = "10ms Delay"
Delay20ms = "20ms Delay"
Delay40ms = "40ms Delay"
Delay80ms = "80ms Delay"
[RS232ResponseDelay]
LABEL = "RS232 Response Delay(10 ms)"
HELP = "Response delay for RS232 communication in 10ms units"
[RS232BaudRate]
LABEL = "RS232 Baud Rate"
HELP = "RS232 baud rate"
[RS232BaudRate.Options]
BR9600 = "9600"
BR19200 = "19200"
BR38400 = "38400"
BR57600 = "57600"
BR115200 = "115200"
[RS232Parity]
LABEL = "RS232 Parity"
HELP = "Parity check mode for RS232 communication"
[RS232Parity.Options]
None = "No Parity"
Odd = "Odd Parity"
Even = "Even Parity"
[RS232DataBits]
LABEL = "RS232 Data Bits"
HELP = "Number of data bits for RS232 communication"
[RS232DataBits.Options]
Bits8 = "8 Bits"
Bits7 = "7 Bits"
[RS232StopBits]
LABEL = "RS232 Stop Bits"
HELP = "Number of stop bits for RS232 communication"
[RS232StopBits.Options]
Bits1 = "1 Bit"
Bits2 = "2 Bits"
[RS232HostType]
LABEL = "RS232 Host Type"
HELP = "Host communication protocol type for RS232"
[RS232HostType.Options]
Standard = "Standard RS232"
OPOS = "OPOS/JPOS"
MDAux = "MD Auxiliary Port"
[RS232DecoDataFormat]
LABEL = "RS232 Decode Data Format"
HELP = "Format of decoded data for RS232 transmission"
[RS232DecoDataFormat.Options]
Raw = "Raw Format"
Packed = "Packed Format"
[RS232HsCharTimeout]
LABEL = "RS232 Handshake Character Timeout(10 ms)"
HELP = "Handshake character timeout for RS232 in 10ms units"
[USBType]
LABEL = "USB Type"
HELP = "Select USB device type"
[USBType.Options]
PC = "USB HID keyboard emulation"
MAC = "Apple Mac HID keyboard emulation"
VISUAL_COM = "USB virtual COM port"
SIMPLE_COM = "Simple COM port emulation"
OPOS = "OPOS/JPOS compliant HID device"
CUST_VISUAL_COM = "Custom USB virtual COM port"
IBM_HAND_HELD = "IBM Hand Held"
HID_MSD = "USB storage device with HID keyboard"
CUSTOM_BULK = "Custom bulk transfer"
CUSTOM_HID_KEYBOARD_HS_BULK = "Custom HID keyboard with high speed bulk"
PC_LEGACY_KEYBOARD = "Legacy USB HID keyboard"
VCOM_OPOS = "OPOS/JPOS compliant VCOM device"
CUSTOM_HID_INT_HS_BULK = "Custom HID interrupt with high speed bulk"
CUSTOM_HID_KBD_HID_CUST = "Custom HID keyboard with custom HID"
CUSTOM_HID_KBD_VISUAL_COM = "Custom HID keyboard with virtual COM"
NET = "USB Net device"
CUSTOM_HID_KBD_NET = "Custom HID keyboard with Net"
CUSTOM_VISUAL_COM_NET = "Custom virtual COM with Net"
CUSTOM = "Custom port for special applications"
[USBKeyboardLayout]
LABEL = "USB Keyboard Layout"
HELP = "Select USB keyboard layout"
[USBKeyboardLayout.Options]
CHN_USA = "US keyboard layout"
TKYF = "Turkish F keyboard layout"
TKYQ = "Turkish Q keyboard layout"
FRN = "French keyboard layout"
ITA = "Italian keyboard layout"
SPA = "Spanish keyboard layout"
SLK = "Slovak keyboard layout"
DMK = "Danish keyboard layout"
JAP = "Japanese keyboard layout"
GER = "German keyboard layout"
BEL = "Belgian keyboard layout"
RUS = "Russian keyboard layout"
CZE = "Czech keyboard layout"
THAI = "Thai keyboard layout"
HUNGARY = "Hungarian keyboard layout"
SWISS_GERMAN = "Swiss German keyboard layout"
PORTUGUESE = "Portuguese keyboard layout"
UKRAINIAN = "Ukrainian keyboard layout"
POLISH214 = "Polish keyboard layout"
VIETNAM = "Vietnamese keyboard layout"
RUSSIAN_ANDROID = "Russian Android keyboard layout"
VIETNAM_TELEX = "Vietnamese Telex input method"
[USBKeyDly]
LABEL = "USB Key Delay"
HELP = "Set delay between adjacent key transmissions"
[USBKeyDly.Options]
DELAY_0MS = "0ms"
DELAY_5MS = "5ms"
DELAY_10MS = "10ms"
DELAY_20MS = "20ms"
DELAY_40MS = "40ms"
DELAY_60MS = "60ms"
[USBNumbKey]
LABEL = "USB Number Key"
HELP = "Select USB number key output mode"
[USBNumbKey.Options]
DISABLE = "Use letter keyboard codes"
NUM = "Use numeric keypad codes"
ALT_KEYPAD = "Use Alt+number key simulation"
ALT_KEYPAD_GBK = "Use Alt+number key with GBK encoding"
RAW_HEX_IN_STRINGS = "Output raw hex values"
UOS_UNICODE_IN_DEC = "Output Unicode in decimal (UOS specific)"
[USBFuncKey]
LABEL = "USB Function Key"
HELP = "Select USB function key output mode"
[USBFuncKey.Options]
NON_PRINTABLE = "Output as non-printable characters"
MINDEO = "Output as MINDEO function keys"
CUSTOMIZE_PPN = "Output as PPN code mapping"
FORBID = "Forbid output"
DATALOGIC_CTRL_CHAR_00 = "Output as Datalogic Control Character 00"
[USBHostPollingInter]
LABEL = "USB Host Polling Interval"
HELP = "Set USB host port polling interval"
[USBHostPollingInter.Options]
INTERVAL_1MS = "1ms"
INTERVAL_2MS = "2ms"
INTERVAL_5MS = "5ms"
INTERVAL_8MS = "8ms"
[USBKeySendMode]
LABEL = "USB Key Send Mode"
HELP = "Select USB key value sending mode"
[USBKeySendMode.Options]
DISCONTINUOUS = "Discontinuous key value sending"
CONTINUOUS = "Continuous key value sending"
[USBEnterMode]
LABEL = "USB Enter Mode"
HELP = "Select USB enter key mapping mode"
[USBEnterMode.Options]
ACCORDING_FUNC_KEY = "Follow function key config"
FORCE_ALPHABET_ENTER = "Force alphabet keyboard enter"
FORCE_NUMERIC_ENTER = "Force numeric keypad enter"
FORCE_ALT_013_ENTER = "Force Alt+013 enter"
[USBModKeysReleaseMode]
LABEL = "USB Mod Keys Release Mode"
HELP = "Control how modifier keys are released in USB keyboard mode"
[USBModKeysReleaseMode.Options]
SAME_TIME = "Release all keys at the same time"
AFTER_NORMAL_KEYS = "Release normal keys first, then modifier keys"
[USBKeyTxTimeout]
LABEL = "USB Key Transmission Timeout(100ms)"
HELP = "Set USB key value transmission timeout (in 100ms)"
[UsbEnumFailReboot]
LABEL = "USB Enumeration Failure Reboot"
HELP = "Enable/Disable automatic reboot on USB enumeration failure"
[Focal]
LABEL = "Focal control value (mV)"
HELP = "Adjust the focal control value"
[ConfigByCode]
LABEL = "Config By Code"
HELP = "Enable/Disable configuration by scanning code"
[InterfaceTypeMain]
LABEL = "Interface Type"
HELP = "Select interface type for communication"
[InterfaceTypeMain.Options]
Auto = "Auto Detect"
RS232 = "RS-232 Only"
USB = "USB Only"
RS232_ETH = "RS232+Ethernet Only"
RS232_USB = "RS232+USB Only"
SOFTWARE = "SOFTWARE Only"
[RS232ByteDelay]
LABEL = "RS232 Byte Delay(10 ms)"
HELP = "Set RS232 interface byte delay"
[CodeMaxLen1D]
LABEL = "1D Code Max Length"
HELP = "Maximum length for 1D barcode"
[CodeMinLen1D]
LABEL = "1D Code Min Length"
HELP = "Minimum length for 1D barcode"
[CodeElementAmend]
LABEL = "Code Element Amend"
HELP = "Enable/Disable barcode width correction"
[DecodeOptimize]
LABEL = "Decode Optimize"
HELP = "Enable/Disable decode optimization"
[OutputDelayContScan]
LABEL = "Output Delay Continuous Scan"
HELP = "Set output delay for continuous scanning mode"
[SameBarDelayDR]
LABEL = "Same Barcode Delay Double Read(ms)"
HELP = "Set delay time for double read same barcode (unit: 50ms)"
[SweepingScanEnhance]
LABEL = "Sweeping Scan Enhancement"
HELP = "Enhance scanning experience for different sweeping speeds"
[SweepingScanEnhance.Options]
LowSpeed = "Low Sweeping Speed"
MediumSpeed = "Medium Sweeping Speed"
HighSpeed = "High Sweeping Speed"
[VibratorIndication]
LABEL = "Vibrator Indication"
HELP = "Enable/Disable vibrator indication after successful decode"
[SilentMode]
LABEL = "Silent Mode"
HELP = "Enable/Disable silent mode"
[BeepSoundMode]
LABEL = "Beep Sound Mode"
HELP = "Select beep sound mode for successful decode"
[BeepSoundMode.Options]
MODE_0 = "Default beep sound mode"
MODE_1 = "Level 2 tone, medium volume"
MODE_2 = "Level 0 tone, high volume"
MODE_3 = "Level 0 tone, medium volume"
MODE_4 = "Default for 1D, high volume for 2D"
[IndicationTxDelay]
LABEL = "Indication Transmission Delay(10 ms)"
HELP = "Set delay time between successful decode indication and data transmission"
[VoiceDly]
LABEL = "Voice Delay(100 ms)"
HELP = "Set delay time for voice playback reset counter"
[InfraredSensor]
LABEL = "Infrared Sensor"
HELP = "Enable/Disable infrared sensor function"
[InfraredMode]
LABEL = "Infrared Mode"
HELP = "Select infrared sensor operation mode"
[InfraredMode.Options]
IN_STAND = "In Stand"
CONTINUE = "Continue"
[AutoDeteKeepTime]
LABEL = "Auto Detect Keep Time(seconds)"
HELP = "Set auto detection keep time (unit: 1s)"
[AutoDeteTexture]
LABEL = "Auto Detect Texture"
HELP = "Enable/Disable auto detect texture function"
[AutoDeteLum]
LABEL = "Auto Detect Luminance"
HELP = "Control luminance in auto detect mode"
[AutoDeteLum.Options]
ALWAYS_OFF = "Always Off"
ON_IN_DARKNESS = "On in Darkness"
ALWAYS_ON = "Always On"
[AutoDeteTxtureInter]
LABEL = "Auto Detect Texture Interval"
HELP = "Set texture detection start interval in auto detect mode"
[AutoDeteTxtureInter.Options]
INTERVAL_0S = "0 seconds"
INTERVAL_5S = "5 seconds"
INTERVAL_10S = "10 seconds"
INTERVAL_30S = "30 seconds"
INTERVAL_60S = "60 seconds"
INTERVAL_INFINITY = "Infinity"
[AutoDeteSleepFrmInter]
LABEL = "Auto Detect Sleep Frame Interval"
HELP = "Set frame capture interval in sleep mode"
[AutoDeteSleepFrmInter.Options]
INTERVAL_250MS = "250ms"
INTERVAL_500MS = "500ms"
INTERVAL_1000MS = "1000ms"
[InfraredMsgSwitch]
LABEL = "Infrared Message Switch"
HELP = "Enable/Disable infrared mode message upload"
[InfraredLum]
LABEL = "Infrared Luminance"
HELP = "Enable/Disable luminance during infrared detection"
[IRLumDeteSenLevel]
LABEL = "IR Luminance Detection Sensitivity Level"
HELP = "Set IR luminance detection sensitivity level"
[DPMRead]
LABEL = "DPM Code Read"
HELP = "Enable/Disable reading of Direct Part Marking codes"
[CompositeRead]
LABEL = "Composite Read"
HELP = "Set composite code reading options"
[CompositeRead.Options]
DISABLE = "Disable reading"
DATABAR_GS1_128 = "Read DataBar composite and GS1-128 composite"
ALL = "Read all composite codes"
[OCRRecognizeMain]
LABEL = "OCR Recognition"
HELP = "Set OCR recognition options"
[OCRRecognizeMain.Options]
AS_PARA = "Follow parameter settings"
DISABLE_ALL = "Disable all OCR functions"
ENABLE_ALL = "Enable all OCR functions"
[OCRRecognizePassportMRZ]
LABEL = "OCR Passport MRZ Recognition"
HELP = "Enable/Disable recognition of passport machine readable zone"
[OCRRecognizeNationalCardID]
LABEL = "OCR National Card ID Recognition"
HELP = "Enable/Disable recognition of national ID cards"
[OCRTurkishIDOutputFormat]
LABEL = "OCR Turkish ID Output Format"
HELP = "Set output format for Turkish ID cards"
[OCRTurkishIDOutputFormat.Options]
ORIGINAL = "Original format"
CUSTOMIZED = "Customized format"
[OCRDoubleCheck]
LABEL = "OCR Double Check"
HELP = "Enable/Disable double check for OCR recognition"
[IVDDeteTubeCapMain]
LABEL = "IVD Tube Cap Detection"
HELP = "Enable/Disable tube cap detection for IVD"
[IVDDeteTubeHeight]
LABEL = "IVD Tube Height Detection"
HELP = "Enable/Disable tube height detection for IVD"
[AimKeepTimeAfterDeco]
LABEL = "Aim Keep Time After Decoding(seconds)"
HELP = "Set aim light keep time after decoding (in seconds)"
[LightDriveCapacity]
LABEL = "Light Drive Capacity(mA)"
HELP = "Set maximum drive current capacity for illumination (in mA)"
[LightMaxPerLED]
LABEL = "Maximum Current Per LED(mA)"
HELP = "Set maximum current per LED when all LEDs are on (in 10mA)"
[LightGroup1Current]
LABEL = "Light Group 1 Current(mA)"
HELP = "Set current for light group 1 to achieve standard maximum image brightness (in 10mA)"
[LightGroup2Current]
LABEL = "Light Group 2 Current(mA)"
HELP = "Set current for light group 2 to achieve standard maximum image brightness (in 10mA)"
[AimKeepTimeBeforeDeco]
LABEL = "Aim Keep Time Before Decoding(seconds)"
HELP = "Set aim light keep time before decoding (in seconds)"
[AuxiliaryLum]
LABEL = "Auxiliary Illumination"
HELP = "Enable/Disable auxiliary illumination control"
[AuxiliaryLumCompTime]
LABEL = "Auxiliary Illumination Compensation Time(us)"
HELP = "Set light stability compensation time for auxiliary illumination (in 10us)"
[IVDTubeHightInfo]
LABEL = "IVD Tube Height Info"
HELP = "Reserved parameter for IVD tube height related information"
[CodeRsltTxIndiDlyMode]
LABEL = "Code Result Transmission Indication Delay Mode"
HELP = "Set delay mode for code result transmission and indication"
[CodeRsltTxIndiDlyMode.Options]
NONE = "No delay"
ONLY_TX = "Only transmission delay"
TX_AND_INDI = "Both transmission and indication delay"
[DecoStay]
LABEL = "Decode Stay"
HELP = "Enable/Disable Decode Stay option for command triggered decoding"
[PacketOutputDataCtr]
LABEL = "Packet Output Data Control"
HELP = "Enable/Disable packet output data control"
[AddWatermark]
LABEL = "Add Watermark"
HELP = "Set watermark options for images"
[AddWatermark.Options]
DISABLE = "Disable watermark"
WITHOUT_BACKGROUND = "Add watermark without background"
WITH_BACKGROUND = "Add watermark with background"
[InstandIndicate]
LABEL = "In-Stand Indication"
HELP = "Enable/Disable buzzer indication when in stand"
[QRTerminatorPos]
LABEL = "QR Code Terminator Position"
HELP = "Set QR code decoding terminator position"
[QRTerminatorPos.Options]
FIRST = "Stop at first terminator"
SECOND = "Stop at second terminator"
[CodeErrOutput2D]
LABEL = "2D Code Error Correction Output"
HELP = "Enable/Disable output of 2D code error correction code"
[CodeErrLevel2D]
LABEL = "2D Code Error Correction Level Output"
HELP = "Enable/Disable output of 2D code error correction level"
[CodeVersion2D]
LABEL = "2D Code Version Output"
HELP = "Enable/Disable output of 2D code version information"
[ContModeLumLevel]
LABEL = "Continuous Mode Luminance Level"
HELP = "Set luminance level for continuous scan mode auto detection"
[ContModeLumLevel.Options]
LOW = "Low level"
MID = "Medium level"
HIGH = "High level"
[ContModeNoActionDura]
LABEL = "Continuous Mode No Action Duration(seconds)"
HELP = "Set no action duration for continuous scan mode (range: 1-90 seconds)"
[TimeToReboot]
LABEL = "Time to Reboot"
HELP = "Set timed reboot options for corded products"
[TimeToReboot.Options]
FORBID = "Forbid reboot"
SECS_30 = "30 seconds"
MINS_5 = "5 minutes"
MINS_15 = "15 minutes"
MINS_30 = "30 minutes"
HOUR_1 = "1 hour"
HOURS_2 = "2 hours"
HOURS_3 = "3 hours"
HOURS_4 = "4 hours"
HOURS_8 = "8 hours"
HOURS_12 = "12 hours"
HOURS_24 = "24 hours"
HOURS_48 = "48 hours"
[ImageFlipMode]
LABEL = "Image Flip Mode"
HELP = "Set image flip mode relative to original position"
[ImageFlipMode.Options]
NONE = "No flip"
HORIZONTAL = "Horizontal flip"
VERTICAL = "Vertical flip"
DIAGONAL = "Diagonal flip"
[AttachCodePosOption]
LABEL = "Attach Code Position Option"
HELP = "Enable/Disable attaching code position information to barcode data"
[LastSuccImaging]
LABEL = "Last Successful Imaging"
HELP = "Enable/Disable using last successful imaging parameters for new imaging"
[HiLum4HDCode]
LABEL = "High Luminance for HD Code"
HELP = "Enable/Disable high luminance mode for HD code scanning"
[SysRecoverIndication]
LABEL = "System Recovery Indication"
HELP = "Enable/Disable system recovery indication"
[OutputMultiple]
LABEL = "Output Multiple"
HELP = "Enable/Disable multiple output mode"
[ConfigByCodeFb]
LABEL = "Feedback after configuration by Code"
HELP = "Enable/Disable configuration code feedback"
[QueryConfigByCode]
LABEL = "Query Configuration by Code"
HELP = "Enable/Disable configuration query by code"
[ResultCheck]
LABEL = "Result Check"
HELP = "Enable/Disable scan result verification"
[RainbowRead]
LABEL = "Rainbow Read"
HELP = "Enable/Disable rainbow read mode for color barcodes"
[PowerUpReport]
LABEL = "Power Up Report"
HELP = "Enable/Disable power up status report"
[ExpRebootIndication]
LABEL = "Exposure Reboot Indication"
HELP = "Enable/Disable exposure reboot indication"
[DecodeVertex]
LABEL = "Decode Vertex"
HELP = "Enable/Disable decode vertex output"
[AgingTest]
LABEL = "Aging Test"
HELP = "Enable/Disable aging test mode"
[AgingTest.Options]
OFF = "Off"
MODE_1 = "Mode 1"
MODE_2 = "Mode 2"
MODE_3 = "Mode 3"
MODE_4 = "Mode 4"
[PrefixText]
LABEL = "Prefix Text"
HELP = "Set the prefix text"
[PrefixTextTransmission]
LABEL = "Prefix Text Transmission"
HELP = "Enable/Disable prefix text transmission"
[SuffixText]
LABEL = "Suffix Text"
HELP = "Set the suffix text"
[PreambText]
LABEL = "Preamble Text"
HELP = "Set the preamble text"
[PostambText]
LABEL = "Postamble Text"
HELP = "Set the postamble text"
[FN1SubstuText]
LABEL = "FN1 Substitution Text"
HELP = "Set the FN1 substitution text"
[TruncLeadG5]
LABEL = "Truncate Leading G5"
HELP = "Set the truncate leading G5 character"
[RepeatG5]
LABEL = "Repeat G5"
HELP = "Set the G5 repeat times"
[TruncEndG6]
LABEL = "Truncate End G6"
HELP = "Set the truncate end G6 character"
[RepeatG6]
LABEL = "Repeat G6"
HELP = "Set the G6 repeat times"
[CodeIDPos]
LABEL = "Code ID Position"
HELP = "Set the code ID position"
[CodeIDPos.Options]
Before = "Before"
After = "After"
[SuffixTextEnable]
LABEL = "Enable Suffix Text"
HELP = "Enable/Disable suffix text transmission"
[CodeNameEnable]
LABEL = "Enable Code Name"
HELP = "Enable/Disable code name transmission"
[PreambTextEnable]
LABEL = "Enable Preamble Text"
HELP = "Enable/Disable preamble text transmission"
[PostambTextEnable]
LABEL = "Enable Postamble Text"
HELP = "Enable/Disable postamble text transmission"
[CodeIDTextEnable]
LABEL = "Enable Code ID Text"
HELP = "Set code ID text transmission mode"
[CodeIDTextEnable.Options]
Disable = "Disable"
PropID = "Property ID"
AIMID = "AIM ID"
Both = "Both"
[CodeLenEnable]
LABEL = "Enable Code Length"
HELP = "Enable/Disable code length transmission"
[NonePrintStrEnable]
LABEL = "Enable Non-Printable String"
HELP = "Enable/Disable non-printable string transmission"
[BanSpecialKeys]
LABEL = "Ban Special Keys"
HELP = "Enable/Disable special keys (TAB, Delete, Backspace)"
[TextEdit]
LABEL = "Text Edit"
HELP = "Set text edit mode"
[TextEdit.Options]
Disable = "Disable"
Invoice = "Invoice"
Evotrue = "Evotrue"
Prowill = "Prowill"
Inspur = "Inspur"
Elgin = "Elgin"
Pulsa = "Pulsa"
GeLunBu = "GeLunBu"
ShengXiaoBang = "ShengXiaoBang"
XinGuoDu_GSGL = "XinGuoDu Highway"
InvoiceCust = "Invoice Custom"
[InvoiceCustStr1]
LABEL = "Invoice Custom String 1"
HELP = "Set invoice custom string 1"
[InvoiceCustStr2]
LABEL = "Invoice Custom String 2"
HELP = "Set invoice custom string 2"
[InvoiceCustStr3]
LABEL = "Invoice Custom String 3"
HELP = "Set invoice custom string 3"
[InvoiceCustStr4]
LABEL = "Invoice Custom String 4"
HELP = "Set invoice custom string 4"
[BackupDecoImg]
LABEL = "Backup Deco Img"
HELP = "Select Backup Deco Img Opthon"
[BackupDecoImg.Options]
OFF = "Backup Deco Img"
Both = "Backup Decoder And System Deco Succ Img"
Succ = "Only Backup Decoder Deco Succ Img"
[OnTimeTriggerDecode]
LABEL = "On Time Trigger Decode Duration"
HELP = "Decode duration when triggered by timer (in 100ms units)"
[UCIPGroupResetPUB]
LABEL = "UCIPGroup Reset"
HELP = "Reset custom imaging parameters group configuration"
[VisionApp]
LABEL = "Vision App"
HELP = "Select Vision App"
[VisionApp.Options]
OFF = "Vision App OFF"
OCR = "OCR"
Class = "Classification"
DeteFeature = "DeteFeature"
