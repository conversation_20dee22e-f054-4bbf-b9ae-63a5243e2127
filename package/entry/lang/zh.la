[PathKeys]
ImagingCtr = "图像控制"
UCIPGroup1 = "组1"
UCIPGroup2 = "组2"
UCIPGroup3 = "组3"
UCIPGroup4 = "组4"
UCIPGroup5 = "组5"
UCIPGroup6 = "组6"
UCIPGroup7 = "组7"
UCIPGroup8 = "组8"
IllControl = "照明控制"
AimControl = "瞄准控制"
DataProcessing = "数据处理"
DataFilter = "数据过滤"
Rule1 = "规则1"
Rule2 = "规则2"
Rule3 = "规则3"
Rule4 = "规则4"
Rule5 = "规则5"
Rule6 = "规则6"
Rule7 = "规则7"
Rule8 = "规则8"
Rule9 = "规则9"
Rule10 = "规则10"
AlgorithmCtr = "算法配置"
Code = "码制"
Code39 = "Code39"
Code128 = "Code128码"
Codabar = "Codabar码"
Interleaved25 = "交叉25码"
Code93 = "Code93码"
EAN-8 = "EAN-8"
EAN-13 = "EAN-13"
UPCA = "UPC-A"
UPCE = "UPC-E"
Code11 = "Code11码"
GS1EXP = "GS1 Expanded"
GS1DataBar = "GS1DataBar"
GS1DataBarLimited = "GS1DataBar Limited"
ISBT128 = "ISBT128码"
MSIPL = "MSI-Plessey码"
UCCEAN128 = "UCCEAN 128码"
UKPL = "UKPlessey码"
UPCE1 = "UPC-E1码"
CHNPST = "中国邮政码"
Industrial25 = "工业25码"
Matrix25 = "矩阵25码"
QRCode = "QR码"
DM = "DataMatrix码"
Aztec = "Aztec码"
CodaBlockF = "CodaBlockF码"
GM = "GM码"
MaxiCode = "Maxicode码"
MICPDF417 = "MicroPDF417"
MicroQR = "MicroQR码"
PDF417 = "PDF417"
BinaryCode = "二进制码"
CSCode = "汉信码"
DotCode = "Dot Code"
CHNFNC = "中国金融码"
ROI = "算法ROI"
DeviceCtr = "设备控制"
NetworkCtr = "网络控制"
InputOutputCtr = "输入输出"
Transmission = "传输"
Modes = "扫描模式"
Indicators = "指示器"
CustomIO1 = "自定义IO1"
CustomIO2 = "自定义IO2"
CustomIO3 = "自定义IO3"
CustomIO4 = "自定义IO4"
Input = "输入"
Output = "输出格式"
Encoding = "条码编码"
OutputFormat = "输出格式"
InsertStr = "插入字符串功能"
Communication = "通信"
Interface = "接口"
USB = "USB"
IndustrialProtocol = "工业协议"
Profinet = "Profinet"
TCPClient = "TCP 客户端"
TCPServer = "TCP 服务器"
RS232 = "RS232"
FocalCtrValue = "焦距控制值"
Reserved = "保留功能"
TriggerConfig = "触发配置"
Delays = "时延"
ImageControl = "图像控制"
[Temperature]
LABEL = "温度"
HELP = "设备温度，单位：摄氏度"
[UCIPGroupFinishPUB]
LABEL = "组参数配置完成"
HELP = "完成组参数配置"
[UCIPGroup1Enable]
LABEL = "使能"
HELP = "使能组1成像参数"
[UCIPGroup2Enable]
LABEL = "使能"
HELP = "使能组2成像参数"
[UCIPGroup3Enable]
LABEL = "使能"
HELP = "使能组3成像参数"
[UCIPGroup4Enable]
LABEL = "使能"
HELP = "使能组4成像参数"
[UCIPGroup5Enable]
LABEL = "使能"
HELP = "使能组5成像参数"
[UCIPGroup6Enable]
LABEL = "使能"
HELP = "使能组6成像参数"
[UCIPGroup7Enable]
LABEL = "使能"
HELP = "使能组7成像参数"
[UCIPGroup8Enable]
LABEL = "使能"
HELP = "使能组8成像参数"
[UCIPGroup1Expo]
LABEL = "曝光时间（微秒）"
HELP = "设置组1曝光时间(微秒)"
[UCIPGroup2Expo]
LABEL = "曝光时间（微秒）"
HELP = "设置组2曝光时间(微秒)"
[UCIPGroup3Expo]
LABEL = "曝光时间（微秒）"
HELP = "设置组3曝光时间(微秒)"
[UCIPGroup4Expo]
LABEL = "曝光时间（微秒）"
HELP = "设置组4曝光时间(微秒)"
[UCIPGroup5Expo]
LABEL = "曝光时间（微秒）"
HELP = "设置组5曝光时间(微秒)"
[UCIPGroup6Expo]
LABEL = "曝光时间（微秒）"
HELP = "设置组6曝光时间(微秒)"
[UCIPGroup7Expo]
LABEL = "曝光时间（微秒）"
HELP = "设置组7曝光时间(微秒)"
[UCIPGroup8Expo]
LABEL = "曝光时间（微秒）"
HELP = "设置组8曝光时间(微秒)"
[UCIPGroup1Gain]
LABEL = "增益（倍）"
HELP = "设置组1增益值"
[UCIPGroup2Gain]
LABEL = "增益（倍）"
HELP = "设置组2增益值"
[UCIPGroup3Gain]
LABEL = "增益（倍）"
HELP = "设置组3增益值"
[UCIPGroup4Gain]
LABEL = "增益（倍）"
HELP = "设置组4增益值"
[UCIPGroup5Gain]
LABEL = "增益（倍）"
HELP = "设置组5增益值"
[UCIPGroup6Gain]
LABEL = "增益（倍）"
HELP = "设置组6增益值"
[UCIPGroup7Gain]
LABEL = "增益（倍）"
HELP = "设置组7增益值"
[UCIPGroup8Gain]
LABEL = "增益（倍）"
HELP = "设置组8增益值"
[UCIPGroup1LuminLevel]
LABEL = "补光灯亮度等级"
HELP = "设置组1补光灯亮度等级"
[UCIPGroup2LuminLevel]
LABEL = "补光灯亮度等级"
HELP = "设置组2补光灯亮度等级"
[UCIPGroup3LuminLevel]
LABEL = "补光灯亮度等级"
HELP = "设置组3补光灯亮度等级"
[UCIPGroup4LuminLevel]
LABEL = "补光灯亮度等级"
HELP = "设置组4补光灯亮度等级"
[UCIPGroup5LuminLevel]
LABEL = "补光灯亮度等级"
HELP = "设置组5补光灯亮度等级"
[UCIPGroup6LuminLevel]
LABEL = "补光灯亮度等级"
HELP = "设置组6补光灯亮度等级"
[UCIPGroup7LuminLevel]
LABEL = "补光灯亮度等级"
HELP = "设置组7补光灯亮度等级"
[UCIPGroup8LuminLevel]
LABEL = "补光灯亮度等级"
HELP = "设置组8补光灯亮度等级"
[UCIPGroup1HoldTime]
LABEL = "保持时间（毫秒）"
HELP = "设置组1保持时间(毫秒)"
[UCIPGroup2HoldTime]
LABEL = "保持时间（毫秒）"
HELP = "设置组2保持时间(毫秒)"
[UCIPGroup3HoldTime]
LABEL = "保持时间（毫秒）"
HELP = "设置组3保持时间(毫秒)"
[UCIPGroup4HoldTime]
LABEL = "保持时间（毫秒）"
HELP = "设置组4保持时间(毫秒)"
[UCIPGroup5HoldTime]
LABEL = "保持时间（毫秒）"
HELP = "设置组5保持时间(毫秒)"
[UCIPGroup6HoldTime]
LABEL = "保持时间（毫秒）"
HELP = "设置组6保持时间(毫秒)"
[UCIPGroup7HoldTime]
LABEL = "保持时间（毫秒）"
HELP = "设置组7保持时间(毫秒)"
[UCIPGroup8HoldTime]
LABEL = "保持时间（毫秒）"
HELP = "设置组8保持时间(毫秒)"
[IlluminationMode]
LABEL = "照明模式"
HELP = "设置解码时的照明模式"
[IlluminationMode.Options]
ALWAYS_OFF = "始终关闭"
ALWAYS_ON = "始终开启"
FLASH = "闪光模式"
ON_WHEN_READING = "读取时开启"
[IlluminationLevel]
LABEL = "照明亮度"
HELP = "设置照明亮度级别"
[IlluminationLevel.Options]
LOW = "低亮度"
MID = "中等亮度"
HIGH = "高亮度"
[LightGroupSelect]
LABEL = "光源组选择"
HELP = "选择双色照明的光源组"
[LightGroupSelect.Options]
GROUP1 = "组1"
GROUP2 = "组2"
[AimMode]
LABEL = "瞄准模式"
HELP = "设置解码时的瞄准模式"
[DataFilterMode]
LABEL = "数据过滤模式"
HELP = "设置数据过滤模式"
[DataFilterMode.Options]
Disable = "禁用"
Normal = "普通"
Advanced = "高级"
[DataFilterEnable1]
LABEL = "数据过滤使能1"
HELP = "设置普通数据过滤使能"
[DataFilterEnable2]
LABEL = "数据过滤使能2"
HELP = "设置普通数据过滤使能"
[DataFilterEnable3]
LABEL = "数据过滤使能3"
HELP = "设置普通数据过滤使能"
[DataFilterEnable4]
LABEL = "数据过滤使能4"
HELP = "设置普通数据过滤使能"
[DataFilterFunc1]
LABEL = "匹配功能1"
HELP = "设置正则表达式1的匹配功能"
[DataFilterFunc1.Options]
FILTER = "过滤"
DELETE = "删除"
[DataFilterFunc2]
LABEL = "匹配功能2"
HELP = "设置正则表达式2的匹配功能"
[DataFilterFunc3]
LABEL = "匹配功能3"
HELP = "设置正则表达式3的匹配功能"
[DataFilterFunc4]
LABEL = "匹配功能4"
HELP = "设置正则表达式4的匹配功能"
[DataFilterFunc5]
LABEL = "匹配功能5"
HELP = "设置正则表达式5的匹配功能"
[DataFilterFunc6]
LABEL = "匹配功能6"
HELP = "设置正则表达式6的匹配功能"
[DataFilterFunc7]
LABEL = "匹配功能7"
HELP = "设置正则表达式7的匹配功能"
[DataFilterFunc8]
LABEL = "匹配功能8"
HELP = "设置正则表达式8的匹配功能"
[DataFilterFunc9]
LABEL = "匹配功能9"
HELP = "设置正则表达式9的匹配功能"
[DataFilterFunc10]
LABEL = "匹配功能10"
HELP = "设置正则表达式10的匹配功能"
[DataFilterRegex1]
LABEL = "正则表达式1"
HELP = "设置扫描正则表达式规则 1，用于数据格式化和校验"
[DataFilterRegex2]
LABEL = "正则表达式2"
HELP = "设置扫描正则表达式规则 2，用于数据格式化和校验"
[DataFilterRegex3]
LABEL = "正则表达式3"
HELP = "设置扫描正则表达式规则 3，用于数据格式化和校验"
[DataFilterRegex4]
LABEL = "正则表达式4"
HELP = "设置扫描正则表达式规则 4，用于数据格式化和校验"
[DataFilterRegex5]
LABEL = "正则表达式5"
HELP = "设置扫描正则表达式规则 5，用于数据格式化和校验"
[DataFilterRegex6]
LABEL = "正则表达式6"
HELP = "设置扫描正则表达式规则 6，用于数据格式化和校验"
[DataFilterRegex7]
LABEL = "正则表达式7"
HELP = "设置扫描正则表达式规则 7，用于数据格式化和校验"
[DataFilterRegex8]
LABEL = "正则表达式8"
HELP = "设置扫描正则表达式规则 8，用于数据格式化和校验"
[DataFilterRegex9]
LABEL = "正则表达式9"
HELP = "设置扫描正则表达式规则 9，用于数据格式化和校验"
[DataFilterRegex10]
LABEL = "正则表达式10"
HELP = "设置扫描正则表达式规则 10，用于数据格式化和校验"
[AimDecoVideoSetDecoTimeMs]
LABEL = "单次解码时间上限（毫秒）"
HELP = "设置单次解码时间上限"
[DoubleConfirm]
LABEL = "多重确认（次）"
HELP = "需要确认的次数（0-9）"
[SameBarDelay1D]
LABEL = "1D重码有效时延（毫秒）"
HELP = "1D重码有效时延（单位：50ms）"
[SameBarDelay2D]
LABEL = "2D重码时延（毫秒）"
HELP = "二维重码时延，单位50ms"
[GoodReadDelay]
LABEL = "解码成功静默时延（毫秒）"
HELP = "解码成功静默时延，单位毫秒"
[CodeSymb1D]
LABEL = "一维条码识读功能"
HELP = "控制哪些一维条码可以被识读"
[CodeSymb1D.Options]
AS_PARA = "遵循各类1D条码的自定义识读设定"
DISABLE_ALL = "失能全部1D条码的识读设定"
ENABLE_ALL = "使能全部1D条码的识读设定"
[CodeSymb2D]
LABEL = "二维条码识读功能"
HELP = "控制哪些2D条码可以被识读"
[CodeSymb2D.Options]
AS_PARA = "遵循各类2D条码的自定义识读设定"
DISABLE_ALL = "失能全部2D条码的识读设定"
ENABLE_ALL = "使能全部2D条码的识读设定"
PDF417_ONLY = "仅PDF417开启"
QRCODE_ONLY = "仅QR码开启"
DATAMATRIX_ONLY = "仅Data Matrix开启"
MAXICODE_ONLY = "仅MaxiCode开启"
AZTECCODE_ONLY = "仅AztecCode开启"
CSCODE_ONLY = "仅汉信码开启"
[MultiSymbRead]
LABEL = "同图多条码识读功能"
HELP = "控制同图多条码识读模式"
[MultiSymbRead.Options]
DISABLE = "失能同图多条码识读"
ONLY_1D = "同图多条码只识读 1D 条码"
ONLY_2D = "同图多条码只识读 2D 条码"
BOTH_1D_2D = "同图多条码同时识读 1D + 2D 条码"
[CenteringRead]
LABEL = "中心解码模式"
HELP = "控制中心解码模式"
[CenteringRead.Options]
NONE = "失能中心解码模式"
VERTICAL = "使能垂直居中解码模式"
NEAR = "使能邻近中心解码模式"
[ScreenRead]
LABEL = "移动屏（手机屏）条码识读功能"
HELP = "使能/失能识读移动屏（手机屏）条码"
[Code39ReadMain]
LABEL = "Code 39识读"
HELP = "启用/禁用Code 39码的识读功能"
[Code39ChkDigVer]
LABEL = "Code 39校验符确认"
HELP = "启用/禁用Code 39校验符确认功能"
[Code39ChkDigTx]
LABEL = "Code 39校验符传送"
HELP = "启用/禁用Code 39校验符传送功能"
[Code39MaxLen]
LABEL = "Code 39最大长度"
HELP = "设置Code 39码的最大长度"
[Code39MinLen]
LABEL = "Code 39最小长度"
HELP = "设置Code 39码的最小长度"
[Code39CodeID]
LABEL = "Code 39码制识别符"
HELP = "设置Code 39的自定义码制识别符"
[Code39InsertStr]
LABEL = "Code 39插入字符串"
HELP = "选择Code 39插入的字符串组"
[Code39Format]
LABEL = "Code 39格式"
HELP = "设置Code 39的数据传输格式"
[Code39Format.Options]
STANDARD = "标准格式"
FULL_ASCII = "完整ASCII格式"
[Code39StEdTrans]
LABEL = "Code 39起始/终止符传送"
HELP = "启用/禁用Code 39起始/终止符的传送功能"
[Code39AsteAsChar]
LABEL = "Code 39星号作为数据字符"
HELP = "启用/禁用Code 39中星号作为数据字符的功能"
[Code39Code32]
LABEL = "Code 39转Code 32"
HELP = "启用/禁用Code 39转Code 32的功能"
[Code39Code32Pre]
LABEL = "Code 39 Code 32前缀"
HELP = "启用/禁用Code 32前缀'A'的传送功能"
[Code39Trioptic]
LABEL = "Code 39 Trioptic"
HELP = "启用/禁用Trioptic Code 39的识读功能"
[Code39TOStEdTrans]
LABEL = "Code 39 Trioptic起始/终止符传送"
HELP = "启用/禁用Trioptic Code 39起始/终止符的传送功能"
[Code39AimID]
LABEL = "Code 39 自定义AIM ID"
HELP = "设置Code 39自定义AIM ID"
[Code128ReadMain]
LABEL = "Code 128码读取"
HELP = "启用/禁用Code 128码的读取功能"
[Code128ChkDigVer]
LABEL = "Code 128码校验位验证"
HELP = "启用/禁用Code 128码的校验位验证功能"
[Code128ChkDigTx]
LABEL = "Code 128码校验位传输"
HELP = "启用/禁用Code 128码的校验位传输功能"
[Code128MaxLen]
LABEL = "Code 128码最大长度"
HELP = "设置Code 128码的最大长度"
[Code128MinLen]
LABEL = "Code 128码最小长度"
HELP = "设置Code 128码的最小长度"
[Code128CodeID]
LABEL = "Code 128码标识符"
HELP = "设置Code 128码的自定义标识符"
[Code128InsertStr]
LABEL = "Code 128码插入字符串组"
HELP = "选择Code 128码插入的字符串组"
[Code128TruZero]
LABEL = "Code 128码截去前导零"
HELP = "控制Code 128码的前导零截去功能"
[Code128TruZero.Options]
DISABLE = "不截去前导零"
ALL = "截去所有前导零"
FIRST = "仅截去第一个前导零"
[Code128AimID]
LABEL = "Code 128自定义AIM ID"
HELP = "设置Code 128自定义AIM ID"
[CodabarReadMain]
LABEL = "Codabar码读取"
HELP = "启用/禁用Codabar码的读取功能"
[CodabarChkDigVer]
LABEL = "Codabar码校验位验证"
HELP = "启用/禁用Codabar码的校验位验证功能"
[CodabarChkDigTx]
LABEL = "Codabar码校验位传输"
HELP = "启用/禁用Codabar码的校验位传输功能"
[CodabarMaxLen]
LABEL = "Codabar码最大长度"
HELP = "设置Codabar码的最大长度"
[CodabarMinLen]
LABEL = "Codabar码最小长度"
HELP = "设置Codabar码的最小长度"
[CodabarCodeID]
LABEL = "Codabar码标识符"
HELP = "设置Codabar码的自定义标识符"
[CodabarInsertStr]
LABEL = "Codabar码插入字符串"
HELP = "选择Codabar码插入的字符串组"
[CodabarStartStopType]
LABEL = "Codabar码起始/结束字符类型"
HELP = "设置Codabar码的起始/结束字符类型"
[CodabarStartStopType.Options]
ABCD = "ABCD类型"
abcd = "abcd类型"
TNE = "ABCD/TN*E类型"
tne = "abcd/tn*e类型"
[CodabarStartStopTx]
LABEL = "Codabar码起始/结束字符传输"
HELP = "启用/禁用Codabar码的起始/结束字符传输功能"
[CodabarStartEndCharEq]
LABEL = "Codabar码起始/结束字符相等"
HELP = "启用/禁用Codabar码的起始/结束字符必须相等的要求"
[CodabarAimID]
LABEL = "库德巴码自定义AIM ID"
HELP = "设置库德巴码自定义AIM ID"
[Intl25ReadMain]
LABEL = "交叉25码识读"
HELP = "启用/禁用交叉25码的识读功能"
[Intl25ChkDigVer]
LABEL = "交叉25码校验符确认"
HELP = "启用/禁用交叉25码校验符的确认功能"
[Intl25ChkDigVer.Options]
DISABLE = "禁用"
USS = "采用USS检测校验符"
OPCC = "采用OPCC检测校验符"
[Intl25ChkDigTx]
LABEL = "交叉25码校验符传送"
HELP = "启用/禁用交叉25码校验符的传送功能"
[Intl25MaxLen]
LABEL = "交叉25码最大长度"
HELP = "设置交叉25码的最大长度"
[Intl25MinLen]
LABEL = "交叉25码最小长度"
HELP = "设置交叉25码的最小长度"
[Intl25CodeID]
LABEL = "交叉25码识别符"
HELP = "设置交叉25码的自定义识别符"
[Intl25InsertStr]
LABEL = "交叉25码插入字符串组"
HELP = "选择交叉25码插入的字符串组"
[Intl25LenRestr]
LABEL = "交叉25码长度限制"
HELP = "控制交叉25码的长度限制功能"
[Intl25LenRestr.Options]
ANY = "无长度限制"
ONLY_44 = "仅限44个字符"
[Intl25BankFormatConv]
LABEL = "交叉25码银行格式转换"
HELP = "启用/禁用交叉25码的银行格式转换功能"
[Intl25BankFormatType]
LABEL = "交叉25码银行格式类型"
HELP = "设置交叉25码的银行格式类型"
[Intl25BankFormatType.Options]
NO_TAB = "不插入Tab键"
ALGUNS = "按照ALGUNS规则插入若干个Tab键"
TODOS = "按照TODOS OS规则插入若干个Tab键"
[Intl25AimID]
LABEL = "交叉25码自定义AIM ID"
HELP = "设置交叉25码自定义AIM ID"
[Code93ReadMain]
LABEL = "Code 93码读取"
HELP = "启用/禁用Code 93码的读取功能"
[Code93ChkDigVer]
LABEL = "Code 93码校验位验证"
HELP = "启用/禁用Code 93码的校验位验证功能"
[Code93ChkDigTx]
LABEL = "Code 93码校验位传输"
HELP = "启用/禁用Code 93码的校验位传输功能"
[Code93MaxLen]
LABEL = "Code 93码最大长度"
HELP = "设置Code 93码的最大长度"
[Code93MinLen]
LABEL = "Code 93码最小长度"
HELP = "设置Code 93码的最小长度"
[Code93CodeID]
LABEL = "Code 93码标识符"
HELP = "设置Code 93码的自定义标识符"
[Code93InsertStr]
LABEL = "Code 93码插入字符串组"
HELP = "选择Code 93码插入的字符串组"
[Code93AimID]
LABEL = "Code 93自定义AIM ID"
HELP = "设置Code 93自定义AIM ID"
[EAN8ReadMain]
LABEL = "EAN-8识读功能"
HELP = "使能/失能识读EAN-8码"
[EAN8ChkDigVer]
LABEL = "EAN-8校验符确认功能"
HELP = "使能/失能EAN-8校验符确认功能"
[EAN8ChkDigTx]
LABEL = "EAN-8校验符传送功能"
HELP = "使能/失能EAN-8校验符传送功能"
[EAN8CodeID]
LABEL = "EAN-8码制识别符"
HELP = "设置EAN-8码制识别符"
[EAN8InsertStr]
LABEL = "EAN-8插入字符串组功能"
HELP = "选择EAN-8插入的字符串组"
[EAN8SuplDig]
LABEL = "EAN-8附加码功能"
HELP = "控制EAN-8附加码功能选项"
[EAN8SuplDig.Options]
NONE = "无附加码功能"
TWO_DIGITS = "两位附加码"
FIVE_DIGITS = "五位附加码"
TWO_OR_FIVE = "两位或五位附加码"
ONLY_WITH_SUPPLEMENTAL = "只解带附加码的条码"
[EAN8TrunExp]
LABEL = "EAN-8截取/扩展"
HELP = "控制EAN-8截取和扩展选项"
[EAN8TrunExp.Options]
NONE = "不进行截取或扩展"
TRUNCATE = "截取前导零"
EXPAND = "扩展为EAN-13"
[EAN8AimID]
LABEL = "EAN-8 自定义AIM ID"
HELP = "设置EAN-8自定义AIM ID"
[EAN13ReadMain]
LABEL = "EAN-13识读功能"
HELP = "使能/失能识读EAN-13码"
[EAN13ChkDigVer]
LABEL = "EAN-13校验符确认功能"
HELP = "使能/失能检测EAN-13校验符"
[EAN13ChkDigTx]
LABEL = "EAN-13校验符传送功能"
HELP = "使能/失能传送EAN-13校验符"
[EAN13CodeID]
LABEL = "EAN-13码制识别符"
HELP = "设置EAN-13码制识别符"
[EAN13InsertStr]
LABEL = "EAN-13插入字符串组"
HELP = "选择EAN-13插入的字符串组"
[EAN13SuplDig]
LABEL = "EAN-13附加码功能"
HELP = "控制EAN-13附加码功能选项"
[EAN13SuplDig.Options]
NONE = "无附加码"
TWO_DIGITS = "2位附加码"
FIVE_DIGITS = "5位附加码"
TWO_OR_FIVE = "2位或5位附加码"
ONLY_WITH_SUPPLEMENTAL = "只解带附加码的EAN13条码"
[EAN13ISBSN]
LABEL = "EAN13 ISBN/ISSN转换功能"
HELP = "使能/失能ISBN/ISSN转换功能"
[EAN13ISSNISBNID]
LABEL = "EAN-13 ISBN/ISSN码制识别符"
HELP = "设置EAN-13 ISBN/ISSN码制识别符"
[EAN13AimID]
LABEL = "EAN-13 自定义AIM ID"
HELP = "设置EAN-13自定义AIM ID"
[UPCAReadMain]
LABEL = "UPC-A码识读功能"
HELP = "使能/失能识读UPC-A码"
[UPCAChkDigVer]
LABEL = "UPC-A码校验符确认功能"
HELP = "使能/失能UPC-A码校验符确认功能"
[UPCAChkDigTx]
LABEL = "UPC-A码校验符传送功能"
HELP = "使能/失能UPC-A码校验符传送功能"
[UPCACodeID]
LABEL = "UPC-A码制识别符"
HELP = "设置UPC-A的自定义码制识别符"
[UPCAInsertStr]
LABEL = "UPC-A插入字符串组功能"
HELP = "选择UPC-A插入的字符串组"
[UPCASuplDig]
LABEL = "UPC-A附加码功能"
HELP = "控制UPC-A附加码功能选项"
[UPCASuplDig.Options]
NONE = "无附加码功能"
TWO_DIGITS = "可以识读带2位附加码的UPC-A码"
FIVE_DIGITS = "可以识读带5位附加码的UPC-A码"
TWO_OR_FIVE = "可以识读带2位或5位附加码的UPC-A码"
ONLY_WITH_SUPPLEMENTAL = "只解带附加码的UPC-A码"
[UPCATrunExp]
LABEL = "UPC-A截去、扩展功能"
HELP = "控制UPC-A截去、扩展功能选项"
[UPCATrunExp.Options]
NONE = "不进行截去、扩展"
TRUNCATE = "截去前导零"
EXPAND = "输出数据扩展成 13位的 EAN-13 码"
[UPCAAimID]
LABEL = "UPC-A 自定义AIM ID"
HELP = "设置UPC-A自定义AIM ID"
[UPCEReadMain]
LABEL = "UPC-E识读功能"
HELP = "使能/失能识读UPC-E码"
[UPCEChkDigVer]
LABEL = "UPC-E校验符确认功能"
HELP = "使能/失能检测UPC-E码的校验符"
[UPCEChkDigTx]
LABEL = "UPC-E校验符传送功能"
HELP = "使能/失能传送UPC-E码的校验符"
[UPCECodeID]
LABEL = "UPC-E码制识别符"
HELP = "设置UPC-E的自定义码制识别符"
[UPCEInsertStr]
LABEL = "UPC-E插入字符串组功能"
HELP = "选择UPC-E插入的字符串组"
[UPCESuplDig]
LABEL = "UPC-E附加码功能"
HELP = "控制UPC-E附加码功能选项"
[UPCESuplDig.Options]
NONE = "无附加码功能"
TWO_DIGITS = "可以识读带2位附加码的UPC-E码"
FIVE_DIGITS = "可以识读带5位附加码的UPC-E码"
TWO_OR_FIVE = "可以识读带2位或5位附加码的UPC-E码"
ONLY_WITH_SUPPLEMENTAL = "只解带附加码的UPC-E条码"
[UPCETrunExp]
LABEL = "UPC-E截去、扩展功能"
HELP = "控制UPC-E截去、扩展功能选项"
[UPCETrunExp.Options]
NONE = "不进行截去、扩展"
TRUNCATE = "截去前导零"
EXPAND_EAN13 = "输出数据扩展成 13位的 EAN-13 码"
EXPAND_UPCA = "输出数据扩展成 12位的 UPC-A 码"
[UPCEAimID]
LABEL = "UPC-E 自定义AIM ID"
HELP = "设置UPC-E自定义AIM ID"
[Code11ReadMain]
LABEL = "Code 11码读取"
HELP = "启用/禁用Code 11码的读取功能"
[Code11ChkDigVer]
LABEL = "Code 11码校验位验证"
HELP = "设置Code 11码的校验位验证方式"
[Code11ChkDigVer.Options]
NONE = "不校验"
ONE = "一位校验位"
TWO = "两位校验位"
RESERVED = "保留"
[Code11ChkDigTx]
LABEL = "Code 11码校验位传输"
HELP = "启用/禁用Code 11码的校验位传输功能"
[Code11MaxLen]
LABEL = "Code 11码最大长度"
HELP = "设置Code 11码的最大长度"
[Code11MinLen]
LABEL = "Code 11码最小长度"
HELP = "设置Code 11码的最小长度"
[Code11CodeID]
LABEL = "Code 11码标识符"
HELP = "设置Code 11码的自定义标识符"
[Code11InsertStr]
LABEL = "Code 11码插入字符串"
HELP = "选择Code 11码插入的字符串组"
[Code11AimID]
LABEL = "Code 11自定义AIM ID"
HELP = "设置Code 11自定义AIM ID"
[GS1EXPAReadMain]
LABEL = "GS1 DataBar Expanded码读取"
HELP = "启用/禁用GS1 DataBar Expanded码的读取功能"
[GS1EXPAMaxLen]
LABEL = "GS1 DataBar Expanded码最大长度"
HELP = "设置GS1 DataBar Expanded码的最大长度"
[GS1EXPAMinLen]
LABEL = "GS1 DataBar Expanded码最小长度"
HELP = "设置GS1 DataBar Expanded码的最小长度"
[GS1EXPACodeID]
LABEL = "GS1 DataBar Expanded码标识符"
HELP = "设置GS1 DataBar Expanded码的自定义标识符"
[GS1EXPAInsertStr]
LABEL = "GS1 DataBar Expanded码插入字符串组"
HELP = "选择GS1 DataBar Expanded码插入的字符串组"
[GS1EXPAConversion]
LABEL = "GS1 DataBar Expanded码转换"
HELP = "控制GS1 DataBar Expanded码的转换模式"
[GS1EXPAConversion.Options]
NONE = "不进行转换"
UCC_EAN128 = "转换为UCC/EAN-128码"
[GS1EXPAimID]
LABEL = "GS1 DataBar Expanded自定义AIM ID"
HELP = "设置GS1 DataBar Expanded自定义AIM ID"
[GS1DBReadMain]
LABEL = "GS1 DataBar码读取"
HELP = "启用/禁用GS1 DataBar码的读取功能"
[GS1DBCodeID]
LABEL = "GS1 DataBar码标识符"
HELP = "设置GS1 DataBar码的自定义标识符"
[GS1DBInsertStr]
LABEL = "GS1 DataBar码插入字符串组"
HELP = "选择GS1 DataBar码插入的字符串组"
[GS1DBConversion]
LABEL = "GS1 DataBar码转换"
HELP = "控制GS1 DataBar码的转换模式"
[GS1DBConversion.Options]
NONE = "不进行转换"
UCC_EAN128 = "转换为UCC/EAN-128码"
UPC_EAN13 = "转换为UPC/EAN-13码"
[GS1DBOutputBrackets]
LABEL = "GS1 DataBar码输出括号"
HELP = "启用/禁用GS1 DataBar码的输出括号功能"
[GS1DBAimID]
LABEL = "GS1 DataBar自定义AIM ID"
HELP = "设置GS1 DataBar自定义AIM ID"
[GS1LIMIReadMain]
LABEL = "GS1 DataBar Limited码读取"
HELP = "启用/禁用GS1 DataBar Limited码的读取功能"
[GS1LIMICodeID]
LABEL = "GS1 DataBar Limited码标识符"
HELP = "设置GS1 DataBar Limited码的自定义标识符"
[GS1LIMIInsertStr]
LABEL = "GS1 DataBar Limited码插入字符串组"
HELP = "选择GS1 DataBar Limited码插入的字符串组"
[GS1LIMIConversion]
LABEL = "GS1 DataBar Limited码转换"
HELP = "控制GS1 DataBar Limited码的转换模式"
[GS1LIMIConversion.Options]
NONE = "不进行转换"
UCC_EAN128 = "转换为UCC/EAN-128码"
UPC_EAN13 = "转换为UPC/EAN-13码"
[GS1LIMIAimID]
LABEL = "GS1 DataBar Limited自定义AIM ID"
HELP = "设置GS1 DataBar Limited自定义AIM ID"
[ISBT128ReadMain]
LABEL = "ISBT128码读取"
HELP = "启用/禁用ISBT128码的读取功能"
[ISBT128CkDigiVer]
LABEL = "ISBT128码校验位验证"
HELP = "启用/禁用ISBT128码的校验位验证功能"
[ISBT128ChkDigTrans]
LABEL = "ISBT128码校验位传输"
HELP = "启用/禁用ISBT128码的校验位传输功能"
[ISBT128MaxLen]
LABEL = "ISBT128码最大长度"
HELP = "设置ISBT128码的最大长度"
[ISBT128MinLen]
LABEL = "ISBT128码最小长度"
HELP = "设置ISBT128码的最小长度"
[ISBT128CodeID]
LABEL = "ISBT128码标识符"
HELP = "设置ISBT128码的自定义标识符"
[ISBT128InsertStr]
LABEL = "ISBT128码插入字符串"
HELP = "选择ISBT128码插入的字符串组"
[ISBT128AimID]
LABEL = "ISBT 128自定义AIM ID"
HELP = "设置ISBT 128自定义AIM ID"
[MSIPLReadMain]
LABEL = "MSI/Plessey码读取"
HELP = "启用/禁用MSI/Plessey码的读取功能"
[MSIPLChkDigVer]
LABEL = "MSI/Plessey码校验位验证"
HELP = "控制MSI/Plessey码的校验位验证方式"
[MSIPLChkDigVer.Options]
DISABLE = "禁用校验位验证"
ONE_MOD10 = "一位MOD10校验位"
TWO_MOD10 = "两位MOD10校验位"
MOD10_MOD11 = "MOD10/MOD11校验位"
[MSIPLChkDigTx]
LABEL = "MSI/Plessey码校验位传输"
HELP = "启用/禁用MSI/Plessey码的校验位传输功能"
[MSIPLMaxLen]
LABEL = "MSI/Plessey码最大长度"
HELP = "设置MSI/Plessey码的最大长度"
[MSIPLMinLen]
LABEL = "MSI/Plessey码最小长度"
HELP = "设置MSI/Plessey码的最小长度"
[MSIPLCodeID]
LABEL = "MSI/Plessey码标识符"
HELP = "设置MSI/Plessey码的自定义标识符"
[MSIPLInsertStr]
LABEL = "MSI/Plessey码插入字符串组"
HELP = "选择MSI/Plessey码插入的字符串组"
[MSIPLAimID]
LABEL = "MSI/Plessey自定义AIM ID"
HELP = "设置MSI/Plessey自定义AIM ID"
[UCCEAN128ReadMain]
LABEL = "UCC/EAN 128码读取"
HELP = "启用/禁用UCC/EAN 128码的读取功能"
[UCCEAN128ChkDigVer]
LABEL = "UCC/EAN 128码校验位验证"
HELP = "启用/禁用UCC/EAN 128码的校验位验证功能"
[UCCEAN128ChkDigTx]
LABEL = "UCC/EAN 128码校验位传输"
HELP = "启用/禁用UCC/EAN 128码的校验位传输功能"
[UCCEAN128MaxLen]
LABEL = "UCC/EAN 128码最大长度"
HELP = "设置UCC/EAN 128码的最大长度"
[UCCEAN128MinLen]
LABEL = "UCC/EAN 128码最小长度"
HELP = "设置UCC/EAN 128码的最小长度"
[UCCEAN128CodeID]
LABEL = "UCC/EAN 128码标识符"
HELP = "设置UCC/EAN 128码的自定义标识符"
[UCCEAN128InsertStr]
LABEL = "UCC/EAN 128码插入字符串组"
HELP = "选择UCC/EAN 128码插入的字符串组"
[UCCEAN128TruZero]
LABEL = "UCC/EAN 128码截去前导零"
HELP = "控制UCC/EAN 128码的前导零截去功能"
[UCCEAN128TruZero.Options]
DISABLE = "不截去前导零"
ALL = "截去所有前导零"
FIRST = "仅截去第一个前导零"
[UCCEAN128AimID]
LABEL = "UCC/EAN 128自定义AIM ID"
HELP = "设置UCC/EAN 128自定义AIM ID"
[UKPLReadMain]
LABEL = "UK/Plessey码读取"
HELP = "启用/禁用UK/Plessey码的读取功能"
[UKPLChkDigVer]
LABEL = "UK/Plessey码校验位验证"
HELP = "启用/禁用UK/Plessey码的校验位验证功能"
[UKPLChkDigTx]
LABEL = "UK/Plessey码校验位传输"
HELP = "启用/禁用UK/Plessey码的校验位传输功能"
[UKPLMaxLen]
LABEL = "UK/Plessey码最大长度"
HELP = "设置UK/Plessey码的最大长度"
[UKPLMinLen]
LABEL = "UK/Plessey码最小长度"
HELP = "设置UK/Plessey码的最小长度"
[UKPLCodeID]
LABEL = "UK/Plessey码标识符"
HELP = "设置UK/Plessey码的自定义标识符"
[UKPLInsertStr]
LABEL = "UK/Plessey码插入字符串组"
HELP = "选择UK/Plessey码插入的字符串组"
[UKPLAimID]
LABEL = "UK/Plessey自定义AIM ID"
HELP = "设置UK/Plessey自定义AIM ID"
[UPCE1ReadMain]
LABEL = "UPC-E1码读取"
HELP = "启用/禁用UPC-E1码的读取功能"
[UPCE1ChkDigVer]
LABEL = "UPC-E1码校验位验证"
HELP = "启用/禁用UPC-E1码的校验位验证功能"
[UPCE1ChkDigTrans]
LABEL = "UPC-E1码校验位传输"
HELP = "启用/禁用UPC-E1码的校验位传输功能"
[UPCE1CodeID]
LABEL = "UPC-E1码标识符"
HELP = "设置UPC-E1码的自定义标识符"
[UPCE1InsertStr]
LABEL = "UPC-E1码插入字符串"
HELP = "选择UPC-E1码插入的字符串组"
[UPCE1SuplDig]
LABEL = "UPC-E1码补充数字"
HELP = "设置UPC-E1码的补充数字选项"
[UPCE1SuplDig.Options]
NONE = "无补充数字"
TWO = "两位补充数字"
FIVE = "五位补充数字"
TWO_FIVE = "两位或五位补充数字"
ONLY = "仅补充数字"
[UPCE1TrunExp]
LABEL = "UPC-E1码截取/扩展"
HELP = "设置UPC-E1码的截取/扩展选项"
[UPCE1TrunExp.Options]
NONE = "不进行截取或扩展"
TRUNCATE = "截取前导零"
EXPAND_EAN13 = "扩展为EAN-13码"
EXPAND_UPCA = "扩展为UPC-A码"
[UPCE1AimID]
LABEL = "UPC-E1自定义AIM ID"
HELP = "设置UPC-E1自定义AIM ID"
[CHNPSTReadMain]
LABEL = "中国邮政码读取"
HELP = "启用/禁用中国邮政码的读取功能"
[CHNPSTChkDigVer]
LABEL = "中国邮政码校验位验证"
HELP = "启用/禁用中国邮政码的校验位验证功能"
[CHNPSTChkDigTx]
LABEL = "中国邮政码校验位传输"
HELP = "启用/禁用中国邮政码的校验位传输功能"
[CHNPSTMaxLen]
LABEL = "中国邮政码最大长度"
HELP = "设置中国邮政码的最大长度"
[CHNPSTMinLen]
LABEL = "中国邮政码最小长度"
HELP = "设置中国邮政码的最小长度"
[CHNPSTCodeID]
LABEL = "中国邮政码标识符"
HELP = "设置中国邮政码的自定义标识符"
[CHNPSTInsertStr]
LABEL = "中国邮政码插入字符串组"
HELP = "选择中国邮政码插入的字符串组"
[CHNPSTAimID]
LABEL = "中国邮政码自定义AIM ID"
HELP = "设置中国邮政码自定义AIM ID"
[Industrial25ReadMain]
LABEL = "工业25码识读"
HELP = "启用/禁用工业25码的识读"
[Industrial25MaxLen]
LABEL = "工业25码最大长度"
HELP = "设置工业25码的最大长度"
[Industrial25MinLen]
LABEL = "工业25码最小长度"
HELP = "设置工业25码的最小长度"
[Industrial25CodeID]
LABEL = "工业25码识别符"
HELP = "设置工业25码的自定义识别符"
[Industrial25InsertStr]
LABEL = "工业25码插入字符串"
HELP = "选择工业25码插入的字符串组"
[Industrial25AimID]
LABEL = "工业25码自定义AIM ID"
HELP = "设置工业25码自定义AIM ID"
[Matrix25ReadMain]
LABEL = "矩阵25码识读"
HELP = "启用/禁用矩阵25码的识读功能"
[Matrix25ChkDigVer]
LABEL = "矩阵25码校验符确认"
HELP = "启用/禁用矩阵25码的校验符确认功能"
[Matrix25ChkDigTrans]
LABEL = "矩阵25码校验符传送"
HELP = "启用/禁用矩阵25码的校验符传送功能"
[Matrix25MaxLen]
LABEL = "矩阵25码最大长度"
HELP = "设置矩阵25码的最大长度"
[Matrix25MinLen]
LABEL = "矩阵25码最小长度"
HELP = "设置矩阵25码的最小长度"
[Matrix25CodeID]
LABEL = "矩阵25码标识符"
HELP = "设置矩阵25码的自定义标识符"
[Matrix25InsertStr]
LABEL = "矩阵25码插入字符串"
HELP = "选择矩阵25码插入的字符串组"
[Matrix25AimID]
LABEL = "矩阵25码自定义AIM ID"
HELP = "设置矩阵25码自定义AIM ID"
[QRCodeReadMain]
LABEL = "QR码读取"
HELP = "启用/禁用QR码的读取功能"
[QRCodeCodeID]
LABEL = "QR码标识符"
HELP = "设置QR码的自定义标识符"
[QRCodeWebCodeForbid]
LABEL = "QR码网址访问控制"
HELP = "控制QR码中网址的访问权限"
[QRCodeWebCodeForbid.Options]
ALLOW = "允许访问网站"
FORBID = "禁止访问网站"
DOUYIN_ONLY = "仅允许访问抖音网站"
[QRCodeMaxLen]
LABEL = "QR码最大长度"
HELP = "设置QR码的最大长度"
[QRCodeMinLen]
LABEL = "QR码最小长度"
HELP = "设置QR码的最小长度"
[QRCodeAimID]
LABEL = "QR码自定义AIM ID"
HELP = "设置QR码自定义AIM ID"
[DMReadMain]
LABEL = "DataMatrix码读取"
HELP = "启用/禁用DataMatrix码的读取功能"
[DMCodeID]
LABEL = "DataMatrix码标识符"
HELP = "设置DataMatrix码的自定义标识符"
[DMMaxLen]
LABEL = "DataMatrix码最大长度"
HELP = "设置DataMatrix码的最大长度"
[DMMinLen]
LABEL = "DataMatrix码最小长度"
HELP = "设置DataMatrix码的最小长度"
[DMPPNCODE]
LABEL = "DataMatrix PPN码"
HELP = "启用/禁用PPN（药品产品编号）码的输出功能"
[DMAimID]
LABEL = "DataMatrix自定义AIM ID"
HELP = "设置DataMatrix自定义AIM ID"
[AztecReadMain]
LABEL = "Aztec码读取"
HELP = "启用/禁用Aztec码的读取功能"
[AztecCodeID]
LABEL = "Aztec码标识符"
HELP = "设置Aztec码的自定义标识符"
[AztecMaxLen]
LABEL = "Aztec码最大长度"
HELP = "设置Aztec码的最大长度"
[AztecMinLen]
LABEL = "Aztec码最小长度"
HELP = "设置Aztec码的最小长度"
[AztecAimID]
LABEL = "Aztec码自定义AIM ID"
HELP = "设置Aztec码自定义AIM ID"
[CodaBlockFReadMain]
LABEL = "CodaBlock F码读取"
HELP = "启用/禁用CodaBlock F码读取"
[CodaBlockFCodeID]
LABEL = "CodaBlock F码ID"
HELP = "设置CodaBlock F码的码ID"
[CodaBlockFMaxLen]
LABEL = "CodaBlock F码最大长度"
HELP = "设置CodaBlock F码的最大长度"
[CodaBlockFMinLen]
LABEL = "CodaBlock F码最小长度"
HELP = "设置CodaBlock F码的最小长度"
[CodaBlockFAimID]
LABEL = "CodaBlock F自定义AIM ID"
HELP = "设置CodaBlock F自定义AIM ID"
[GMCodeReadMain]
LABEL = "GM码读取"
HELP = "启用/禁用GM码读取"
[GMCodeCodeID]
LABEL = "GM码ID"
HELP = "设置GM码的码ID"
[GMCodeMaxLen]
LABEL = "GM码最大长度"
HELP = "设置GM码的最大长度"
[GMCodeMinLen]
LABEL = "GM码最小长度"
HELP = "设置GM码的最小长度"
[MaxiCodeReadMain]
LABEL = "Maxicode码读取"
HELP = "启用/禁用Maxicode码的读取功能"
[MaxiCodeMaxLen]
LABEL = "Maxicode码最大长度"
HELP = "设置Maxicode码的最大长度"
[MaxiCodeMinLen]
LABEL = "Maxicode码最小长度"
HELP = "设置Maxicode码的最小长度"
[MaxiCodeAimID]
LABEL = "MaxiCode自定义AIM ID"
HELP = "设置MaxiCode自定义AIM ID"
[MICPDF417ReadMain]
LABEL = "MicroPDF417码读取"
HELP = "启用/禁用MicroPDF417码的读取功能"
[MICPDF417CodeID]
LABEL = "MicroPDF417码标识符"
HELP = "设置MicroPDF417码的自定义标识符"
[MICPDF417InsertStr]
LABEL = "MicroPDF417码插入字符串"
HELP = "选择MicroPDF417码插入的字符串组"
[MICPDF417Conversion]
LABEL = "MicroPDF417码转换"
HELP = "设置MicroPDF417码的转换模式"
[MICPDF417Conversion.Options]
OFF = "不进行转换"
UCCEAN128 = "转换为UCC/EAN-128码"
UPCA_EAN13 = "转换为UPC-A/EAN-13码"
[MICPDF417MaxLen]
LABEL = "MicroPDF417码最大长度"
HELP = "设置MicroPDF417码的最大长度"
[MICPDF417MinLen]
LABEL = "MicroPDF417码最小长度"
HELP = "设置MicroPDF417码的最小长度"
[MICPDF417AimID]
LABEL = "MicroPDF417自定义AIM ID"
HELP = "设置MicroPDF417自定义AIM ID"
[MicroQRReadMain]
LABEL = "Micro QR码读取"
HELP = "启用/禁用Micro QR码读取"
[MicroQRCodeID]
LABEL = "Micro QR码ID"
HELP = "设置Micro QR码的码ID"
[MicroQRMaxLen]
LABEL = "Micro QR码最大长度"
HELP = "设置Micro QR码的最大长度"
[MicroQRMinLen]
LABEL = "Micro QR码最小长度"
HELP = "设置Micro QR码的最小长度"
[MicroQRAimID]
LABEL = "MicroQR码自定义AIM ID"
HELP = "设置MicroQR码自定义AIM ID"
[PDF417ReadMain]
LABEL = "PDF417码读取"
HELP = "启用/禁用PDF417码的读取功能"
[PDF417CodeID]
LABEL = "PDF417码标识符"
HELP = "设置PDF417码的自定义标识符"
[PDF417InsertStr]
LABEL = "PDF417码插入字符串组"
HELP = "选择PDF417码插入的字符串组"
[PDF417Conversion]
LABEL = "PDF417码转换"
HELP = "控制PDF417码的转换模式"
[PDF417Conversion.Options]
NONE = "不进行转换"
UCC_EAN128 = "转换为UCC/EAN-128码"
UPC_EAN13 = "转换为UPC/EAN-13码"
[PDF417MaxLen]
LABEL = "PDF417码最大长度"
HELP = "设置PDF417码的最大长度"
[PDF417MinLen]
LABEL = "PDF417码最小长度"
HELP = "设置PDF417码的最小长度"
[PDF417AimID]
LABEL = "PDF417自定义AIM ID"
HELP = "设置PDF417自定义AIM ID"
[BinaryCodeReadMain]
LABEL = "二进制码读取"
HELP = "启用/禁用二进制码的读取功能"
[BinaryCodeCodeID]
LABEL = "二进制码标识符"
HELP = "设置二进制码的自定义标识符"
[BinaryCodeMaxLen]
LABEL = "二进制码最大长度"
HELP = "设置二进制码的最大长度"
[BinaryCodeMinLen]
LABEL = "二进制码最小长度"
HELP = "设置二进制码的最小长度"
[BinaryCodeAimID]
LABEL = "Binary码自定义AIM ID"
HELP = "设置Binary码自定义AIM ID"
[CSCodeReadMain]
LABEL = "中国汉信码读取"
HELP = "启用/禁用汉信码的读取功能"
[CSCodeCodeID]
LABEL = "汉信码标识符"
HELP = "设置汉信码的自定义标识符"
[CSCodeMaxLen]
LABEL = "汉信码最大长度"
HELP = "设置汉信码的最大长度"
[CSCodeMinLen]
LABEL = "汉信码最小长度"
HELP = "设置汉信码的最小长度"
[CSCodeAimID]
LABEL = "汉信码自定义AIM ID"
HELP = "设置汉信码自定义AIM ID"
[DotCodeReadMain]
LABEL = "Dot Code读取"
HELP = "启用/禁用Dot Code的读取功能"
[DotCodeCodeID]
LABEL = "Dot Code标识符"
HELP = "设置Dot Code的自定义标识符"
[DotCodeMaxLen]
LABEL = "Dot Code最大长度"
HELP = "设置Dot Code的最大长度"
[DotCodeMinLen]
LABEL = "Dot Code最小长度"
HELP = "设置Dot Code的最小长度"
[DotCodeAimID]
LABEL = "Dot Code自定义AIM ID"
HELP = "设置Dot Code自定义AIM ID"
[CHNFNCReadMain]
LABEL = "中国金融码读取"
HELP = "启用/禁用中国金融码的读取功能"
[CHNFNCMaxLen]
LABEL = "中国金融码最大长度"
HELP = "设置中国金融码的最大长度"
[CHNFNCMinLen]
LABEL = "中国金融码最小长度"
HELP = "设置中国金融码的最小长度"
[CHNFNCChkDigVer]
LABEL = "中国金融码校验位验证"
HELP = "启用/禁用中国金融码的校验位验证功能"
[CHNFNCFrtCharConv]
LABEL = "中国金融码首字符转换"
HELP = "设置中国金融码的首字符转换模式"
[CHNFNCFrtCharConv.Options]
DISABLE = "禁用转换"
ENABLE = "启用所有转换"
FIVE_TO_A = "仅5转换为A"
SIX_TO_B = "仅6转换为B"
SEVEN_TO_C = "仅7转换为C"
EIGHT_TO_D = "仅8转换为D"
NINE_TO_E = "仅9转换为E"
[CHNFNCFrtCharSpec]
LABEL = "中国金融码首字符规范"
HELP = "设置中国金融码的首字符规范"
[CHNFNCFrtCharSpec.Options]
DISABLE = "禁用规范"
ZERO = "指定为0"
FIVE_OR_A = "指定为5或A"
SIX_OR_B = "指定为6或B"
SEVEN_OR_C = "指定为7或C"
EIGHT_OR_D = "指定为8或D"
NINE_OR_E = "指定为9或E"
ONE = "指定为1"
TWO = "指定为2"
THREE = "指定为3"
FOUR = "指定为4"
[CHNFNCCodeID]
LABEL = "中国金融码标识符"
HELP = "设置中国金融码的自定义标识符"
[CHNFNCInsertStr]
LABEL = "中国金融码插入字符串"
HELP = "选择中国金融码插入的字符串组"
[CHNFNCAimID]
LABEL = "中国财政码自定义AIM ID"
HELP = "设置中国财政码自定义AIM ID"
[Custom_Decode_ROI_1]
LABEL = "解码 ROI 1"
HELP = "设置第 1 个解码 ROI 区域"
[Custom_Decode_ROI_2]
LABEL = "解码 ROI 2"
HELP = "设置第 2 个解码 ROI 区域"
[Custom_Decode_ROI_3]
LABEL = "解码 ROI 3"
HELP = "设置第 3 个解码 ROI 区域"
[Custom_Decode_ROI_4]
LABEL = "解码 ROI 4"
HELP = "设置第 4 个解码 ROI 区域"
[Custom_Decode_ROI_5]
LABEL = "解码 ROI 5"
HELP = "设置第 5 个解码 ROI 区域"
[Custom_Decode_ROI_6]
LABEL = "解码 ROI 6"
HELP = "设置第 6 个解码 ROI 区域"
[Custom_Decode_ROI_7]
LABEL = "解码 ROI 7"
HELP = "设置第 7 个解码 ROI 区域"
[Custom_Decode_ROI_8]
LABEL = "解码 ROI 8"
HELP = "设置第 8 个解码 ROI 区域"
[Custom_Decode_ROI_9]
LABEL = "解码 ROI 9"
HELP = "设置第 9 个解码 ROI 区域"
[Custom_Decode_ROI_10]
LABEL = "解码 ROI 10"
HELP = "设置第 10 个解码 ROI 区域"
[Custom_Decode_ROI_11]
LABEL = "解码 ROI 11"
HELP = "设置第 11 个解码 ROI 区域"
[Custom_Decode_ROI_12]
LABEL = "解码 ROI 12"
HELP = "设置第 12 个解码 ROI 区域"
[NetLed]
LABEL = "网络状态灯模式"
HELP = "网络状态灯模式"
[NetLed.Options]
LINK = "连接时亮"
TX = "发送时闪烁"
RX = "接收时闪烁"
[InsertStr]
LABEL = "插入字符串"
HELP = "选择全局插入的字符串组"
[BackTx]
LABEL = "边解边传"
HELP = "解码数据边解边传模式"
[NoReadOutput]
LABEL = "No Read输出"
HELP = "使能/失能No Read输出"
[DataBatch]
LABEL = "数据批处理"
HELP = "控制无线数据批处理"
[DataBatch.Options]
NONE = "失能"
OUT_RANGE = "区外存储"
STANDARD = "标准存储"
STANDARD_MANUAL = "标准存储（数据需手动清除）"
[ScanMode]
LABEL = "扫描模式"
HELP = "扫描器工作模式选择"
[ScanMode.Options]
GoodReadOff = "单次按键触发模式"
Mome = "按键保持模式"
Alter = "开关持续模式"
Cont = "持续模式"
GoodReadOn = "单次按键保持模式"
SciTrigger = "命令控制触发模式"
AutoDete = "自动感应-单次按键保持模式"
AutoDeteIMG = "自动感应-单次按键触发模式"
AutoDeteIR_GoodReadOn = "红外感应-单次按键保持"
AutoDeteIR_GoodReadOff = "红外感应-单次按键触发"
AutoDeteIR_Mome = "红外感应-按键保持"
Desktop = "桌面模式"
ButtonCont = "按键持续模式"
MultiSymbolsCont = "多信号保持模式"
[ScanKeepTime]
LABEL = "扫描保持时长（秒）"
HELP = "扫描器保持时长（单位：秒）"
[ScanKeepTimeAdvanced]
LABEL = "扫描保持时长（毫秒）"
HELP = "扫描器保持时长（单位：毫秒）"
[MovementScanMode]
LABEL = "移动扫码模式"
HELP = "移动物体的扫码模式"
[MovementScanMode.Options]
Standard = "标准模式"
Enhance = "增强模式"
[PowerIndication]
LABEL = "上电指示"
HELP = "使能/失能上电指示"
[LedIndication]
LABEL = "LED指示"
HELP = "使能/失能解码成功后LED灯闪烁"
[BeepIndication]
LABEL = "蜂鸣器指示"
HELP = "使能/失能解码成功后蜂鸣器鸣叫"
[BeepTime]
LABEL = "解码指示灯提示时长（毫秒）"
HELP = "设置解码指示灯提示时长，单位10ms"
[BeepVolume]
LABEL = "蜂鸣器音量"
HELP = "设置蜂鸣器音量等级"
[BeepVolume.Options]
LOW = "低音量"
MID = "中音量"
HIGH = "高音量"
LOW2 = "更低音量（15%）"
LOW3 = "更低音量（10%）"
LOW4 = "更低音量（8%）"
LOW5 = "更低音量（6%）"
LOW6 = "更低音量（4%）"
LOW7 = "更低音量（2%）"
LOW8 = "更低音量（1%）"
[BeepTone]
LABEL = "蜂鸣器音调"
HELP = "设置蜂鸣器音调等级"
[BeepTone.Options]
LEVEL0 = "0级音调"
LEVEL1 = "1级音调"
LEVEL2 = "2级音调"
LEVEL3 = "3级音调"
[PowerLEDCtrl]
LABEL = "电源指示灯控制"
HELP = "使能/失能电源指示灯控制"
[CustomIO1ModeMain]
LABEL = "自定义IO 模式"
HELP = "选择自定义IO工作模式"
[CustomIO1ModeMain.Options]
Close = "关闭"
InputHigh = "输入，默认上拉"
InputLow = "输入，默认下拉"
OutputHigh = "输出，默认高电平"
OutputLow = "输出，默认低电平"
[CustomIO2ModeMain]
LABEL = "自定义IO 模式"
HELP = "选择自定义IO工作模式"
[CustomIO3ModeMain]
LABEL = "自定义IO 模式"
HELP = "选择自定义IO工作模式"
[CustomIO4ModeMain]
LABEL = "自定义IO 模式"
HELP = "选择自定义IO工作模式"
[CustomIO1InputCount]
LABEL = "自定义IO输入边沿计数"
HELP = "自定义IO输入边沿计数"
[CustomIO2InputCount]
LABEL = "自定义IO输入边沿计数"
HELP = "自定义IO输入边沿计数"
[CustomIO3InputCount]
LABEL = "自定义IO输入边沿计数"
HELP = "自定义IO输入边沿计数"
[CustomIO4InputCount]
LABEL = "自定义IO输入边沿计数"
HELP = "自定义IO输入边沿计数"
[CustomIO1Debounce]
LABEL = "自定义IO输入消抖时间（毫秒）"
HELP = "设置输入消抖时间(毫秒)"
[CustomIO2Debounce]
LABEL = "自定义IO输入消抖时间（毫秒）"
HELP = "设置输入消抖时间(毫秒)"
[CustomIO3Debounce]
LABEL = "自定义IO输入消抖时间（毫秒）"
HELP = "设置输入消抖时间(毫秒)"
[CustomIO4Debounce]
LABEL = "自定义IO输入消抖时间（毫秒）"
HELP = "设置输入消抖时间(毫秒)"
[CustomIO1RisingAction]
LABEL = "自定义IO 上升沿触发动作"
HELP = "配置上升沿触发动作"
[CustomIO1RisingAction.Options]
None = "无"
StartDecode = "开始解码"
EndDecode = "结束解码"
[CustomIO2RisingAction]
LABEL = "自定义IO 上升沿触发动作"
HELP = "配置上升沿触发动作"
[CustomIO3RisingAction]
LABEL = "自定义IO 上升沿触发动作"
HELP = "配置上升沿触发动作"
[CustomIO4RisingAction]
LABEL = "自定义IO 上升沿触发动作"
HELP = "配置上升沿触发动作"
[CustomIO1RisingDelay]
LABEL = "自定义IO上升沿延迟时间（毫秒）"
HELP = "设置上升沿延迟时间(毫秒)"
[CustomIO2RisingDelay]
LABEL = "自定义IO上升沿延迟时间（毫秒）"
HELP = "设置上升沿延迟时间(毫秒)"
[CustomIO3RisingDelay]
LABEL = "自定义IO上升沿延迟时间（毫秒）"
HELP = "设置上升沿延迟时间(毫秒)"
[CustomIO4RisingDelay]
LABEL = "自定义IO上升沿延迟时间（毫秒）"
HELP = "设置上升沿延迟时间(毫秒)"
[CustomIO1FallingAction]
LABEL = "自定义IO 下降沿触发动作"
HELP = "配置下降沿触发动作"
[CustomIO1FallingAction.Options]
None = "无"
StartDecode = "开始解码"
EndDecode = "结束解码"
[CustomIO2FallingAction]
LABEL = "自定义IO 下降沿触发动作"
HELP = "配置下降沿触发动作"
[CustomIO3FallingAction]
LABEL = "自定义IO 下降沿触发动作"
HELP = "配置下降沿触发动作"
[CustomIO4FallingAction]
LABEL = "自定义IO 下降沿触发动作"
HELP = "配置下降沿触发动作"
[CustomIO1FallingDelay]
LABEL = "自定义IO下降沿延迟时间（毫秒）"
HELP = "设置下降沿延迟时间(毫秒)"
[CustomIO2FallingDelay]
LABEL = "自定义IO下降沿延迟时间（毫秒）"
HELP = "设置下降沿延迟时间(毫秒)"
[CustomIO3FallingDelay]
LABEL = "自定义IO下降沿延迟时间（毫秒）"
HELP = "设置下降沿延迟时间(毫秒)"
[CustomIO4FallingDelay]
LABEL = "自定义IO下降沿延迟时间（毫秒）"
HELP = "设置下降沿延迟时间(毫秒)"
[CustomIO1ActiveCondition]
LABEL = "激活条件"
HELP = "配置激活条件"
[CustomIO1ActiveCondition.Options]
None = "无"
DecodeSuccess = "解码成功"
NoRead = "未读到条码"
DecodeStarted = "开始解码"
DecodeStopped = "结束解码"
Command = "命令"
Timer = "定时"
Input1High = "输入1拉高"
Input2High = "输入2拉高"
Input3High = "输入3拉高"
Input4High = "输入4拉高"
Input1Low = "输入1拉低"
Input2Low = "输入2拉低"
Input3Low = "输入3拉低"
Input4Low = "输入4拉低"
[CustomIO2ActiveCondition]
LABEL = "激活条件"
HELP = "配置激活条件"
[CustomIO3ActiveCondition]
LABEL = "激活条件"
HELP = "配置激活条件"
[CustomIO4ActiveCondition]
LABEL = "激活条件"
HELP = "配置激活条件"
[CustomIO1InactiveCondition]
LABEL = "失效条件"
HELP = "配置失效条件"
[CustomIO1InactiveCondition.Options]
None = "无"
DecodeSuccess = "解码成功"
NoRead = "未读到条码"
DecodeStarted = "开始解码"
DecodeStopped = "结束解码"
Command = "命令"
Timer = "定时"
Input1High = "输入1拉高"
Input2High = "输入2拉高"
Input3High = "输入3拉高"
Input4High = "输入4拉高"
Input1Low = "输入1拉低"
Input2Low = "输入2拉低"
Input3Low = "输入3拉低"
Input4Low = "输入4拉低"
[CustomIO2InactiveCondition]
LABEL = "失效条件"
HELP = "配置失效条件"
[CustomIO3InactiveCondition]
LABEL = "失效条件"
HELP = "配置失效条件"
[CustomIO4InactiveCondition]
LABEL = "失效条件"
HELP = "配置失效条件"
[CustomIO1ActiveDelay]
LABEL = "激活延迟（毫秒）"
HELP = "设置激活延迟时间(毫秒)"
[CustomIO2ActiveDelay]
LABEL = "激活延迟（毫秒）"
HELP = "设置激活延迟时间(毫秒)"
[CustomIO3ActiveDelay]
LABEL = "激活延迟（毫秒）"
HELP = "设置激活延迟时间(毫秒)"
[CustomIO4ActiveDelay]
LABEL = "激活延迟（毫秒）"
HELP = "设置激活延迟时间(毫秒)"
[CustomIO1InactiveTimer]
LABEL = "失效定时（毫秒）"
HELP = "设置自动失效定时(毫秒)，0表示不自动失效"
[CustomIO2InactiveTimer]
LABEL = "失效定时（毫秒）"
HELP = "设置自动失效定时(毫秒)，0表示不自动失效"
[CustomIO3InactiveTimer]
LABEL = "失效定时（毫秒）"
HELP = "设置自动失效定时(毫秒)，0表示不自动失效"
[CustomIO4InactiveTimer]
LABEL = "失效定时（毫秒）"
HELP = "设置自动失效定时(毫秒)，0表示不自动失效"
[CustomIO1ActiveCmd]
LABEL = "激活命令"
HELP = "设置激活自定义IO的命令"
[CustomIO2ActiveCmd]
LABEL = "激活命令"
HELP = "设置激活自定义IO的命令"
[CustomIO3ActiveCmd]
LABEL = "激活命令"
HELP = "设置激活自定义IO的命令"
[CustomIO4ActiveCmd]
LABEL = "激活命令"
HELP = "设置激活自定义IO的命令"
[CustomIO1InactiveCmd]
LABEL = "失效命令"
HELP = "设置失效自定义IO的命令"
[CustomIO2InactiveCmd]
LABEL = "失效命令"
HELP = "设置失效自定义IO的命令"
[CustomIO3InactiveCmd]
LABEL = "失效命令"
HELP = "设置失效自定义IO的命令"
[CustomIO4InactiveCmd]
LABEL = "失效命令"
HELP = "设置失效自定义IO的命令"
[CharEncode]
LABEL = "字符编码"
HELP = "字符编码系统"
[CharEncode.Options]
ASCII = "ASCII"
UTF8 = "UTF-8"
Windows1251 = "Windows-1251"
SimpChinese = "简体中文字符编码系统"
TraChinese = "繁体中文字符编码系统"
Windows1250 = "Windows-1250"
KOI8R = "KOI8-R"
Japanese = "日文字符编码系统"
[CharPrintCtr]
LABEL = "字符输出控制"
HELP = "控制输出哪些字符"
[CharPrintCtr.Options]
Disable = "不进行控制"
Printable = "仅输出可打印字符"
Alphanumeric = "仅输出字母与数字字符"
[DecodeResultMaxNum]
LABEL = "解码结果最大数量"
HELP = "设置一次扫描的最大解码结果数量（范围：1-30）"
[FirstOnly]
LABEL = "仅前N个字符"
HELP = "设置仅传送前N个字符"
[LastOnly]
LABEL = "仅后N个字符"
HELP = "设置仅传送后N个字符"
[SingReplaC1]
LABEL = "单字符C1替换"
HELP = "设置单字符C1替换"
[SingReplaC2]
LABEL = "单字符C2替换"
HELP = "设置单字符C2替换"
[SingReplaC3]
LABEL = "单字符C3替换"
HELP = "设置单字符C3替换"
[ReplaChar2Str]
LABEL = "字符替换为字符串"
HELP = "设置字符到字符串的替换"
[CaseConv]
LABEL = "大小写转换"
HELP = "设置大小写转换模式"
[CaseConv.Options]
Disable = "禁用"
UpperDatOnly = "仅数据大写"
LowerDatOnly = "仅数据小写"
UpperWholeStr = "整串大写"
LowWholeStr = "整串小写"
[FN1SubstuTextEnable]
LABEL = "启用FN1替换文本"
HELP = "启用/禁用FN1替换文本传送"
[CharactersToStrings]
LABEL = "文本形式显示特殊字符"
HELP = "启用/禁用控制字符转字符串"
[DecoRsltCheck]
LABEL = "解码结果检查"
HELP = "设置解码结果检查模式"
[DecoRsltCheck.Options]
Disable = "禁用"
EnableAll = "全部启用"
HeadCheckOnly = "仅头部检查"
TailCheckOnly = "仅尾部检查"
[DecoRsltCheck1HeadStr]
LABEL = "解码结果检查头字符串 1"
HELP = "设置解码结果检查头字符串 1"
[DecoRsltCheck1TailStr]
LABEL = "解码结果检查尾字符串 1"
HELP = "设置解码结果检查尾字符串 1"
[DecoResultCheckFailIndication]
LABEL = "解码结果检查失败提示"
HELP = "启用/禁用解码结果检查失败提示"
[Group1Text]
LABEL = "字符串1"
HELP = "设置组1文本"
[Group2Text]
LABEL = "字符串2"
HELP = "设置组2文本"
[Group3Text]
LABEL = "字符串3"
HELP = "设置组3文本"
[Group4Text]
LABEL = "字符串4"
HELP = "设置组4文本"
[Group1Pos]
LABEL = "字符串1位置"
HELP = "设置组1位置"
[Group2Pos]
LABEL = "字符串2位置"
HELP = "设置组2位置"
[Group3Pos]
LABEL = "字符串3位置"
HELP = "设置组3位置"
[Group4Pos]
LABEL = "字符串4位置"
HELP = "设置组4位置"
[USBEthernetCompositeEnMain]
LABEL = "USB 以太网复合使能"
HELP = "使能 USB 以太网复合使能"
[USBEthernetCompositeType]
LABEL = "USB 以太网复合类型"
HELP = "USB 以太网复合类型"
[USBEthernetCompositeType.Options]
Keyboard = "键盘"
VCOM = "虚拟串口"
[EthernetIpOutputEn]
LABEL = "EthernetIp 输出使能"
HELP = "使能 EthernetIp 输出"
[ModbusSvrOutputEn]
LABEL = "ModbusServer 输出使能"
HELP = "使能 ModbusServer 输出"
[UartEnable]
LABEL = "主串口输出使能"
HELP = "使能主串口输出"
[ProfinetOutputEnMain]
LABEL = "Profinet 输出使能"
HELP = "使能 Profinet 输出"
[ProfinetStationName]
LABEL = "Profinet 站点名称"
HELP = "Profinet 站点名称"
[TCPCliOutputEnMain]
LABEL = "TCP 客户端输出使能"
HELP = "使能 TCP 客户端输出"
[TCPCliTargetSvrIP]
LABEL = "TCP 客户端目标服务器IP地址"
HELP = "TCP 客户端目标服务器IP地址"
[TCPCliTargetSvrPort]
LABEL = "TCP 客户端目标服务器端口"
HELP = "TCP 客户端目标服务器端口"
[TCPCliOutputFmt]
LABEL = "TCP 客户端输出格式化"
HELP = "TCP 客户端输出格式化"
[TCPCliOutputPrefix]
LABEL = "TCP 客户端输出前缀"
HELP = "TCP 客户端输出前缀"
[TCPCliOutputSuffix]
LABEL = "TCP 客户端输出后缀"
HELP = "TCP 客户端输出后缀"
[TCPSvrOutputEnableMain]
LABEL = "TCP 服务器输出使能"
HELP = "使能 TCP 服务器输出"
[TCPSvrOutputFmt]
LABEL = "TCP 服务器输出格式化"
HELP = "TCP 服务器输出格式化"
[TCPSvrOutputPrefix]
LABEL = "TCP 服务器输出前缀"
HELP = "TCP 服务器输出前缀"
[TCPSvrOutputSuffix]
LABEL = "TCP 服务器输出后缀"
HELP = "TCP 服务器输出后缀"
[TCPSvrServicePort]
LABEL = "TCP 服务器服务端口"
HELP = "TCP 服务器服务端口"
[RS232FlowControl]
LABEL = "RS232流量控制"
HELP = "RS232流量控制模式"
[RS232FlowControl.Options]
None = "无流量控制"
RtsCtsLow = "RTS/CTS (低电平)"
RtsCtsHigh = "RTS/CTS (高电平)"
XonXoff = "XON/XOFF"
AckNak = "ACK/NAK"
RtsLow = "RTS低电平(仅ES命令体系)"
AckNakRtsCtsHigh = "ACK/NAK+RTS/CTS高电平(仅uE命令体系)"
CtsScan = "CTS扫描控制"
[RS232CharDelay]
LABEL = "RS232字符时延"
HELP = "RS232字符时延"
[RS232CharDelay.Options]
Forbid = "无时延"
Delay5ms = "5毫秒时延"
Delay10ms = "10毫秒时延"
Delay20ms = "20毫秒时延"
Delay40ms = "40毫秒时延"
Delay80ms = "80毫秒时延"
[RS232ResponseDelay]
LABEL = "RS232反馈时延（10毫秒）"
HELP = "RS232反馈时延，单位10ms"
[RS232BaudRate]
LABEL = "RS232波特率"
HELP = "RS232波特率"
[RS232BaudRate.Options]
BR9600 = "9600"
BR19200 = "19200"
BR38400 = "38400"
BR57600 = "57600"
BR115200 = "115200"
[RS232Parity]
LABEL = "RS232奇偶校验"
HELP = "RS232奇偶校验模式"
[RS232Parity.Options]
None = "无奇偶校验"
Odd = "奇校验"
Even = "偶校验"
[RS232DataBits]
LABEL = "RS232数据位"
HELP = "RS232数据位"
[RS232DataBits.Options]
Bits8 = "8位"
Bits7 = "7位"
[RS232StopBits]
LABEL = "RS232停止位"
HELP = "RS232停止位"
[RS232StopBits.Options]
Bits1 = "1位"
Bits2 = "2位"
[RS232HostType]
LABEL = "RS232主机类型"
HELP = "RS232主机通信协议类型"
[RS232HostType.Options]
Standard = "标准RS232"
OPOS = "OPOS/JPOS"
MDAux = "MD辅助端口"
[RS232DecoDataFormat]
LABEL = "RS232解码数据格式"
HELP = "RS232解码数据格式"
[RS232DecoDataFormat.Options]
Raw = "原始格式"
Packed = "打包格式"
[RS232HsCharTimeout]
LABEL = "RS232握手字符时延（10毫秒）"
HELP = "RS232握手字符时延（单位：10ms）"
[USBType]
LABEL = "USB设备类型"
HELP = "选择USB设备类型"
[USBType.Options]
PC = "USB HID键盘仿真"
MAC = "Apple Mac HID键盘仿真"
VISUAL_COM = "USB虚拟串口"
SIMPLE_COM = "Simple COM端口仿真"
OPOS = "OPOS/JPOS兼容HID设备"
CUST_VISUAL_COM = "自定义USB虚拟串口"
IBM_HAND_HELD = "IBM掌上电脑"
HID_MSD = "USB存储设备，同时支持HID键盘"
CUSTOM_BULK = "自定义批量传输"
CUSTOM_HID_KEYBOARD_HS_BULK = "自定义HID键盘，支持高速批量传输"
PC_LEGACY_KEYBOARD = "兼容旧版本协议的USB HID键盘"
VCOM_OPOS = "OPOS/JPOS兼容VCOM设备"
CUSTOM_HID_INT_HS_BULK = "自定义HID中断，支持高速批量传输"
CUSTOM_HID_KBD_HID_CUST = "自定义HID键盘，支持自定义HID"
CUSTOM_HID_KBD_VISUAL_COM = "自定义HID键盘，支持虚拟串口"
NET = "USB 网口设备"
CUSTOM_HID_KBD_NET = "自定义HID键盘，支持网口"
CUSTOM_VISUAL_COM_NET = "自定义虚拟串口，支持网口"
CUSTOM = "自定义端口，用于特殊应用"
[USBKeyboardLayout]
LABEL = "USB键盘布局"
HELP = "选择USB键盘布局"
[USBKeyboardLayout.Options]
CHN_USA = "美式键盘布局"
TKYF = "土耳其F式键盘布局"
TKYQ = "土耳其Q时键盘布局"
FRN = "法式键盘布局"
ITA = "意大利式键盘布局"
SPA = "西班牙式键盘布局"
SLK = "斯洛伐克式键盘布局"
DMK = "丹麦式键盘布局"
JAP = "日式键盘布局"
GER = "德式键盘布局"
BEL = "比利时式键盘布局"
RUS = "俄罗斯式键盘布局"
CZE = "捷克式键盘布局"
THAI = "泰式键盘布局"
HUNGARY = "匈牙利式键盘布局"
SWISS_GERMAN = "瑞士德语键盘布局"
PORTUGUESE = "葡萄牙文键盘布局"
UKRAINIAN = "乌克兰语键盘布局"
POLISH214 = "波兰语键盘布局"
VIETNAM = "越南语键盘布局"
RUSSIAN_ANDROID = "适配安卓设备的俄罗斯键盘布局"
VIETNAM_TELEX = "越南语Telex输入法"
[USBKeyDly]
LABEL = "USB键值发送时延"
HELP = "设置相邻键值发送时延"
[USBKeyDly.Options]
DELAY_0MS = "0毫秒"
DELAY_5MS = "5毫秒"
DELAY_10MS = "10毫秒"
DELAY_20MS = "20毫秒"
DELAY_40MS = "40毫秒"
DELAY_60MS = "60毫秒"
[USBNumbKey]
LABEL = "USB数字键"
HELP = "选择USB数字键输出模式"
[USBNumbKey.Options]
DISABLE = "使用字母键盘码"
NUM = "使用数字小键盘码"
ALT_KEYPAD = "使用Alt+数字键模拟"
ALT_KEYPAD_GBK = "使用Alt+数字键，根据GBK编码输出对应字符"
RAW_HEX_IN_STRINGS = "直接输出编码对应的16进制值"
UOS_UNICODE_IN_DEC = "模拟数字键的方式( 统信Uos系统专用 )"
[USBFuncKey]
LABEL = "USB功能键"
HELP = "选择USB功能键输出模式"
[USBFuncKey.Options]
NON_PRINTABLE = "按非打印字符方式输出"
MINDEO = "按MINDEO定义的功能键方式输出"
CUSTOMIZE_PPN = "按PPN码定制映射方式输出"
FORBID = "禁止输出"
DATALOGIC_CTRL_CHAR_00 = "按Datalogic Control Character 00映射方式输出"
[USBHostPollingInter]
LABEL = "USB主机轮询间隔"
HELP = "设置USB主机端口轮询间隔"
[USBHostPollingInter.Options]
INTERVAL_1MS = ""
INTERVAL_2MS = ""
INTERVAL_5MS = ""
INTERVAL_8MS = ""
[USBKeySendMode]
LABEL = "USB键值发送方式"
HELP = "选择USB键值发送模式"
[USBKeySendMode.Options]
DISCONTINUOUS = "不连续的键值发送方式"
CONTINUOUS = "连续的键值发送方式"
[USBEnterMode]
LABEL = "USB回车键模式"
HELP = "选择USB回车键映射模式"
[USBEnterMode.Options]
ACCORDING_FUNC_KEY = "遵循当前0905参数值的功能键映射"
FORCE_ALPHABET_ENTER = "强制使用字母键盘回车"
FORCE_NUMERIC_ENTER = "强制使用数字键盘回车"
FORCE_ALT_013_ENTER = "强制使用Alt+013回车"
[USBModKeysReleaseMode]
LABEL = "USB组合键释放模式"
HELP = "控制USB键盘模式下组合键的释放方式"
[USBModKeysReleaseMode.Options]
SAME_TIME = "组合键同时释放全部按键"
AFTER_NORMAL_KEYS = "组合键先释放普通按键，延迟释放修饰键"
[USBKeyTxTimeout]
LABEL = "USB键值传输超时（100毫秒）"
HELP = "设置USB键值传输超时时间（100毫秒）"
[UsbEnumFailReboot]
LABEL = "USB枚举失败重启"
HELP = "启用/禁用USB枚举失败时自动重启"
[Focal]
LABEL = "焦距控制值（毫伏）"
HELP = "调整焦距控制值"
[ConfigByCode]
LABEL = "扫码设置参数"
HELP = "使能/失能扫码设置参数"
[InterfaceTypeMain]
LABEL = "接口类型"
HELP = "选择通信接口类型"
[InterfaceTypeMain.Options]
Auto = "自动检测"
RS232 = "RS-232接口"
USB = "USB接口"
RS232_ETH = "RS232+以太网 接口"
RS232_USB = "RS232+USB 接口"
SOFTWARE = "SOFTWARE 接口"
[RS232ByteDelay]
LABEL = "RS232字节延时（10毫秒）"
HELP = "设置RS232接口字节延时"
[CodeMaxLen1D]
LABEL = "1D码字最大长度"
HELP = "1D码字最大长度"
[CodeMinLen1D]
LABEL = "1D码字最小长度"
HELP = "1D码字最小长度"
[CodeElementAmend]
LABEL = "码字宽度校正"
HELP = "使能/失能码字宽度校正"
[DecodeOptimize]
LABEL = "纠错优化解码"
HELP = "使能/失能纠错优化解码"
[OutputDelayContScan]
LABEL = "连续扫描数据输出时延"
HELP = "设置连续扫描数据输出时延"
[SameBarDelayDR]
LABEL = "双读重码时延（毫秒）"
HELP = "双读重码时延，单位50ms"
[SweepingScanEnhance]
LABEL = "掠过式扫码体验增强"
HELP = "不同掠过速度的扫码体验增强"
[SweepingScanEnhance.Options]
LowSpeed = "低掠过速度"
MediumSpeed = "中掠过速度"
HighSpeed = "高掠过速度"
[VibratorIndication]
LABEL = "振动器指示"
HELP = "使能/失能解码成功后振动"
[SilentMode]
LABEL = "静音模式"
HELP = "使能/失能静音模式"
[BeepSoundMode]
LABEL = "蜂鸣器音效模式"
HELP = "选择解码成功后的蜂鸣器音效模式"
[BeepSoundMode.Options]
MODE_0 = "默认蜂鸣器音效模式"
MODE_1 = "2级音调，中音量"
MODE_2 = "0级音调，高音量"
MODE_3 = "0级音调，中音量"
MODE_4 = "1D默认，2D高音量"
[IndicationTxDelay]
LABEL = "传输提示延时（10毫秒）"
HELP = "设置解码成功提示与传输解码结果时延"
[VoiceDly]
LABEL = "语音播报延时（100毫秒）"
HELP = "设置语音播报完成后重置计数延时"
[InfraredSensor]
LABEL = "红外传感器"
HELP = "使能/失能红外传感器功能"
[InfraredMode]
LABEL = "红外模式"
HELP = "选择红外传感器操作模式"
[InfraredMode.Options]
IN_STAND = "在架"
CONTINUE = "不在架"
[AutoDeteKeepTime]
LABEL = "自动感应保持时长（秒）"
HELP = "设置自动感应保持时长（单位：1s）"
[AutoDeteTexture]
LABEL = "自动感应纹理检测"
HELP = "使能/失能自动感应纹理检测功能"
[AutoDeteLum]
LABEL = "自动感应照明"
HELP = "控制自动感应模式下的照明"
[AutoDeteLum.Options]
ALWAYS_OFF = "常灭"
ON_IN_DARKNESS = "弱光下开启照明"
ALWAYS_ON = "常亮"
[AutoDeteTxtureInter]
LABEL = "自动感应纹理检测启动时间间隔"
HELP = "设置自动感应纹理检测启动时间间隔"
[AutoDeteTxtureInter.Options]
INTERVAL_0S = "0秒"
INTERVAL_5S = "5秒"
INTERVAL_10S = "10秒"
INTERVAL_30S = "30秒"
INTERVAL_60S = "60秒"
INTERVAL_INFINITY = "无穷大"
[AutoDeteSleepFrmInter]
LABEL = "休眠自动感应图像拍摄时间间隔"
HELP = "设置休眠自动感应图像拍摄时间间隔"
[AutoDeteSleepFrmInter.Options]
INTERVAL_250MS = "250毫秒"
INTERVAL_500MS = "500毫秒"
INTERVAL_1000MS = "1000毫秒"
[InfraredMsgSwitch]
LABEL = "红外报文上传开关"
HELP = "使能/失能红外自动感应模式报文上传功能"
[InfraredLum]
LABEL = "红外感应过程中的照明"
HELP = "使能/失能红外感应过程中的照明"
[IRLumDeteSenLevel]
LABEL = "红外感应灵敏度等级"
HELP = "设置红外感应灵敏度等级"
[DPMRead]
LABEL = "直接零部件标识条码识读功能"
HELP = "使能/失能识读直接零部件标识条码"
[CompositeRead]
LABEL = "复合码读取"
HELP = "设置复合码读取选项"
[CompositeRead.Options]
DISABLE = "禁用读取"
DATABAR_GS1_128 = "读取DataBar复合码和GS1-128复合码"
ALL = "读取所有复合码"
[OCRRecognizeMain]
LABEL = "OCR识别"
HELP = "设置OCR识别选项"
[OCRRecognizeMain.Options]
AS_PARA = "按参数设置执行"
DISABLE_ALL = "禁用所有OCR功能"
ENABLE_ALL = "启用所有OCR功能"
[OCRRecognizePassportMRZ]
LABEL = "OCR护照机读码识别"
HELP = "启用/禁用护照机读码区的识别功能"
[OCRRecognizeNationalCardID]
LABEL = "OCR身份证识别"
HELP = "启用/禁用身份证的识别功能"
[OCRTurkishIDOutputFormat]
LABEL = "OCR土耳其身份证输出格式"
HELP = "设置土耳其身份证的输出格式"
[OCRTurkishIDOutputFormat.Options]
ORIGINAL = "原始格式"
CUSTOMIZED = "自定义格式"
[OCRDoubleCheck]
LABEL = "OCR双重检查"
HELP = "启用/禁用OCR识别的双重检查功能"
[IVDDeteTubeCapMain]
LABEL = "IVD试管盖检测"
HELP = "启用/禁用IVD试管盖检测"
[IVDDeteTubeHeight]
LABEL = "IVD试管高度检测"
HELP = "启用/禁用IVD试管高度检测"
[AimKeepTimeAfterDeco]
LABEL = "解码后瞄准保持时间（秒）"
HELP = "设置解码后瞄准灯保持时间（秒）"
[LightDriveCapacity]
LABEL = "光源驱动容量（毫安）"
HELP = "设置照明的最大驱动电流容量（毫安）"
[LightMaxPerLED]
LABEL = "每个LED最大电流（毫安）"
HELP = "设置所有LED开启时每个LED的最大电流（10毫安）"
[LightGroup1Current]
LABEL = "光源组1电流（毫安）"
HELP = "设置光源组1的电流以达到标准最大图像亮度（10毫安）"
[LightGroup2Current]
LABEL = "光源组2电流（毫安）"
HELP = "设置光源组2的电流以达到标准最大图像亮度（10毫安）"
[AimKeepTimeBeforeDeco]
LABEL = "解码前瞄准保持时间（秒）"
HELP = "设置解码前瞄准灯保持时间（秒）"
[AuxiliaryLum]
LABEL = "辅助照明"
HELP = "启用/禁用辅助照明控制"
[AuxiliaryLumCompTime]
LABEL = "辅助照明补偿时间（微秒）"
HELP = "设置辅助照明的光稳定补偿时间（10微秒）"
[IVDTubeHightInfo]
LABEL = "IVD试管高度信息"
HELP = "IVD试管高度相关信息的保留参数"
[CodeRsltTxIndiDlyMode]
LABEL = "码结果传输指示延迟模式"
HELP = "设置码结果传输和指示的延迟模式"
[CodeRsltTxIndiDlyMode.Options]
NONE = "无延迟"
ONLY_TX = "仅传输延迟"
TX_AND_INDI = "传输和指示都延迟"
[DecoStay]
LABEL = "解码保持"
HELP = "启用/禁用命令触发解码的解码保持选项"
[PacketOutputDataCtr]
LABEL = "数据包输出控制"
HELP = "启用/禁用数据包输出控制"
[AddWatermark]
LABEL = "添加水印"
HELP = "设置图像的水印选项"
[AddWatermark.Options]
DISABLE = "禁用水印"
WITHOUT_BACKGROUND = "添加无背景水印"
WITH_BACKGROUND = "添加带背景水印"
[InstandIndicate]
LABEL = "支架指示"
HELP = "启用/禁用在支架上时的蜂鸣器指示"
[QRTerminatorPos]
LABEL = "QR码终止符位置"
HELP = "设置QR码解码终止符位置"
[QRTerminatorPos.Options]
FIRST = "在第一个终止符停止"
SECOND = "在第二个终止符停止"
[CodeErrOutput2D]
LABEL = "二维码纠错输出"
HELP = "启用/禁用二维码纠错码的输出功能"
[CodeErrLevel2D]
LABEL = "二维码纠错级别输出"
HELP = "启用/禁用二维码纠错级别的输出功能"
[CodeVersion2D]
LABEL = "二维码版本输出"
HELP = "启用/禁用二维码版本信息的输出功能"
[ContModeLumLevel]
LABEL = "连续模式亮度级别"
HELP = "设置连续扫描模式自动检测的亮度级别"
[ContModeLumLevel.Options]
LOW = "低级别"
MID = "中级别"
HIGH = "高级别"
[ContModeNoActionDura]
LABEL = "连续模式无动作持续时间（秒）"
HELP = "设置连续扫描模式的无动作持续时间（范围：1-90秒）"
[TimeToReboot]
LABEL = "重启时间"
HELP = "设置有线产品的定时重启选项"
[TimeToReboot.Options]
FORBID = "禁止重启"
SECS_30 = "30秒"
MINS_5 = "5分钟"
MINS_15 = "15分钟"
MINS_30 = "30分钟"
HOUR_1 = "1小时"
HOURS_2 = "2小时"
HOURS_3 = "3小时"
HOURS_4 = "4小时"
HOURS_8 = "8小时"
HOURS_12 = "12小时"
HOURS_24 = "24小时"
HOURS_48 = "48小时"
[ImageFlipMode]
LABEL = "图像翻转模式"
HELP = "设置相对于原始位置的图像翻转模式"
[ImageFlipMode.Options]
NONE = "不翻转"
HORIZONTAL = "水平翻转"
VERTICAL = "垂直翻转"
DIAGONAL = "对角翻转"
[AttachCodePosOption]
LABEL = "附加码位置选项"
HELP = "启用/禁用在条码数据中附加码位置信息"
[LastSuccImaging]
LABEL = "上次成功成像"
HELP = "启用/禁用使用上次成功成像参数进行新成像"
[HiLum4HDCode]
LABEL = "高清码高亮度"
HELP = "启用/禁用高清码扫描的高亮度模式"
[SysRecoverIndication]
LABEL = "系统恢复指示"
HELP = "启用/禁用系统恢复指示"
[OutputMultiple]
LABEL = "多重输出"
HELP = "启用/禁用多重输出模式"
[ConfigByCodeFb]
LABEL = "设置码结果反馈"
HELP = "启用/禁用设置码结果反馈"
[QueryConfigByCode]
LABEL = "扫码查询配置"
HELP = "启用/禁用扫码查询配置功能"
[ResultCheck]
LABEL = "结果检查"
HELP = "启用/禁用扫描结果验证"
[RainbowRead]
LABEL = "彩虹读取"
HELP = "启用/禁用彩色条码的彩虹读取模式"
[PowerUpReport]
LABEL = "开机报告"
HELP = "启用/禁用开机状态报告"
[ExpRebootIndication]
LABEL = "曝光重启指示"
HELP = "启用/禁用曝光重启指示"
[DecodeVertex]
LABEL = "解码顶点"
HELP = "启用/禁用解码顶点输出"
[AgingTest]
LABEL = "老化测试"
HELP = "启用/禁用老化测试模式"
[AgingTest.Options]
OFF = "关闭"
MODE_1 = "模式1"
MODE_2 = "模式2"
MODE_3 = "模式3"
MODE_4 = "模式4"
[PrefixText]
LABEL = "前缀文本"
HELP = "设置前缀文本"
[PrefixTextTransmission]
LABEL = "前缀文本传送"
HELP = "使能/失能前缀文本传送"
[SuffixText]
LABEL = "后缀文本"
HELP = "设置后缀文本"
[PreambText]
LABEL = "前置文本"
HELP = "设置前置文本"
[PostambText]
LABEL = "后置文本"
HELP = "设置后置文本"
[FN1SubstuText]
LABEL = "FN1替换文本"
HELP = "设置FN1替换文本"
[TruncLeadG5]
LABEL = "截去前导G5"
HELP = "设置截去前导G5字符"
[RepeatG5]
LABEL = "G5重复次数"
HELP = "设置G5重复次数"
[TruncEndG6]
LABEL = "截去末尾G6"
HELP = "设置截去末尾G6字符"
[RepeatG6]
LABEL = "G6重复次数"
HELP = "设置G6重复次数"
[CodeIDPos]
LABEL = "码制识别符位置"
HELP = "设置码制识别符位置"
[CodeIDPos.Options]
Before = "前"
After = "后"
[SuffixTextEnable]
LABEL = "启用后缀文本"
HELP = "启用/禁用后缀文本传送"
[CodeNameEnable]
LABEL = "启用码制名称"
HELP = "启用/禁用码制名称传送"
[PreambTextEnable]
LABEL = "启用前置文本"
HELP = "启用/禁用前置文本传送"
[PostambTextEnable]
LABEL = "启用后置文本"
HELP = "启用/禁用后置文本传送"
[CodeIDTextEnable]
LABEL = "启用码制识别符"
HELP = "设置码制识别符传送模式"
[CodeIDTextEnable.Options]
Disable = "禁用"
PropID = "属性ID"
AIMID = "AIM ID"
Both = "两者"
[CodeLenEnable]
LABEL = "启用码长度"
HELP = "启用/禁用码长度传送"
[NonePrintStrEnable]
LABEL = "启用非打印字符串"
HELP = "启用/禁用非打印字符串传送"
[BanSpecialKeys]
LABEL = "禁用特殊键"
HELP = "启用/禁用特殊键(TAB、Delete、Backspace)"
[TextEdit]
LABEL = "文本编辑"
HELP = "设置文本编辑模式"
[TextEdit.Options]
Disable = "禁用"
Invoice = "税票"
Evotrue = "天演维真"
Prowill = "代理商"
Inspur = "浪潮"
Elgin = "巴西"
Pulsa = "德国"
GeLunBu = "哥伦布"
ShengXiaoBang = "盛销邦"
XinGuoDu_GSGL = "新国都高速公路"
InvoiceCust = "税票自定义"
[InvoiceCustStr1]
LABEL = "税票自定义字符串1"
HELP = "设置税票自定义字符串1"
[InvoiceCustStr2]
LABEL = "税票自定义字符串2"
HELP = "设置税票自定义字符串2"
[InvoiceCustStr3]
LABEL = "税票自定义字符串3"
HELP = "设置税票自定义字符串3"
[InvoiceCustStr4]
LABEL = "税票自定义字符串4"
HELP = "设置税票自定义字符串4"
[BackupDecoImg]
LABEL = "备份解码图像"
HELP = "选择备份解码图像选项"
[BackupDecoImg.Options]
OFF = "关闭备份解码图像"
Both = "备份解码器级与系统级解码成功图像"
Succ = "仅备份解码器级解码成功图像"
[OnTimeTriggerDecode]
LABEL = "定时触发解码时长"
HELP = "定时触发时的解码时长（单位100ms）"
[UCIPGroupResetPUB]
LABEL = "重置用户自定义成像参数"
HELP = "重置用户自定义成像参数为默认值"
[VisionApp]
LABEL = "视觉应用"
HELP = "选择视觉应用"
[VisionApp.Options]
OFF = "关闭视觉应用"
OCR = "OCR"
Class = "分类"
DeteFeature = "特征检测"
