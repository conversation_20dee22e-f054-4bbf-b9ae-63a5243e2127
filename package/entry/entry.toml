# @sstar
[vscan-server]
type = "src"
srcs = [
    "lang/*.la",
    "src/*.c|*.ob",
    "src/cbeans.cst.h",
    "src/cbeans.otb.h",
    "src/mdscanner.lex.h",
    "src/mdscanner.svr.h",
    "src/cpropr.ctx.h",
    "src/cpropr.cfg.h",
    "../vscan/src/mdscanner.cli.h",
    "../vscan/src/autosdk.api.h",
    "../vscan/api/mdscanner.py:cli",
]
incp = ["src"]
deps = ["cbeans", "network", "gvcp", "stream", "clexer", "autosdk-svr", "cpropr"]
autosdk_api_suffix = "_"
