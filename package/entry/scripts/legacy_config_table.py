#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import re
import os
import sys
from typing import Dict, List, Tuple, Optional

def parse_legacy_config_file(file_path: str) -> List[Tuple[str, str]]:
    """
    Parse legacy_config.c file to extract property mappings
    Returns list of (prop_name, legacy_code) tuples
    """
    mappings = []
    
    # Try different encodings
    encodings = ['utf-8', 'gbk', 'gb2312', 'latin1']
    content = None
    
    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
            print(f"Successfully read file with {encoding} encoding")
            break
        except UnicodeDecodeError:
            continue
    
    if content is None:
        print("Error: Could not read file with any encoding")
        return []
    
    # Find all function definitions with @PROP comments
    # Pattern matches: /* @PROP: PropertyName ... */ void function_name(...) // "legacy_code"
    pattern = r'/\*\s*@PROP:\s*([^*\s]+).*?\*/.*?void\s+\w+.*?//\s*"([^"]+)"'
    
    matches = re.findall(pattern, content, re.DOTALL)
    
    for prop_name, legacy_code in matches:
        # Clean up property name (remove any trailing whitespace/newlines)
        prop_name = prop_name.strip()
        legacy_code = legacy_code.strip()
        
        # Skip complex legacy codes like "8501-8512" for now
        if '-' not in legacy_code:
            mappings.append((prop_name, legacy_code))
    
    return mappings

def convert_prop_name_to_constant(prop_name: str) -> str:
    """
    Convert property name to PROP_ constant format
    e.g., "CodaBlockFAimID" -> "PROP_CODABLOCKFAIMID"
    """
    # Convert camelCase to UPPER_CASE without extra underscores
    result = re.sub(r'([a-z0-9])([A-Z])', r'\1\2', prop_name)
    return f"PROP_{result.upper()}"

def legacy_code_to_macro(legacy_code: str) -> str:
    """
    Convert legacy code to MACR_PARACODE_ macro format
    e.g., "402" -> "MACR_PARACODE_0402"
    """
    try:
        if legacy_code.startswith('0x') or legacy_code.startswith('0X'):
            code_value = int(legacy_code, 16)
        else:
            code_value = int(legacy_code)
        return f"MACR_PARACODE_{code_value:04d}"
    except ValueError:
        return f"0x{legacy_code}"  # fallback for invalid codes

def generate_header_file(mappings: List[Tuple[str, str]], output_path: str):
    """
    Generate the header file with mapping function
    """
    header_content = """// Auto generated, don't edit

#ifndef _LEGACY_CONFIG_TABLE_H
#define _LEGACY_CONFIG_TABLE_H

#include "autosdk_notify.h"
#include "cpropr.cfg.h"
#include "para_code_macro.h"

#define _LEGACY_CONFIG_NOTIFY_ID_ 0xffff
#define _ALL_CONFIG_NOTIFY_ID_    0xfffe

static inline int legacy_code_to_property_id(int legacy_code)
{
    switch (legacy_code) {
"""

    # Add switch cases for each mapping
    for prop_name, legacy_code in sorted(mappings, key=lambda x: x[1]):
        prop_constant = convert_prop_name_to_constant(prop_name)
        legacy_macro = legacy_code_to_macro(legacy_code)
        
        header_content += f"        case {legacy_macro}: return {prop_constant};\n"
    
    header_content += f"""        default: return _LEGACY_CONFIG_NOTIFY_ID_;
    }}
}}

static inline int legacy_code_to_notify_id(int legacy_code)
{{
    return MD_NOTIFY_PROP(legacy_code_to_property_id(legacy_code));
}}

#endif //_LEGACY_CONFIG_TABLE_H
"""

    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(header_content)

def generate_legacy_config_table():
    # Input and output paths
    input_file = 'package/entry/src/legacy_config.c'
    output_file = 'package/entry/src/legacy_config_table.h'
    
    if not os.path.exists(input_file):
        print(f"Error: Input file not found: {input_file}")
        sys.exit(1)
    
    print(f"Parsing {input_file}...")
    mappings = parse_legacy_config_file(input_file)
    
    print(f"Found {len(mappings)} property mappings:")
    for prop_name, legacy_code in mappings[:5]:  # Show first 5 as examples
        print(f"  {prop_name} -> {legacy_code}")
    if len(mappings) > 5:
        print(f"  ... and {len(mappings) - 5} more")
    
    print(f"Generating {output_file}...")
    generate_header_file(mappings, output_file)
    
    print("Done!")

if __name__ == '__main__':
    generate_legacy_config_table() 