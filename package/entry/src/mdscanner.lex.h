/***********************************************************************************************************************************
|    | 0dh | 16h |  %  |  -  |  .  | 0-9 |  A  |  B  |  C  |  D  |  E  |  F  |  G  |  H  |  K  |  L  |  M  |  O  |  S  |  T  |  U  |
| 00 |     | T01 |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |
| 01 |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     | T02 |     |     |     |     |
| 02 | T03 |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |
| 03 |     |     | T04 |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |
| 04 |     |     |     |     |     |     |     |     |     | T06 |     | T07 |     | T05 |     |     |     |     |     |     |     |
| 05 |     |     |     |     |     |     |     |     |     |     | T08 |     |     |     |     |     |     |     |     |     |     |
| 06 |     |     |     |     |     |     |     |     |     |     | T09 |     |     |     |     |     |     |     |     |     |     |
| 07 |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     | T10 |     |     |     |
| 08 |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     | T11 |     |     |     |     |     |
| 09 |     |     |     |     |     |     |     | T12 |     |     |     |     |     |     |     |     |     |     |     |     |     |
| 10 |     |     |     |     |     |     |     |     | T13 |     |     |     |     |     |     |     |     |     |     |     |     |
| 11 |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     | T14 |     |     |     |     |     |
| 12 |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     | T15 |
| 13 |     |     |     |     |     | T16 |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |
| 14 |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     | T17 |     |     |     |
| 15 |     |     |     |     |     |     |     |     |     |     |     |     | T18 |     |     |     |     |     |     |     |     |
| 16 |     |     |     |     | T19 | T16 |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |
| 17 |     |     |     |     | T20 |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |
| 18 |     |     |     | T21 |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |
| 19 | ------------------------------------------------------------A02------------------------------------------------------------ |
| 20 | ------------------------------------------------------------A01------------------------------------------------------------ |
| 21 |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     | T22 |     |
| 22 |     |     |     |     |     |     | T23 |     |     |     |     |     |     |     |     |     |     |     |     |     |     |
| 23 |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     | T24 |     |     |
| 24 |     |     |     |     |     |     |     |     |     |     |     |     |     |     | T25 |     |     |     |     |     |     |
| 25 |     |     |     |     | T26 |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |     |
| 26 | ------------------------------------------------------------A00------------------------------------------------------------ |
***********************************************************************************************************************************/
void debug_task_lex(); // 00 \x16M\r%DEBUG-TASK.
void hello_lex(); // 01 \x16M\r%HELLO.
void focuser_lex(); // 02 \x16M\r%FOC\\d+.
static const void *_lex_hooks_[] = {
	debug_task_lex, // 00 \x16M\r%DEBUG-TASK.
	hello_lex, // 01 \x16M\r%HELLO.
	focuser_lex, // 02 \x16M\r%FOC\\d+.
};
static const unsigned int _lex_move_0_[] = {
	0x00064444, // Pick D To 6
	0x00074646, // Pick F To 7
	0x00054848, // Pick H To 5
};
static const unsigned int _lex_move_1_[] = {
	0x00103930, // Pick 0-9 To 16
};
static const unsigned int _lex_move_2_[] = {
	0x00132e2e, // Pick . To 19
	0x00103930, // Pick 0-9 To 16
};
static const unsigned int *_lex_moves_[] = {
	_lex_move_0_,
	_lex_move_1_,
	_lex_move_2_,
};
static const unsigned int _lex_states_[] = {
	0x80000116, // 00 ONE 16h -> 1
	0x8000024d, // 01 ONE M -> 2
	0x8000030d, // 02 ONE 0dh -> 3
	0x80000425, // 03 ONE % -> 4
	0x00000003, // 04 MOV 3 @ 0
	0x80000845, // 05 ONE E -> 8
	0x80000945, // 06 ONE E -> 9
	0x80000a4f, // 07 ONE O -> 10
	0x80000b4c, // 08 ONE L -> 11
	0x80000c42, // 09 ONE B -> 12
	0x80000d43, // 10 ONE C -> 13
	0x80000e4c, // 11 ONE L -> 14
	0x80000f55, // 12 ONE U -> 15
	0x00010001, // 13 MOV 1 @ 1
	0x8000114f, // 14 ONE O -> 17
	0x80001247, // 15 ONE G -> 18
	0x00020002, // 16 MOV 2 @ 2
	0x8000142e, // 17 ONE . -> 20
	0x8000152d, // 18 ONE - -> 21
	0x40000002, // 19 ACC 2
	0x40000001, // 20 ACC 1
	0x80001654, // 21 ONE T -> 22
	0x80001741, // 22 ONE A -> 23
	0x80001853, // 23 ONE S -> 24
	0x8000194b, // 24 ONE K -> 25
	0x80001a2e, // 25 ONE . -> 26
	0x40000000, // 26 ACC 0
};
// Total 33 u32 + 6 ptr used.
