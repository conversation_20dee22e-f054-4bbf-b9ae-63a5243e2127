//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: property.c
// Author		: XuCF
// Created On	: 2024/12/31
// Description	: property.c
//
// History
// 1. V1.0, Created by XuCF. 2024/12/31
//=============================================================================

#include "cpropr.h"
#include "autosdk_server.h"
#include "libcustom.h"

#define _PROPERTY_FILE_PATH_ "property.bin"

static void _save_property_(void)
{
	// for legacy config
	void flush_legacy_cfg(s32 temp);
	flush_legacy_cfg(0);
	// save
	cpropr_save_binary(_PROPERTY_FILE_PATH_);
}

any MdProperty_New(void)
{
	cpropr_init(_PROPERTY_FILE_PATH_);
	return 0;
}

API int MdScanner_GetPropFloat(/* get a float property */
	S32(I) id,				   /* property id */
	F32(O) prop				   /* output property */
)
{
	*prop = cpropr_get_float(id);
	LOGD("--%f--", *prop);
	return 0;
}

API int MdScanner_SetPropFloat(/* set a float property */
	S32(I) id,				   /* property id */
	F32(I) prop				   /* input property */
)
{
	cpropr_set_float(id, prop);
	_save_property_();
	return 0;
}

API int MdScanner_GetPropInt(/* get an integer property */
	S32(I) id,				 /* property id */
	S32(O) prop				 /* output property */
)
{
	*prop = cpropr_get_int(id);
	LOGD("--%d--", *prop);
	return 0;
}

API int MdScanner_SetPropInt(/* set an integer property */
	S32(I) id,				 /* property id */
	S32(I) prop				 /* input property */
)
{
	cpropr_set_int(id, prop);
	_save_property_();
	return 0;
}

API int MdScanner_GetPropStr(/* get a string property */
	S32(I) id,				 /* property id */
	BLK(O) prop				 /* output property */
)
{
	cpropr_bytes_t *bytes = cpropr_get_string(id);
	if (!bytes) return -1;
	prop->len = bytes->length;
	prop->ptr = (any)bytes->data;
	return 0;
}

API int MdScanner_SetPropStr(/* set a string property */
	S32(I) id,				 /* property id */
	ARR(I) prop				 /* input property */
)
{
	((chr *)(prop->ptr))[prop->len] = 0;
	cpropr_set_string(id, prop->ptr, prop->len);
	_save_property_();
	return 0;
}

API int MdScanner_GetPropBool(/* get a boolean property */
	S32(I) id,				  /* property id */
	S32(O) prop				  /* output property */
)
{
	*prop = cpropr_get_bool(id);
	LOGD("--%d--", *prop);
	return 0;
}

API int MdScanner_SetPropBool(/* set a boolean property */
	S32(I) id,				  /* property id */
	S32(I) prop				  /* input property */
)
{
	cpropr_set_bool(id, prop);
	_save_property_();
	return 0;
}

API int MdScanner_GetPropEnum(/* get an enum property */
	S32(I) id,				  /* property id */
	S32(O) prop				  /* output property */
)
{
	*prop = cpropr_get_enum(id);
	LOGD("--%d--", *prop);
	return 0;
}

API int MdScanner_SetPropEnum(/* set an enum property */
	S32(I) id,				  /* property id */
	S32(I) prop				  /* input property */
)
{
	cpropr_set_enum(id, prop);
	_save_property_();
	return 0;
}

API int MdScanner_GetPropComb(/* get a combination property */
	S32(I) id,				  /* property id */
	S32(O) prop				  /* output property */
)
{
	*prop = cpropr_get_comb(id);
	LOGD("--%d--", *prop);
	return 0;
}

API int MdScanner_SetPropComb(/* set a combination property */
	S32(I) id,				  /* property id */
	S32(I) prop				  /* input property */
)
{
	cpropr_set_comb(id, prop);
	_save_property_();
	return 0;
}

API int MdScanner_GetPropertiesJson(/* get all properties in json str*/
	ARR(O) prop						/* output properties json str */
)
{
	prop->len = cpropr_export_str(prop->ptr, prop->len);
	return 0;
}

API int MdScanner_SetPropertiesJson(/* set all property by json str*/
	ARR(I) prop						/* input properties json str */
)
{
	((chr *)(prop->ptr))[prop->len - 1] = 0;
	cpropr_import_str(prop->ptr);
	_save_property_();
	return 0;
}

API int MdScanner_GetPropertiesBatch(/* get properties in batch */
	ARR(I) ids,						 /* input property ids */
	ARR(O) props					 /* output properties */
)
{
	// Validate input parameters
	clike_assert(!ids || !props, return -1);
	clike_assert(!ids->ptr || !props->ptr, return -1);
	clike_assert(ids->len <= 0 || props->len <= 0, return -1);

	int count = ids->len / sizeof(short); // 16bits id
	clike_assert(count <= 0, return -1);

	// Cache id_list since ids and props may share memory
	short stack_id_cache[128];
	short *id_list = NULL;
	short *dynamic_id_cache = NULL;
	
	if (count <= 128)
	{
		// Use stack cache for small batches
		id_list = stack_id_cache;
	}
	else
	{
		// Use dynamic memory for large batches
		dynamic_id_cache = clike_malloc(count * sizeof(short));
		clike_assert(!dynamic_id_cache, return -1);
		id_list = dynamic_id_cache;
	}
	
	// Copy id list to cache
	clike_copy(id_list, ids->ptr, count * sizeof(short));

	int prop_id;
	int prop_size;
	cpropr_type_t prop_type;
	void *output_ptr = props->ptr, *output_end = props->ptr + props->len;
	int limit;
	int ret = 0;

	for (int i = 0; i < count; i++)
	{
		limit = output_end - output_ptr;
		clike_assert(limit < 8, ret = -1; goto cleanup); // 4 bytes for id and type, 4 bytes for value at least

		prop_id = id_list[i];
		prop_size = cpropr_get(prop_id, &prop_type, output_ptr + 4, limit - 4);
		clike_assert(prop_size < 0, ret = -1; goto cleanup);

		*(short *)output_ptr = prop_id;
		*(short *)(output_ptr + 2) = prop_type;
		output_ptr += 4 + prop_size;
	}

	props->len = output_ptr - props->ptr;

cleanup:
	// Free dynamic memory if allocated
	if (dynamic_id_cache)
	{
		clike_free(dynamic_id_cache);
	}
	
	return ret;
}
