//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: version.c
// Author		: XuCF
// Created On	: 2024/11/26
// Description	: version.c
//
// History
// 1. V1.0, Created by XuCF. 2024/11/26
//=============================================================================

#include "libcustom.h"
#include "autosdk_server.h"
#include "clexer.h"
#include "MdClexerServer.h"

static const str _hello_str_ = "hello you";

LEX non hello_lex(any obj, any data, int len) /* \x16\x4d\x0d%HELLO\x2e */
{
	MdWriter *writer = MdClexerServer_GetWriter(obj);
	MdWriter_Print(writer, "%s\n\x25HELLO\x06", _hello_str_);
}

API int MdScanner_Hello(/* readout the version */
	ARR(O) out			/* output buffer */
)
{
	int copy_len = strlen(_hello_str_) + 1;
	if (copy_len > out->len) // truncate
		copy_len = out->len;
	else // all copy
		out->len = copy_len;
	clike_copy(out->ptr, _hello_str_, copy_len);
	return AUTOSDK_STAT_DONE;
}
