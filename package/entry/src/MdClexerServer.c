//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: MdClexerServer.c
// Author		: XuCF
// Created On	: 2024/12/02
// Description	: MdClexerServer.c
//
// History
// 1. V1.0, Created by XuCF. 2024/12/02
//=============================================================================

#include "MdClexerServer.h"
#include "cbeans.h"
#include "MdNotice.h"
#include "MdBuffer.h"
#include "clexer.h"
#include "mdscanner.lex.h"

ClassRealize(MdClexerServer)
{
	int value;
	MdWriter *writer;
	clexer_t *clexer;
};

#if ULOGL >= 3
static int _received_(MdClexerServer *thiz, MdBuffer *buffer, any ndata, snv nsize)
{
	int ret;
	void (*func)(any, any, int);
	if (nsize <= 0) return 0; // channel error
	func = clexer_input(thiz->clexer, (byt *)ndata, nsize, &ret);
	if (func)
	{
		func((any)thiz, buffer->data, buffer->size);
		MdBuffer_Drop(buffer);
		return ret; // buffer used
	}
	if (ret == nsize) return -1; // intermediate state
	return 0;					 // format not match
}
#endif // ULOGL >= 3

any MdClexerServer_New(any notice, any writer)
{
#if ULOGL >= 3
	Assert(!notice, return 0);
	Assert(!writer, return 0);
	MdClexerServer *thiz = clike_new(MdClexerServer);
	thiz->writer = writer;
	MdNotice_AttachUser(notice, _received_, thiz, 0);
	thiz->clexer = clexer_create((any)_lex_states_, (any)_lex_moves_, (any)_lex_hooks_);
	Assert(!thiz->clexer, return 0);
	return thiz;
#else
	return 0;
#endif // ULOGL >= 3
}

MdWriter *MdClexerServer_GetWriter(MdClexerServer *thiz)
{
	return thiz->writer;
}
