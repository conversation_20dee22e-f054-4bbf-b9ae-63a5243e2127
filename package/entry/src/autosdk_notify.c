//=============================================================================
// Copyright (C), 2004-2025, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: notify.c
// Author		: XuCF
// Created On	: 2025/05/26
// Description	: notify.c
//
// History
// 1. V1.0, Created by XuCF. 2025/05/26
//=============================================================================

#define CQUEUE_TYPE int
#define CQUEUE_SIZE 0xff
#include "cqueue.h"

#include "autosdk_server.h"
#include "MdRunner.h"
#include "cbeans.h"

typedef struct
{
	cqueue_t *queue; // int queue
	MdMutex *mutex;
} _autosdk_notify_t_;

static _autosdk_notify_t_ *_notify_ = 0;

ACT int MdScanner_GetNotifyAwake(void *hand); /* get notify items */
API int MdScanner_GetNotify(				  /* get notify items */
	ARR(O) items							  /* output notify items */
)
{
	// create notify
	if (!_notify_)
	{
		_notify_ = clike_new(_autosdk_notify_t_);
		_notify_->queue = cqueue_create(CQUEUE_SIZE);
		_notify_->mutex = MdMutex_Create();
		return AUTOSDK_STAT_PROC;
	}
	// take notify items
	MdMutex_Lock(_notify_->mutex);
	int count = cqueue_take(_notify_->queue, items->ptr, items->len / sizeof(int));
	MdMutex_Unlock(_notify_->mutex);
	// return notify items
	if (count == 0) return AUTOSDK_STAT_PROC;
	items->len = count * sizeof(int);
	return AUTOSDK_STAT_DONE;
}

void autosdk_notify(int event)
{
	int err;
	if (!_notify_) return;
	MdMutex_Lock(_notify_->mutex);
	if (event == 0x1fffe || cqueue_left(_notify_->queue) < (CQUEUE_SIZE / 4))
	{
		cqueue_drop_all(_notify_->queue);
		err = cqueue_push_one(_notify_->queue, 0x1fffe);
	}
	else { err = cqueue_push_one(_notify_->queue, event); }
	MdMutex_Unlock(_notify_->mutex);
	clike_assert(err < 0, return);
	MdScanner_GetNotifyAwake(cbeans_find(BEAN_AUTOSDK));
}
