// Auto generated, don't edit

#ifndef _LEGACY_CONFIG_TABLE_H
#define _LEGACY_CONFIG_TABLE_H

#include "autosdk_notify.h"
#include "cpropr.cfg.h"
#include "para_code_macro.h"

#define _LEGACY_CONFIG_NOTIFY_ID_ 0xffff
#define _ALL_CONFIG_NOTIFY_ID_    0xfffe

static inline int legacy_code_to_property_id(int legacy_code)
{
    switch (legacy_code) {
        case MACR_PARACODE_0010: return PROP_CONFIGBYCODE;
        case MACR_PARACODE_0101: return PROP_INTERFACETYPEMAIN;
        case MACR_PARACODE_0301: return PROP_RS232FLOWCONTROL;
        case MACR_PARACODE_0302: return PROP_RS232CHARDELAY;
        case MACR_PARACODE_0303: return PROP_RS232BYTEDELAY;
        case MACR_PARACODE_0304: return PROP_RS232RESPONSEDELAY;
        case MACR_PARACODE_0305: return PROP_RS232BAUDRATE;
        case MACR_PARACODE_0306: return PROP_RS232PARITY;
        case MACR_PARACODE_0307: return PROP_RS232DATABITS;
        case MACR_PARACODE_0308: return PROP_RS232STOPBITS;
        case MACR_PARACODE_0310: return PROP_RS232HOSTTYPE;
        case MACR_PARACODE_0311: return PROP_RS232DECODATAFORMAT;
        case MACR_PARACODE_0312: return PROP_RS232HSCHARTIMEOUT;
        case MACR_PARACODE_0401: return PROP_SCANMODE;
        case MACR_PARACODE_0402: return PROP_SCANKEEPTIME;
        case MACR_PARACODE_0403: return PROP_SAMEBARDELAY1D;
        case MACR_PARACODE_0404: return PROP_DOUBLECONFIRM;
        case MACR_PARACODE_0405: return PROP_CODEMAXLEN1D;
        case MACR_PARACODE_0406: return PROP_CODEMINLEN1D;
        case MACR_PARACODE_0407: return PROP_INSERTSTR;
        case MACR_PARACODE_0408: return PROP_CODEELEMENTAMEND;
        case MACR_PARACODE_0409: return PROP_CHARPRINTCTR;
        case MACR_PARACODE_0410: return PROP_DECODEOPTIMIZE;
        case MACR_PARACODE_0411: return PROP_OUTPUTDELAYCONTSCAN;
        case MACR_PARACODE_0413: return PROP_CHARENCODE;
        case MACR_PARACODE_0414: return PROP_BACKTX;
        case MACR_PARACODE_0415: return PROP_SAMEBARDELAY2D;
        case MACR_PARACODE_0421: return PROP_NOREADOUTPUT;
        case MACR_PARACODE_0422: return PROP_SAMEBARDELAYDR;
        case MACR_PARACODE_0428: return PROP_SWEEPINGSCANENHANCE;
        case MACR_PARACODE_0430: return PROP_GOODREADDELAY;
        case MACR_PARACODE_0431: return PROP_MOVEMENTSCANMODE;
        case MACR_PARACODE_0435: return PROP_ONTIMETRIGGERDECODE;
        case MACR_PARACODE_0501: return PROP_POWERINDICATION;
        case MACR_PARACODE_0502: return PROP_LEDINDICATION;
        case MACR_PARACODE_0503: return PROP_BEEPINDICATION;
        case MACR_PARACODE_0504: return PROP_BEEPTIME;
        case MACR_PARACODE_0505: return PROP_BEEPVOLUME;
        case MACR_PARACODE_0506: return PROP_BEEPTONE;
        case MACR_PARACODE_0507: return PROP_VIBRATORINDICATION;
        case MACR_PARACODE_0508: return PROP_POWERLEDCTRL;
        case MACR_PARACODE_0509: return PROP_SILENTMODE;
        case MACR_PARACODE_0510: return PROP_BEEPSOUNDMODE;
        case MACR_PARACODE_0511: return PROP_INDICATIONTXDELAY;
        case MACR_PARACODE_0512: return PROP_VOICEDLY;
        case MACR_PARACODE_0601: return PROP_INFRAREDSENSOR;
        case MACR_PARACODE_0602: return PROP_INFRAREDMODE;
        case MACR_PARACODE_0603: return PROP_AUTODETEKEEPTIME;
        case MACR_PARACODE_0604: return PROP_AUTODETETEXTURE;
        case MACR_PARACODE_0605: return PROP_AUTODETELUM;
        case MACR_PARACODE_0606: return PROP_AUTODETETXTUREINTER;
        case MACR_PARACODE_0607: return PROP_AUTODETESLEEPFRMINTER;
        case MACR_PARACODE_0608: return PROP_INFRAREDMSGSWITCH;
        case MACR_PARACODE_0609: return PROP_INFRAREDLUM;
        case MACR_PARACODE_0610: return PROP_IRLUMDETESENLEVEL;
        case MACR_PARACODE_0705: return PROP_DATABATCH;
        case MACR_PARACODE_0901: return PROP_USBTYPE;
        case MACR_PARACODE_0902: return PROP_USBKEYBOARDLAYOUT;
        case MACR_PARACODE_0903: return PROP_USBKEYDLY;
        case MACR_PARACODE_0904: return PROP_USBNUMBKEY;
        case MACR_PARACODE_0905: return PROP_USBFUNCKEY;
        case MACR_PARACODE_0906: return PROP_USBHOSTPOLLINGINTER;
        case MACR_PARACODE_0907: return PROP_USBKEYSENDMODE;
        case MACR_PARACODE_0908: return PROP_USBENTERMODE;
        case MACR_PARACODE_0909: return PROP_USBMODKEYSRELEASEMODE;
        case MACR_PARACODE_1001: return PROP_CODESYMB2D;
        case MACR_PARACODE_1002: return PROP_DPMREAD;
        case MACR_PARACODE_1003: return PROP_MULTISYMBREAD;
        case MACR_PARACODE_1004: return PROP_CENTERINGREAD;
        case MACR_PARACODE_1005: return PROP_CODESYMB1D;
        case MACR_PARACODE_1007: return PROP_SCREENREAD;
        case MACR_PARACODE_1101: return PROP_UPCAREADMAIN;
        case MACR_PARACODE_1102: return PROP_UPCACHKDIGVER;
        case MACR_PARACODE_1103: return PROP_UPCACHKDIGTX;
        case MACR_PARACODE_1104: return PROP_UPCACODEID;
        case MACR_PARACODE_1105: return PROP_UPCAINSERTSTR;
        case MACR_PARACODE_1106: return PROP_UPCASUPLDIG;
        case MACR_PARACODE_1107: return PROP_UPCATRUNEXP;
        case MACR_PARACODE_1108: return PROP_UPCAAIMID;
        case MACR_PARACODE_1201: return PROP_UPCEREADMAIN;
        case MACR_PARACODE_1202: return PROP_UPCECHKDIGVER;
        case MACR_PARACODE_1203: return PROP_UPCECHKDIGTX;
        case MACR_PARACODE_1204: return PROP_UPCECODEID;
        case MACR_PARACODE_1205: return PROP_UPCEINSERTSTR;
        case MACR_PARACODE_1206: return PROP_UPCESUPLDIG;
        case MACR_PARACODE_1207: return PROP_UPCETRUNEXP;
        case MACR_PARACODE_1208: return PROP_UPCEAIMID;
        case MACR_PARACODE_1301: return PROP_EAN13READMAIN;
        case MACR_PARACODE_1302: return PROP_EAN13CHKDIGVER;
        case MACR_PARACODE_1303: return PROP_EAN13CHKDIGTX;
        case MACR_PARACODE_1304: return PROP_EAN13CODEID;
        case MACR_PARACODE_1305: return PROP_EAN13INSERTSTR;
        case MACR_PARACODE_1306: return PROP_EAN13SUPLDIG;
        case MACR_PARACODE_1307: return PROP_EAN13ISBSN;
        case MACR_PARACODE_1309: return PROP_EAN13ISSNISBNID;
        case MACR_PARACODE_1310: return PROP_EAN13AIMID;
        case MACR_PARACODE_1401: return PROP_EAN8READMAIN;
        case MACR_PARACODE_1402: return PROP_EAN8CHKDIGVER;
        case MACR_PARACODE_1403: return PROP_EAN8CHKDIGTX;
        case MACR_PARACODE_1404: return PROP_EAN8CODEID;
        case MACR_PARACODE_1405: return PROP_EAN8INSERTSTR;
        case MACR_PARACODE_1406: return PROP_EAN8SUPLDIG;
        case MACR_PARACODE_1407: return PROP_EAN8TRUNEXP;
        case MACR_PARACODE_1408: return PROP_EAN8AIMID;
        case MACR_PARACODE_1501: return PROP_CODE39READMAIN;
        case MACR_PARACODE_1502: return PROP_CODE39CHKDIGVER;
        case MACR_PARACODE_1503: return PROP_CODE39CHKDIGTX;
        case MACR_PARACODE_1504: return PROP_CODE39MAXLEN;
        case MACR_PARACODE_1505: return PROP_CODE39MINLEN;
        case MACR_PARACODE_1506: return PROP_CODE39CODEID;
        case MACR_PARACODE_1507: return PROP_CODE39INSERTSTR;
        case MACR_PARACODE_1508: return PROP_CODE39FORMAT;
        case MACR_PARACODE_1509: return PROP_CODE39STEDTRANS;
        case MACR_PARACODE_1510: return PROP_CODE39ASTEASCHAR;
        case MACR_PARACODE_1511: return PROP_CODE39CODE32;
        case MACR_PARACODE_1512: return PROP_CODE39CODE32PRE;
        case MACR_PARACODE_1513: return PROP_CODE39TRIOPTIC;
        case MACR_PARACODE_1514: return PROP_CODE39TOSTEDTRANS;
        case MACR_PARACODE_1515: return PROP_CODE39AIMID;
        case MACR_PARACODE_1601: return PROP_INTL25READMAIN;
        case MACR_PARACODE_1602: return PROP_INTL25CHKDIGVER;
        case MACR_PARACODE_1603: return PROP_INTL25CHKDIGTX;
        case MACR_PARACODE_1604: return PROP_INTL25MAXLEN;
        case MACR_PARACODE_1605: return PROP_INTL25MINLEN;
        case MACR_PARACODE_1606: return PROP_INTL25CODEID;
        case MACR_PARACODE_1607: return PROP_INTL25INSERTSTR;
        case MACR_PARACODE_1609: return PROP_INTL25LENRESTR;
        case MACR_PARACODE_1610: return PROP_INTL25BANKFORMATCONV;
        case MACR_PARACODE_1611: return PROP_INTL25BANKFORMATTYPE;
        case MACR_PARACODE_1612: return PROP_INTL25AIMID;
        case MACR_PARACODE_1701: return PROP_INDUSTRIAL25READMAIN;
        case MACR_PARACODE_1702: return PROP_INDUSTRIAL25MAXLEN;
        case MACR_PARACODE_1703: return PROP_INDUSTRIAL25MINLEN;
        case MACR_PARACODE_1704: return PROP_INDUSTRIAL25CODEID;
        case MACR_PARACODE_1705: return PROP_INDUSTRIAL25INSERTSTR;
        case MACR_PARACODE_1706: return PROP_INDUSTRIAL25AIMID;
        case MACR_PARACODE_1801: return PROP_MATRIX25READMAIN;
        case MACR_PARACODE_1802: return PROP_MATRIX25CHKDIGVER;
        case MACR_PARACODE_1803: return PROP_MATRIX25CHKDIGTRANS;
        case MACR_PARACODE_1804: return PROP_MATRIX25MAXLEN;
        case MACR_PARACODE_1805: return PROP_MATRIX25MINLEN;
        case MACR_PARACODE_1806: return PROP_MATRIX25CODEID;
        case MACR_PARACODE_1807: return PROP_MATRIX25INSERTSTR;
        case MACR_PARACODE_1808: return PROP_MATRIX25AIMID;
        case MACR_PARACODE_1901: return PROP_CODABARREADMAIN;
        case MACR_PARACODE_1902: return PROP_CODABARCHKDIGVER;
        case MACR_PARACODE_1903: return PROP_CODABARCHKDIGTX;
        case MACR_PARACODE_1904: return PROP_CODABARMAXLEN;
        case MACR_PARACODE_1905: return PROP_CODABARMINLEN;
        case MACR_PARACODE_1906: return PROP_CODABARCODEID;
        case MACR_PARACODE_1907: return PROP_CODABARINSERTSTR;
        case MACR_PARACODE_1908: return PROP_CODABARSTARTSTOPTYPE;
        case MACR_PARACODE_1909: return PROP_CODABARSTARTSTOPTX;
        case MACR_PARACODE_1910: return PROP_CODABARSTARTENDCHAREQ;
        case MACR_PARACODE_1911: return PROP_CODABARAIMID;
        case MACR_PARACODE_2001: return PROP_CODE128READMAIN;
        case MACR_PARACODE_2002: return PROP_CODE128CHKDIGVER;
        case MACR_PARACODE_2003: return PROP_CODE128CHKDIGTX;
        case MACR_PARACODE_2004: return PROP_CODE128MAXLEN;
        case MACR_PARACODE_2005: return PROP_CODE128MINLEN;
        case MACR_PARACODE_2006: return PROP_CODE128CODEID;
        case MACR_PARACODE_2007: return PROP_CODE128INSERTSTR;
        case MACR_PARACODE_2008: return PROP_CODE128TRUZERO;
        case MACR_PARACODE_2009: return PROP_CODE128AIMID;
        case MACR_PARACODE_2101: return PROP_CODE93READMAIN;
        case MACR_PARACODE_2102: return PROP_CODE93CHKDIGVER;
        case MACR_PARACODE_2103: return PROP_CODE93CHKDIGTX;
        case MACR_PARACODE_2104: return PROP_CODE93MAXLEN;
        case MACR_PARACODE_2105: return PROP_CODE93MINLEN;
        case MACR_PARACODE_2106: return PROP_CODE93CODEID;
        case MACR_PARACODE_2107: return PROP_CODE93INSERTSTR;
        case MACR_PARACODE_2108: return PROP_CODE93AIMID;
        case MACR_PARACODE_2201: return PROP_CODE11READMAIN;
        case MACR_PARACODE_2202: return PROP_CODE11CHKDIGVER;
        case MACR_PARACODE_2203: return PROP_CODE11CHKDIGTX;
        case MACR_PARACODE_2204: return PROP_CODE11MAXLEN;
        case MACR_PARACODE_2205: return PROP_CODE11MINLEN;
        case MACR_PARACODE_2206: return PROP_CODE11CODEID;
        case MACR_PARACODE_2207: return PROP_CODE11INSERTSTR;
        case MACR_PARACODE_2208: return PROP_CODE11AIMID;
        case MACR_PARACODE_2301: return PROP_MSIPLREADMAIN;
        case MACR_PARACODE_2302: return PROP_MSIPLCHKDIGVER;
        case MACR_PARACODE_2303: return PROP_MSIPLCHKDIGTX;
        case MACR_PARACODE_2304: return PROP_MSIPLMAXLEN;
        case MACR_PARACODE_2305: return PROP_MSIPLMINLEN;
        case MACR_PARACODE_2306: return PROP_MSIPLCODEID;
        case MACR_PARACODE_2307: return PROP_MSIPLINSERTSTR;
        case MACR_PARACODE_2308: return PROP_MSIPLAIMID;
        case MACR_PARACODE_2401: return PROP_UKPLREADMAIN;
        case MACR_PARACODE_2402: return PROP_UKPLCHKDIGVER;
        case MACR_PARACODE_2403: return PROP_UKPLCHKDIGTX;
        case MACR_PARACODE_2404: return PROP_UKPLMAXLEN;
        case MACR_PARACODE_2405: return PROP_UKPLMINLEN;
        case MACR_PARACODE_2406: return PROP_UKPLCODEID;
        case MACR_PARACODE_2407: return PROP_UKPLINSERTSTR;
        case MACR_PARACODE_2408: return PROP_UKPLAIMID;
        case MACR_PARACODE_2501: return PROP_UCCEAN128READMAIN;
        case MACR_PARACODE_2502: return PROP_UCCEAN128CHKDIGVER;
        case MACR_PARACODE_2503: return PROP_UCCEAN128CHKDIGTX;
        case MACR_PARACODE_2504: return PROP_UCCEAN128MAXLEN;
        case MACR_PARACODE_2505: return PROP_UCCEAN128MINLEN;
        case MACR_PARACODE_2506: return PROP_UCCEAN128CODEID;
        case MACR_PARACODE_2507: return PROP_UCCEAN128INSERTSTR;
        case MACR_PARACODE_2508: return PROP_UCCEAN128TRUZERO;
        case MACR_PARACODE_2509: return PROP_UCCEAN128AIMID;
        case MACR_PARACODE_2601: return PROP_CHNPSTREADMAIN;
        case MACR_PARACODE_2602: return PROP_CHNPSTCHKDIGVER;
        case MACR_PARACODE_2603: return PROP_CHNPSTCHKDIGTX;
        case MACR_PARACODE_2604: return PROP_CHNPSTMAXLEN;
        case MACR_PARACODE_2605: return PROP_CHNPSTMINLEN;
        case MACR_PARACODE_2606: return PROP_CHNPSTCODEID;
        case MACR_PARACODE_2607: return PROP_CHNPSTINSERTSTR;
        case MACR_PARACODE_2608: return PROP_CHNPSTAIMID;
        case MACR_PARACODE_2701: return PROP_GS1DBREADMAIN;
        case MACR_PARACODE_2702: return PROP_GS1DBCODEID;
        case MACR_PARACODE_2703: return PROP_GS1DBINSERTSTR;
        case MACR_PARACODE_2704: return PROP_GS1DBCONVERSION;
        case MACR_PARACODE_2705: return PROP_GS1DBOUTPUTBRACKETS;
        case MACR_PARACODE_2706: return PROP_GS1DBAIMID;
        case MACR_PARACODE_2801: return PROP_GS1LIMIREADMAIN;
        case MACR_PARACODE_2802: return PROP_GS1LIMICODEID;
        case MACR_PARACODE_2803: return PROP_GS1LIMIINSERTSTR;
        case MACR_PARACODE_2804: return PROP_GS1LIMICONVERSION;
        case MACR_PARACODE_2805: return PROP_GS1LIMIAIMID;
        case MACR_PARACODE_2901: return PROP_GS1EXPAREADMAIN;
        case MACR_PARACODE_2902: return PROP_GS1EXPAMAXLEN;
        case MACR_PARACODE_2903: return PROP_GS1EXPAMINLEN;
        case MACR_PARACODE_2904: return PROP_GS1EXPACODEID;
        case MACR_PARACODE_2905: return PROP_GS1EXPAINSERTSTR;
        case MACR_PARACODE_2906: return PROP_GS1EXPACONVERSION;
        case MACR_PARACODE_2907: return PROP_GS1EXPAIMID;
        case MACR_PARACODE_3001: return PROP_PDF417READMAIN;
        case MACR_PARACODE_3002: return PROP_PDF417CODEID;
        case MACR_PARACODE_3003: return PROP_PDF417INSERTSTR;
        case MACR_PARACODE_3004: return PROP_PDF417CONVERSION;
        case MACR_PARACODE_3005: return PROP_PDF417MAXLEN;
        case MACR_PARACODE_3006: return PROP_PDF417MINLEN;
        case MACR_PARACODE_3007: return PROP_PDF417AIMID;
        case MACR_PARACODE_3101: return PROP_MICPDF417READMAIN;
        case MACR_PARACODE_3102: return PROP_MICPDF417CODEID;
        case MACR_PARACODE_3103: return PROP_MICPDF417INSERTSTR;
        case MACR_PARACODE_3104: return PROP_MICPDF417CONVERSION;
        case MACR_PARACODE_3105: return PROP_MICPDF417MAXLEN;
        case MACR_PARACODE_3106: return PROP_MICPDF417MINLEN;
        case MACR_PARACODE_3107: return PROP_MICPDF417AIMID;
        case MACR_PARACODE_3201: return PROP_CHNFNCREADMAIN;
        case MACR_PARACODE_3202: return PROP_CHNFNCMAXLEN;
        case MACR_PARACODE_3203: return PROP_CHNFNCMINLEN;
        case MACR_PARACODE_3204: return PROP_CHNFNCCHKDIGVER;
        case MACR_PARACODE_3205: return PROP_CHNFNCFRTCHARCONV;
        case MACR_PARACODE_3206: return PROP_CHNFNCFRTCHARSPEC;
        case MACR_PARACODE_3207: return PROP_CHNFNCCODEID;
        case MACR_PARACODE_3208: return PROP_CHNFNCINSERTSTR;
        case MACR_PARACODE_3209: return PROP_CHNFNCAIMID;
        case MACR_PARACODE_3301: return PROP_ISBT128READMAIN;
        case MACR_PARACODE_3302: return PROP_ISBT128CKDIGIVER;
        case MACR_PARACODE_3303: return PROP_ISBT128CHKDIGTRANS;
        case MACR_PARACODE_3304: return PROP_ISBT128MAXLEN;
        case MACR_PARACODE_3305: return PROP_ISBT128MINLEN;
        case MACR_PARACODE_3306: return PROP_ISBT128CODEID;
        case MACR_PARACODE_3307: return PROP_ISBT128INSERTSTR;
        case MACR_PARACODE_3308: return PROP_ISBT128AIMID;
        case MACR_PARACODE_3401: return PROP_UPCE1READMAIN;
        case MACR_PARACODE_3402: return PROP_UPCE1CHKDIGVER;
        case MACR_PARACODE_3403: return PROP_UPCE1CHKDIGTRANS;
        case MACR_PARACODE_3404: return PROP_UPCE1CODEID;
        case MACR_PARACODE_3405: return PROP_UPCE1INSERTSTR;
        case MACR_PARACODE_3406: return PROP_UPCE1SUPLDIG;
        case MACR_PARACODE_3407: return PROP_UPCE1TRUNEXP;
        case MACR_PARACODE_3408: return PROP_UPCE1AIMID;
        case MACR_PARACODE_3501: return PROP_COMPOSITEREAD;
        case MACR_PARACODE_4001: return PROP_QRCODEREADMAIN;
        case MACR_PARACODE_4002: return PROP_QRCODECODEID;
        case MACR_PARACODE_4003: return PROP_QRCODEWEBCODEFORBID;
        case MACR_PARACODE_4004: return PROP_QRCODEMAXLEN;
        case MACR_PARACODE_4005: return PROP_QRCODEMINLEN;
        case MACR_PARACODE_4006: return PROP_QRCODEAIMID;
        case MACR_PARACODE_4101: return PROP_DMREADMAIN;
        case MACR_PARACODE_4102: return PROP_DMCODEID;
        case MACR_PARACODE_4103: return PROP_DMMAXLEN;
        case MACR_PARACODE_4104: return PROP_DMMINLEN;
        case MACR_PARACODE_4108: return PROP_DMPPNCODE;
        case MACR_PARACODE_4109: return PROP_DMAIMID;
        case MACR_PARACODE_4201: return PROP_CSCODEREADMAIN;
        case MACR_PARACODE_4202: return PROP_CSCODECODEID;
        case MACR_PARACODE_4203: return PROP_CSCODEMAXLEN;
        case MACR_PARACODE_4204: return PROP_CSCODEMINLEN;
        case MACR_PARACODE_4205: return PROP_CSCODEAIMID;
        case MACR_PARACODE_4301: return PROP_AZTECREADMAIN;
        case MACR_PARACODE_4302: return PROP_AZTECCODEID;
        case MACR_PARACODE_4303: return PROP_AZTECMAXLEN;
        case MACR_PARACODE_4304: return PROP_AZTECMINLEN;
        case MACR_PARACODE_4305: return PROP_AZTECAIMID;
        case MACR_PARACODE_4401: return PROP_MAXICODEREADMAIN;
        case MACR_PARACODE_4402: return PROP_MAXICODEMAXLEN;
        case MACR_PARACODE_4403: return PROP_MAXICODEMINLEN;
        case MACR_PARACODE_4404: return PROP_MAXICODEAIMID;
        case MACR_PARACODE_4501: return PROP_MICROQRREADMAIN;
        case MACR_PARACODE_4502: return PROP_MICROQRCODEID;
        case MACR_PARACODE_4503: return PROP_MICROQRMAXLEN;
        case MACR_PARACODE_4504: return PROP_MICROQRMINLEN;
        case MACR_PARACODE_4505: return PROP_MICROQRAIMID;
        case MACR_PARACODE_4601: return PROP_CODABLOCKFREADMAIN;
        case MACR_PARACODE_4602: return PROP_CODABLOCKFCODEID;
        case MACR_PARACODE_4603: return PROP_CODABLOCKFMAXLEN;
        case MACR_PARACODE_4604: return PROP_CODABLOCKFMINLEN;
        case MACR_PARACODE_4605: return PROP_CODABLOCKFAIMID;
        case MACR_PARACODE_4701: return PROP_GMCODEREADMAIN;
        case MACR_PARACODE_4702: return PROP_GMCODECODEID;
        case MACR_PARACODE_4703: return PROP_GMCODEMAXLEN;
        case MACR_PARACODE_4704: return PROP_GMCODEMINLEN;
        case MACR_PARACODE_4801: return PROP_BINARYCODEREADMAIN;
        case MACR_PARACODE_4802: return PROP_BINARYCODECODEID;
        case MACR_PARACODE_4803: return PROP_BINARYCODEMAXLEN;
        case MACR_PARACODE_4804: return PROP_BINARYCODEMINLEN;
        case MACR_PARACODE_4805: return PROP_BINARYCODEAIMID;
        case MACR_PARACODE_4901: return PROP_DOTCODEREADMAIN;
        case MACR_PARACODE_4902: return PROP_DOTCODECODEID;
        case MACR_PARACODE_4903: return PROP_DOTCODEMAXLEN;
        case MACR_PARACODE_4904: return PROP_DOTCODEMINLEN;
        case MACR_PARACODE_4905: return PROP_DOTCODEAIMID;
        case MACR_PARACODE_8001: return PROP_PREFIXTEXT;
        case MACR_PARACODE_8002: return PROP_SUFFIXTEXT;
        case MACR_PARACODE_8003: return PROP_PREAMBTEXT;
        case MACR_PARACODE_8004: return PROP_POSTAMBTEXT;
        case MACR_PARACODE_8005: return PROP_GROUP1TEXT;
        case MACR_PARACODE_8006: return PROP_GROUP2TEXT;
        case MACR_PARACODE_8007: return PROP_GROUP3TEXT;
        case MACR_PARACODE_8008: return PROP_GROUP4TEXT;
        case MACR_PARACODE_8009: return PROP_FN1SUBSTUTEXT;
        case MACR_PARACODE_8010: return PROP_TRUNCLEADG5;
        case MACR_PARACODE_8011: return PROP_REPEATG5;
        case MACR_PARACODE_8012: return PROP_TRUNCENDG6;
        case MACR_PARACODE_8013: return PROP_REPEATG6;
        case MACR_PARACODE_8014: return PROP_SINGREPLAC1;
        case MACR_PARACODE_8015: return PROP_SINGREPLAC2;
        case MACR_PARACODE_8016: return PROP_SINGREPLAC3;
        case MACR_PARACODE_8017: return PROP_REPLACHAR2STR;
        case MACR_PARACODE_8101: return PROP_GROUP1POS;
        case MACR_PARACODE_8102: return PROP_GROUP2POS;
        case MACR_PARACODE_8103: return PROP_GROUP3POS;
        case MACR_PARACODE_8104: return PROP_GROUP4POS;
        case MACR_PARACODE_8105: return PROP_CODEIDPOS;
        case MACR_PARACODE_8201: return PROP_PREFIXTEXTTRANSMISSION;
        case MACR_PARACODE_8202: return PROP_SUFFIXTEXTENABLE;
        case MACR_PARACODE_8203: return PROP_CODENAMEENABLE;
        case MACR_PARACODE_8204: return PROP_PREAMBTEXTENABLE;
        case MACR_PARACODE_8205: return PROP_POSTAMBTEXTENABLE;
        case MACR_PARACODE_8206: return PROP_CODEIDTEXTENABLE;
        case MACR_PARACODE_8207: return PROP_CODELENENABLE;
        case MACR_PARACODE_8208: return PROP_CASECONV;
        case MACR_PARACODE_8209: return PROP_FN1SUBSTUTEXTENABLE;
        case MACR_PARACODE_8210: return PROP_NONEPRINTSTRENABLE;
        case MACR_PARACODE_8211: return PROP_FIRSTONLY;
        case MACR_PARACODE_8212: return PROP_LASTONLY;
        case MACR_PARACODE_8213: return PROP_BANSPECIALKEYS;
        case MACR_PARACODE_8214: return PROP_TEXTEDIT;
        case MACR_PARACODE_8215: return PROP_CHARACTERSTOSTRINGS;
        case MACR_PARACODE_8216: return PROP_INVOICECUSTSTR1;
        case MACR_PARACODE_8217: return PROP_INVOICECUSTSTR2;
        case MACR_PARACODE_8218: return PROP_INVOICECUSTSTR3;
        case MACR_PARACODE_8219: return PROP_INVOICECUSTSTR4;
        case MACR_PARACODE_8301: return PROP_DECORSLTCHECK;
        case MACR_PARACODE_8302: return PROP_DECORSLTCHECK1HEADSTR;
        case MACR_PARACODE_8303: return PROP_DECORSLTCHECK1TAILSTR;
        case MACR_PARACODE_8304: return PROP_DECORESULTCHECKFAILINDICATION;
        case MACR_PARACODE_8401: return PROP_OCRRECOGNIZEMAIN;
        case MACR_PARACODE_8402: return PROP_OCRRECOGNIZEPASSPORTMRZ;
        case MACR_PARACODE_8403: return PROP_OCRRECOGNIZENATIONALCARDID;
        case MACR_PARACODE_8450: return PROP_OCRTURKISHIDOUTPUTFORMAT;
        case MACR_PARACODE_8460: return PROP_OCRDOUBLECHECK;
        case MACR_PARACODE_8601: return PROP_IVDDETETUBECAPMAIN;
        case MACR_PARACODE_8602: return PROP_IVDDETETUBEHEIGHT;
        case MACR_PARACODE_9001: return PROP_ILLUMINATIONMODE;
        case MACR_PARACODE_9002: return PROP_AIMMODE;
        case MACR_PARACODE_9003: return PROP_ILLUMINATIONLEVEL;
        case MACR_PARACODE_9004: return PROP_AIMKEEPTIMEAFTERDECO;
        case MACR_PARACODE_9005: return PROP_LIGHTGROUPSELECT;
        case MACR_PARACODE_9006: return PROP_LIGHTDRIVECAPACITY;
        case MACR_PARACODE_9007: return PROP_LIGHTMAXPERLED;
        case MACR_PARACODE_9008: return PROP_LIGHTGROUP1CURRENT;
        case MACR_PARACODE_9009: return PROP_LIGHTGROUP2CURRENT;
        case MACR_PARACODE_9010: return PROP_AIMKEEPTIMEBEFOREDECO;
        case MACR_PARACODE_9011: return PROP_AUXILIARYLUM;
        case MACR_PARACODE_9012: return PROP_AUXILIARYLUMCOMPTIME;
        case MACR_PARACODE_9896: return PROP_VISIONAPP;
        case MACR_PARACODE_9898: return PROP_SCANKEEPTIMEADVANCED;
        case MACR_PARACODE_9900: return PROP_IVDTUBEHIGHTINFO;
        case MACR_PARACODE_9901: return PROP_CODERSLTTXINDIDLYMODE;
        case MACR_PARACODE_9907: return PROP_DECOSTAY;
        case MACR_PARACODE_9909: return PROP_DECODERESULTMAXNUM;
        case MACR_PARACODE_9910: return PROP_PACKETOUTPUTDATACTR;
        case MACR_PARACODE_9911: return PROP_ADDWATERMARK;
        case MACR_PARACODE_9912: return PROP_INSTANDINDICATE;
        case MACR_PARACODE_9924: return PROP_QRTERMINATORPOS;
        case MACR_PARACODE_9925: return PROP_CODEERROUTPUT2D;
        case MACR_PARACODE_9926: return PROP_CODEERRLEVEL2D;
        case MACR_PARACODE_9927: return PROP_CODEVERSION2D;
        case MACR_PARACODE_9935: return PROP_CONTMODELUMLEVEL;
        case MACR_PARACODE_9936: return PROP_CONTMODENOACTIONDURA;
        case MACR_PARACODE_9938: return PROP_TIMETOREBOOT;
        case MACR_PARACODE_9939: return PROP_IMAGEFLIPMODE;
        case MACR_PARACODE_9941: return PROP_ATTACHCODEPOSOPTION;
        case MACR_PARACODE_9946: return PROP_USBKEYTXTIMEOUT;
        case MACR_PARACODE_9958: return PROP_LASTSUCCIMAGING;
        case MACR_PARACODE_9961: return PROP_HILUM4HDCODE;
        case MACR_PARACODE_9965: return PROP_SYSRECOVERINDICATION;
        case MACR_PARACODE_9966: return PROP_OUTPUTMULTIPLE;
        case MACR_PARACODE_9973: return PROP_CONFIGBYCODEFB;
        case MACR_PARACODE_9974: return PROP_QUERYCONFIGBYCODE;
        case MACR_PARACODE_9975: return PROP_RESULTCHECK;
        case MACR_PARACODE_9976: return PROP_RAINBOWREAD;
        case MACR_PARACODE_9984: return PROP_POWERUPREPORT;
        case MACR_PARACODE_9985: return PROP_USBENUMFAILREBOOT;
        case MACR_PARACODE_9988: return PROP_EXPREBOOTINDICATION;
        case MACR_PARACODE_9995: return PROP_BACKUPDECOIMG;
        case MACR_PARACODE_9997: return PROP_DECODEVERTEX;
        case MACR_PARACODE_9998: return PROP_AGINGTEST;
        default: return _LEGACY_CONFIG_NOTIFY_ID_;
    }
}

static inline int legacy_code_to_notify_id(int legacy_code)
{
    return MD_NOTIFY_PROP(legacy_code_to_property_id(legacy_code));
}

#endif //_LEGACY_CONFIG_TABLE_H
