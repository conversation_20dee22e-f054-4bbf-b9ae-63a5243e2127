//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: MdAutoSdkServer.c
// Author		: XuCF
// Created On	: 2025/03/18
// Description	: MdAutoSdkServer.c
//
// History
// 1. V1.0, Created by XuCF. 2025/03/18
//=============================================================================

#include "libcustom.h"
#include "autosdk_server.h"
#include "mdscanner.svr.h"
#include "cbeans.h"
any MdAutoSdkServer_New(any notice, any writer)
{
	any AutoSdkServer_New(any notice, any writer, const any *func_tb, const any *hand_tb, int func_cnt);
	return AutoSdkServer_New(notice, writer, _autosdk_func_tb_, _autosdk_hand_tb_, ARRAY_CNT(_autosdk_func_tb_));
}

void MdAutoSdkServer_Del(any obj)
{
	void AutoSdkServer_Del(any obj);
	AutoSdkServer_Del(obj);
}

int MdAutoSdkServer_Activate(any func, any ctx)
{
	any server = cbeans_find(BEAN_AUTOSDK);
	if (!server) return -1;
	for (int i = 0; i < ARRAY_CNT(_autosdk_hand_tb_); i++)
	{
		if (_autosdk_hand_tb_[i] == func)
		{
			// found the func
			return autosdk_portme_awake(server, i);
		}
	}
	return -1;
}

API int MDScanner_Version(	/* get AutoSDK version */
	BLK(O) out				/* output: version string */
)
{
	out->len = strlen(_autosdk_version_);
	out->ptr = (void *)_autosdk_version_;
	out->clean = NULL;
	out->arg = NULL;
	return AUTOSDK_STAT_DONE;
}