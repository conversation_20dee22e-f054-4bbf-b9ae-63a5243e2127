//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: ucip_config.c
// Author		: XuCF
// Created On	: 2025/07/08
// Description	: ucip_config.c
//
// History
// 1. V1.0, Created by XuCF. 2025/07/08
//=============================================================================

#include "mdcmd_proxy.h"
#include "cpropr_types.h"
#include "cpropr.cfg.h"
#include "MdRunner.h"
#include "autosdk_notify.h"

typedef enum
{
	_STATUS_NONE = 0,
	_STATUS_PEND,
	_STATUS_OKAY,
} _ucip_status_t;

typedef struct
{
	MdSynchronizer *sync;
	_ucip_status_t status;
	struct
	{
		byt enable;
		u32 exposure; // in microseconds
		byt gain;	  // 1-8
		byt illumine; // 0-32
		u32 holdtime; // in milliseconds
	} groups[8];
} UCIPConfiger;

static UCIPConfiger *_configer_ = 0;

any UCIPConfiger_New(void)
{
	if (_configer_) return _configer_;
	UCIPConfiger *thiz = clike_new(UCIPConfiger);
	thiz->sync = MdSynchronizer_Create();
	thiz->status = 0;
	_configer_ = thiz;
	return thiz;
}

non UCIPConfiger_Del(any obj)
{
	UCIPConfiger *thiz = (UCIPConfiger *)obj;
	if (!thiz) return;
	if (thiz->sync)
	{
		MdSynchronizer_Delete(thiz->sync);
		thiz->sync = 0;
	}
	clike_free(thiz);
	_configer_ = 0;
}

static void _unpack_ucip_data_(void *data_ptr, int data_size)
{
	// Validate input parameters
	clike_assert(!data_ptr, return);
	clike_assert(data_size <= 2, return);
	clike_assert(!_configer_, return);

	byt *data = (byt *)data_ptr;
	u32 offset = 0;

	// Read total length (u16, little endian)
	u16 total_len = *(u16 *)(data + offset);
	clike_assert(total_len > data_size, return);
	offset += 2;

	// Parse each package and store configuration
	while (offset < total_len)
	{
		// Read package header
		clike_assert(offset + 3 > total_len, return);
		byt package_id = data[offset];
		u16 package_len = *(u16 *)(data + offset + 1);
		offset += 3;

		// Validate package length
		clike_assert(offset + package_len > total_len, return);

		// Validate package ID range (0-7 for 8 groups)
		clike_assert(package_id > 7, return);

		// Read package content
		clike_assert(package_len < 7, return);
		byt enable = data[offset];
		u32 exposure = *(u32 *)(data + offset + 1);
		byt gain = data[offset + 5];
		byt illumine = data[offset + 6];
		u32 holdtime = 0;
		if (package_len >= 11) holdtime = *(u32 *)(data + offset + 7);

		LOGD("Custom imaging parameters group %d:", package_id);
		LOGD("  Enable: %s", enable ? "true" : "false");
		LOGD("  Exposure: %d us", exposure);
		LOGD("  Gain: %d", gain);
		LOGD("  Illumine level: %d", illumine);
		LOGD("  Hold time: %d ms", holdtime);

		// Store configuration in the configer structure
		_configer_->groups[package_id].enable = enable;
		_configer_->groups[package_id].exposure = exposure;
		_configer_->groups[package_id].gain = gain;
		_configer_->groups[package_id].illumine = illumine;
		_configer_->groups[package_id].holdtime = holdtime;

		offset += package_len;
	}
}

static int _pack_ucip_data_(void *data_ptr, int data_limit)
{
	clike_assert(!data_ptr, return -1);
	clike_assert(data_limit < 2, return -1);
	clike_assert(!_configer_, return -1);
	byt *data = (byt *)data_ptr;
	u32 offset = 2; // 2 bytes for total length
	// Pack groups
	for (int group_id = 0; group_id < 8; group_id++)
	{
		clike_assert(offset + 14 > data_limit, return -1);
		*(byt *)(data + offset) = (byt)group_id; // Package ID
		*(u16 *)(data + offset + 1) = 11;		 // Package length (fixed 11 bytes)
		*(byt *)(data + offset + 3) = _configer_->groups[group_id].enable;
		*(u32 *)(data + offset + 4) = _configer_->groups[group_id].exposure;
		*(byt *)(data + offset + 8) = _configer_->groups[group_id].gain;
		*(byt *)(data + offset + 9) = _configer_->groups[group_id].illumine;
		*(u32 *)(data + offset + 10) = _configer_->groups[group_id].holdtime;
		offset += 14;
	}
	// Write total length at the beginning
	*(u16 *)(data) = (u16)offset;
	return (int)offset;
}

static void _command_do_(mdcommand_block_t *blocks, int count,
	int (*cbk)(void *ctx, void *data_ptr, unsigned int data_size, mdcommand_flag_t flag))
{
	int req = -1;
	MdSynchronizer_Lock(_configer_->sync);
	if (_STATUS_NONE == _configer_->status)
	{
		_configer_->status = _STATUS_PEND;
		req = mdcommand_go(blocks, count, 0, 0, cbk);
	}
	MdSynchronizer_Timeout(_configer_->sync, _STATUS_OKAY != _configer_->status, 1000);
	_configer_->status = _STATUS_NONE; // reset status
	if (req != -1) mdcommand_abt(req);
	MdSynchronizer_Unlock(_configer_->sync);
}

static int _list_cbk_(any ctx, any data_ptr, u32 data_size, mdcommand_flag_t flag)
{
	LOGD("list_cbk %d %d", flag, data_size);
	MdSynchronizer_Lock(_configer_->sync);
	_unpack_ucip_data_(data_ptr, data_size);
	_configer_->status = _STATUS_OKAY;
	MdSynchronizer_Wake(_configer_->sync);
	MdSynchronizer_Unlock(_configer_->sync);
	return 0;
}

static void _update_configer_(void)
{
	mdcommand_block_t blocks[] = {
		{"%%UCIP%PLIST", strlen("%%UCIP%PLIST")},
	};
	_command_do_(blocks, ARRAY_CNT(blocks), _list_cbk_);
}

static int _conf_cbk_(any ctx, any data_ptr, u32 data_size, mdcommand_flag_t flag)
{
	LOGD("conf_cbk %d %d", flag, data_size);
	if (FLAG_PROCESS == flag) return 1; // not finished
	MdSynchronizer_Lock(_configer_->sync);
	_configer_->status = _STATUS_OKAY;
	MdSynchronizer_Wake(_configer_->sync);
	MdSynchronizer_Unlock(_configer_->sync);
	return 0;
}

static void _apply_configer_(void)
{
	byt data[128];
	int data_len = _pack_ucip_data_(data, sizeof(data));
	clike_assert(data_len <= 0, return);
	mdcommand_block_t blocks[] = {
		{"%%UCIP%%PCFG", strlen("%%UCIP%%PCFG")},
		{data, data_len},
	};
	_command_do_(blocks, ARRAY_CNT(blocks), _conf_cbk_);
}

static void _reset_configer_(void)
{
	mdcommand_block_t blocks[] = {
		{"%%UCIP%RESET", strlen("%%UCIP%RESET")},
	};
	_command_do_(blocks, ARRAY_CNT(blocks), _conf_cbk_);
}

/* @PROP: UCIPGroup[1,2,3,4,5,6,7,8]Enable
 * @LABEL: Enable
 * @TYPE: BOOL
 * @DEFAULT: false
 * @FLAG: NOSAVE
 * @PATH: /ImagingCtr/ImagingCtr/UCIPGroup%i
 * @HELP: Enable custom imaging parameters group %i
 */
void _ucip_group_enable_prop_hook_(int id, cpropr_hook_type_t type, void *value)
{
	byt group_index = id - PROP_UCIPGROUP1ENABLE;
	clike_assert(group_index > 7, return);
	clike_assert(!_configer_, return);
	if (CPROPR_HOOK_READ == type && value)
	{
		_update_configer_();
		*(int *)value = _configer_->groups[group_index].enable;
	}
	else if (CPROPR_HOOK_WRITE == type && value)
	{
		// cache
		_configer_->groups[group_index].enable = *(byt *)value;
	}
}

/* @PROP: UCIPGroup[1,2,3,4,5,6,7,8]Expo
 * @LABEL: Exposure Time(us)
 * @TYPE: INT
 * @RANGE: 0,10000
 * @DEFAULT: 1000
 * @FLAG: NOSAVE
 * @PATH: /ImagingCtr/ImagingCtr/UCIPGroup%i
 * @HELP: Set custom imaging parameters group %i exposure time in microseconds
 */
void _ucip_group_expo_prop_hook_(int id, cpropr_hook_type_t type, void *value)
{
	byt group_index = id - PROP_UCIPGROUP1EXPO;
	clike_assert(group_index > 7, return);
	clike_assert(!_configer_, return);
	if (CPROPR_HOOK_READ == type && value)
	{
		_update_configer_();
		*(int *)value = (int)_configer_->groups[group_index].exposure;
	}
	else if (CPROPR_HOOK_WRITE == type && value)
	{
		// cache
		_configer_->groups[group_index].exposure = *(u32 *)value;
	}
}

/* @PROP: UCIPGroup[1,2,3,4,5,6,7,8]Gain
 * @LABEL: Gain
 * @TYPE: INT
 * @RANGE: 1,8
 * @DEFAULT: 8
 * @FLAG: NOSAVE
 * @PATH: /ImagingCtr/ImagingCtr/UCIPGroup%i
 * @HELP: Set custom imaging parameters group %i gain value
 */
void _ucip_group_gain_prop_hook_(int id, cpropr_hook_type_t type, void *value)
{
	byt group_index = id - PROP_UCIPGROUP1GAIN;
	clike_assert(group_index > 7, return);
	clike_assert(!_configer_, return);
	if (CPROPR_HOOK_READ == type && value)
	{
		_update_configer_();
		*(int *)value = (int)_configer_->groups[group_index].gain;
	}
	else if (CPROPR_HOOK_WRITE == type && value)
	{
		// cache
		_configer_->groups[group_index].gain = *(byt *)value;
	}
}

/* @PROP: UCIPGroup[1,2,3,4,5,6,7,8]LuminLevel
 * @LABEL: Luminance Level
 * @TYPE: INT
 * @RANGE: 0,32
 * @DEFAULT: 32
 * @FLAG: NOSAVE
 * @PATH: /ImagingCtr/ImagingCtr/UCIPGroup%i
 * @HELP: Set custom imaging parameters group %i fill light brightness level
 */
void _ucip_group_lumin_level_prop_hook_(int id, cpropr_hook_type_t type, void *value)
{
	byt group_index = id - PROP_UCIPGROUP1LUMINLEVEL;
	clike_assert(group_index > 7, return);
	clike_assert(!_configer_, return);
	if (CPROPR_HOOK_READ == type && value)
	{
		_update_configer_();
		*(int *)value = (int)_configer_->groups[group_index].illumine;
	}
	else if (CPROPR_HOOK_WRITE == type && value)
	{
		// cache
		_configer_->groups[group_index].illumine = *(byt *)value;
	}
}

/* @PROP: UCIPGroup[1,2,3,4,5,6,7,8]HoldTime
 * @LABEL: Hold Time(ms)
 * @TYPE: INT
 * @RANGE: 0,3600000
 * @DEFAULT: 0
 * @FLAG: NOSAVE
 * @PATH: /ImagingCtr/ImagingCtr/UCIPGroup%i
 * @HELP: Set custom imaging parameters group %i hold time in milliseconds
 */
void _ucip_group_hold_time_prop_hook_(int id, cpropr_hook_type_t type, void *value)
{
	byt group_index = id - PROP_UCIPGROUP1HOLDTIME;
	clike_assert(group_index > 7, return);
	clike_assert(!_configer_, return);
	if (CPROPR_HOOK_READ == type && value)
	{
		_update_configer_();
		*(int *)value = (int)_configer_->groups[group_index].holdtime;
	}
	else if (CPROPR_HOOK_WRITE == type && value)
	{
		// cache
		_configer_->groups[group_index].holdtime = *(int *)value;
	}
}

/* @PROP: UCIPGroupFinishPUB
 * @LABEL: UCIPGroup Finish
 * @TYPE: BOOL
 * @DEFAULT: false
 * @FLAG: WRONLY
 * @PATH: /ImagingCtr/ImagingCtr
 * @HELP: Finish custom imaging parameters group configuration
 */
void _ucip_group_finish_prop_hook_(int id, cpropr_hook_type_t type, void *value)
{
	if (CPROPR_HOOK_READ == type)
	{
		*(int *)value = 0; // always false
	}
	else if (CPROPR_HOOK_WRITE == type && value)
	{
		_apply_configer_(); // apply configer anyway
	}
}

/* @PROP: UCIPGroupResetPUB
 * @LABEL: UCIPGroup Reset
 * @TYPE: BOOL
 * @DEFAULT: false
 * @FLAG: WRONLY
 * @PATH: /ImagingCtr/ImagingCtr
 * @HELP: Reset custom imaging parameters group configuration
 */
void _ucip_group_reset_prop_hook_(int id, cpropr_hook_type_t type, void *value)
{
	if (CPROPR_HOOK_READ == type)
	{
		*(int *)value = 0; // always false
	}
	else if (CPROPR_HOOK_WRITE == type && value)
	{
		_reset_configer_(); // reset configer anyway
		for (int i = 0; i < 8; i++)
		{
			autosdk_notify(MD_NOTIFY_PROP(PROP_UCIPGROUP1ENABLE + i));
			autosdk_notify(MD_NOTIFY_PROP(PROP_UCIPGROUP1EXPO + i));
			autosdk_notify(MD_NOTIFY_PROP(PROP_UCIPGROUP1GAIN + i));
			autosdk_notify(MD_NOTIFY_PROP(PROP_UCIPGROUP1LUMINLEVEL + i));
			autosdk_notify(MD_NOTIFY_PROP(PROP_UCIPGROUP1HOLDTIME + i));
		}
	}
}
