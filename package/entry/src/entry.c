//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: entry.c
// Author		: XuCF
// Created On	: 2024/12/20
// Description	: entry.c
//
// History
// 1. V1.0, Created by XuCF. 2024/12/20
//=============================================================================

#include "cbeans.cst.h"
#include "MdNotice.h"

any VscanBasic_Initialize(non)
{
	VscanBasic basic;
	VscanBasic_Init(&basic);
	return 0;
}

any VscanServer_Initialize(non)
{
	VscanServer server;
	VscanServer_Init(&server);
	MdNotice_Invoke(server.booter, 0, NULL, 0);
#ifdef BEAN_BOOT_NOTICE
	MdNotice_Invoke(cbeans_find(BEAN_BOOT_NOTICE), 0, NULL, 0);
#endif
	return 0;
}
