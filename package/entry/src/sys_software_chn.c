//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: sys_software_chn.c
// Author		: XuCF
// Created On	: 2024/12/23
// Description	: sys_software_chn.c
//
// History
// 1. V1.0, Created by XuCF. 2024/12/23
//=============================================================================

#include "MdWriter.h"
#include "MdBuffer.h"
#include "MdNotice.h"
#include "cbeans.h"
#include "system_input.h"
#include "system_msg_manager.h"

ClassDefined(SysSoftwareChn)
{
	MdWriter *writer;
};

static int _received_(SysSoftwareChn *thiz, MdBuffer *buffer, any ndata, snv nsize)
{
	sys_input_data_info msg_info;
	if (nsize <= 0) return 0;
	msg_info.irq_type = 0; // not used
	msg_info.data_len = buffer->size;
	msg_info.data_pt = buffer->data;
	MsgManager->SendMessage(Message(Msg_Exe_Data_In_SOFTWARE, &msg_info, sizeof(msg_info)));
	MdBuffer_Drop(buffer);
	return buffer->size;
}

any SysSoftwareChn_New(any notice, any writer)
{
	Assert(!notice, return 0);
	Assert(!writer, return 0);
	SysSoftwareChn *thiz = clike_new(SysSoftwareChn);
	MdNotice_AttachUser(notice, _received_, thiz, 0);
	thiz->writer = writer;
	return thiz;
}

int SysSoftwareChn_Write(void *data, int size)
{
	SysSoftwareChn *thiz = cbeans_find(BEAN_SYS_SOFTWARE_CHN);
	Assert(!thiz || !thiz->writer, return -1);
	return MdWriter_Write(thiz->writer, data, size);
}
