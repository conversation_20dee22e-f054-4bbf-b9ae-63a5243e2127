//=============================================================================
// Copyright (C), 2004-2025, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: legacy_config_notify.c
// Author		: XuCF
// Created On	: 2025/06/30
// Description	: legacy_config_notify.c
//
// History
// 1. V1.0, Created by XuCF. 2025/06/30
//=============================================================================

#include "libcustom.h"
#include "autosdk_notify.h"
#include "system_msg_manager.h"
#include "system_config/system_cfg.h"
#include "legacy_config_table.h"

static SMsgHandler _legacy_config_msg_handler_;

static void _legacy_config_handle_message_(SMsg msg)
{
	if (msg.id == Msg_Exe_Param_Cfg_Content)
	{
		u8 *para_info = (u8 *)msg.info;
		u16 para_code = 0;

		// Extract parameter code from message info (same as service_power.c)
		para_code = para_info[MACR_SYS_CFG_PCMI_PACK_OFFSET_PARA_CODE];
		para_code += ((u16)para_info[MACR_SYS_CFG_PCMI_PACK_OFFSET_PARA_CODE + 1]) << 8;

		if ((para_code == MACR_PARACODE_9500) ||
			(para_code == MACR_PARACODE_9501) ||
			(para_code == MACR_PARACODE_9502))
		{
			autosdk_notify(MD_NOTIFY_PROP(_ALL_CONFIG_NOTIFY_ID_));
			return;
		}

		// Use mapping table to convert legacy code to notification ID
		int notify_id = legacy_code_to_notify_id(para_code);

		// Send notification
		autosdk_notify(notify_id);
	}
}

any LegacyConfigNotify_New(void)
{
	_legacy_config_msg_handler_.HandleMessage = _legacy_config_handle_message_;

	s32 result = MsgManager->AttachMessageMap(&_legacy_config_msg_handler_, Msg_Exe_Param_Cfg_Content);

	// Return a dummy pointer to indicate success/failure
	return result == MACR_SUCCESS ? (any)&_legacy_config_msg_handler_ : 0;
}

non LegacyConfigNotify_Del(any obj)
{
	MsgManager->DetachMessageMap(&_legacy_config_msg_handler_, Msg_Exe_Param_Cfg_Content);
}
