// Auto generated by cpropr.py, DO NOT EDIT!

#ifndef __CPROPR_CTX_H__
#define __CPROPR_CTX_H__

#include "cpropr_types.h"
#include "cpropr.cfg.h"

// Hook function declarations
void _temp_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ucip_group_finish_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ucip_group_enable_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ucip_group_expo_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ucip_group_gain_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ucip_group_lumin_level_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ucip_group_hold_time_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _illumination_mode_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _illumination_level_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _light_group_select_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _aim_mode_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _data_filter_mode_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _data_filter_enable_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _data_filter_func_normal_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _advanced_filter_func_advanced_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _data_filter_regex_normal_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _data_filter_regex_advanced_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _aadv_set_deco_time_ms_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _double_confirm_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _same_bar_delay_1d_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _same_bar_delay_2d_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _good_read_delay_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code_symb_1d_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code_symb_2d_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _multi_symb_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _centering_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _screen_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code39_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code39_chk_dig_ver_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code39_chk_dig_tx_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code39_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code39_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code39_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code39_insert_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code39_format_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code39_sted_trans_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code39_aste_as_char_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code39_code32_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code39_code32_pre_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code39_trioptic_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code39_to_sted_trans_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code39_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code128_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code128_chk_dig_ver_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code128_chk_dig_tx_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code128_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code128_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code128_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code128_insert_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code128_tru_zero_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code128_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _codabar_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _codabar_check_digit_verify_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _codabar_check_digit_transmit_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _codabar_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _codabar_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _codabar_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _codabar_insert_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _codabar_start_stop_type_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _codabar_start_stop_transmit_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _codabar_start_end_char_eq_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _codabar_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _intl25_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _intl25_chk_dig_ver_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _intl25_chk_dig_tx_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _intl25_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _intl25_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _intl25_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _intl25_insert_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _intl25_len_restr_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _intl25_bank_format_conv_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _intl25_bank_format_type_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _intl25_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code93_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code93_check_digit_verify_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code93_check_digit_transmit_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code93_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code93_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code93_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code93_insert_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code93_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ean8_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ean8_chk_dig_ver_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ean8_chk_dig_tx_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ean8_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ean8_insert_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ean8_supl_dig_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ean8_trun_exp_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ean8_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ean13_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ean13_chk_dig_ver_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ean13_chk_dig_tx_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ean13_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ean13_insert_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ean13_supl_dig_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ean13_isbsn_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ean13_issn_isbn_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ean13_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _upca_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _upca_chk_dig_ver_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _upca_chk_dig_tx_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _upca_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _upca_insert_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _upca_supl_dig_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _upca_trun_exp_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _upca_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _upce_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _upce_chk_dig_ver_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _upce_chk_dig_tx_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _upce_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _upce_insert_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _upce_supl_dig_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _upce_trun_exp_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _upce_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code11_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code11_chk_dig_ver_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code11_check_digit_transmit_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code11_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code11_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code11_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code11_insert_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code11_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _gs1expa_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _gs1expa_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _gs1expa_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _gs1expa_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _gs1expa_insert_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _gs1expa_conversion_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _gs1expa_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _gs1db_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _gs1db_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _gs1db_insert_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _gs1db_conversion_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _gs1db_output_brackets_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _gs1db_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _gs1limi_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _gs1limi_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _gs1limi_insert_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _gs1limi_conversion_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _gs1limi_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _isbt128_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _isbt128_ck_digi_ver_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _isbt128_chk_dig_trans_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _isbt128_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _isbt128_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _isbt128_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _isbt128_insert_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _isbt128_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _msipl_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _msipl_chk_dig_ver_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _msipl_chk_dig_tx_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _msipl_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _msipl_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _msipl_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _msipl_insert_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _msipl_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _uccean128_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _uccean128_chk_dig_ver_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _uccean128_chk_dig_tx_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _uccean128_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _uccean128_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _uccean128_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _uccean128_insert_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _uccean128_tru_zero_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _uccean128_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ukpl_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ukpl_chk_dig_ver_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ukpl_chk_dig_tx_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ukpl_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ukpl_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ukpl_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ukpl_insert_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ukpl_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _upce1_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _upce1_chk_dig_ver_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _upce1_chk_dig_trans_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _upce1_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _upce1_insert_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _upce1_supl_dig_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _upce1_trun_exp_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _upce1_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _chnpst_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _chnpst_chk_dig_ver_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _chnpst_chk_dig_tx_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _chnpst_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _chnpst_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _chnpst_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _chnpst_insert_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _chnpst_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _industrial25_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _industrial25_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _industrial25_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _industrial25_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _industrial25_insert_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _industrial25_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _matrix25_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _matrix25_check_digit_verify_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _matrix25_check_digit_transmit_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _matrix25_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _matrix25_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _matrix25_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _matrix25_insert_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _matrix25_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _qrcode_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _qrcode_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _qrcode_web_code_forbid_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _qrcode_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _qrcode_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _qrcode_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _dm_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _dm_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _dm_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _dm_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _dm_ppn_code_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _dm_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _aztec_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _aztec_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _aztec_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _aztec_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _aztec_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _coda_block_f_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _coda_block_f_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _coda_block_f_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _coda_block_f_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _codablockf_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _gm_code_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _gm_code_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _gm_code_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _gm_code_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _maxicode_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _maxicode_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _maxicode_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _maxicode_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _micpdf417_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _micpdf417_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _micpdf417_insert_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _micpdf417_conversion_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _micpdf417_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _micpdf417_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _micpdf417_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _micro_qr_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _micro_qr_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _micro_qr_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _micro_qr_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _microqr_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _pdf417_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _pdf417_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _pdf417_insert_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _pdf417_conversion_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _pdf417_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _pdf417_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _pdf417_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _binary_code_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _binary_code_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _binary_code_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _binary_code_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _binary_code_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _cs_code_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _cs_code_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _cs_code_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _cs_code_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _cscode_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _dot_code_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _dot_code_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _dot_code_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _dot_code_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _dot_code_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _chnfnc_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _chnfnc_max_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _chnfnc_min_len_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _chnfnc_chk_dig_ver_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _chnfnc_frt_char_conv_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _chnfnc_frt_char_spec_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _chnfnc_code_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _chnfnc_insert_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _chnfnc_aim_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_deco_roi_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _netled_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _insert_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _back_tx_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _no_read_output_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _data_batch_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _scan_mode_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _scan_keep_time_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _scan_keep_time_advanced_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _movement_scan_mode_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _power_indication_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _led_indication_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _beep_indication_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _beep_time_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _beep_volume_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _beep_tone_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _power_led_ctrl_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_mode_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_incount_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_debounce_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_revent_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_rdelay_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_fevent_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_fdelay_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_active_cond_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_inactive_cond_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_active_delay_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_inactive_timer_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_active_cmd_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _custom_io_inactive_cmd_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _char_encode_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _char_print_ctr_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _decode_result_max_num_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _first_only_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _last_only_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _sing_repla_c1_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _sing_repla_c2_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _sing_repla_c3_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _repla_char2str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _case_conv_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _fn1_substu_text_enable_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _characters_to_strings_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _deco_rslt_check_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _deco_rslt_check_1head_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _deco_rslt_check_1tail_str_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _deco_result_check_fail_indication_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _group1_text_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _group2_text_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _group3_text_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _group4_text_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _group1_pos_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _group2_pos_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _group3_pos_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _group4_pos_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _usb_ethernet_composite_enable_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _usb_ethernet_composite_type_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ethernet_ip_enable_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _modbus_server_enable_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _uart_enable_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _profinet_enable_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _profinet_station_name_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _tcp_client_enable_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _tcp_target_server_ip_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _tcp_target_server_port_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _tcp_client_output_format_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _tcp_client_output_prefix_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _tcp_client_output_suffix_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _tcp_server_enable_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _tcp_server_output_format_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _tcp_server_output_prefix_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _tcp_server_output_suffix_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _tcp_server_service_port_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _rs232_flow_control_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _rs232_char_delay_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _rs232_response_delay_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _rs232_baud_rate_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _rs232_parity_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _rs232_data_bits_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _rs232_stop_bits_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _rs232_host_type_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _rs232_deco_data_format_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _rs232_hs_char_timeout_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _usb_type_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _usb_keyboard_layout_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _usb_key_delay_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _usb_numb_key_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _usb_func_key_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _usb_host_polling_inter_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _usb_key_send_mode_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _usb_enter_mode_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _usb_mod_keys_release_mode_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _usb_key_tx_timeout_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _usb_enum_fail_reboot_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _focuser_value_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _config_by_code_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _interface_type_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _rs232_byte_delay_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code_max_len_1d_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code_min_len_1d_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code_element_amend_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _decode_optimize_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _output_delay_cont_scan_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _same_bar_delay_dr_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _sweeping_scan_enhance_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _vibrator_indication_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _silent_mode_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _beep_sound_mode_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _indication_tx_delay_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _voice_delay_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _infrared_sensor_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _infrared_mode_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _auto_dete_keep_time_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _auto_dete_texture_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _auto_dete_lum_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _auto_dete_txture_inter_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _auto_dete_sleep_frm_inter_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _infrared_msg_switch_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _infrared_lum_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ir_lum_dete_sen_level_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _dpm_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _composite_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ocr_recognize_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ocr_recognize_passport_mrz_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ocr_recognize_national_card_id_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ocr_turkish_id_output_format_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ocr_double_check_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ivd_dete_tube_cap_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ivd_dete_tube_height_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _aim_keep_time_after_deco_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _light_drive_capacity_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _light_max_per_led_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _light_group1_current_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _light_group2_current_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _aim_keep_time_before_deco_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _auxiliary_lum_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _auxiliary_lum_comp_time_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ivd_tube_hight_info_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code_rslt_tx_indi_dly_mode_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _deco_stay_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _packet_output_data_ctr_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _add_watermark_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _instand_indicate_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _qr_terminator_pos_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code_err_output_2d_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code_err_level_2d_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code_version_2d_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _cont_mode_lum_level_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _cont_mode_no_action_dura_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _time_to_reboot_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _image_flip_mode_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _attach_code_pos_option_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _last_succ_imaging_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _hi_lum_4hd_code_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _sys_recover_indication_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _output_multiple_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _config_by_code_fb_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _query_config_by_code_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _result_check_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _rainbow_read_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _power_up_report_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _exp_reboot_indication_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _decode_vertex_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _aging_test_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _prefix_text_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _prefix_transmission_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _suffix_text_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _preamb_text_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _postamb_text_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _fn1_substu_text_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _trunc_lead_g5_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _repeat_g5_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _trunc_end_g6_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _repeat_g6_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code_id_pos_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _suffix_text_enable_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code_name_enable_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _preamb_text_enable_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _postamb_text_enable_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code_id_text_enable_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _code_len_enable_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _none_print_str_enable_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ban_special_keys_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _text_edit_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _invoice_cust_str1_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _invoice_cust_str2_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _invoice_cust_str3_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _invoice_cust_str4_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _backup_deco_img_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _on_time_trigger_decode_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _ucip_group_reset_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _vision_app_prop_hook_(int id, cpropr_hook_type_t type, void *value);

// Options for IlluminationMode
static const char *_illuminationmode_names_[] = {
    "ALWAYS_OFF",
    "ALWAYS_ON",
    "FLASH",
    "ON_WHEN_READING",
    0
};
// Options for IlluminationLevel
static const char *_illuminationlevel_names_[] = {
    "LOW",
    "MID",
    "HIGH",
    0
};
// Options for LightGroupSelect
static const char *_lightgroupselect_names_[] = {
    "GROUP1",
    "GROUP2",
    0
};
// Options for DataFilterMode
static const char *_datafiltermode_names_[] = {
    "Disable",
    "Normal",
    "Advanced",
    0
};
// Options for DataFilterFunc
static const char *_datafilterfunc_names_[] = {
    "FILTER",
    "DELETE",
    0
};
// Options for CodeSymb1D
static const char *_codesymb1d_names_[] = {
    "AS_PARA",
    "DISABLE_ALL",
    "ENABLE_ALL",
    0
};
// Options for CodeSymb2D
static const char *_codesymb2d_names_[] = {
    "AS_PARA",
    "DISABLE_ALL",
    "ENABLE_ALL",
    "PDF417_ONLY",
    "QRCODE_ONLY",
    "DATAMATRIX_ONLY",
    "MAXICODE_ONLY",
    "AZTECCODE_ONLY",
    "CSCODE_ONLY",
    0
};
// Options for MultiSymbRead
static const char *_multisymbread_names_[] = {
    "DISABLE",
    "ONLY_1D",
    "ONLY_2D",
    "BOTH_1D_2D",
    0
};
// Options for CenteringRead
static const char *_centeringread_names_[] = {
    "NONE",
    "VERTICAL",
    "NEAR",
    0
};
// Options for Code39Format
static const char *_code39format_names_[] = {
    "STANDARD",
    "FULL_ASCII",
    0
};
// Options for Code128TruZero
static const char *_code128truzero_names_[] = {
    "DISABLE",
    "ALL",
    "FIRST",
    0
};
// Options for CodabarStartStopType
static const char *_codabarstartstoptype_names_[] = {
    "ABCD",
    "abcd",
    "TNE",
    "tne",
    0
};
// Options for Intl25ChkDigVer
static const char *_intl25chkdigver_names_[] = {
    "DISABLE",
    "USS",
    "OPCC",
    0
};
// Options for Intl25LenRestr
static const char *_intl25lenrestr_names_[] = {
    "ANY",
    "ONLY_44",
    0
};
// Options for Intl25BankFormatType
static const char *_intl25bankformattype_names_[] = {
    "NO_TAB",
    "ALGUNS",
    "TODOS",
    0
};
// Options for EAN8SuplDig
static const char *_ean8supldig_names_[] = {
    "NONE",
    "TWO_DIGITS",
    "FIVE_DIGITS",
    "TWO_OR_FIVE",
    "ONLY_WITH_SUPPLEMENTAL",
    0
};
// Options for EAN8TrunExp
static const char *_ean8trunexp_names_[] = {
    "NONE",
    "TRUNCATE",
    "EXPAND",
    0
};
// Options for EAN13SuplDig
static const char *_ean13supldig_names_[] = {
    "NONE",
    "TWO_DIGITS",
    "FIVE_DIGITS",
    "TWO_OR_FIVE",
    "ONLY_WITH_SUPPLEMENTAL",
    0
};
// Options for UPCASuplDig
static const char *_upcasupldig_names_[] = {
    "NONE",
    "TWO_DIGITS",
    "FIVE_DIGITS",
    "TWO_OR_FIVE",
    "ONLY_WITH_SUPPLEMENTAL",
    0
};
// Options for UPCATrunExp
static const char *_upcatrunexp_names_[] = {
    "NONE",
    "TRUNCATE",
    "EXPAND",
    0
};
// Options for UPCESuplDig
static const char *_upcesupldig_names_[] = {
    "NONE",
    "TWO_DIGITS",
    "FIVE_DIGITS",
    "TWO_OR_FIVE",
    "ONLY_WITH_SUPPLEMENTAL",
    0
};
// Options for UPCETrunExp
static const char *_upcetrunexp_names_[] = {
    "NONE",
    "TRUNCATE",
    "EXPAND_EAN13",
    "EXPAND_UPCA",
    0
};
// Options for Code11ChkDigVer
static const char *_code11chkdigver_names_[] = {
    "NONE",
    "ONE",
    "TWO",
    "RESERVED",
    0
};
// Options for GS1EXPAConversion
static const char *_gs1expaconversion_names_[] = {
    "NONE",
    "UCC_EAN128",
    0
};
// Options for GS1DBConversion
static const char *_gs1dbconversion_names_[] = {
    "NONE",
    "UCC_EAN128",
    "UPC_EAN13",
    0
};
// Options for GS1LIMIConversion
static const char *_gs1limiconversion_names_[] = {
    "NONE",
    "UCC_EAN128",
    "UPC_EAN13",
    0
};
// Options for MSIPLChkDigVer
static const char *_msiplchkdigver_names_[] = {
    "DISABLE",
    "ONE_MOD10",
    "TWO_MOD10",
    "MOD10_MOD11",
    0
};
// Options for UCCEAN128TruZero
static const char *_uccean128truzero_names_[] = {
    "DISABLE",
    "ALL",
    "FIRST",
    0
};
// Options for UPCE1SuplDig
static const char *_upce1supldig_names_[] = {
    "NONE",
    "TWO",
    "FIVE",
    "TWO_FIVE",
    "ONLY",
    0
};
// Options for UPCE1TrunExp
static const char *_upce1trunexp_names_[] = {
    "NONE",
    "TRUNCATE",
    "EXPAND_EAN13",
    "EXPAND_UPCA",
    0
};
// Options for QRCodeWebCodeForbid
static const char *_qrcodewebcodeforbid_names_[] = {
    "ALLOW",
    "FORBID",
    "DOUYIN_ONLY",
    0
};
// Options for MICPDF417Conversion
static const char *_micpdf417conversion_names_[] = {
    "OFF",
    "UCCEAN128",
    "UPCA_EAN13",
    0
};
// Options for PDF417Conversion
static const char *_pdf417conversion_names_[] = {
    "NONE",
    "UCC_EAN128",
    "UPC_EAN13",
    0
};
// Options for CHNFNCFrtCharConv
static const char *_chnfncfrtcharconv_names_[] = {
    "DISABLE",
    "ENABLE",
    "FIVE_TO_A",
    "SIX_TO_B",
    "SEVEN_TO_C",
    "EIGHT_TO_D",
    "NINE_TO_E",
    0
};
// Options for CHNFNCFrtCharSpec
static const char *_chnfncfrtcharspec_names_[] = {
    "DISABLE",
    "ZERO",
    "FIVE_OR_A",
    "SIX_OR_B",
    "SEVEN_OR_C",
    "EIGHT_OR_D",
    "NINE_OR_E",
    "ONE",
    "TWO",
    "THREE",
    "FOUR",
    0
};
// Options for NetLed
static const char *_netled_names_[] = {
    "LINK",
    "TX",
    "RX",
    0
};
// Options for DataBatch
static const char *_databatch_names_[] = {
    "NONE",
    "OUT_RANGE",
    "STANDARD",
    "STANDARD_MANUAL",
    0
};
// Options for ScanMode
static const char *_scanmode_names_[] = {
    "GoodReadOff",
    "Mome",
    "Alter",
    "Cont",
    "GoodReadOn",
    "SciTrigger",
    "AutoDete",
    "AutoDeteIMG",
    "AutoDeteIR_GoodReadOn",
    "AutoDeteIR_GoodReadOff",
    "AutoDeteIR_Mome",
    "Desktop",
    "ButtonCont",
    "MultiSymbolsCont",
    0
};
// Options for MovementScanMode
static const char *_movementscanmode_names_[] = {
    "Standard",
    "Enhance",
    0
};
// Options for BeepVolume
static const char *_beepvolume_names_[] = {
    "LOW",
    "MID",
    "HIGH",
    "LOW2",
    "LOW3",
    "LOW4",
    "LOW5",
    "LOW6",
    "LOW7",
    "LOW8",
    0
};
// Options for BeepTone
static const char *_beeptone_names_[] = {
    "LEVEL0",
    "LEVEL1",
    "LEVEL2",
    "LEVEL3",
    0
};
// Options for CustomIOModeMain
static const char *_customiomodemain_names_[] = {
    "Close",
    "InputHigh",
    "InputLow",
    "OutputHigh",
    "OutputLow",
    0
};
// Options for CustomIORisingAction
static const char *_customiorisingaction_names_[] = {
    "None",
    "StartDecode",
    "EndDecode",
    0
};
// Options for CustomIOFallingAction
static const char *_customiofallingaction_names_[] = {
    "None",
    "StartDecode",
    "EndDecode",
    0
};
// Options for CustomIOActiveCondition
static const char *_customioactivecondition_names_[] = {
    "None",
    "DecodeSuccess",
    "NoRead",
    "DecodeStarted",
    "DecodeStopped",
    "Command",
    "Timer",
    "Input1High",
    "Input2High",
    "Input3High",
    "Input4High",
    "Input1Low",
    "Input2Low",
    "Input3Low",
    "Input4Low",
    0
};
// Options for CustomIOInactiveCondition
static const char *_customioinactivecondition_names_[] = {
    "None",
    "DecodeSuccess",
    "NoRead",
    "DecodeStarted",
    "DecodeStopped",
    "Command",
    "Timer",
    "Input1High",
    "Input2High",
    "Input3High",
    "Input4High",
    "Input1Low",
    "Input2Low",
    "Input3Low",
    "Input4Low",
    0
};
// Options for CharEncode
static const char *_charencode_names_[] = {
    "ASCII",
    "UTF8",
    "Windows1251",
    "SimpChinese",
    "TraChinese",
    "Windows1250",
    "KOI8R",
    "Japanese",
    0
};
// Options for CharPrintCtr
static const char *_charprintctr_names_[] = {
    "Disable",
    "Printable",
    "Alphanumeric",
    0
};
// Options for CaseConv
static const char *_caseconv_names_[] = {
    "Disable",
    "UpperDatOnly",
    "LowerDatOnly",
    "UpperWholeStr",
    "LowWholeStr",
    0
};
// Options for DecoRsltCheck
static const char *_decorsltcheck_names_[] = {
    "Disable",
    "EnableAll",
    "HeadCheckOnly",
    "TailCheckOnly",
    0
};
// Options for USBEthernetCompositeType
static const char *_usbethernetcompositetype_names_[] = {
    "Keyboard",
    "VCOM",
    0
};
// Options for RS232FlowControl
static const char *_rs232flowcontrol_names_[] = {
    "None",
    "RtsCtsLow",
    "RtsCtsHigh",
    "XonXoff",
    "AckNak",
    "RtsLow",
    "AckNakRtsCtsHigh",
    "CtsScan",
    0
};
// Options for RS232CharDelay
static const char *_rs232chardelay_names_[] = {
    "Forbid",
    "Delay5ms",
    "Delay10ms",
    "Delay20ms",
    "Delay40ms",
    "Delay80ms",
    0
};
// Options for RS232BaudRate
static const char *_rs232baudrate_names_[] = {
    "BR9600",
    "BR19200",
    "BR38400",
    "BR57600",
    "BR115200",
    0
};
// Options for RS232Parity
static const char *_rs232parity_names_[] = {
    "None",
    "Odd",
    "Even",
    0
};
// Options for RS232DataBits
static const char *_rs232databits_names_[] = {
    "Bits8",
    "Bits7",
    0
};
// Options for RS232StopBits
static const char *_rs232stopbits_names_[] = {
    "Bits1",
    "Bits2",
    0
};
// Options for RS232HostType
static const char *_rs232hosttype_names_[] = {
    "Standard",
    "OPOS",
    "MDAux",
    0
};
// Options for RS232DecoDataFormat
static const char *_rs232decodataformat_names_[] = {
    "Raw",
    "Packed",
    0
};
// Options for USBType
static const char *_usbtype_names_[] = {
    "PC",
    "MAC",
    "VISUAL_COM",
    "SIMPLE_COM",
    "OPOS",
    "CUST_VISUAL_COM",
    "IBM_HAND_HELD",
    "HID_MSD",
    "CUSTOM_BULK",
    "CUSTOM_HID_KEYBOARD_HS_BULK",
    "PC_LEGACY_KEYBOARD",
    "VCOM_OPOS",
    "CUSTOM_HID_INT_HS_BULK",
    "CUSTOM_HID_KBD_HID_CUST",
    "CUSTOM_HID_KBD_VISUAL_COM",
    "NET",
    "CUSTOM_HID_KBD_NET",
    "CUSTOM_VISUAL_COM_NET",
    "CUSTOM",
    0
};
// Options for USBKeyboardLayout
static const char *_usbkeyboardlayout_names_[] = {
    "CHN_USA",
    "TKYF",
    "TKYQ",
    "FRN",
    "ITA",
    "SPA",
    "SLK",
    "DMK",
    "JAP",
    "GER",
    "BEL",
    "RUS",
    "CZE",
    "THAI",
    "HUNGARY",
    "SWISS_GERMAN",
    "PORTUGUESE",
    "UKRAINIAN",
    "POLISH214",
    "VIETNAM",
    "RUSSIAN_ANDROID",
    "VIETNAM_TELEX",
    0
};
// Options for USBKeyDly
static const char *_usbkeydly_names_[] = {
    "DELAY_0MS",
    "DELAY_5MS",
    "DELAY_10MS",
    "DELAY_20MS",
    "DELAY_40MS",
    "DELAY_60MS",
    0
};
// Options for USBNumbKey
static const char *_usbnumbkey_names_[] = {
    "DISABLE",
    "NUM",
    "ALT_KEYPAD",
    "ALT_KEYPAD_GBK",
    "RAW_HEX_IN_STRINGS",
    "UOS_UNICODE_IN_DEC",
    0
};
// Options for USBFuncKey
static const char *_usbfunckey_names_[] = {
    "NON_PRINTABLE",
    "MINDEO",
    "CUSTOMIZE_PPN",
    "FORBID",
    "DATALOGIC_CTRL_CHAR_00",
    0
};
// Options for USBHostPollingInter
static const char *_usbhostpollinginter_names_[] = {
    "INTERVAL_1MS",
    "INTERVAL_2MS",
    "INTERVAL_5MS",
    "INTERVAL_8MS",
    0
};
// Options for USBKeySendMode
static const char *_usbkeysendmode_names_[] = {
    "DISCONTINUOUS",
    "CONTINUOUS",
    0
};
// Options for USBEnterMode
static const char *_usbentermode_names_[] = {
    "ACCORDING_FUNC_KEY",
    "FORCE_ALPHABET_ENTER",
    "FORCE_NUMERIC_ENTER",
    "FORCE_ALT_013_ENTER",
    0
};
// Options for USBModKeysReleaseMode
static const char *_usbmodkeysreleasemode_names_[] = {
    "SAME_TIME",
    "AFTER_NORMAL_KEYS",
    0
};
// Options for InterfaceTypeMain
static const char *_interfacetypemain_names_[] = {
    "Auto",
    "RS232",
    "USB",
    "RS232_ETH",
    "RS232_USB",
    "SOFTWARE",
    0
};
// Options for SweepingScanEnhance
static const char *_sweepingscanenhance_names_[] = {
    "LowSpeed",
    "MediumSpeed",
    "HighSpeed",
    0
};
// Options for BeepSoundMode
static const char *_beepsoundmode_names_[] = {
    "MODE_0",
    "MODE_1",
    "MODE_2",
    "MODE_3",
    "MODE_4",
    0
};
// Options for InfraredMode
static const char *_infraredmode_names_[] = {
    "IN_STAND",
    "CONTINUE",
    0
};
// Options for AutoDeteLum
static const char *_autodetelum_names_[] = {
    "ALWAYS_OFF",
    "ON_IN_DARKNESS",
    "ALWAYS_ON",
    0
};
// Options for AutoDeteTxtureInter
static const char *_autodetetxtureinter_names_[] = {
    "INTERVAL_0S",
    "INTERVAL_5S",
    "INTERVAL_10S",
    "INTERVAL_30S",
    "INTERVAL_60S",
    "INTERVAL_INFINITY",
    0
};
// Options for AutoDeteSleepFrmInter
static const char *_autodetesleepfrminter_names_[] = {
    "INTERVAL_250MS",
    "INTERVAL_500MS",
    "INTERVAL_1000MS",
    0
};
// Options for CompositeRead
static const char *_compositeread_names_[] = {
    "DISABLE",
    "DATABAR_GS1_128",
    "ALL",
    0
};
// Options for OCRRecognizeMain
static const char *_ocrrecognizemain_names_[] = {
    "AS_PARA",
    "DISABLE_ALL",
    "ENABLE_ALL",
    0
};
// Options for OCRTurkishIDOutputFormat
static const char *_ocrturkishidoutputformat_names_[] = {
    "ORIGINAL",
    "CUSTOMIZED",
    0
};
// Options for CodeRsltTxIndiDlyMode
static const char *_coderslttxindidlymode_names_[] = {
    "NONE",
    "ONLY_TX",
    "TX_AND_INDI",
    0
};
// Options for AddWatermark
static const char *_addwatermark_names_[] = {
    "DISABLE",
    "WITHOUT_BACKGROUND",
    "WITH_BACKGROUND",
    0
};
// Options for QRTerminatorPos
static const char *_qrterminatorpos_names_[] = {
    "FIRST",
    "SECOND",
    0
};
// Options for ContModeLumLevel
static const char *_contmodelumlevel_names_[] = {
    "LOW",
    "MID",
    "HIGH",
    0
};
// Options for TimeToReboot
static const char *_timetoreboot_names_[] = {
    "FORBID",
    "SECS_30",
    "MINS_5",
    "MINS_15",
    "MINS_30",
    "HOUR_1",
    "HOURS_2",
    "HOURS_3",
    "HOURS_4",
    "HOURS_8",
    "HOURS_12",
    "HOURS_24",
    "HOURS_48",
    0
};
// Options for ImageFlipMode
static const char *_imageflipmode_names_[] = {
    "NONE",
    "HORIZONTAL",
    "VERTICAL",
    "DIAGONAL",
    0
};
// Options for AgingTest
static const char *_agingtest_names_[] = {
    "OFF",
    "MODE_1",
    "MODE_2",
    "MODE_3",
    "MODE_4",
    0
};
// Options for CodeIDPos
static const char *_codeidpos_names_[] = {
    "Before",
    "After",
    0
};
// Options for CodeIDTextEnable
static const char *_codeidtextenable_names_[] = {
    "Disable",
    "PropID",
    "AIMID",
    "Both",
    0
};
// Options for TextEdit
static const char *_textedit_names_[] = {
    "Disable",
    "Invoice",
    "Evotrue",
    "Prowill",
    "Inspur",
    "Elgin",
    "Pulsa",
    "GeLunBu",
    "ShengXiaoBang",
    "XinGuoDu_GSGL",
    "InvoiceCust",
    0
};
// Options for BackupDecoImg
static const char *_backupdecoimg_names_[] = {
    "OFF",
    "Both",
    "Succ",
    0
};
// Options for VisionApp
static const char *_visionapp_names_[] = {
    "OFF",
    "OCR",
    "Class",
    "DeteFeature",
    0
};
static cpropr_item_t *_cpropr_items_[] = {
    &(cpropr_item_t){
        .id = PROP_TEMPERATURE,
        .name = "Temperature",
        .type = CPROPR_TYPE_FLOAT,
        .range = { .f = { -40.0, 85.0 } },
        .defval = { .f = -40.0 },
        .flags = CPROPR_FLAG_RDONLY,
        .children = 0,
        .hook = _temp_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUPFINISHPUB,
        .name = "UCIPGroupFinishPUB",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_WRONLY,
        .children = 0,
        .hook = _ucip_group_finish_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP1ENABLE,
        .name = "UCIPGroup1Enable",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP2ENABLE,
        .name = "UCIPGroup2Enable",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP3ENABLE,
        .name = "UCIPGroup3Enable",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP4ENABLE,
        .name = "UCIPGroup4Enable",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP5ENABLE,
        .name = "UCIPGroup5Enable",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP6ENABLE,
        .name = "UCIPGroup6Enable",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP7ENABLE,
        .name = "UCIPGroup7Enable",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP8ENABLE,
        .name = "UCIPGroup8Enable",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP1EXPO,
        .name = "UCIPGroup1Expo",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 1000 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_expo_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP2EXPO,
        .name = "UCIPGroup2Expo",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 1000 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_expo_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP3EXPO,
        .name = "UCIPGroup3Expo",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 1000 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_expo_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP4EXPO,
        .name = "UCIPGroup4Expo",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 1000 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_expo_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP5EXPO,
        .name = "UCIPGroup5Expo",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 1000 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_expo_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP6EXPO,
        .name = "UCIPGroup6Expo",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 1000 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_expo_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP7EXPO,
        .name = "UCIPGroup7Expo",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 1000 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_expo_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP8EXPO,
        .name = "UCIPGroup8Expo",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 1000 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_expo_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP1GAIN,
        .name = "UCIPGroup1Gain",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 8 } },
        .defval = { .i = 8 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_gain_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP2GAIN,
        .name = "UCIPGroup2Gain",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 8 } },
        .defval = { .i = 8 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_gain_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP3GAIN,
        .name = "UCIPGroup3Gain",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 8 } },
        .defval = { .i = 8 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_gain_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP4GAIN,
        .name = "UCIPGroup4Gain",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 8 } },
        .defval = { .i = 8 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_gain_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP5GAIN,
        .name = "UCIPGroup5Gain",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 8 } },
        .defval = { .i = 8 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_gain_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP6GAIN,
        .name = "UCIPGroup6Gain",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 8 } },
        .defval = { .i = 8 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_gain_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP7GAIN,
        .name = "UCIPGroup7Gain",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 8 } },
        .defval = { .i = 8 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_gain_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP8GAIN,
        .name = "UCIPGroup8Gain",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 8 } },
        .defval = { .i = 8 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_gain_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP1LUMINLEVEL,
        .name = "UCIPGroup1LuminLevel",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 32 } },
        .defval = { .i = 32 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_lumin_level_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP2LUMINLEVEL,
        .name = "UCIPGroup2LuminLevel",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 32 } },
        .defval = { .i = 32 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_lumin_level_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP3LUMINLEVEL,
        .name = "UCIPGroup3LuminLevel",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 32 } },
        .defval = { .i = 32 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_lumin_level_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP4LUMINLEVEL,
        .name = "UCIPGroup4LuminLevel",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 32 } },
        .defval = { .i = 32 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_lumin_level_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP5LUMINLEVEL,
        .name = "UCIPGroup5LuminLevel",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 32 } },
        .defval = { .i = 32 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_lumin_level_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP6LUMINLEVEL,
        .name = "UCIPGroup6LuminLevel",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 32 } },
        .defval = { .i = 32 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_lumin_level_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP7LUMINLEVEL,
        .name = "UCIPGroup7LuminLevel",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 32 } },
        .defval = { .i = 32 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_lumin_level_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP8LUMINLEVEL,
        .name = "UCIPGroup8LuminLevel",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 32 } },
        .defval = { .i = 32 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_lumin_level_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP1HOLDTIME,
        .name = "UCIPGroup1HoldTime",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 3600000 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_hold_time_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP2HOLDTIME,
        .name = "UCIPGroup2HoldTime",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 3600000 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_hold_time_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP3HOLDTIME,
        .name = "UCIPGroup3HoldTime",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 3600000 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_hold_time_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP4HOLDTIME,
        .name = "UCIPGroup4HoldTime",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 3600000 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_hold_time_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP5HOLDTIME,
        .name = "UCIPGroup5HoldTime",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 3600000 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_hold_time_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP6HOLDTIME,
        .name = "UCIPGroup6HoldTime",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 3600000 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_hold_time_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP7HOLDTIME,
        .name = "UCIPGroup7HoldTime",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 3600000 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_hold_time_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUP8HOLDTIME,
        .name = "UCIPGroup8HoldTime",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 3600000 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ucip_group_hold_time_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_ILLUMINATIONMODE,
        .name = "IlluminationMode",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _illuminationmode_names_ } },
        .defval = { .i = ILLUMINATIONMODE_E_ON_WHEN_READING },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _illumination_mode_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_ILLUMINATIONLEVEL,
        .name = "IlluminationLevel",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _illuminationlevel_names_ } },
        .defval = { .i = ILLUMINATIONLEVEL_E_HIGH },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _illumination_level_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_LIGHTGROUPSELECT,
        .name = "LightGroupSelect",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _lightgroupselect_names_ } },
        .defval = { .i = LIGHTGROUPSELECT_E_GROUP1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _light_group_select_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_AIMMODE,
        .name = "AimMode",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _aim_mode_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATAFILTERMODE,
        .name = "DataFilterMode",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _datafiltermode_names_ } },
        .defval = { .i = DATAFILTERMODE_E_Normal },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _data_filter_mode_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATAFILTERENABLE1,
        .name = "DataFilterEnable1",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _data_filter_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATAFILTERENABLE2,
        .name = "DataFilterEnable2",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _data_filter_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATAFILTERENABLE3,
        .name = "DataFilterEnable3",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _data_filter_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATAFILTERENABLE4,
        .name = "DataFilterEnable4",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _data_filter_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATAFILTERFUNC1,
        .name = "DataFilterFunc1",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _datafilterfunc_names_ } },
        .defval = { .i = DATAFILTERFUNC_E_FILTER },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _data_filter_func_normal_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATAFILTERFUNC2,
        .name = "DataFilterFunc2",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _datafilterfunc_names_ } },
        .defval = { .i = DATAFILTERFUNC_E_FILTER },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _data_filter_func_normal_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATAFILTERFUNC3,
        .name = "DataFilterFunc3",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _datafilterfunc_names_ } },
        .defval = { .i = DATAFILTERFUNC_E_FILTER },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _data_filter_func_normal_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATAFILTERFUNC4,
        .name = "DataFilterFunc4",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _datafilterfunc_names_ } },
        .defval = { .i = DATAFILTERFUNC_E_FILTER },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _data_filter_func_normal_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATAFILTERFUNC5,
        .name = "DataFilterFunc5",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _datafilterfunc_names_ } },
        .defval = { .i = DATAFILTERFUNC_E_FILTER },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _advanced_filter_func_advanced_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATAFILTERFUNC6,
        .name = "DataFilterFunc6",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _datafilterfunc_names_ } },
        .defval = { .i = DATAFILTERFUNC_E_FILTER },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _advanced_filter_func_advanced_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATAFILTERFUNC7,
        .name = "DataFilterFunc7",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _datafilterfunc_names_ } },
        .defval = { .i = DATAFILTERFUNC_E_FILTER },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _advanced_filter_func_advanced_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATAFILTERFUNC8,
        .name = "DataFilterFunc8",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _datafilterfunc_names_ } },
        .defval = { .i = DATAFILTERFUNC_E_FILTER },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _advanced_filter_func_advanced_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATAFILTERFUNC9,
        .name = "DataFilterFunc9",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _datafilterfunc_names_ } },
        .defval = { .i = DATAFILTERFUNC_E_FILTER },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _advanced_filter_func_advanced_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATAFILTERFUNC10,
        .name = "DataFilterFunc10",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _datafilterfunc_names_ } },
        .defval = { .i = DATAFILTERFUNC_E_FILTER },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _advanced_filter_func_advanced_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATAFILTERREGEX1,
        .name = "DataFilterRegex1",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 128 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _data_filter_regex_normal_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATAFILTERREGEX2,
        .name = "DataFilterRegex2",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 128 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _data_filter_regex_normal_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATAFILTERREGEX3,
        .name = "DataFilterRegex3",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 128 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _data_filter_regex_normal_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATAFILTERREGEX4,
        .name = "DataFilterRegex4",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 128 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _data_filter_regex_normal_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATAFILTERREGEX5,
        .name = "DataFilterRegex5",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 128 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _data_filter_regex_advanced_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATAFILTERREGEX6,
        .name = "DataFilterRegex6",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 128 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _data_filter_regex_advanced_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATAFILTERREGEX7,
        .name = "DataFilterRegex7",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 128 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _data_filter_regex_advanced_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATAFILTERREGEX8,
        .name = "DataFilterRegex8",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 128 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _data_filter_regex_advanced_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATAFILTERREGEX9,
        .name = "DataFilterRegex9",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 128 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _data_filter_regex_advanced_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATAFILTERREGEX10,
        .name = "DataFilterRegex10",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 128 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _data_filter_regex_advanced_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_AIMDECOVIDEOSETDECOTIMEMS,
        .name = "AimDecoVideoSetDecoTimeMs",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 3600000 } },
        .defval = { .i = 500 },
        .flags = CPROPR_FLAG_WRONLY,
        .children = 0,
        .hook = _aadv_set_deco_time_ms_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DOUBLECONFIRM,
        .name = "DoubleConfirm",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 9 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _double_confirm_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_SAMEBARDELAY1D,
        .name = "SameBarDelay1D",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 12750 } },
        .defval = { .i = 300 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _same_bar_delay_1d_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_SAMEBARDELAY2D,
        .name = "SameBarDelay2D",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 12750 } },
        .defval = { .i = 300 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _same_bar_delay_2d_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GOODREADDELAY,
        .name = "GoodReadDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 49500 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _good_read_delay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODESYMB1D,
        .name = "CodeSymb1D",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _codesymb1d_names_ } },
        .defval = { .i = CODESYMB1D_E_AS_PARA },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _code_symb_1d_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODESYMB2D,
        .name = "CodeSymb2D",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _codesymb2d_names_ } },
        .defval = { .i = CODESYMB2D_E_AS_PARA },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _code_symb_2d_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MULTISYMBREAD,
        .name = "MultiSymbRead",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _multisymbread_names_ } },
        .defval = { .i = MULTISYMBREAD_E_DISABLE },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _multi_symb_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CENTERINGREAD,
        .name = "CenteringRead",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _centeringread_names_ } },
        .defval = { .i = CENTERINGREAD_E_NONE },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _centering_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_SCREENREAD,
        .name = "ScreenRead",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _screen_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE39READMAIN,
        .name = "Code39ReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _code39_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE39CHKDIGVER,
        .name = "Code39ChkDigVer",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code39_chk_dig_ver_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE39CHKDIGTX,
        .name = "Code39ChkDigTx",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code39_chk_dig_tx_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE39MAXLEN,
        .name = "Code39MaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 99 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code39_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE39MINLEN,
        .name = "Code39MinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code39_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE39CODEID,
        .name = "Code39CodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 77 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code39_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE39INSERTSTR,
        .name = "Code39InsertStr",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 66 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code39_insert_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE39FORMAT,
        .name = "Code39Format",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _code39format_names_ } },
        .defval = { .i = CODE39FORMAT_E_STANDARD },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code39_format_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE39STEDTRANS,
        .name = "Code39StEdTrans",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code39_sted_trans_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE39ASTEASCHAR,
        .name = "Code39AsteAsChar",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code39_aste_as_char_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE39CODE32,
        .name = "Code39Code32",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code39_code32_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE39CODE32PRE,
        .name = "Code39Code32Pre",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code39_code32_pre_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE39TRIOPTIC,
        .name = "Code39Trioptic",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code39_trioptic_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE39TOSTEDTRANS,
        .name = "Code39TOStEdTrans",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code39_to_sted_trans_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE39AIMID,
        .name = "Code39AimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code39_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE128READMAIN,
        .name = "Code128ReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _code128_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE128CHKDIGVER,
        .name = "Code128ChkDigVer",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code128_chk_dig_ver_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE128CHKDIGTX,
        .name = "Code128ChkDigTx",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code128_chk_dig_tx_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE128MAXLEN,
        .name = "Code128MaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 99 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code128_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE128MINLEN,
        .name = "Code128MinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code128_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE128CODEID,
        .name = "Code128CodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 75 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code128_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE128INSERTSTR,
        .name = "Code128InsertStr",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 66 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code128_insert_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE128TRUZERO,
        .name = "Code128TruZero",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _code128truzero_names_ } },
        .defval = { .i = CODE128TRUZERO_E_DISABLE },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code128_tru_zero_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE128AIMID,
        .name = "Code128AimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code128_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODABARREADMAIN,
        .name = "CodabarReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _codabar_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODABARCHKDIGVER,
        .name = "CodabarChkDigVer",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _codabar_check_digit_verify_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODABARCHKDIGTX,
        .name = "CodabarChkDigTx",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _codabar_check_digit_transmit_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODABARMAXLEN,
        .name = "CodabarMaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 99 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _codabar_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODABARMINLEN,
        .name = "CodabarMinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 4 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _codabar_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODABARCODEID,
        .name = "CodabarCodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 78 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _codabar_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODABARINSERTSTR,
        .name = "CodabarInsertStr",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 66 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _codabar_insert_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODABARSTARTSTOPTYPE,
        .name = "CodabarStartStopType",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _codabarstartstoptype_names_ } },
        .defval = { .i = CODABARSTARTSTOPTYPE_E_ABCD },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _codabar_start_stop_type_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODABARSTARTSTOPTX,
        .name = "CodabarStartStopTx",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _codabar_start_stop_transmit_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODABARSTARTENDCHAREQ,
        .name = "CodabarStartEndCharEq",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _codabar_start_end_char_eq_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODABARAIMID,
        .name = "CodabarAimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _codabar_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INTL25READMAIN,
        .name = "Intl25ReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _intl25_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INTL25CHKDIGVER,
        .name = "Intl25ChkDigVer",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _intl25chkdigver_names_ } },
        .defval = { .i = INTL25CHKDIGVER_E_DISABLE },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _intl25_chk_dig_ver_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INTL25CHKDIGTX,
        .name = "Intl25ChkDigTx",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _intl25_chk_dig_tx_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INTL25MAXLEN,
        .name = "Intl25MaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 99 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _intl25_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INTL25MINLEN,
        .name = "Intl25MinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 6 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _intl25_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INTL25CODEID,
        .name = "Intl25CodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 73 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _intl25_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INTL25INSERTSTR,
        .name = "Intl25InsertStr",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 66 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _intl25_insert_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INTL25LENRESTR,
        .name = "Intl25LenRestr",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _intl25lenrestr_names_ } },
        .defval = { .i = INTL25LENRESTR_E_ANY },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _intl25_len_restr_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INTL25BANKFORMATCONV,
        .name = "Intl25BankFormatConv",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _intl25_bank_format_conv_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INTL25BANKFORMATTYPE,
        .name = "Intl25BankFormatType",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _intl25bankformattype_names_ } },
        .defval = { .i = INTL25BANKFORMATTYPE_E_NO_TAB },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _intl25_bank_format_type_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INTL25AIMID,
        .name = "Intl25AimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _intl25_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE93READMAIN,
        .name = "Code93ReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code93_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE93CHKDIGVER,
        .name = "Code93ChkDigVer",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code93_check_digit_verify_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE93CHKDIGTX,
        .name = "Code93ChkDigTx",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code93_check_digit_transmit_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE93MAXLEN,
        .name = "Code93MaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 99 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code93_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE93MINLEN,
        .name = "Code93MinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code93_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE93CODEID,
        .name = "Code93CodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 76 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code93_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE93INSERTSTR,
        .name = "Code93InsertStr",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 66 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code93_insert_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE93AIMID,
        .name = "Code93AimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code93_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_EAN8READMAIN,
        .name = "EAN8ReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ean8_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_EAN8CHKDIGVER,
        .name = "EAN8ChkDigVer",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _ean8_chk_dig_ver_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_EAN8CHKDIGTX,
        .name = "EAN8ChkDigTx",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _ean8_chk_dig_tx_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_EAN8CODEID,
        .name = "EAN8CodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 67 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _ean8_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_EAN8INSERTSTR,
        .name = "EAN8InsertStr",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 66 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _ean8_insert_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_EAN8SUPLDIG,
        .name = "EAN8SuplDig",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _ean8supldig_names_ } },
        .defval = { .i = EAN8SUPLDIG_E_NONE },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _ean8_supl_dig_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_EAN8TRUNEXP,
        .name = "EAN8TrunExp",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _ean8trunexp_names_ } },
        .defval = { .i = EAN8TRUNEXP_E_NONE },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _ean8_trun_exp_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_EAN8AIMID,
        .name = "EAN8AimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _ean8_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_EAN13READMAIN,
        .name = "EAN13ReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _ean13_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_EAN13CHKDIGVER,
        .name = "EAN13ChkDigVer",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _ean13_chk_dig_ver_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_EAN13CHKDIGTX,
        .name = "EAN13ChkDigTx",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _ean13_chk_dig_tx_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_EAN13CODEID,
        .name = "EAN13CodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 65 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _ean13_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_EAN13INSERTSTR,
        .name = "EAN13InsertStr",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 66 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _ean13_insert_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_EAN13SUPLDIG,
        .name = "EAN13SuplDig",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _ean13supldig_names_ } },
        .defval = { .i = EAN13SUPLDIG_E_NONE },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _ean13_supl_dig_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_EAN13ISBSN,
        .name = "EAN13ISBSN",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _ean13_isbsn_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_EAN13ISSNISBNID,
        .name = "EAN13ISSNISBNID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 66 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _ean13_issn_isbn_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_EAN13AIMID,
        .name = "EAN13AimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _ean13_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UPCAREADMAIN,
        .name = "UPCAReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _upca_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UPCACHKDIGVER,
        .name = "UPCAChkDigVer",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _upca_chk_dig_ver_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UPCACHKDIGTX,
        .name = "UPCAChkDigTx",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _upca_chk_dig_tx_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UPCACODEID,
        .name = "UPCACodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 65 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _upca_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UPCAINSERTSTR,
        .name = "UPCAInsertStr",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 66 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _upca_insert_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UPCASUPLDIG,
        .name = "UPCASuplDig",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _upcasupldig_names_ } },
        .defval = { .i = UPCASUPLDIG_E_NONE },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _upca_supl_dig_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UPCATRUNEXP,
        .name = "UPCATrunExp",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _upcatrunexp_names_ } },
        .defval = { .i = UPCATRUNEXP_E_NONE },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _upca_trun_exp_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UPCAAIMID,
        .name = "UPCAAimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _upca_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UPCEREADMAIN,
        .name = "UPCEReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _upce_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UPCECHKDIGVER,
        .name = "UPCEChkDigVer",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _upce_chk_dig_ver_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UPCECHKDIGTX,
        .name = "UPCEChkDigTx",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _upce_chk_dig_tx_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UPCECODEID,
        .name = "UPCECodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 68 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _upce_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UPCEINSERTSTR,
        .name = "UPCEInsertStr",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 66 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _upce_insert_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UPCESUPLDIG,
        .name = "UPCESuplDig",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _upcesupldig_names_ } },
        .defval = { .i = UPCESUPLDIG_E_NONE },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _upce_supl_dig_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UPCETRUNEXP,
        .name = "UPCETrunExp",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _upcetrunexp_names_ } },
        .defval = { .i = UPCETRUNEXP_E_NONE },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _upce_trun_exp_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UPCEAIMID,
        .name = "UPCEAimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _upce_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE11READMAIN,
        .name = "Code11ReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code11_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE11CHKDIGVER,
        .name = "Code11ChkDigVer",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _code11chkdigver_names_ } },
        .defval = { .i = CODE11CHKDIGVER_E_ONE },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code11_chk_dig_ver_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE11CHKDIGTX,
        .name = "Code11ChkDigTx",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code11_check_digit_transmit_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE11MAXLEN,
        .name = "Code11MaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 99 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code11_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE11MINLEN,
        .name = "Code11MinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 4 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code11_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE11CODEID,
        .name = "Code11CodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 86 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code11_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE11INSERTSTR,
        .name = "Code11InsertStr",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 66 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code11_insert_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODE11AIMID,
        .name = "Code11AimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _code11_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GS1EXPAREADMAIN,
        .name = "GS1EXPAReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _gs1expa_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GS1EXPAMAXLEN,
        .name = "GS1EXPAMaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 99 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _gs1expa_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GS1EXPAMINLEN,
        .name = "GS1EXPAMinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _gs1expa_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GS1EXPACODEID,
        .name = "GS1EXPACodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 82 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _gs1expa_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GS1EXPAINSERTSTR,
        .name = "GS1EXPAInsertStr",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 66 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _gs1expa_insert_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GS1EXPACONVERSION,
        .name = "GS1EXPAConversion",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _gs1expaconversion_names_ } },
        .defval = { .i = GS1EXPACONVERSION_E_NONE },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _gs1expa_conversion_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GS1EXPAIMID,
        .name = "GS1EXPAimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _gs1expa_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GS1DBREADMAIN,
        .name = "GS1DBReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _gs1db_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GS1DBCODEID,
        .name = "GS1DBCodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 82 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _gs1db_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GS1DBINSERTSTR,
        .name = "GS1DBInsertStr",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 66 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _gs1db_insert_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GS1DBCONVERSION,
        .name = "GS1DBConversion",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _gs1dbconversion_names_ } },
        .defval = { .i = GS1DBCONVERSION_E_NONE },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _gs1db_conversion_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GS1DBOUTPUTBRACKETS,
        .name = "GS1DBOutputBrackets",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _gs1db_output_brackets_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GS1DBAIMID,
        .name = "GS1DBAimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _gs1db_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GS1LIMIREADMAIN,
        .name = "GS1LIMIReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _gs1limi_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GS1LIMICODEID,
        .name = "GS1LIMICodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 82 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _gs1limi_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GS1LIMIINSERTSTR,
        .name = "GS1LIMIInsertStr",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 66 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _gs1limi_insert_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GS1LIMICONVERSION,
        .name = "GS1LIMIConversion",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _gs1limiconversion_names_ } },
        .defval = { .i = GS1LIMICONVERSION_E_NONE },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _gs1limi_conversion_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GS1LIMIAIMID,
        .name = "GS1LIMIAimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _gs1limi_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_ISBT128READMAIN,
        .name = "ISBT128ReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _isbt128_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_ISBT128CKDIGIVER,
        .name = "ISBT128CkDigiVer",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _isbt128_ck_digi_ver_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_ISBT128CHKDIGTRANS,
        .name = "ISBT128ChkDigTrans",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _isbt128_chk_dig_trans_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_ISBT128MAXLEN,
        .name = "ISBT128MaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 99 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _isbt128_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_ISBT128MINLEN,
        .name = "ISBT128MinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _isbt128_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_ISBT128CODEID,
        .name = "ISBT128CodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 75 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _isbt128_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_ISBT128INSERTSTR,
        .name = "ISBT128InsertStr",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 66 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _isbt128_insert_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_ISBT128AIMID,
        .name = "ISBT128AimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _isbt128_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MSIPLREADMAIN,
        .name = "MSIPLReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _msipl_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MSIPLCHKDIGVER,
        .name = "MSIPLChkDigVer",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _msiplchkdigver_names_ } },
        .defval = { .i = MSIPLCHKDIGVER_E_DISABLE },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _msipl_chk_dig_ver_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MSIPLCHKDIGTX,
        .name = "MSIPLChkDigTx",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _msipl_chk_dig_tx_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MSIPLMAXLEN,
        .name = "MSIPLMaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 99 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _msipl_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MSIPLMINLEN,
        .name = "MSIPLMinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 4 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _msipl_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MSIPLCODEID,
        .name = "MSIPLCodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 79 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _msipl_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MSIPLINSERTSTR,
        .name = "MSIPLInsertStr",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 66 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _msipl_insert_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MSIPLAIMID,
        .name = "MSIPLAimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _msipl_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCCEAN128READMAIN,
        .name = "UCCEAN128ReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _uccean128_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCCEAN128CHKDIGVER,
        .name = "UCCEAN128ChkDigVer",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _uccean128_chk_dig_ver_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCCEAN128CHKDIGTX,
        .name = "UCCEAN128ChkDigTx",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _uccean128_chk_dig_tx_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCCEAN128MAXLEN,
        .name = "UCCEAN128MaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 99 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _uccean128_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCCEAN128MINLEN,
        .name = "UCCEAN128MinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _uccean128_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCCEAN128CODEID,
        .name = "UCCEAN128CodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 75 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _uccean128_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCCEAN128INSERTSTR,
        .name = "UCCEAN128InsertStr",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 66 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _uccean128_insert_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCCEAN128TRUZERO,
        .name = "UCCEAN128TruZero",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _uccean128truzero_names_ } },
        .defval = { .i = UCCEAN128TRUZERO_E_DISABLE },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _uccean128_tru_zero_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCCEAN128AIMID,
        .name = "UCCEAN128AimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _uccean128_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UKPLREADMAIN,
        .name = "UKPLReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _ukpl_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UKPLCHKDIGVER,
        .name = "UKPLChkDigVer",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _ukpl_chk_dig_ver_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UKPLCHKDIGTX,
        .name = "UKPLChkDigTx",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _ukpl_chk_dig_tx_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UKPLMAXLEN,
        .name = "UKPLMaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 99 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _ukpl_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UKPLMINLEN,
        .name = "UKPLMinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _ukpl_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UKPLCODEID,
        .name = "UKPLCodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 85 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _ukpl_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UKPLINSERTSTR,
        .name = "UKPLInsertStr",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 66 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _ukpl_insert_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UKPLAIMID,
        .name = "UKPLAimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _ukpl_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UPCE1READMAIN,
        .name = "UPCE1ReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _upce1_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UPCE1CHKDIGVER,
        .name = "UPCE1ChkDigVer",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _upce1_chk_dig_ver_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UPCE1CHKDIGTRANS,
        .name = "UPCE1ChkDigTrans",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _upce1_chk_dig_trans_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UPCE1CODEID,
        .name = "UPCE1CodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 68 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _upce1_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UPCE1INSERTSTR,
        .name = "UPCE1InsertStr",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 66 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _upce1_insert_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UPCE1SUPLDIG,
        .name = "UPCE1SuplDig",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _upce1supldig_names_ } },
        .defval = { .i = UPCE1SUPLDIG_E_NONE },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _upce1_supl_dig_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UPCE1TRUNEXP,
        .name = "UPCE1TrunExp",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _upce1trunexp_names_ } },
        .defval = { .i = UPCE1TRUNEXP_E_NONE },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _upce1_trun_exp_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UPCE1AIMID,
        .name = "UPCE1AimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _upce1_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CHNPSTREADMAIN,
        .name = "CHNPSTReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _chnpst_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CHNPSTCHKDIGVER,
        .name = "CHNPSTChkDigVer",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _chnpst_chk_dig_ver_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CHNPSTCHKDIGTX,
        .name = "CHNPSTChkDigTx",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _chnpst_chk_dig_tx_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CHNPSTMAXLEN,
        .name = "CHNPSTMaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 11 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _chnpst_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CHNPSTMINLEN,
        .name = "CHNPSTMinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 11 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _chnpst_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CHNPSTCODEID,
        .name = "CHNPSTCodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 84 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _chnpst_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CHNPSTINSERTSTR,
        .name = "CHNPSTInsertStr",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 66 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _chnpst_insert_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CHNPSTAIMID,
        .name = "CHNPSTAimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _chnpst_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INDUSTRIAL25READMAIN,
        .name = "Industrial25ReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _industrial25_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INDUSTRIAL25MAXLEN,
        .name = "Industrial25MaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 99 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _industrial25_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INDUSTRIAL25MINLEN,
        .name = "Industrial25MinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 4 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _industrial25_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INDUSTRIAL25CODEID,
        .name = "Industrial25CodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 72 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _industrial25_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INDUSTRIAL25INSERTSTR,
        .name = "Industrial25InsertStr",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 66 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _industrial25_insert_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INDUSTRIAL25AIMID,
        .name = "Industrial25AimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _industrial25_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MATRIX25READMAIN,
        .name = "Matrix25ReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _matrix25_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MATRIX25CHKDIGVER,
        .name = "Matrix25ChkDigVer",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _matrix25_check_digit_verify_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MATRIX25CHKDIGTRANS,
        .name = "Matrix25ChkDigTrans",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _matrix25_check_digit_transmit_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MATRIX25MAXLEN,
        .name = "Matrix25MaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 99 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _matrix25_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MATRIX25MINLEN,
        .name = "Matrix25MinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 6 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _matrix25_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MATRIX25CODEID,
        .name = "Matrix25CodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 88 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _matrix25_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MATRIX25INSERTSTR,
        .name = "Matrix25InsertStr",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 66 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _matrix25_insert_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MATRIX25AIMID,
        .name = "Matrix25AimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _matrix25_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_QRCODEREADMAIN,
        .name = "QRCodeReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _qrcode_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_QRCODECODEID,
        .name = "QRCodeCodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 113 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _qrcode_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_QRCODEWEBCODEFORBID,
        .name = "QRCodeWebCodeForbid",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _qrcodewebcodeforbid_names_ } },
        .defval = { .i = QRCODEWEBCODEFORBID_E_ALLOW },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _qrcode_web_code_forbid_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_QRCODEMAXLEN,
        .name = "QRCodeMaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 9999 } },
        .defval = { .i = 9999 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _qrcode_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_QRCODEMINLEN,
        .name = "QRCodeMinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 9999 } },
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _qrcode_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_QRCODEAIMID,
        .name = "QRCodeAimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _qrcode_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DMREADMAIN,
        .name = "DMReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _dm_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DMCODEID,
        .name = "DMCodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 100 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _dm_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DMMAXLEN,
        .name = "DMMaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 9999 } },
        .defval = { .i = 9999 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _dm_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DMMINLEN,
        .name = "DMMinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 9999 } },
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _dm_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DMPPNCODE,
        .name = "DMPPNCODE",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _dm_ppn_code_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DMAIMID,
        .name = "DMAimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _dm_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_AZTECREADMAIN,
        .name = "AztecReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _aztec_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_AZTECCODEID,
        .name = "AztecCodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 97 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _aztec_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_AZTECMAXLEN,
        .name = "AztecMaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 9999 } },
        .defval = { .i = 9999 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _aztec_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_AZTECMINLEN,
        .name = "AztecMinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 9999 } },
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _aztec_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_AZTECAIMID,
        .name = "AztecAimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _aztec_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODABLOCKFREADMAIN,
        .name = "CodaBlockFReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _coda_block_f_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODABLOCKFCODEID,
        .name = "CodaBlockFCodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 99 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _coda_block_f_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODABLOCKFMAXLEN,
        .name = "CodaBlockFMaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 9999 } },
        .defval = { .i = 9999 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _coda_block_f_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODABLOCKFMINLEN,
        .name = "CodaBlockFMinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 9999 } },
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _coda_block_f_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODABLOCKFAIMID,
        .name = "CodaBlockFAimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _codablockf_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GMCODEREADMAIN,
        .name = "GMCodeReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _gm_code_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GMCODECODEID,
        .name = "GMCodeCodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 103 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _gm_code_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GMCODEMAXLEN,
        .name = "GMCodeMaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 9999 } },
        .defval = { .i = 9999 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _gm_code_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GMCODEMINLEN,
        .name = "GMCodeMinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 9999 } },
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _gm_code_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MAXICODEREADMAIN,
        .name = "MaxiCodeReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _maxicode_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MAXICODEMAXLEN,
        .name = "MaxiCodeMaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 9999 } },
        .defval = { .i = 9999 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _maxicode_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MAXICODEMINLEN,
        .name = "MaxiCodeMinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 9999 } },
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _maxicode_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MAXICODEAIMID,
        .name = "MaxiCodeAimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _maxicode_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MICPDF417READMAIN,
        .name = "MICPDF417ReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _micpdf417_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MICPDF417CODEID,
        .name = "MICPDF417CodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 112 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _micpdf417_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MICPDF417INSERTSTR,
        .name = "MICPDF417InsertStr",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 66 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _micpdf417_insert_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MICPDF417CONVERSION,
        .name = "MICPDF417Conversion",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _micpdf417conversion_names_ } },
        .defval = { .i = MICPDF417CONVERSION_E_OFF },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _micpdf417_conversion_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MICPDF417MAXLEN,
        .name = "MICPDF417MaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 9999 } },
        .defval = { .i = 9999 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _micpdf417_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MICPDF417MINLEN,
        .name = "MICPDF417MinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 9999 } },
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _micpdf417_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MICPDF417AIMID,
        .name = "MICPDF417AimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _micpdf417_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MICROQRREADMAIN,
        .name = "MicroQRReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _micro_qr_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MICROQRCODEID,
        .name = "MicroQRCodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 113 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _micro_qr_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MICROQRMAXLEN,
        .name = "MicroQRMaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 9999 } },
        .defval = { .i = 9999 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _micro_qr_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MICROQRMINLEN,
        .name = "MicroQRMinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 9999 } },
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _micro_qr_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MICROQRAIMID,
        .name = "MicroQRAimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _microqr_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_PDF417READMAIN,
        .name = "PDF417ReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _pdf417_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_PDF417CODEID,
        .name = "PDF417CodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 112 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _pdf417_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_PDF417INSERTSTR,
        .name = "PDF417InsertStr",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 66 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _pdf417_insert_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_PDF417CONVERSION,
        .name = "PDF417Conversion",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _pdf417conversion_names_ } },
        .defval = { .i = PDF417CONVERSION_E_NONE },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _pdf417_conversion_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_PDF417MAXLEN,
        .name = "PDF417MaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 9999 } },
        .defval = { .i = 9999 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _pdf417_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_PDF417MINLEN,
        .name = "PDF417MinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 9999 } },
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _pdf417_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_PDF417AIMID,
        .name = "PDF417AimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _pdf417_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_BINARYCODEREADMAIN,
        .name = "BinaryCodeReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _binary_code_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_BINARYCODECODEID,
        .name = "BinaryCodeCodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 98 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _binary_code_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_BINARYCODEMAXLEN,
        .name = "BinaryCodeMaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 9999 } },
        .defval = { .i = 9999 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _binary_code_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_BINARYCODEMINLEN,
        .name = "BinaryCodeMinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 9999 } },
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _binary_code_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_BINARYCODEAIMID,
        .name = "BinaryCodeAimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _binary_code_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CSCODEREADMAIN,
        .name = "CSCodeReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _cs_code_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CSCODECODEID,
        .name = "CSCodeCodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 104 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _cs_code_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CSCODEMAXLEN,
        .name = "CSCodeMaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 9999 } },
        .defval = { .i = 9999 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _cs_code_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CSCODEMINLEN,
        .name = "CSCodeMinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 9999 } },
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _cs_code_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CSCODEAIMID,
        .name = "CSCodeAimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _cscode_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DOTCODEREADMAIN,
        .name = "DotCodeReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _dot_code_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DOTCODECODEID,
        .name = "DotCodeCodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 90 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _dot_code_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DOTCODEMAXLEN,
        .name = "DotCodeMaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 9999 } },
        .defval = { .i = 9999 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _dot_code_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DOTCODEMINLEN,
        .name = "DotCodeMinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 9999 } },
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _dot_code_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DOTCODEAIMID,
        .name = "DotCodeAimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _dot_code_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CHNFNCREADMAIN,
        .name = "CHNFNCReadMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _chnfnc_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CHNFNCMAXLEN,
        .name = "CHNFNCMaxLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 10 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _chnfnc_max_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CHNFNCMINLEN,
        .name = "CHNFNCMinLen",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 10 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _chnfnc_min_len_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CHNFNCCHKDIGVER,
        .name = "CHNFNCChkDigVer",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _chnfnc_chk_dig_ver_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CHNFNCFRTCHARCONV,
        .name = "CHNFNCFrtCharConv",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _chnfncfrtcharconv_names_ } },
        .defval = { .i = CHNFNCFRTCHARCONV_E_ENABLE },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _chnfnc_frt_char_conv_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CHNFNCFRTCHARSPEC,
        .name = "CHNFNCFrtCharSpec",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _chnfncfrtcharspec_names_ } },
        .defval = { .i = CHNFNCFRTCHARSPEC_E_ZERO },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _chnfnc_frt_char_spec_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CHNFNCCODEID,
        .name = "CHNFNCCodeID",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 89 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _chnfnc_code_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CHNFNCINSERTSTR,
        .name = "CHNFNCInsertStr",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 66 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _chnfnc_insert_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CHNFNCAIMID,
        .name = "CHNFNCAimID",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _chnfnc_aim_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOM_DECODE_ROI_1,
        .name = "Custom_Decode_ROI_1",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _custom_deco_roi_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOM_DECODE_ROI_2,
        .name = "Custom_Decode_ROI_2",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _custom_deco_roi_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOM_DECODE_ROI_3,
        .name = "Custom_Decode_ROI_3",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _custom_deco_roi_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOM_DECODE_ROI_4,
        .name = "Custom_Decode_ROI_4",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _custom_deco_roi_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOM_DECODE_ROI_5,
        .name = "Custom_Decode_ROI_5",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _custom_deco_roi_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOM_DECODE_ROI_6,
        .name = "Custom_Decode_ROI_6",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _custom_deco_roi_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOM_DECODE_ROI_7,
        .name = "Custom_Decode_ROI_7",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _custom_deco_roi_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOM_DECODE_ROI_8,
        .name = "Custom_Decode_ROI_8",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _custom_deco_roi_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOM_DECODE_ROI_9,
        .name = "Custom_Decode_ROI_9",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _custom_deco_roi_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOM_DECODE_ROI_10,
        .name = "Custom_Decode_ROI_10",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _custom_deco_roi_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOM_DECODE_ROI_11,
        .name = "Custom_Decode_ROI_11",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _custom_deco_roi_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOM_DECODE_ROI_12,
        .name = "Custom_Decode_ROI_12",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _custom_deco_roi_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_NETLED,
        .name = "NetLed",
        .type = CPROPR_TYPE_COMB,
        .range = { .e = { .names = _netled_names_ } },
        .defval = { .i = NETLED_E_LINK | NETLED_E_TX | NETLED_E_RX },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _netled_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INSERTSTR,
        .name = "InsertStr",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 66 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _insert_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_BACKTX,
        .name = "BackTx",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _back_tx_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_NOREADOUTPUT,
        .name = "NoReadOutput",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _no_read_output_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DATABATCH,
        .name = "DataBatch",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _databatch_names_ } },
        .defval = { .i = DATABATCH_E_NONE },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _data_batch_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_SCANMODE,
        .name = "ScanMode",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _scanmode_names_ } },
        .defval = { .i = SCANMODE_E_SciTrigger },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _scan_mode_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_SCANKEEPTIME,
        .name = "ScanKeepTime",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 15 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _scan_keep_time_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_SCANKEEPTIMEADVANCED,
        .name = "ScanKeepTimeAdvanced",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { -1, 2147483647 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _scan_keep_time_advanced_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MOVEMENTSCANMODE,
        .name = "MovementScanMode",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _movementscanmode_names_ } },
        .defval = { .i = MOVEMENTSCANMODE_E_Standard },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _movement_scan_mode_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_POWERINDICATION,
        .name = "PowerIndication",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _power_indication_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_LEDINDICATION,
        .name = "LedIndication",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _led_indication_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_BEEPINDICATION,
        .name = "BeepIndication",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _beep_indication_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_BEEPTIME,
        .name = "BeepTime",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 10, 990 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _beep_time_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_BEEPVOLUME,
        .name = "BeepVolume",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _beepvolume_names_ } },
        .defval = { .i = BEEPVOLUME_E_HIGH },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _beep_volume_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_BEEPTONE,
        .name = "BeepTone",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _beeptone_names_ } },
        .defval = { .i = BEEPTONE_E_LEVEL1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _beep_tone_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_POWERLEDCTRL,
        .name = "PowerLEDCtrl",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _power_led_ctrl_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1MODEMAIN,
        .name = "CustomIO1ModeMain",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customiomodemain_names_ } },
        .defval = { .i = CUSTOMIOMODEMAIN_E_Close },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_mode_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2MODEMAIN,
        .name = "CustomIO2ModeMain",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customiomodemain_names_ } },
        .defval = { .i = CUSTOMIOMODEMAIN_E_Close },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_mode_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3MODEMAIN,
        .name = "CustomIO3ModeMain",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customiomodemain_names_ } },
        .defval = { .i = CUSTOMIOMODEMAIN_E_Close },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_mode_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4MODEMAIN,
        .name = "CustomIO4ModeMain",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customiomodemain_names_ } },
        .defval = { .i = CUSTOMIOMODEMAIN_E_Close },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_mode_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1INPUTCOUNT,
        .name = "CustomIO1InputCount",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_incount_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2INPUTCOUNT,
        .name = "CustomIO2InputCount",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_incount_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3INPUTCOUNT,
        .name = "CustomIO3InputCount",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_incount_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4INPUTCOUNT,
        .name = "CustomIO4InputCount",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 255 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_incount_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1DEBOUNCE,
        .name = "CustomIO1Debounce",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 1000 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_debounce_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2DEBOUNCE,
        .name = "CustomIO2Debounce",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 1000 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_debounce_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3DEBOUNCE,
        .name = "CustomIO3Debounce",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 1000 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_debounce_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4DEBOUNCE,
        .name = "CustomIO4Debounce",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 1000 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_debounce_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1RISINGACTION,
        .name = "CustomIO1RisingAction",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customiorisingaction_names_ } },
        .defval = { .i = CUSTOMIORISINGACTION_E_None },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_revent_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2RISINGACTION,
        .name = "CustomIO2RisingAction",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customiorisingaction_names_ } },
        .defval = { .i = CUSTOMIORISINGACTION_E_None },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_revent_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3RISINGACTION,
        .name = "CustomIO3RisingAction",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customiorisingaction_names_ } },
        .defval = { .i = CUSTOMIORISINGACTION_E_None },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_revent_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4RISINGACTION,
        .name = "CustomIO4RisingAction",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customiorisingaction_names_ } },
        .defval = { .i = CUSTOMIORISINGACTION_E_None },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_revent_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1RISINGDELAY,
        .name = "CustomIO1RisingDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_rdelay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2RISINGDELAY,
        .name = "CustomIO2RisingDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_rdelay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3RISINGDELAY,
        .name = "CustomIO3RisingDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_rdelay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4RISINGDELAY,
        .name = "CustomIO4RisingDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_rdelay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1FALLINGACTION,
        .name = "CustomIO1FallingAction",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customiofallingaction_names_ } },
        .defval = { .i = CUSTOMIOFALLINGACTION_E_None },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_fevent_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2FALLINGACTION,
        .name = "CustomIO2FallingAction",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customiofallingaction_names_ } },
        .defval = { .i = CUSTOMIOFALLINGACTION_E_None },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_fevent_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3FALLINGACTION,
        .name = "CustomIO3FallingAction",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customiofallingaction_names_ } },
        .defval = { .i = CUSTOMIOFALLINGACTION_E_None },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_fevent_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4FALLINGACTION,
        .name = "CustomIO4FallingAction",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customiofallingaction_names_ } },
        .defval = { .i = CUSTOMIOFALLINGACTION_E_None },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_fevent_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1FALLINGDELAY,
        .name = "CustomIO1FallingDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_fdelay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2FALLINGDELAY,
        .name = "CustomIO2FallingDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_fdelay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3FALLINGDELAY,
        .name = "CustomIO3FallingDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_fdelay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4FALLINGDELAY,
        .name = "CustomIO4FallingDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_fdelay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1ACTIVECONDITION,
        .name = "CustomIO1ActiveCondition",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customioactivecondition_names_ } },
        .defval = { .i = CUSTOMIOACTIVECONDITION_E_None },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_active_cond_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2ACTIVECONDITION,
        .name = "CustomIO2ActiveCondition",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customioactivecondition_names_ } },
        .defval = { .i = CUSTOMIOACTIVECONDITION_E_None },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_active_cond_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3ACTIVECONDITION,
        .name = "CustomIO3ActiveCondition",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customioactivecondition_names_ } },
        .defval = { .i = CUSTOMIOACTIVECONDITION_E_None },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_active_cond_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4ACTIVECONDITION,
        .name = "CustomIO4ActiveCondition",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customioactivecondition_names_ } },
        .defval = { .i = CUSTOMIOACTIVECONDITION_E_None },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_active_cond_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1INACTIVECONDITION,
        .name = "CustomIO1InactiveCondition",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customioinactivecondition_names_ } },
        .defval = { .i = CUSTOMIOINACTIVECONDITION_E_Timer },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_inactive_cond_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2INACTIVECONDITION,
        .name = "CustomIO2InactiveCondition",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customioinactivecondition_names_ } },
        .defval = { .i = CUSTOMIOINACTIVECONDITION_E_Timer },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_inactive_cond_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3INACTIVECONDITION,
        .name = "CustomIO3InactiveCondition",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customioinactivecondition_names_ } },
        .defval = { .i = CUSTOMIOINACTIVECONDITION_E_Timer },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_inactive_cond_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4INACTIVECONDITION,
        .name = "CustomIO4InactiveCondition",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _customioinactivecondition_names_ } },
        .defval = { .i = CUSTOMIOINACTIVECONDITION_E_Timer },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_inactive_cond_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1ACTIVEDELAY,
        .name = "CustomIO1ActiveDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_active_delay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2ACTIVEDELAY,
        .name = "CustomIO2ActiveDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_active_delay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3ACTIVEDELAY,
        .name = "CustomIO3ActiveDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_active_delay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4ACTIVEDELAY,
        .name = "CustomIO4ActiveDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_active_delay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1INACTIVETIMER,
        .name = "CustomIO1InactiveTimer",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 100 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_inactive_timer_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2INACTIVETIMER,
        .name = "CustomIO2InactiveTimer",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 100 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_inactive_timer_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3INACTIVETIMER,
        .name = "CustomIO3InactiveTimer",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 100 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_inactive_timer_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4INACTIVETIMER,
        .name = "CustomIO4InactiveTimer",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 10000 } },
        .defval = { .i = 100 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_inactive_timer_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1ACTIVECMD,
        .name = "CustomIO1ActiveCmd",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 64 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_active_cmd_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2ACTIVECMD,
        .name = "CustomIO2ActiveCmd",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 64 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_active_cmd_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3ACTIVECMD,
        .name = "CustomIO3ActiveCmd",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 64 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_active_cmd_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4ACTIVECMD,
        .name = "CustomIO4ActiveCmd",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 64 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_active_cmd_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO1INACTIVECMD,
        .name = "CustomIO1InactiveCmd",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 64 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_inactive_cmd_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO2INACTIVECMD,
        .name = "CustomIO2InactiveCmd",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 64 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_inactive_cmd_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO3INACTIVECMD,
        .name = "CustomIO3InactiveCmd",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 64 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_inactive_cmd_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CUSTOMIO4INACTIVECMD,
        .name = "CustomIO4InactiveCmd",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 64 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _custom_io_inactive_cmd_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CHARENCODE,
        .name = "CharEncode",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _charencode_names_ } },
        .defval = { .i = CHARENCODE_E_ASCII },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _char_encode_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CHARPRINTCTR,
        .name = "CharPrintCtr",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _charprintctr_names_ } },
        .defval = { .i = CHARPRINTCTR_E_Disable },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _char_print_ctr_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DECODERESULTMAXNUM,
        .name = "DecodeResultMaxNum",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 30 } },
        .defval = { .i = 30 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _decode_result_max_num_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_FIRSTONLY,
        .name = "FirstOnly",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 99 } },
        .defval = { .i = 99 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _first_only_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_LASTONLY,
        .name = "LastOnly",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 99 } },
        .defval = { .i = 99 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _last_only_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_SINGREPLAC1,
        .name = "SingReplaC1",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _sing_repla_c1_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_SINGREPLAC2,
        .name = "SingReplaC2",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _sing_repla_c2_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_SINGREPLAC3,
        .name = "SingReplaC3",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _sing_repla_c3_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_REPLACHAR2STR,
        .name = "ReplaChar2Str",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _repla_char2str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CASECONV,
        .name = "CaseConv",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _caseconv_names_ } },
        .defval = { .i = CASECONV_E_Disable },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _case_conv_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_FN1SUBSTUTEXTENABLE,
        .name = "FN1SubstuTextEnable",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _fn1_substu_text_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CHARACTERSTOSTRINGS,
        .name = "CharactersToStrings",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _characters_to_strings_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DECORSLTCHECK,
        .name = "DecoRsltCheck",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _decorsltcheck_names_ } },
        .defval = { .i = DECORSLTCHECK_E_Disable },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _deco_rslt_check_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DECORSLTCHECK1HEADSTR,
        .name = "DecoRsltCheck1HeadStr",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _deco_rslt_check_1head_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DECORSLTCHECK1TAILSTR,
        .name = "DecoRsltCheck1TailStr",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _deco_rslt_check_1tail_str_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DECORESULTCHECKFAILINDICATION,
        .name = "DecoResultCheckFailIndication",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _deco_result_check_fail_indication_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GROUP1TEXT,
        .name = "Group1Text",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _group1_text_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GROUP2TEXT,
        .name = "Group2Text",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _group2_text_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GROUP3TEXT,
        .name = "Group3Text",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _group3_text_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GROUP4TEXT,
        .name = "Group4Text",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _group4_text_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GROUP1POS,
        .name = "Group1Pos",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _group1_pos_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GROUP2POS,
        .name = "Group2Pos",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _group2_pos_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GROUP3POS,
        .name = "Group3Pos",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _group3_pos_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_GROUP4POS,
        .name = "Group4Pos",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _group4_pos_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_USBETHERNETCOMPOSITEENMAIN,
        .name = "USBEthernetCompositeEnMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_REBOOT,
        .children = (short[]){435, -1},
        .hook = _usb_ethernet_composite_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_USBETHERNETCOMPOSITETYPE,
        .name = "USBEthernetCompositeType",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _usbethernetcompositetype_names_ } },
        .defval = { .i = USBETHERNETCOMPOSITETYPE_E_Keyboard },
        .flags = CPROPR_FLAG_REBOOT,
        .children = 0,
        .hook = _usb_ethernet_composite_type_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_ETHERNETIPOUTPUTEN,
        .name = "EthernetIpOutputEn",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_REBOOT,
        .children = 0,
        .hook = _ethernet_ip_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_MODBUSSVROUTPUTEN,
        .name = "ModbusSvrOutputEn",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_REBOOT,
        .children = 0,
        .hook = _modbus_server_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UARTENABLE,
        .name = "UartEnable",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _uart_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_PROFINETOUTPUTENMAIN,
        .name = "ProfinetOutputEnMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_REBOOT,
        .children = (short[]){440, -1},
        .hook = _profinet_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_PROFINETSTATIONNAME,
        .name = "ProfinetStationName",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 32 },
        .defval = { .p = (cpropr_bytes_t*)"\x0b""ad-irseries" },
        .flags = CPROPR_FLAG_REBOOT,
        .children = 0,
        .hook = _profinet_station_name_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TCPCLIOUTPUTENMAIN,
        .name = "TCPCliOutputEnMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NONE,
        .children = (short[]){442, 443, 444, 445, 446, -1},
        .hook = _tcp_client_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TCPCLITARGETSVRIP,
        .name = "TCPCliTargetSvrIP",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 16 },
        .defval = { .p = (cpropr_bytes_t*)"\x07""0.0.0.0" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _tcp_target_server_ip_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TCPCLITARGETSVRPORT,
        .name = "TCPCliTargetSvrPort",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 65535 } },
        .defval = { .i = 5000 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _tcp_target_server_port_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TCPCLIOUTPUTFMT,
        .name = "TCPCliOutputFmt",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 200 },
        .defval = { .p = (cpropr_bytes_t*)"\x0e""<code_content>" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _tcp_client_output_format_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TCPCLIOUTPUTPREFIX,
        .name = "TCPCliOutputPrefix",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 32 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _tcp_client_output_prefix_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TCPCLIOUTPUTSUFFIX,
        .name = "TCPCliOutputSuffix",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 32 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _tcp_client_output_suffix_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TCPSVROUTPUTENABLEMAIN,
        .name = "TCPSvrOutputEnableMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NONE,
        .children = (short[]){448, 449, 450, 451, -1},
        .hook = _tcp_server_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TCPSVROUTPUTFMT,
        .name = "TCPSvrOutputFmt",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 200 },
        .defval = { .p = (cpropr_bytes_t*)"\x0e""<code_content>" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _tcp_server_output_format_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TCPSVROUTPUTPREFIX,
        .name = "TCPSvrOutputPrefix",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 32 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _tcp_server_output_prefix_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TCPSVROUTPUTSUFFIX,
        .name = "TCPSvrOutputSuffix",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 32 },
        .defval = { .p = (cpropr_bytes_t*)"\x00" },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _tcp_server_output_suffix_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TCPSVRSERVICEPORT,
        .name = "TCPSvrServicePort",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 65535 } },
        .defval = { .i = 2002 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _tcp_server_service_port_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_RS232FLOWCONTROL,
        .name = "RS232FlowControl",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _rs232flowcontrol_names_ } },
        .defval = { .i = RS232FLOWCONTROL_E_None },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _rs232_flow_control_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_RS232CHARDELAY,
        .name = "RS232CharDelay",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _rs232chardelay_names_ } },
        .defval = { .i = RS232CHARDELAY_E_Forbid },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _rs232_char_delay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_RS232RESPONSEDELAY,
        .name = "RS232ResponseDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _rs232_response_delay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_RS232BAUDRATE,
        .name = "RS232BaudRate",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _rs232baudrate_names_ } },
        .defval = { .i = RS232BAUDRATE_E_BR9600 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _rs232_baud_rate_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_RS232PARITY,
        .name = "RS232Parity",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _rs232parity_names_ } },
        .defval = { .i = RS232PARITY_E_None },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _rs232_parity_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_RS232DATABITS,
        .name = "RS232DataBits",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _rs232databits_names_ } },
        .defval = { .i = RS232DATABITS_E_Bits8 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _rs232_data_bits_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_RS232STOPBITS,
        .name = "RS232StopBits",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _rs232stopbits_names_ } },
        .defval = { .i = RS232STOPBITS_E_Bits1 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _rs232_stop_bits_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_RS232HOSTTYPE,
        .name = "RS232HostType",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _rs232hosttype_names_ } },
        .defval = { .i = RS232HOSTTYPE_E_Standard },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _rs232_host_type_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_RS232DECODATAFORMAT,
        .name = "RS232DecoDataFormat",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _rs232decodataformat_names_ } },
        .defval = { .i = RS232DECODATAFORMAT_E_Raw },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _rs232_deco_data_format_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_RS232HSCHARTIMEOUT,
        .name = "RS232HsCharTimeout",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 20 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _rs232_hs_char_timeout_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_USBTYPE,
        .name = "USBType",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _usbtype_names_ } },
        .defval = { .i = USBTYPE_E_PC },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _usb_type_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_USBKEYBOARDLAYOUT,
        .name = "USBKeyboardLayout",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _usbkeyboardlayout_names_ } },
        .defval = { .i = USBKEYBOARDLAYOUT_E_CHN_USA },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _usb_keyboard_layout_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_USBKEYDLY,
        .name = "USBKeyDly",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _usbkeydly_names_ } },
        .defval = { .i = USBKEYDLY_E_DELAY_0MS },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _usb_key_delay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_USBNUMBKEY,
        .name = "USBNumbKey",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _usbnumbkey_names_ } },
        .defval = { .i = USBNUMBKEY_E_DISABLE },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _usb_numb_key_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_USBFUNCKEY,
        .name = "USBFuncKey",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _usbfunckey_names_ } },
        .defval = { .i = USBFUNCKEY_E_MINDEO },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _usb_func_key_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_USBHOSTPOLLINGINTER,
        .name = "USBHostPollingInter",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _usbhostpollinginter_names_ } },
        .defval = { .i = USBHOSTPOLLINGINTER_E_INTERVAL_1MS },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _usb_host_polling_inter_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_USBKEYSENDMODE,
        .name = "USBKeySendMode",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _usbkeysendmode_names_ } },
        .defval = { .i = USBKEYSENDMODE_E_DISCONTINUOUS },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _usb_key_send_mode_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_USBENTERMODE,
        .name = "USBEnterMode",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _usbentermode_names_ } },
        .defval = { .i = USBENTERMODE_E_ACCORDING_FUNC_KEY },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _usb_enter_mode_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_USBMODKEYSRELEASEMODE,
        .name = "USBModKeysReleaseMode",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _usbmodkeysreleasemode_names_ } },
        .defval = { .i = USBMODKEYSRELEASEMODE_E_SAME_TIME },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _usb_mod_keys_release_mode_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_USBKEYTXTIMEOUT,
        .name = "USBKeyTxTimeout",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _usb_key_tx_timeout_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_USBENUMFAILREBOOT,
        .name = "UsbEnumFailReboot",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _usb_enum_fail_reboot_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_FOCAL,
        .name = "Focal",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 60000 } },
        .defval = { .i = 10000 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _focuser_value_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CONFIGBYCODE,
        .name = "ConfigByCode",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _config_by_code_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INTERFACETYPEMAIN,
        .name = "InterfaceTypeMain",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _interfacetypemain_names_ } },
        .defval = { .i = INTERFACETYPEMAIN_E_RS232_ETH },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_AUTORB | CPROPR_FLAG_HIDDEN,
        .children = (short[]){452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 476, -1},
        .hook = _interface_type_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_RS232BYTEDELAY,
        .name = "RS232ByteDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _rs232_byte_delay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODEMAXLEN1D,
        .name = "CodeMaxLen1D",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 4, 99 } },
        .defval = { .i = 99 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _code_max_len_1d_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODEMINLEN1D,
        .name = "CodeMinLen1D",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 99 } },
        .defval = { .i = 4 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _code_min_len_1d_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODEELEMENTAMEND,
        .name = "CodeElementAmend",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _code_element_amend_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DECODEOPTIMIZE,
        .name = "DecodeOptimize",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _decode_optimize_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_OUTPUTDELAYCONTSCAN,
        .name = "OutputDelayContScan",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _output_delay_cont_scan_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_SAMEBARDELAYDR,
        .name = "SameBarDelayDR",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 4950 } },
        .defval = { .i = 400 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _same_bar_delay_dr_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_SWEEPINGSCANENHANCE,
        .name = "SweepingScanEnhance",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _sweepingscanenhance_names_ } },
        .defval = { .i = SWEEPINGSCANENHANCE_E_LowSpeed },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _sweeping_scan_enhance_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_VIBRATORINDICATION,
        .name = "VibratorIndication",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _vibrator_indication_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_SILENTMODE,
        .name = "SilentMode",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _silent_mode_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_BEEPSOUNDMODE,
        .name = "BeepSoundMode",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _beepsoundmode_names_ } },
        .defval = { .i = BEEPSOUNDMODE_E_MODE_0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _beep_sound_mode_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INDICATIONTXDELAY,
        .name = "IndicationTxDelay",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 9999 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _indication_tx_delay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_VOICEDLY,
        .name = "VoiceDly",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _voice_delay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INFRAREDSENSOR,
        .name = "InfraredSensor",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _infrared_sensor_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INFRAREDMODE,
        .name = "InfraredMode",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _infraredmode_names_ } },
        .defval = { .i = INFRAREDMODE_E_IN_STAND },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _infrared_mode_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_AUTODETEKEEPTIME,
        .name = "AutoDeteKeepTime",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 4 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _auto_dete_keep_time_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_AUTODETETEXTURE,
        .name = "AutoDeteTexture",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _auto_dete_texture_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_AUTODETELUM,
        .name = "AutoDeteLum",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _autodetelum_names_ } },
        .defval = { .i = AUTODETELUM_E_ALWAYS_ON },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _auto_dete_lum_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_AUTODETETXTUREINTER,
        .name = "AutoDeteTxtureInter",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _autodetetxtureinter_names_ } },
        .defval = { .i = AUTODETETXTUREINTER_E_INTERVAL_10S },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _auto_dete_txture_inter_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_AUTODETESLEEPFRMINTER,
        .name = "AutoDeteSleepFrmInter",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _autodetesleepfrminter_names_ } },
        .defval = { .i = AUTODETESLEEPFRMINTER_E_INTERVAL_250MS },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _auto_dete_sleep_frm_inter_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INFRAREDMSGSWITCH,
        .name = "InfraredMsgSwitch",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _infrared_msg_switch_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INFRAREDLUM,
        .name = "InfraredLum",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _infrared_lum_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_IRLUMDETESENLEVEL,
        .name = "IRLumDeteSenLevel",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 10 } },
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _ir_lum_dete_sen_level_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DPMREAD,
        .name = "DPMRead",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _dpm_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_COMPOSITEREAD,
        .name = "CompositeRead",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _compositeread_names_ } },
        .defval = { .i = COMPOSITEREAD_E_DISABLE },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _composite_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_OCRRECOGNIZEMAIN,
        .name = "OCRRecognizeMain",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _ocrrecognizemain_names_ } },
        .defval = { .i = OCRRECOGNIZEMAIN_E_AS_PARA },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _ocr_recognize_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_OCRRECOGNIZEPASSPORTMRZ,
        .name = "OCRRecognizePassportMRZ",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _ocr_recognize_passport_mrz_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_OCRRECOGNIZENATIONALCARDID,
        .name = "OCRRecognizeNationalCardID",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _ocr_recognize_national_card_id_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_OCRTURKISHIDOUTPUTFORMAT,
        .name = "OCRTurkishIDOutputFormat",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _ocrturkishidoutputformat_names_ } },
        .defval = { .i = OCRTURKISHIDOUTPUTFORMAT_E_ORIGINAL },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _ocr_turkish_id_output_format_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_OCRDOUBLECHECK,
        .name = "OCRDoubleCheck",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _ocr_double_check_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_IVDDETETUBECAPMAIN,
        .name = "IVDDeteTubeCapMain",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _ivd_dete_tube_cap_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_IVDDETETUBEHEIGHT,
        .name = "IVDDeteTubeHeight",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _ivd_dete_tube_height_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_AIMKEEPTIMEAFTERDECO,
        .name = "AimKeepTimeAfterDeco",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _aim_keep_time_after_deco_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_LIGHTDRIVECAPACITY,
        .name = "LightDriveCapacity",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 1400 } },
        .defval = { .i = 840 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _light_drive_capacity_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_LIGHTMAXPERLED,
        .name = "LightMaxPerLED",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 10, 350 } },
        .defval = { .i = 350 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _light_max_per_led_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_LIGHTGROUP1CURRENT,
        .name = "LightGroup1Current",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 10, 350 } },
        .defval = { .i = 350 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _light_group1_current_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_LIGHTGROUP2CURRENT,
        .name = "LightGroup2Current",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 10, 350 } },
        .defval = { .i = 350 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _light_group2_current_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_AIMKEEPTIMEBEFOREDECO,
        .name = "AimKeepTimeBeforeDeco",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 99 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _aim_keep_time_before_deco_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_AUXILIARYLUM,
        .name = "AuxiliaryLum",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _auxiliary_lum_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_AUXILIARYLUMCOMPTIME,
        .name = "AuxiliaryLumCompTime",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 8000 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _auxiliary_lum_comp_time_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_IVDTUBEHIGHTINFO,
        .name = "IVDTubeHightInfo",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _ivd_tube_hight_info_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODERSLTTXINDIDLYMODE,
        .name = "CodeRsltTxIndiDlyMode",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _coderslttxindidlymode_names_ } },
        .defval = { .i = CODERSLTTXINDIDLYMODE_E_NONE },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _code_rslt_tx_indi_dly_mode_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DECOSTAY,
        .name = "DecoStay",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _deco_stay_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_PACKETOUTPUTDATACTR,
        .name = "PacketOutputDataCtr",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _packet_output_data_ctr_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_ADDWATERMARK,
        .name = "AddWatermark",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _addwatermark_names_ } },
        .defval = { .i = ADDWATERMARK_E_DISABLE },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _add_watermark_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INSTANDINDICATE,
        .name = "InstandIndicate",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _instand_indicate_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_QRTERMINATORPOS,
        .name = "QRTerminatorPos",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _qrterminatorpos_names_ } },
        .defval = { .i = QRTERMINATORPOS_E_FIRST },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _qr_terminator_pos_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODEERROUTPUT2D,
        .name = "CodeErrOutput2D",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _code_err_output_2d_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODEERRLEVEL2D,
        .name = "CodeErrLevel2D",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _code_err_level_2d_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODEVERSION2D,
        .name = "CodeVersion2D",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _code_version_2d_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CONTMODELUMLEVEL,
        .name = "ContModeLumLevel",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _contmodelumlevel_names_ } },
        .defval = { .i = CONTMODELUMLEVEL_E_LOW },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _cont_mode_lum_level_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CONTMODENOACTIONDURA,
        .name = "ContModeNoActionDura",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 90 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _cont_mode_no_action_dura_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TIMETOREBOOT,
        .name = "TimeToReboot",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _timetoreboot_names_ } },
        .defval = { .i = TIMETOREBOOT_E_FORBID },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _time_to_reboot_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_IMAGEFLIPMODE,
        .name = "ImageFlipMode",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _imageflipmode_names_ } },
        .defval = { .i = IMAGEFLIPMODE_E_NONE },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _image_flip_mode_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_ATTACHCODEPOSOPTION,
        .name = "AttachCodePosOption",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _attach_code_pos_option_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_LASTSUCCIMAGING,
        .name = "LastSuccImaging",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _last_succ_imaging_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_HILUM4HDCODE,
        .name = "HiLum4HDCode",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _hi_lum_4hd_code_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_SYSRECOVERINDICATION,
        .name = "SysRecoverIndication",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _sys_recover_indication_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_OUTPUTMULTIPLE,
        .name = "OutputMultiple",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _output_multiple_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CONFIGBYCODEFB,
        .name = "ConfigByCodeFb",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _config_by_code_fb_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_QUERYCONFIGBYCODE,
        .name = "QueryConfigByCode",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _query_config_by_code_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_RESULTCHECK,
        .name = "ResultCheck",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _result_check_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_RAINBOWREAD,
        .name = "RainbowRead",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _rainbow_read_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_POWERUPREPORT,
        .name = "PowerUpReport",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _power_up_report_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_EXPREBOOTINDICATION,
        .name = "ExpRebootIndication",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _exp_reboot_indication_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_DECODEVERTEX,
        .name = "DecodeVertex",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _decode_vertex_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_AGINGTEST,
        .name = "AgingTest",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _agingtest_names_ } },
        .defval = { .i = AGINGTEST_E_OFF },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _aging_test_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_PREFIXTEXT,
        .name = "PrefixText",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _prefix_text_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_PREFIXTEXTTRANSMISSION,
        .name = "PrefixTextTransmission",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _prefix_transmission_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_SUFFIXTEXT,
        .name = "SuffixText",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _suffix_text_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_PREAMBTEXT,
        .name = "PreambText",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _preamb_text_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_POSTAMBTEXT,
        .name = "PostambText",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _postamb_text_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_FN1SUBSTUTEXT,
        .name = "FN1SubstuText",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _fn1_substu_text_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TRUNCLEADG5,
        .name = "TruncLeadG5",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _trunc_lead_g5_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_REPEATG5,
        .name = "RepeatG5",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 99 } },
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _repeat_g5_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TRUNCENDG6,
        .name = "TruncEndG6",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _trunc_end_g6_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_REPEATG6,
        .name = "RepeatG6",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 1, 99 } },
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _repeat_g6_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODEIDPOS,
        .name = "CodeIDPos",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _codeidpos_names_ } },
        .defval = { .i = CODEIDPOS_E_Before },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _code_id_pos_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_SUFFIXTEXTENABLE,
        .name = "SuffixTextEnable",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _suffix_text_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODENAMEENABLE,
        .name = "CodeNameEnable",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _code_name_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_PREAMBTEXTENABLE,
        .name = "PreambTextEnable",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _preamb_text_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_POSTAMBTEXTENABLE,
        .name = "PostambTextEnable",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _postamb_text_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODEIDTEXTENABLE,
        .name = "CodeIDTextEnable",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _codeidtextenable_names_ } },
        .defval = { .i = CODEIDTEXTENABLE_E_Disable },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _code_id_text_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_CODELENENABLE,
        .name = "CodeLenEnable",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _code_len_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_NONEPRINTSTRENABLE,
        .name = "NonePrintStrEnable",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _none_print_str_enable_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_BANSPECIALKEYS,
        .name = "BanSpecialKeys",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 1 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _ban_special_keys_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TEXTEDIT,
        .name = "TextEdit",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _textedit_names_ } },
        .defval = { .i = TEXTEDIT_E_Disable },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _text_edit_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INVOICECUSTSTR1,
        .name = "InvoiceCustStr1",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _invoice_cust_str1_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INVOICECUSTSTR2,
        .name = "InvoiceCustStr2",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _invoice_cust_str2_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INVOICECUSTSTR3,
        .name = "InvoiceCustStr3",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _invoice_cust_str3_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INVOICECUSTSTR4,
        .name = "InvoiceCustStr4",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 0 },
        .defval = { .p = 0 },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_HIDDEN,
        .children = 0,
        .hook = _invoice_cust_str4_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_BACKUPDECOIMG,
        .name = "BackupDecoImg",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _backupdecoimg_names_ } },
        .defval = { .i = BACKUPDECOIMG_E_OFF },
        .flags = CPROPR_FLAG_NOSAVE | CPROPR_FLAG_EXPERT,
        .children = 0,
        .hook = _backup_deco_img_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_ONTIMETRIGGERDECODE,
        .name = "OnTimeTriggerDecode",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 9900 } },
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _on_time_trigger_decode_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_UCIPGROUPRESETPUB,
        .name = "UCIPGroupResetPUB",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_WRONLY,
        .children = 0,
        .hook = _ucip_group_reset_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_VISIONAPP,
        .name = "VisionApp",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _visionapp_names_ } },
        .defval = { .i = VISIONAPP_E_OFF },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = 0,
        .hook = _vision_app_prop_hook_,
    },
};

#define _cpropr_count_ (sizeof(_cpropr_items_)/sizeof(_cpropr_items_[0]))

#endif // __CPROPR_CTX_H__
