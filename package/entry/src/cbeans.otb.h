// Auto generated, don't edit.
#include "libcustom.h"
typedef struct
{
	any _obj0; // MdNotice
	any boot_notice; // MdNoticeNode
	any _obj1; // MdTaskServer
	any poller; // MdPoller
	any _obj2; // UCIPConfiger
	any _obj3; // MdProperty
} VscanBasic;
typedef struct
{
	any _obj4; // CustomIO
	any _obj5; // CustomIO
	any _obj6; // CustomIO
	any _obj7; // CustomIO
	any _obj8; // CustomIOManager
	any _obj9; // MdNetworkConf
	any allocator; // MdAllocator
	any notice; // MdNoticeNode
	any _obj10; // MdReadHelper
	any booter; // MdNoticeNode
	any tcp_server; // MdTcpServer
	any ticker; // MdStreamDaemon
	any autosdk; // MdAutoSdkServer
	any _obj11; // MdClexerServer
	any sys_software_chn; // SysSoftwareChn
	any _obj12; // LegacyConfigNotify
} VscanServer;
non VscanBasic_Init(VscanBasic *thiz);
non VscanServer_Init(VscanServer *thiz);
int VscanBasic_Free(VscanBasic *thiz);
int VscanServer_Free(VscanServer *thiz);
#define BEAN_BOOT_NOTICE 0
#define BEAN_POLLER 1
#define BEAN_TCP_SERVER 2
#define BEAN_AUTOSDK 3
#define BEAN_SYS_SOFTWARE_CHN 4
#define BEAN_EXPORT_CNT 5
