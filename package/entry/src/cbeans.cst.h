// Auto generated, don't edit.
#include "cbeans.h"
any CustomIO_New(int id, int reverse);
any CustomIOManager_New(void);
any MdAutoSdkServer_New(any notice, any writer);
any MdClexerServer_New(any notice, any writer);
any LegacyConfigNotify_New(void);
any MdNetworkConf_New(void);
any MdProperty_New(void);
any SysSoftwareChn_New(any notice, any writer);
any UCIPConfiger_New(void);
any MdNotice_New(non);
any MdNoticeNode_New(non);
any MdTaskServer_New(int count);
any MdAllocator_New(int size, int count);
any MdReadHelper_New(any allocator, any notice);
any MdStreamDaemon_New(any notice, any stream, int heartbeat);
any MdPoller_New(non);
any MdTcpServer_New(any helper, int port, any booter);
non CustomIO_Del(any obj);
non LegacyConfigNotify_Del(any obj);
non UCIPConfiger_Del(any obj);
non MdNotice_Del(any obj);
non MdNoticeNode_Del(any node);
non MdTaskServer_Del(any obj);
non MdAllocator_Del(any thiz);
non MdReadHelper_Del(any obj);
non MdStreamDaemon_Del(any obj);
non MdPoller_Del(any obj);
non MdTcpServer_Del(any obj);
non VscanBasic_Init(VscanBasic *thiz)
{
	if (!thiz) return;
	thiz->_obj0 = MdNotice_New();
	thiz->boot_notice = MdNoticeNode_New();
	cbeans_fill(thiz->boot_notice, BEAN_BOOT_NOTICE);
	thiz->_obj1 = MdTaskServer_New(64);
	thiz->poller = MdPoller_New();
	cbeans_fill(thiz->poller, BEAN_POLLER);
	thiz->_obj2 = UCIPConfiger_New();
	thiz->_obj3 = MdProperty_New();
}
non VscanServer_Init(VscanServer *thiz)
{
	if (!thiz) return;
	thiz->_obj4 = CustomIO_New(22, 1);
	thiz->_obj5 = CustomIO_New(23, 1);
	thiz->_obj6 = CustomIO_New(44, 1);
	thiz->_obj7 = CustomIO_New(45, 1);
	thiz->_obj8 = CustomIOManager_New();
	thiz->_obj9 = MdNetworkConf_New();
	thiz->allocator = MdAllocator_New(256, 8);
	thiz->notice = MdNoticeNode_New();
	thiz->_obj10 = MdReadHelper_New(thiz->allocator, thiz->notice);
	thiz->booter = MdNoticeNode_New();
	thiz->tcp_server = MdTcpServer_New(thiz->_obj10, 9999, thiz->booter);
	cbeans_fill(thiz->tcp_server, BEAN_TCP_SERVER);
	thiz->ticker = MdStreamDaemon_New(thiz->notice, thiz->tcp_server, 1);
	thiz->autosdk = MdAutoSdkServer_New(thiz->notice, thiz->ticker);
	cbeans_fill(thiz->autosdk, BEAN_AUTOSDK);
	thiz->_obj11 = MdClexerServer_New(thiz->notice, thiz->ticker);
	thiz->sys_software_chn = SysSoftwareChn_New(thiz->notice, thiz->ticker);
	cbeans_fill(thiz->sys_software_chn, BEAN_SYS_SOFTWARE_CHN);
	thiz->_obj12 = LegacyConfigNotify_New();
}
int VscanBasic_Free(VscanBasic *thiz)
{
	if (!thiz) return 0;
	if (thiz->_obj2)
	{
		UCIPConfiger_Del(thiz->_obj2);
		thiz->_obj2 = 0;
	}
	if (thiz->poller)
	{
		MdPoller_Del(thiz->poller);
		thiz->poller = 0;
		cbeans_fill(0, BEAN_POLLER);
	}
	if (thiz->_obj1)
	{
		MdTaskServer_Del(thiz->_obj1);
		thiz->_obj1 = 0;
	}
	if (thiz->boot_notice)
	{
		MdNoticeNode_Del(thiz->boot_notice);
		thiz->boot_notice = 0;
		cbeans_fill(0, BEAN_BOOT_NOTICE);
	}
	if (thiz->_obj0)
	{
		MdNotice_Del(thiz->_obj0);
		thiz->_obj0 = 0;
	}
	return 0;
}
int VscanServer_Free(VscanServer *thiz)
{
	if (!thiz) return 0;
	if (thiz->_obj12)
	{
		LegacyConfigNotify_Del(thiz->_obj12);
		thiz->_obj12 = 0;
	}
	if (thiz->ticker)
	{
		MdStreamDaemon_Del(thiz->ticker);
		thiz->ticker = 0;
	}
	if (thiz->tcp_server)
	{
		MdTcpServer_Del(thiz->tcp_server);
		thiz->tcp_server = 0;
		cbeans_fill(0, BEAN_TCP_SERVER);
	}
	if (thiz->booter)
	{
		MdNoticeNode_Del(thiz->booter);
		thiz->booter = 0;
	}
	if (thiz->_obj10)
	{
		MdReadHelper_Del(thiz->_obj10);
		thiz->_obj10 = 0;
	}
	if (thiz->notice)
	{
		MdNoticeNode_Del(thiz->notice);
		thiz->notice = 0;
	}
	if (thiz->allocator)
	{
		MdAllocator_Del(thiz->allocator);
		thiz->allocator = 0;
	}
	if (thiz->_obj7)
	{
		CustomIO_Del(thiz->_obj7);
		thiz->_obj7 = 0;
	}
	if (thiz->_obj6)
	{
		CustomIO_Del(thiz->_obj6);
		thiz->_obj6 = 0;
	}
	if (thiz->_obj5)
	{
		CustomIO_Del(thiz->_obj5);
		thiz->_obj5 = 0;
	}
	if (thiz->_obj4)
	{
		CustomIO_Del(thiz->_obj4);
		thiz->_obj4 = 0;
	}
	return 0;
}
