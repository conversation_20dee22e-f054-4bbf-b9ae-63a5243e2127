//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: tracer.c
// Author		: XuCF
// Created On	: 2025/05/13
// Description	: tracer.c
//
// History
// 1. V1.0, Created by XuCF. 2025/05/13
//=============================================================================

#include "libcustom.h"
#include "autosdk_server.h"
#include "tracer.h"

API int MdScanner_GetTrace(/* get trace clip */
	ARR(O) out			   /* output buffer */
)
{
	int limit = out->len, size = 0, chunk = 0;
	while (TRACER_LOG_MAX_LEN <= limit)
	{
		chunk = tracer_read(out->ptr + size, limit);
		if (chunk <= 0) break;
		size += chunk, limit -= chunk;
	}
	out->len = size;
	LOGI("size: %d", size);
	return AUTOSDK_STAT_DONE;
}
