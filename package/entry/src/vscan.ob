VscanBasic_Init(non)
{
    MdNotice();
    MdNoticeNode#boot-notice();
    MdTaskServer(64);
    MdPoller#poller();
    UCIPConfiger();
    MdProperty();
}

VscanServer_Init(non)
{
    CustomIO(22, 1);
    CustomIO(23, 1);
    CustomIO(44, 1);
    CustomIO(45, 1);
    CustomIOManager();
    MdNetworkConf();

    // Tcp channel
    MdTcpServer#tcp-server(
        MdReadHelper(
            MdAllocator@allocator(256, 8),
            MdNoticeNode@notice()
        ),
        9999,
        MdNoticeNode@booter()
    );
    MdStreamDaemon@ticker(notice, tcp-server, 1);

    // Tcp users
    MdAutoSdkServer#autosdk(notice, ticker);
    MdClexerServer(notice, ticker);
    SysSoftwareChn#sys-software-chn(notice, ticker);

    LegacyConfigNotify();
}
