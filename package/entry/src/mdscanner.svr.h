// Auto generated, don't edit
// MdScanner_CusROI_CapROIVideo
// MdScanner_CusROI_StpROIVideo
// MdScanner_CusROI_SttROIVideo
// MdScanner_GetCodeResult O.ARR
// MdScanner_GetImage I.S32 O.ARR
// MdScanner_GetImageSizeInfo O.ARR
// MdScanner_GetPropBool I.S32 O.S32
// MdScanner_GetPropComb I.S32 O.S32
// MdScanner_GetPropEnum I.S32 O.S32
// MdScanner_GetPropFloat I.S32 O.F32
// MdScanner_GetPropInt I.S32 O.S32
// MdScanner_GetPropStr I.S32 O.BLK
// MdScanner_GetPropertiesJson O.ARR
// MdScanner_GetROISuccDecoInfo O.ARR
// MdScanner_Hello O.ARR
// MdScanner_SetPropBool I.S32 I.S32
// MdScanner_SetPropComb I.S32 I.S32
// MdScanner_SetPropEnum I.S32 I.S32
// MdScanner_SetPropFloat I.S32 I.F32
// MdScanner_SetPropInt I.S32 I.S32
// MdScanner_SetPropStr I.S32 I.ARR
// MdScanner_SetPropertiesJson I.ARR
// MdScanner_StartAdjustImagingParaMode O.S32
// MdScanner_StartDeco
// MdScanner_StopAdjustImagingParaMode
// MdScanner_StopDeco
// MdScanner_ClearUCIPList
// MdScanner_GetUCIPList O.ARR
// MdScanner_GetUCIPVersion O.ARR
// MdScanner_SetUCIPList I.ARR
// MdScanner_AppAimDecoVideo_GetInfo O.ARR
// MdScanner_AppAimDecoVideo_Start I.ARR
// MdScanner_AppAimDecoVideo_Stop
// MdScanner_GetTrace O.ARR
// MdScanner_GetCommandValue I.U32 O.ARR
// MdScanner_SetCommandValue I.S32 I.ARR
// MdScanner_GetNotify O.ARR
// MDScanner_Version O.BLK
// MdScanner_GetPropertiesBatch I.ARR O.ARR
#include "libcustom.h"
#include "autosdk_protoc.h"
#include "autosdk_server.h"
int MdScanner_CusROI_CapROIVideo(void);
static int _MdScanner_CusROI_CapROIVideo_svr_(any _hand, any _data)
{
	int _ret = MdScanner_CusROI_CapROIVideo();
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_CusROI_StpROIVideo(void);
static int _MdScanner_CusROI_StpROIVideo_svr_(any _hand, any _data)
{
	int _ret = MdScanner_CusROI_StpROIVideo();
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_CusROI_SttROIVideo(void);
static int _MdScanner_CusROI_SttROIVideo_svr_(any _hand, any _data)
{
	int _ret = MdScanner_CusROI_SttROIVideo();
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_GetCodeResult(autosdk_array_t *out);
static int _MdScanner_GetCodeResult_svr_(any _hand, any _data)
{
	any _wptr = _data + 20;
	autosdk_array_t out = {_wptr, *(int *)(_data + 16)};
	_wptr += out.len;
	int _ret = MdScanner_GetCodeResult(&out);
	if (_ret > 0) return _ret;
	*(int *)(_data + 16) = out.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20 + out.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_write(_hand, out.ptr, out.len);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_GetImage(s32 type, autosdk_array_t *out);
static int _MdScanner_GetImage_svr_(any _hand, any _data)
{
	s32 type = *(s32 *)(_data + 16);
	any _wptr = _data + 20;
	autosdk_array_t out = {_wptr, *(int *)(_data + 20)};
	_wptr += out.len;
	int _ret = MdScanner_GetImage(type, &out);
	if (_ret > 0) return _ret;
	*(int *)(_data + 16) = out.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20 + out.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_write(_hand, out.ptr, out.len);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_GetImageSizeInfo(autosdk_array_t *out);
static int _MdScanner_GetImageSizeInfo_svr_(any _hand, any _data)
{
	any _wptr = _data + 20;
	autosdk_array_t out = {_wptr, *(int *)(_data + 16)};
	_wptr += out.len;
	int _ret = MdScanner_GetImageSizeInfo(&out);
	if (_ret > 0) return _ret;
	*(int *)(_data + 16) = out.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20 + out.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_write(_hand, out.ptr, out.len);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_GetPropBool(s32 id, s32 *prop);
static int _MdScanner_GetPropBool_svr_(any _hand, any _data)
{
	s32 id = *(s32 *)(_data + 16);
	s32 *prop = (s32 *)(_data + 16);
	int _ret = MdScanner_GetPropBool(id, prop);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_GetPropComb(s32 id, s32 *prop);
static int _MdScanner_GetPropComb_svr_(any _hand, any _data)
{
	s32 id = *(s32 *)(_data + 16);
	s32 *prop = (s32 *)(_data + 16);
	int _ret = MdScanner_GetPropComb(id, prop);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_GetPropEnum(s32 id, s32 *prop);
static int _MdScanner_GetPropEnum_svr_(any _hand, any _data)
{
	s32 id = *(s32 *)(_data + 16);
	s32 *prop = (s32 *)(_data + 16);
	int _ret = MdScanner_GetPropEnum(id, prop);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_GetPropFloat(s32 id, f32 *prop);
static int _MdScanner_GetPropFloat_svr_(any _hand, any _data)
{
	s32 id = *(s32 *)(_data + 16);
	f32 *prop = (f32 *)(_data + 16);
	int _ret = MdScanner_GetPropFloat(id, prop);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_GetPropInt(s32 id, s32 *prop);
static int _MdScanner_GetPropInt_svr_(any _hand, any _data)
{
	s32 id = *(s32 *)(_data + 16);
	s32 *prop = (s32 *)(_data + 16);
	int _ret = MdScanner_GetPropInt(id, prop);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_GetPropStr(s32 id, autosdk_block_t *prop);
static int _MdScanner_GetPropStr_svr_(any _hand, any _data)
{
	s32 id = *(s32 *)(_data + 16);
	autosdk_block_t prop = {0, *(int *)(_data + 20), 0, 0};
	int _ret = MdScanner_GetPropStr(id, &prop);
	if (_ret > 0) return _ret;
	*(int *)(_data + 16) = prop.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20 + prop.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_write(_hand, prop.ptr, prop.len);
	if (prop.clean) prop.clean(prop.arg);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_GetPropertiesJson(autosdk_array_t *prop);
static int _MdScanner_GetPropertiesJson_svr_(any _hand, any _data)
{
	any _wptr = _data + 20;
	autosdk_array_t prop = {_wptr, *(int *)(_data + 16)};
	_wptr += prop.len;
	int _ret = MdScanner_GetPropertiesJson(&prop);
	if (_ret > 0) return _ret;
	*(int *)(_data + 16) = prop.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20 + prop.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_write(_hand, prop.ptr, prop.len);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_GetROISuccDecoInfo(autosdk_array_t *out);
static int _MdScanner_GetROISuccDecoInfo_svr_(any _hand, any _data)
{
	any _wptr = _data + 20;
	autosdk_array_t out = {_wptr, *(int *)(_data + 16)};
	_wptr += out.len;
	int _ret = MdScanner_GetROISuccDecoInfo(&out);
	if (_ret > 0) return _ret;
	*(int *)(_data + 16) = out.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20 + out.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_write(_hand, out.ptr, out.len);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_Hello(autosdk_array_t *out);
static int _MdScanner_Hello_svr_(any _hand, any _data)
{
	any _wptr = _data + 20;
	autosdk_array_t out = {_wptr, *(int *)(_data + 16)};
	_wptr += out.len;
	int _ret = MdScanner_Hello(&out);
	if (_ret > 0) return _ret;
	*(int *)(_data + 16) = out.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20 + out.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_write(_hand, out.ptr, out.len);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_SetPropBool(s32 id, s32 prop);
static int _MdScanner_SetPropBool_svr_(any _hand, any _data)
{
	s32 id = *(s32 *)(_data + 16);
	s32 prop = *(s32 *)(_data + 20);
	int _ret = MdScanner_SetPropBool(id, prop);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_SetPropComb(s32 id, s32 prop);
static int _MdScanner_SetPropComb_svr_(any _hand, any _data)
{
	s32 id = *(s32 *)(_data + 16);
	s32 prop = *(s32 *)(_data + 20);
	int _ret = MdScanner_SetPropComb(id, prop);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_SetPropEnum(s32 id, s32 prop);
static int _MdScanner_SetPropEnum_svr_(any _hand, any _data)
{
	s32 id = *(s32 *)(_data + 16);
	s32 prop = *(s32 *)(_data + 20);
	int _ret = MdScanner_SetPropEnum(id, prop);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_SetPropFloat(s32 id, f32 prop);
static int _MdScanner_SetPropFloat_svr_(any _hand, any _data)
{
	s32 id = *(s32 *)(_data + 16);
	f32 prop = *(f32 *)(_data + 20);
	int _ret = MdScanner_SetPropFloat(id, prop);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_SetPropInt(s32 id, s32 prop);
static int _MdScanner_SetPropInt_svr_(any _hand, any _data)
{
	s32 id = *(s32 *)(_data + 16);
	s32 prop = *(s32 *)(_data + 20);
	int _ret = MdScanner_SetPropInt(id, prop);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_SetPropStr(s32 id, autosdk_array_t *prop);
static int _MdScanner_SetPropStr_svr_(any _hand, any _data)
{
	s32 id = *(s32 *)(_data + 16);
	any _rptr = _data + 24;
	autosdk_array_t prop = {_rptr, *(int *)(_data + 20)};
	int _ret = MdScanner_SetPropStr(id, &prop);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_SetPropertiesJson(autosdk_array_t *prop);
static int _MdScanner_SetPropertiesJson_svr_(any _hand, any _data)
{
	any _rptr = _data + 20;
	autosdk_array_t prop = {_rptr, *(int *)(_data + 16)};
	int _ret = MdScanner_SetPropertiesJson(&prop);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_StartAdjustImagingParaMode(s32 *out);
static int _MdScanner_StartAdjustImagingParaMode_svr_(any _hand, any _data)
{
	s32 *out = (s32 *)(_data + 16);
	int _ret = MdScanner_StartAdjustImagingParaMode(out);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_StartDeco(void);
static int _MdScanner_StartDeco_svr_(any _hand, any _data)
{
	int _ret = MdScanner_StartDeco();
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_StopAdjustImagingParaMode(void);
static int _MdScanner_StopAdjustImagingParaMode_svr_(any _hand, any _data)
{
	int _ret = MdScanner_StopAdjustImagingParaMode();
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_StopDeco(void);
static int _MdScanner_StopDeco_svr_(any _hand, any _data)
{
	int _ret = MdScanner_StopDeco();
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_ClearUCIPList(void);
static int _MdScanner_ClearUCIPList_svr_(any _hand, any _data)
{
	int _ret = MdScanner_ClearUCIPList();
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_GetUCIPList(autosdk_array_t *out);
static int _MdScanner_GetUCIPList_svr_(any _hand, any _data)
{
	any _wptr = _data + 20;
	autosdk_array_t out = {_wptr, *(int *)(_data + 16)};
	_wptr += out.len;
	int _ret = MdScanner_GetUCIPList(&out);
	if (_ret > 0) return _ret;
	*(int *)(_data + 16) = out.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20 + out.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_write(_hand, out.ptr, out.len);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_GetUCIPVersion(autosdk_array_t *out);
static int _MdScanner_GetUCIPVersion_svr_(any _hand, any _data)
{
	any _wptr = _data + 20;
	autosdk_array_t out = {_wptr, *(int *)(_data + 16)};
	_wptr += out.len;
	int _ret = MdScanner_GetUCIPVersion(&out);
	if (_ret > 0) return _ret;
	*(int *)(_data + 16) = out.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20 + out.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_write(_hand, out.ptr, out.len);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_SetUCIPList(autosdk_array_t *pl);
static int _MdScanner_SetUCIPList_svr_(any _hand, any _data)
{
	any _rptr = _data + 20;
	autosdk_array_t pl = {_rptr, *(int *)(_data + 16)};
	int _ret = MdScanner_SetUCIPList(&pl);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_AppAimDecoVideo_GetInfo(autosdk_array_t *out);
static int _MdScanner_AppAimDecoVideo_GetInfo_svr_(any _hand, any _data)
{
	any _wptr = _data + 20;
	autosdk_array_t out = {_wptr, *(int *)(_data + 16)};
	_wptr += out.len;
	int _ret = MdScanner_AppAimDecoVideo_GetInfo(&out);
	if (_ret > 0) return _ret;
	*(int *)(_data + 16) = out.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20 + out.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_write(_hand, out.ptr, out.len);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_AppAimDecoVideo_Start(autosdk_array_t *in);
static int _MdScanner_AppAimDecoVideo_Start_svr_(any _hand, any _data)
{
	any _rptr = _data + 20;
	autosdk_array_t in = {_rptr, *(int *)(_data + 16)};
	int _ret = MdScanner_AppAimDecoVideo_Start(&in);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_AppAimDecoVideo_Stop(void);
static int _MdScanner_AppAimDecoVideo_Stop_svr_(any _hand, any _data)
{
	int _ret = MdScanner_AppAimDecoVideo_Stop();
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_GetTrace(autosdk_array_t *out);
static int _MdScanner_GetTrace_svr_(any _hand, any _data)
{
	any _wptr = _data + 20;
	autosdk_array_t out = {_wptr, *(int *)(_data + 16)};
	_wptr += out.len;
	int _ret = MdScanner_GetTrace(&out);
	if (_ret > 0) return _ret;
	*(int *)(_data + 16) = out.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20 + out.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_write(_hand, out.ptr, out.len);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_GetCommandValue(u32 id, autosdk_array_t *out);
static int _MdScanner_GetCommandValue_svr_(any _hand, any _data)
{
	u32 id = *(u32 *)(_data + 16);
	any _wptr = _data + 20;
	autosdk_array_t out = {_wptr, *(int *)(_data + 20)};
	_wptr += out.len;
	int _ret = MdScanner_GetCommandValue(id, &out);
	if (_ret > 0) return _ret;
	*(int *)(_data + 16) = out.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20 + out.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_write(_hand, out.ptr, out.len);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_SetCommandValue(s32 temp, autosdk_array_t *cmd);
static int _MdScanner_SetCommandValue_svr_(any _hand, any _data)
{
	s32 temp = *(s32 *)(_data + 16);
	any _rptr = _data + 24;
	autosdk_array_t cmd = {_rptr, *(int *)(_data + 20)};
	int _ret = MdScanner_SetCommandValue(temp, &cmd);
	if (_ret > 0) return _ret;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 16;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 16);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_GetNotify(autosdk_array_t *items);
static int _MdScanner_GetNotify_svr_(any _hand, any _data)
{
	any _wptr = _data + 20;
	autosdk_array_t items = {_wptr, *(int *)(_data + 16)};
	_wptr += items.len;
	int _ret = MdScanner_GetNotify(&items);
	if (_ret > 0) return _ret;
	*(int *)(_data + 16) = items.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20 + items.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_write(_hand, items.ptr, items.len);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_GetNotifyAwake(void *_hand)
{
	if (!_hand) return -1;
	return autosdk_portme_awake(_hand, 36);
}
int MDScanner_Version(autosdk_block_t *out);
static int _MDScanner_Version_svr_(any _hand, any _data)
{
	autosdk_block_t out = {0, *(int *)(_data + 16), 0, 0};
	int _ret = MDScanner_Version(&out);
	if (_ret > 0) return _ret;
	*(int *)(_data + 16) = out.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20 + out.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_write(_hand, out.ptr, out.len);
	if (out.clean) out.clean(out.arg);
	autosdk_portme_flush(_hand);
	return _ret;
}
int MdScanner_GetPropertiesBatch(autosdk_array_t *ids, autosdk_array_t *props);
static int _MdScanner_GetPropertiesBatch_svr_(any _hand, any _data)
{
	any _rptr = _data + 24;
	autosdk_array_t ids = {_rptr, *(int *)(_data + 16)};
	any _wptr = _data + 20;
	autosdk_array_t props = {_wptr, *(int *)(_data + 20)};
	_wptr += props.len;
	int _ret = MdScanner_GetPropertiesBatch(&ids, &props);
	if (_ret > 0) return _ret;
	*(int *)(_data + 16) = props.len;
	AUTOSDK_PACK_TYP(_data) = AUTOSDK_TYPE_ACK;
	AUTOSDK_PACK_LEN(_data) = 20 + props.len;
	AUTOSDK_PACK_RET(_data) = _ret;
	autosdk_portme_write(_hand, _data, 20);
	autosdk_portme_write(_hand, props.ptr, props.len);
	autosdk_portme_flush(_hand);
	return _ret;
}
static const any _autosdk_func_tb_[] = {
	_MdScanner_CusROI_CapROIVideo_svr_,
	_MdScanner_CusROI_StpROIVideo_svr_,
	_MdScanner_CusROI_SttROIVideo_svr_,
	_MdScanner_GetCodeResult_svr_,
	_MdScanner_GetImage_svr_,
	_MdScanner_GetImageSizeInfo_svr_,
	_MdScanner_GetPropBool_svr_,
	_MdScanner_GetPropComb_svr_,
	_MdScanner_GetPropEnum_svr_,
	_MdScanner_GetPropFloat_svr_,
	_MdScanner_GetPropInt_svr_,
	_MdScanner_GetPropStr_svr_,
	_MdScanner_GetPropertiesJson_svr_,
	_MdScanner_GetROISuccDecoInfo_svr_,
	_MdScanner_Hello_svr_,
	_MdScanner_SetPropBool_svr_,
	_MdScanner_SetPropComb_svr_,
	_MdScanner_SetPropEnum_svr_,
	_MdScanner_SetPropFloat_svr_,
	_MdScanner_SetPropInt_svr_,
	_MdScanner_SetPropStr_svr_,
	_MdScanner_SetPropertiesJson_svr_,
	_MdScanner_StartAdjustImagingParaMode_svr_,
	_MdScanner_StartDeco_svr_,
	_MdScanner_StopAdjustImagingParaMode_svr_,
	_MdScanner_StopDeco_svr_,
	_MdScanner_ClearUCIPList_svr_,
	_MdScanner_GetUCIPList_svr_,
	_MdScanner_GetUCIPVersion_svr_,
	_MdScanner_SetUCIPList_svr_,
	_MdScanner_AppAimDecoVideo_GetInfo_svr_,
	_MdScanner_AppAimDecoVideo_Start_svr_,
	_MdScanner_AppAimDecoVideo_Stop_svr_,
	_MdScanner_GetTrace_svr_,
	_MdScanner_GetCommandValue_svr_,
	_MdScanner_SetCommandValue_svr_,
	_MdScanner_GetNotify_svr_,
	_MDScanner_Version_svr_,
	_MdScanner_GetPropertiesBatch_svr_,
};
static const any _autosdk_hand_tb_[] = {
	MdScanner_CusROI_CapROIVideo,
	MdScanner_CusROI_StpROIVideo,
	MdScanner_CusROI_SttROIVideo,
	MdScanner_GetCodeResult,
	MdScanner_GetImage,
	MdScanner_GetImageSizeInfo,
	MdScanner_GetPropBool,
	MdScanner_GetPropComb,
	MdScanner_GetPropEnum,
	MdScanner_GetPropFloat,
	MdScanner_GetPropInt,
	MdScanner_GetPropStr,
	MdScanner_GetPropertiesJson,
	MdScanner_GetROISuccDecoInfo,
	MdScanner_Hello,
	MdScanner_SetPropBool,
	MdScanner_SetPropComb,
	MdScanner_SetPropEnum,
	MdScanner_SetPropFloat,
	MdScanner_SetPropInt,
	MdScanner_SetPropStr,
	MdScanner_SetPropertiesJson,
	MdScanner_StartAdjustImagingParaMode,
	MdScanner_StartDeco,
	MdScanner_StopAdjustImagingParaMode,
	MdScanner_StopDeco,
	MdScanner_ClearUCIPList,
	MdScanner_GetUCIPList,
	MdScanner_GetUCIPVersion,
	MdScanner_SetUCIPList,
	MdScanner_AppAimDecoVideo_GetInfo,
	MdScanner_AppAimDecoVideo_Start,
	MdScanner_AppAimDecoVideo_Stop,
	MdScanner_GetTrace,
	MdScanner_GetCommandValue,
	MdScanner_SetCommandValue,
	MdScanner_GetNotify,
	MDScanner_Version,
	MdScanner_GetPropertiesBatch,
};
const char *_autosdk_version_ = "c785d211";
