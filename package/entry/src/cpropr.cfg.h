// Auto generated by cpropr.py, DO NOT EDIT!

#ifndef __CPROPR_CFG_H__
#define __CPROPR_CFG_H__

// Property IDs
#define PROP_TEMPERATURE 0 // Temperature
#define PROP_UCIPGROUPFINISHPUB 1 // UCIPGroupFinishPUB
#define PROP_UCIPGROUP1ENABLE 2 // UCIPGroup1Enable
#define PROP_UCIPGROUP2ENABLE 3 // UCIPGroup2Enable
#define PROP_UCIPGROUP3ENABLE 4 // UCIPGroup3Enable
#define PROP_UCIPGROUP4ENABLE 5 // UCIPGroup4Enable
#define PROP_UCIPGROUP5ENABLE 6 // UCIPGroup5Enable
#define PROP_UCIPGROUP6ENABLE 7 // UCIPGroup6Enable
#define PROP_UCIPGROUP7ENABLE 8 // UCIPGroup7Enable
#define PROP_UCIPGROUP8ENABLE 9 // UCIPGroup8Enable
#define PROP_UCIPGROUP1EXPO 10 // UCIPGroup1Ex<PERSON>
#define PROP_UCIPGROUP2EXPO 11 // UCIPGroup2Expo
#define PROP_UCIPGROUP3EXPO 12 // UCIPGroup3Expo
#define PROP_UCIPGROUP4EXPO 13 // UCIPGroup4Expo
#define PROP_UCIPGROUP5EXPO 14 // UCIPGroup5Expo
#define PROP_UCIPGROUP6EXPO 15 // UCIPGroup6Expo
#define PROP_UCIPGROUP7EXPO 16 // UCIPGroup7Expo
#define PROP_UCIPGROUP8EXPO 17 // UCIPGroup8Expo
#define PROP_UCIPGROUP1GAIN 18 // UCIPGroup1Gain
#define PROP_UCIPGROUP2GAIN 19 // UCIPGroup2Gain
#define PROP_UCIPGROUP3GAIN 20 // UCIPGroup3Gain
#define PROP_UCIPGROUP4GAIN 21 // UCIPGroup4Gain
#define PROP_UCIPGROUP5GAIN 22 // UCIPGroup5Gain
#define PROP_UCIPGROUP6GAIN 23 // UCIPGroup6Gain
#define PROP_UCIPGROUP7GAIN 24 // UCIPGroup7Gain
#define PROP_UCIPGROUP8GAIN 25 // UCIPGroup8Gain
#define PROP_UCIPGROUP1LUMINLEVEL 26 // UCIPGroup1LuminLevel
#define PROP_UCIPGROUP2LUMINLEVEL 27 // UCIPGroup2LuminLevel
#define PROP_UCIPGROUP3LUMINLEVEL 28 // UCIPGroup3LuminLevel
#define PROP_UCIPGROUP4LUMINLEVEL 29 // UCIPGroup4LuminLevel
#define PROP_UCIPGROUP5LUMINLEVEL 30 // UCIPGroup5LuminLevel
#define PROP_UCIPGROUP6LUMINLEVEL 31 // UCIPGroup6LuminLevel
#define PROP_UCIPGROUP7LUMINLEVEL 32 // UCIPGroup7LuminLevel
#define PROP_UCIPGROUP8LUMINLEVEL 33 // UCIPGroup8LuminLevel
#define PROP_UCIPGROUP1HOLDTIME 34 // UCIPGroup1HoldTime
#define PROP_UCIPGROUP2HOLDTIME 35 // UCIPGroup2HoldTime
#define PROP_UCIPGROUP3HOLDTIME 36 // UCIPGroup3HoldTime
#define PROP_UCIPGROUP4HOLDTIME 37 // UCIPGroup4HoldTime
#define PROP_UCIPGROUP5HOLDTIME 38 // UCIPGroup5HoldTime
#define PROP_UCIPGROUP6HOLDTIME 39 // UCIPGroup6HoldTime
#define PROP_UCIPGROUP7HOLDTIME 40 // UCIPGroup7HoldTime
#define PROP_UCIPGROUP8HOLDTIME 41 // UCIPGroup8HoldTime
#define PROP_ILLUMINATIONMODE 42 // IlluminationMode
#define PROP_ILLUMINATIONLEVEL 43 // IlluminationLevel
#define PROP_LIGHTGROUPSELECT 44 // LightGroupSelect
#define PROP_AIMMODE 45 // AimMode
#define PROP_DATAFILTERMODE 46 // DataFilterMode
#define PROP_DATAFILTERENABLE1 47 // DataFilterEnable1
#define PROP_DATAFILTERENABLE2 48 // DataFilterEnable2
#define PROP_DATAFILTERENABLE3 49 // DataFilterEnable3
#define PROP_DATAFILTERENABLE4 50 // DataFilterEnable4
#define PROP_DATAFILTERFUNC1 51 // DataFilterFunc1
#define PROP_DATAFILTERFUNC2 52 // DataFilterFunc2
#define PROP_DATAFILTERFUNC3 53 // DataFilterFunc3
#define PROP_DATAFILTERFUNC4 54 // DataFilterFunc4
#define PROP_DATAFILTERFUNC5 55 // DataFilterFunc5
#define PROP_DATAFILTERFUNC6 56 // DataFilterFunc6
#define PROP_DATAFILTERFUNC7 57 // DataFilterFunc7
#define PROP_DATAFILTERFUNC8 58 // DataFilterFunc8
#define PROP_DATAFILTERFUNC9 59 // DataFilterFunc9
#define PROP_DATAFILTERFUNC10 60 // DataFilterFunc10
#define PROP_DATAFILTERREGEX1 61 // DataFilterRegex1
#define PROP_DATAFILTERREGEX2 62 // DataFilterRegex2
#define PROP_DATAFILTERREGEX3 63 // DataFilterRegex3
#define PROP_DATAFILTERREGEX4 64 // DataFilterRegex4
#define PROP_DATAFILTERREGEX5 65 // DataFilterRegex5
#define PROP_DATAFILTERREGEX6 66 // DataFilterRegex6
#define PROP_DATAFILTERREGEX7 67 // DataFilterRegex7
#define PROP_DATAFILTERREGEX8 68 // DataFilterRegex8
#define PROP_DATAFILTERREGEX9 69 // DataFilterRegex9
#define PROP_DATAFILTERREGEX10 70 // DataFilterRegex10
#define PROP_AIMDECOVIDEOSETDECOTIMEMS 71 // AimDecoVideoSetDecoTimeMs
#define PROP_DOUBLECONFIRM 72 // DoubleConfirm
#define PROP_SAMEBARDELAY1D 73 // SameBarDelay1D
#define PROP_SAMEBARDELAY2D 74 // SameBarDelay2D
#define PROP_GOODREADDELAY 75 // GoodReadDelay
#define PROP_CODESYMB1D 76 // CodeSymb1D
#define PROP_CODESYMB2D 77 // CodeSymb2D
#define PROP_MULTISYMBREAD 78 // MultiSymbRead
#define PROP_CENTERINGREAD 79 // CenteringRead
#define PROP_SCREENREAD 80 // ScreenRead
#define PROP_CODE39READMAIN 81 // Code39ReadMain
#define PROP_CODE39CHKDIGVER 82 // Code39ChkDigVer
#define PROP_CODE39CHKDIGTX 83 // Code39ChkDigTx
#define PROP_CODE39MAXLEN 84 // Code39MaxLen
#define PROP_CODE39MINLEN 85 // Code39MinLen
#define PROP_CODE39CODEID 86 // Code39CodeID
#define PROP_CODE39INSERTSTR 87 // Code39InsertStr
#define PROP_CODE39FORMAT 88 // Code39Format
#define PROP_CODE39STEDTRANS 89 // Code39StEdTrans
#define PROP_CODE39ASTEASCHAR 90 // Code39AsteAsChar
#define PROP_CODE39CODE32 91 // Code39Code32
#define PROP_CODE39CODE32PRE 92 // Code39Code32Pre
#define PROP_CODE39TRIOPTIC 93 // Code39Trioptic
#define PROP_CODE39TOSTEDTRANS 94 // Code39TOStEdTrans
#define PROP_CODE39AIMID 95 // Code39AimID
#define PROP_CODE128READMAIN 96 // Code128ReadMain
#define PROP_CODE128CHKDIGVER 97 // Code128ChkDigVer
#define PROP_CODE128CHKDIGTX 98 // Code128ChkDigTx
#define PROP_CODE128MAXLEN 99 // Code128MaxLen
#define PROP_CODE128MINLEN 100 // Code128MinLen
#define PROP_CODE128CODEID 101 // Code128CodeID
#define PROP_CODE128INSERTSTR 102 // Code128InsertStr
#define PROP_CODE128TRUZERO 103 // Code128TruZero
#define PROP_CODE128AIMID 104 // Code128AimID
#define PROP_CODABARREADMAIN 105 // CodabarReadMain
#define PROP_CODABARCHKDIGVER 106 // CodabarChkDigVer
#define PROP_CODABARCHKDIGTX 107 // CodabarChkDigTx
#define PROP_CODABARMAXLEN 108 // CodabarMaxLen
#define PROP_CODABARMINLEN 109 // CodabarMinLen
#define PROP_CODABARCODEID 110 // CodabarCodeID
#define PROP_CODABARINSERTSTR 111 // CodabarInsertStr
#define PROP_CODABARSTARTSTOPTYPE 112 // CodabarStartStopType
#define PROP_CODABARSTARTSTOPTX 113 // CodabarStartStopTx
#define PROP_CODABARSTARTENDCHAREQ 114 // CodabarStartEndCharEq
#define PROP_CODABARAIMID 115 // CodabarAimID
#define PROP_INTL25READMAIN 116 // Intl25ReadMain
#define PROP_INTL25CHKDIGVER 117 // Intl25ChkDigVer
#define PROP_INTL25CHKDIGTX 118 // Intl25ChkDigTx
#define PROP_INTL25MAXLEN 119 // Intl25MaxLen
#define PROP_INTL25MINLEN 120 // Intl25MinLen
#define PROP_INTL25CODEID 121 // Intl25CodeID
#define PROP_INTL25INSERTSTR 122 // Intl25InsertStr
#define PROP_INTL25LENRESTR 123 // Intl25LenRestr
#define PROP_INTL25BANKFORMATCONV 124 // Intl25BankFormatConv
#define PROP_INTL25BANKFORMATTYPE 125 // Intl25BankFormatType
#define PROP_INTL25AIMID 126 // Intl25AimID
#define PROP_CODE93READMAIN 127 // Code93ReadMain
#define PROP_CODE93CHKDIGVER 128 // Code93ChkDigVer
#define PROP_CODE93CHKDIGTX 129 // Code93ChkDigTx
#define PROP_CODE93MAXLEN 130 // Code93MaxLen
#define PROP_CODE93MINLEN 131 // Code93MinLen
#define PROP_CODE93CODEID 132 // Code93CodeID
#define PROP_CODE93INSERTSTR 133 // Code93InsertStr
#define PROP_CODE93AIMID 134 // Code93AimID
#define PROP_EAN8READMAIN 135 // EAN8ReadMain
#define PROP_EAN8CHKDIGVER 136 // EAN8ChkDigVer
#define PROP_EAN8CHKDIGTX 137 // EAN8ChkDigTx
#define PROP_EAN8CODEID 138 // EAN8CodeID
#define PROP_EAN8INSERTSTR 139 // EAN8InsertStr
#define PROP_EAN8SUPLDIG 140 // EAN8SuplDig
#define PROP_EAN8TRUNEXP 141 // EAN8TrunExp
#define PROP_EAN8AIMID 142 // EAN8AimID
#define PROP_EAN13READMAIN 143 // EAN13ReadMain
#define PROP_EAN13CHKDIGVER 144 // EAN13ChkDigVer
#define PROP_EAN13CHKDIGTX 145 // EAN13ChkDigTx
#define PROP_EAN13CODEID 146 // EAN13CodeID
#define PROP_EAN13INSERTSTR 147 // EAN13InsertStr
#define PROP_EAN13SUPLDIG 148 // EAN13SuplDig
#define PROP_EAN13ISBSN 149 // EAN13ISBSN
#define PROP_EAN13ISSNISBNID 150 // EAN13ISSNISBNID
#define PROP_EAN13AIMID 151 // EAN13AimID
#define PROP_UPCAREADMAIN 152 // UPCAReadMain
#define PROP_UPCACHKDIGVER 153 // UPCAChkDigVer
#define PROP_UPCACHKDIGTX 154 // UPCAChkDigTx
#define PROP_UPCACODEID 155 // UPCACodeID
#define PROP_UPCAINSERTSTR 156 // UPCAInsertStr
#define PROP_UPCASUPLDIG 157 // UPCASuplDig
#define PROP_UPCATRUNEXP 158 // UPCATrunExp
#define PROP_UPCAAIMID 159 // UPCAAimID
#define PROP_UPCEREADMAIN 160 // UPCEReadMain
#define PROP_UPCECHKDIGVER 161 // UPCEChkDigVer
#define PROP_UPCECHKDIGTX 162 // UPCEChkDigTx
#define PROP_UPCECODEID 163 // UPCECodeID
#define PROP_UPCEINSERTSTR 164 // UPCEInsertStr
#define PROP_UPCESUPLDIG 165 // UPCESuplDig
#define PROP_UPCETRUNEXP 166 // UPCETrunExp
#define PROP_UPCEAIMID 167 // UPCEAimID
#define PROP_CODE11READMAIN 168 // Code11ReadMain
#define PROP_CODE11CHKDIGVER 169 // Code11ChkDigVer
#define PROP_CODE11CHKDIGTX 170 // Code11ChkDigTx
#define PROP_CODE11MAXLEN 171 // Code11MaxLen
#define PROP_CODE11MINLEN 172 // Code11MinLen
#define PROP_CODE11CODEID 173 // Code11CodeID
#define PROP_CODE11INSERTSTR 174 // Code11InsertStr
#define PROP_CODE11AIMID 175 // Code11AimID
#define PROP_GS1EXPAREADMAIN 176 // GS1EXPAReadMain
#define PROP_GS1EXPAMAXLEN 177 // GS1EXPAMaxLen
#define PROP_GS1EXPAMINLEN 178 // GS1EXPAMinLen
#define PROP_GS1EXPACODEID 179 // GS1EXPACodeID
#define PROP_GS1EXPAINSERTSTR 180 // GS1EXPAInsertStr
#define PROP_GS1EXPACONVERSION 181 // GS1EXPAConversion
#define PROP_GS1EXPAIMID 182 // GS1EXPAimID
#define PROP_GS1DBREADMAIN 183 // GS1DBReadMain
#define PROP_GS1DBCODEID 184 // GS1DBCodeID
#define PROP_GS1DBINSERTSTR 185 // GS1DBInsertStr
#define PROP_GS1DBCONVERSION 186 // GS1DBConversion
#define PROP_GS1DBOUTPUTBRACKETS 187 // GS1DBOutputBrackets
#define PROP_GS1DBAIMID 188 // GS1DBAimID
#define PROP_GS1LIMIREADMAIN 189 // GS1LIMIReadMain
#define PROP_GS1LIMICODEID 190 // GS1LIMICodeID
#define PROP_GS1LIMIINSERTSTR 191 // GS1LIMIInsertStr
#define PROP_GS1LIMICONVERSION 192 // GS1LIMIConversion
#define PROP_GS1LIMIAIMID 193 // GS1LIMIAimID
#define PROP_ISBT128READMAIN 194 // ISBT128ReadMain
#define PROP_ISBT128CKDIGIVER 195 // ISBT128CkDigiVer
#define PROP_ISBT128CHKDIGTRANS 196 // ISBT128ChkDigTrans
#define PROP_ISBT128MAXLEN 197 // ISBT128MaxLen
#define PROP_ISBT128MINLEN 198 // ISBT128MinLen
#define PROP_ISBT128CODEID 199 // ISBT128CodeID
#define PROP_ISBT128INSERTSTR 200 // ISBT128InsertStr
#define PROP_ISBT128AIMID 201 // ISBT128AimID
#define PROP_MSIPLREADMAIN 202 // MSIPLReadMain
#define PROP_MSIPLCHKDIGVER 203 // MSIPLChkDigVer
#define PROP_MSIPLCHKDIGTX 204 // MSIPLChkDigTx
#define PROP_MSIPLMAXLEN 205 // MSIPLMaxLen
#define PROP_MSIPLMINLEN 206 // MSIPLMinLen
#define PROP_MSIPLCODEID 207 // MSIPLCodeID
#define PROP_MSIPLINSERTSTR 208 // MSIPLInsertStr
#define PROP_MSIPLAIMID 209 // MSIPLAimID
#define PROP_UCCEAN128READMAIN 210 // UCCEAN128ReadMain
#define PROP_UCCEAN128CHKDIGVER 211 // UCCEAN128ChkDigVer
#define PROP_UCCEAN128CHKDIGTX 212 // UCCEAN128ChkDigTx
#define PROP_UCCEAN128MAXLEN 213 // UCCEAN128MaxLen
#define PROP_UCCEAN128MINLEN 214 // UCCEAN128MinLen
#define PROP_UCCEAN128CODEID 215 // UCCEAN128CodeID
#define PROP_UCCEAN128INSERTSTR 216 // UCCEAN128InsertStr
#define PROP_UCCEAN128TRUZERO 217 // UCCEAN128TruZero
#define PROP_UCCEAN128AIMID 218 // UCCEAN128AimID
#define PROP_UKPLREADMAIN 219 // UKPLReadMain
#define PROP_UKPLCHKDIGVER 220 // UKPLChkDigVer
#define PROP_UKPLCHKDIGTX 221 // UKPLChkDigTx
#define PROP_UKPLMAXLEN 222 // UKPLMaxLen
#define PROP_UKPLMINLEN 223 // UKPLMinLen
#define PROP_UKPLCODEID 224 // UKPLCodeID
#define PROP_UKPLINSERTSTR 225 // UKPLInsertStr
#define PROP_UKPLAIMID 226 // UKPLAimID
#define PROP_UPCE1READMAIN 227 // UPCE1ReadMain
#define PROP_UPCE1CHKDIGVER 228 // UPCE1ChkDigVer
#define PROP_UPCE1CHKDIGTRANS 229 // UPCE1ChkDigTrans
#define PROP_UPCE1CODEID 230 // UPCE1CodeID
#define PROP_UPCE1INSERTSTR 231 // UPCE1InsertStr
#define PROP_UPCE1SUPLDIG 232 // UPCE1SuplDig
#define PROP_UPCE1TRUNEXP 233 // UPCE1TrunExp
#define PROP_UPCE1AIMID 234 // UPCE1AimID
#define PROP_CHNPSTREADMAIN 235 // CHNPSTReadMain
#define PROP_CHNPSTCHKDIGVER 236 // CHNPSTChkDigVer
#define PROP_CHNPSTCHKDIGTX 237 // CHNPSTChkDigTx
#define PROP_CHNPSTMAXLEN 238 // CHNPSTMaxLen
#define PROP_CHNPSTMINLEN 239 // CHNPSTMinLen
#define PROP_CHNPSTCODEID 240 // CHNPSTCodeID
#define PROP_CHNPSTINSERTSTR 241 // CHNPSTInsertStr
#define PROP_CHNPSTAIMID 242 // CHNPSTAimID
#define PROP_INDUSTRIAL25READMAIN 243 // Industrial25ReadMain
#define PROP_INDUSTRIAL25MAXLEN 244 // Industrial25MaxLen
#define PROP_INDUSTRIAL25MINLEN 245 // Industrial25MinLen
#define PROP_INDUSTRIAL25CODEID 246 // Industrial25CodeID
#define PROP_INDUSTRIAL25INSERTSTR 247 // Industrial25InsertStr
#define PROP_INDUSTRIAL25AIMID 248 // Industrial25AimID
#define PROP_MATRIX25READMAIN 249 // Matrix25ReadMain
#define PROP_MATRIX25CHKDIGVER 250 // Matrix25ChkDigVer
#define PROP_MATRIX25CHKDIGTRANS 251 // Matrix25ChkDigTrans
#define PROP_MATRIX25MAXLEN 252 // Matrix25MaxLen
#define PROP_MATRIX25MINLEN 253 // Matrix25MinLen
#define PROP_MATRIX25CODEID 254 // Matrix25CodeID
#define PROP_MATRIX25INSERTSTR 255 // Matrix25InsertStr
#define PROP_MATRIX25AIMID 256 // Matrix25AimID
#define PROP_QRCODEREADMAIN 257 // QRCodeReadMain
#define PROP_QRCODECODEID 258 // QRCodeCodeID
#define PROP_QRCODEWEBCODEFORBID 259 // QRCodeWebCodeForbid
#define PROP_QRCODEMAXLEN 260 // QRCodeMaxLen
#define PROP_QRCODEMINLEN 261 // QRCodeMinLen
#define PROP_QRCODEAIMID 262 // QRCodeAimID
#define PROP_DMREADMAIN 263 // DMReadMain
#define PROP_DMCODEID 264 // DMCodeID
#define PROP_DMMAXLEN 265 // DMMaxLen
#define PROP_DMMINLEN 266 // DMMinLen
#define PROP_DMPPNCODE 267 // DMPPNCODE
#define PROP_DMAIMID 268 // DMAimID
#define PROP_AZTECREADMAIN 269 // AztecReadMain
#define PROP_AZTECCODEID 270 // AztecCodeID
#define PROP_AZTECMAXLEN 271 // AztecMaxLen
#define PROP_AZTECMINLEN 272 // AztecMinLen
#define PROP_AZTECAIMID 273 // AztecAimID
#define PROP_CODABLOCKFREADMAIN 274 // CodaBlockFReadMain
#define PROP_CODABLOCKFCODEID 275 // CodaBlockFCodeID
#define PROP_CODABLOCKFMAXLEN 276 // CodaBlockFMaxLen
#define PROP_CODABLOCKFMINLEN 277 // CodaBlockFMinLen
#define PROP_CODABLOCKFAIMID 278 // CodaBlockFAimID
#define PROP_GMCODEREADMAIN 279 // GMCodeReadMain
#define PROP_GMCODECODEID 280 // GMCodeCodeID
#define PROP_GMCODEMAXLEN 281 // GMCodeMaxLen
#define PROP_GMCODEMINLEN 282 // GMCodeMinLen
#define PROP_MAXICODEREADMAIN 283 // MaxiCodeReadMain
#define PROP_MAXICODEMAXLEN 284 // MaxiCodeMaxLen
#define PROP_MAXICODEMINLEN 285 // MaxiCodeMinLen
#define PROP_MAXICODEAIMID 286 // MaxiCodeAimID
#define PROP_MICPDF417READMAIN 287 // MICPDF417ReadMain
#define PROP_MICPDF417CODEID 288 // MICPDF417CodeID
#define PROP_MICPDF417INSERTSTR 289 // MICPDF417InsertStr
#define PROP_MICPDF417CONVERSION 290 // MICPDF417Conversion
#define PROP_MICPDF417MAXLEN 291 // MICPDF417MaxLen
#define PROP_MICPDF417MINLEN 292 // MICPDF417MinLen
#define PROP_MICPDF417AIMID 293 // MICPDF417AimID
#define PROP_MICROQRREADMAIN 294 // MicroQRReadMain
#define PROP_MICROQRCODEID 295 // MicroQRCodeID
#define PROP_MICROQRMAXLEN 296 // MicroQRMaxLen
#define PROP_MICROQRMINLEN 297 // MicroQRMinLen
#define PROP_MICROQRAIMID 298 // MicroQRAimID
#define PROP_PDF417READMAIN 299 // PDF417ReadMain
#define PROP_PDF417CODEID 300 // PDF417CodeID
#define PROP_PDF417INSERTSTR 301 // PDF417InsertStr
#define PROP_PDF417CONVERSION 302 // PDF417Conversion
#define PROP_PDF417MAXLEN 303 // PDF417MaxLen
#define PROP_PDF417MINLEN 304 // PDF417MinLen
#define PROP_PDF417AIMID 305 // PDF417AimID
#define PROP_BINARYCODEREADMAIN 306 // BinaryCodeReadMain
#define PROP_BINARYCODECODEID 307 // BinaryCodeCodeID
#define PROP_BINARYCODEMAXLEN 308 // BinaryCodeMaxLen
#define PROP_BINARYCODEMINLEN 309 // BinaryCodeMinLen
#define PROP_BINARYCODEAIMID 310 // BinaryCodeAimID
#define PROP_CSCODEREADMAIN 311 // CSCodeReadMain
#define PROP_CSCODECODEID 312 // CSCodeCodeID
#define PROP_CSCODEMAXLEN 313 // CSCodeMaxLen
#define PROP_CSCODEMINLEN 314 // CSCodeMinLen
#define PROP_CSCODEAIMID 315 // CSCodeAimID
#define PROP_DOTCODEREADMAIN 316 // DotCodeReadMain
#define PROP_DOTCODECODEID 317 // DotCodeCodeID
#define PROP_DOTCODEMAXLEN 318 // DotCodeMaxLen
#define PROP_DOTCODEMINLEN 319 // DotCodeMinLen
#define PROP_DOTCODEAIMID 320 // DotCodeAimID
#define PROP_CHNFNCREADMAIN 321 // CHNFNCReadMain
#define PROP_CHNFNCMAXLEN 322 // CHNFNCMaxLen
#define PROP_CHNFNCMINLEN 323 // CHNFNCMinLen
#define PROP_CHNFNCCHKDIGVER 324 // CHNFNCChkDigVer
#define PROP_CHNFNCFRTCHARCONV 325 // CHNFNCFrtCharConv
#define PROP_CHNFNCFRTCHARSPEC 326 // CHNFNCFrtCharSpec
#define PROP_CHNFNCCODEID 327 // CHNFNCCodeID
#define PROP_CHNFNCINSERTSTR 328 // CHNFNCInsertStr
#define PROP_CHNFNCAIMID 329 // CHNFNCAimID
#define PROP_CUSTOM_DECODE_ROI_1 330 // Custom_Decode_ROI_1
#define PROP_CUSTOM_DECODE_ROI_2 331 // Custom_Decode_ROI_2
#define PROP_CUSTOM_DECODE_ROI_3 332 // Custom_Decode_ROI_3
#define PROP_CUSTOM_DECODE_ROI_4 333 // Custom_Decode_ROI_4
#define PROP_CUSTOM_DECODE_ROI_5 334 // Custom_Decode_ROI_5
#define PROP_CUSTOM_DECODE_ROI_6 335 // Custom_Decode_ROI_6
#define PROP_CUSTOM_DECODE_ROI_7 336 // Custom_Decode_ROI_7
#define PROP_CUSTOM_DECODE_ROI_8 337 // Custom_Decode_ROI_8
#define PROP_CUSTOM_DECODE_ROI_9 338 // Custom_Decode_ROI_9
#define PROP_CUSTOM_DECODE_ROI_10 339 // Custom_Decode_ROI_10
#define PROP_CUSTOM_DECODE_ROI_11 340 // Custom_Decode_ROI_11
#define PROP_CUSTOM_DECODE_ROI_12 341 // Custom_Decode_ROI_12
#define PROP_NETLED 342 // NetLed
#define PROP_INSERTSTR 343 // InsertStr
#define PROP_BACKTX 344 // BackTx
#define PROP_NOREADOUTPUT 345 // NoReadOutput
#define PROP_DATABATCH 346 // DataBatch
#define PROP_SCANMODE 347 // ScanMode
#define PROP_SCANKEEPTIME 348 // ScanKeepTime
#define PROP_SCANKEEPTIMEADVANCED 349 // ScanKeepTimeAdvanced
#define PROP_MOVEMENTSCANMODE 350 // MovementScanMode
#define PROP_POWERINDICATION 351 // PowerIndication
#define PROP_LEDINDICATION 352 // LedIndication
#define PROP_BEEPINDICATION 353 // BeepIndication
#define PROP_BEEPTIME 354 // BeepTime
#define PROP_BEEPVOLUME 355 // BeepVolume
#define PROP_BEEPTONE 356 // BeepTone
#define PROP_POWERLEDCTRL 357 // PowerLEDCtrl
#define PROP_CUSTOMIO1MODEMAIN 358 // CustomIO1ModeMain
#define PROP_CUSTOMIO2MODEMAIN 359 // CustomIO2ModeMain
#define PROP_CUSTOMIO3MODEMAIN 360 // CustomIO3ModeMain
#define PROP_CUSTOMIO4MODEMAIN 361 // CustomIO4ModeMain
#define PROP_CUSTOMIO1INPUTCOUNT 362 // CustomIO1InputCount
#define PROP_CUSTOMIO2INPUTCOUNT 363 // CustomIO2InputCount
#define PROP_CUSTOMIO3INPUTCOUNT 364 // CustomIO3InputCount
#define PROP_CUSTOMIO4INPUTCOUNT 365 // CustomIO4InputCount
#define PROP_CUSTOMIO1DEBOUNCE 366 // CustomIO1Debounce
#define PROP_CUSTOMIO2DEBOUNCE 367 // CustomIO2Debounce
#define PROP_CUSTOMIO3DEBOUNCE 368 // CustomIO3Debounce
#define PROP_CUSTOMIO4DEBOUNCE 369 // CustomIO4Debounce
#define PROP_CUSTOMIO1RISINGACTION 370 // CustomIO1RisingAction
#define PROP_CUSTOMIO2RISINGACTION 371 // CustomIO2RisingAction
#define PROP_CUSTOMIO3RISINGACTION 372 // CustomIO3RisingAction
#define PROP_CUSTOMIO4RISINGACTION 373 // CustomIO4RisingAction
#define PROP_CUSTOMIO1RISINGDELAY 374 // CustomIO1RisingDelay
#define PROP_CUSTOMIO2RISINGDELAY 375 // CustomIO2RisingDelay
#define PROP_CUSTOMIO3RISINGDELAY 376 // CustomIO3RisingDelay
#define PROP_CUSTOMIO4RISINGDELAY 377 // CustomIO4RisingDelay
#define PROP_CUSTOMIO1FALLINGACTION 378 // CustomIO1FallingAction
#define PROP_CUSTOMIO2FALLINGACTION 379 // CustomIO2FallingAction
#define PROP_CUSTOMIO3FALLINGACTION 380 // CustomIO3FallingAction
#define PROP_CUSTOMIO4FALLINGACTION 381 // CustomIO4FallingAction
#define PROP_CUSTOMIO1FALLINGDELAY 382 // CustomIO1FallingDelay
#define PROP_CUSTOMIO2FALLINGDELAY 383 // CustomIO2FallingDelay
#define PROP_CUSTOMIO3FALLINGDELAY 384 // CustomIO3FallingDelay
#define PROP_CUSTOMIO4FALLINGDELAY 385 // CustomIO4FallingDelay
#define PROP_CUSTOMIO1ACTIVECONDITION 386 // CustomIO1ActiveCondition
#define PROP_CUSTOMIO2ACTIVECONDITION 387 // CustomIO2ActiveCondition
#define PROP_CUSTOMIO3ACTIVECONDITION 388 // CustomIO3ActiveCondition
#define PROP_CUSTOMIO4ACTIVECONDITION 389 // CustomIO4ActiveCondition
#define PROP_CUSTOMIO1INACTIVECONDITION 390 // CustomIO1InactiveCondition
#define PROP_CUSTOMIO2INACTIVECONDITION 391 // CustomIO2InactiveCondition
#define PROP_CUSTOMIO3INACTIVECONDITION 392 // CustomIO3InactiveCondition
#define PROP_CUSTOMIO4INACTIVECONDITION 393 // CustomIO4InactiveCondition
#define PROP_CUSTOMIO1ACTIVEDELAY 394 // CustomIO1ActiveDelay
#define PROP_CUSTOMIO2ACTIVEDELAY 395 // CustomIO2ActiveDelay
#define PROP_CUSTOMIO3ACTIVEDELAY 396 // CustomIO3ActiveDelay
#define PROP_CUSTOMIO4ACTIVEDELAY 397 // CustomIO4ActiveDelay
#define PROP_CUSTOMIO1INACTIVETIMER 398 // CustomIO1InactiveTimer
#define PROP_CUSTOMIO2INACTIVETIMER 399 // CustomIO2InactiveTimer
#define PROP_CUSTOMIO3INACTIVETIMER 400 // CustomIO3InactiveTimer
#define PROP_CUSTOMIO4INACTIVETIMER 401 // CustomIO4InactiveTimer
#define PROP_CUSTOMIO1ACTIVECMD 402 // CustomIO1ActiveCmd
#define PROP_CUSTOMIO2ACTIVECMD 403 // CustomIO2ActiveCmd
#define PROP_CUSTOMIO3ACTIVECMD 404 // CustomIO3ActiveCmd
#define PROP_CUSTOMIO4ACTIVECMD 405 // CustomIO4ActiveCmd
#define PROP_CUSTOMIO1INACTIVECMD 406 // CustomIO1InactiveCmd
#define PROP_CUSTOMIO2INACTIVECMD 407 // CustomIO2InactiveCmd
#define PROP_CUSTOMIO3INACTIVECMD 408 // CustomIO3InactiveCmd
#define PROP_CUSTOMIO4INACTIVECMD 409 // CustomIO4InactiveCmd
#define PROP_CHARENCODE 410 // CharEncode
#define PROP_CHARPRINTCTR 411 // CharPrintCtr
#define PROP_DECODERESULTMAXNUM 412 // DecodeResultMaxNum
#define PROP_FIRSTONLY 413 // FirstOnly
#define PROP_LASTONLY 414 // LastOnly
#define PROP_SINGREPLAC1 415 // SingReplaC1
#define PROP_SINGREPLAC2 416 // SingReplaC2
#define PROP_SINGREPLAC3 417 // SingReplaC3
#define PROP_REPLACHAR2STR 418 // ReplaChar2Str
#define PROP_CASECONV 419 // CaseConv
#define PROP_FN1SUBSTUTEXTENABLE 420 // FN1SubstuTextEnable
#define PROP_CHARACTERSTOSTRINGS 421 // CharactersToStrings
#define PROP_DECORSLTCHECK 422 // DecoRsltCheck
#define PROP_DECORSLTCHECK1HEADSTR 423 // DecoRsltCheck1HeadStr
#define PROP_DECORSLTCHECK1TAILSTR 424 // DecoRsltCheck1TailStr
#define PROP_DECORESULTCHECKFAILINDICATION 425 // DecoResultCheckFailIndication
#define PROP_GROUP1TEXT 426 // Group1Text
#define PROP_GROUP2TEXT 427 // Group2Text
#define PROP_GROUP3TEXT 428 // Group3Text
#define PROP_GROUP4TEXT 429 // Group4Text
#define PROP_GROUP1POS 430 // Group1Pos
#define PROP_GROUP2POS 431 // Group2Pos
#define PROP_GROUP3POS 432 // Group3Pos
#define PROP_GROUP4POS 433 // Group4Pos
#define PROP_USBETHERNETCOMPOSITEENMAIN 434 // USBEthernetCompositeEnMain
#define PROP_USBETHERNETCOMPOSITETYPE 435 // USBEthernetCompositeType
#define PROP_ETHERNETIPOUTPUTEN 436 // EthernetIpOutputEn
#define PROP_MODBUSSVROUTPUTEN 437 // ModbusSvrOutputEn
#define PROP_UARTENABLE 438 // UartEnable
#define PROP_PROFINETOUTPUTENMAIN 439 // ProfinetOutputEnMain
#define PROP_PROFINETSTATIONNAME 440 // ProfinetStationName
#define PROP_TCPCLIOUTPUTENMAIN 441 // TCPCliOutputEnMain
#define PROP_TCPCLITARGETSVRIP 442 // TCPCliTargetSvrIP
#define PROP_TCPCLITARGETSVRPORT 443 // TCPCliTargetSvrPort
#define PROP_TCPCLIOUTPUTFMT 444 // TCPCliOutputFmt
#define PROP_TCPCLIOUTPUTPREFIX 445 // TCPCliOutputPrefix
#define PROP_TCPCLIOUTPUTSUFFIX 446 // TCPCliOutputSuffix
#define PROP_TCPSVROUTPUTENABLEMAIN 447 // TCPSvrOutputEnableMain
#define PROP_TCPSVROUTPUTFMT 448 // TCPSvrOutputFmt
#define PROP_TCPSVROUTPUTPREFIX 449 // TCPSvrOutputPrefix
#define PROP_TCPSVROUTPUTSUFFIX 450 // TCPSvrOutputSuffix
#define PROP_TCPSVRSERVICEPORT 451 // TCPSvrServicePort
#define PROP_RS232FLOWCONTROL 452 // RS232FlowControl
#define PROP_RS232CHARDELAY 453 // RS232CharDelay
#define PROP_RS232RESPONSEDELAY 454 // RS232ResponseDelay
#define PROP_RS232BAUDRATE 455 // RS232BaudRate
#define PROP_RS232PARITY 456 // RS232Parity
#define PROP_RS232DATABITS 457 // RS232DataBits
#define PROP_RS232STOPBITS 458 // RS232StopBits
#define PROP_RS232HOSTTYPE 459 // RS232HostType
#define PROP_RS232DECODATAFORMAT 460 // RS232DecoDataFormat
#define PROP_RS232HSCHARTIMEOUT 461 // RS232HsCharTimeout
#define PROP_USBTYPE 462 // USBType
#define PROP_USBKEYBOARDLAYOUT 463 // USBKeyboardLayout
#define PROP_USBKEYDLY 464 // USBKeyDly
#define PROP_USBNUMBKEY 465 // USBNumbKey
#define PROP_USBFUNCKEY 466 // USBFuncKey
#define PROP_USBHOSTPOLLINGINTER 467 // USBHostPollingInter
#define PROP_USBKEYSENDMODE 468 // USBKeySendMode
#define PROP_USBENTERMODE 469 // USBEnterMode
#define PROP_USBMODKEYSRELEASEMODE 470 // USBModKeysReleaseMode
#define PROP_USBKEYTXTIMEOUT 471 // USBKeyTxTimeout
#define PROP_USBENUMFAILREBOOT 472 // UsbEnumFailReboot
#define PROP_FOCAL 473 // Focal
#define PROP_CONFIGBYCODE 474 // ConfigByCode
#define PROP_INTERFACETYPEMAIN 475 // InterfaceTypeMain
#define PROP_RS232BYTEDELAY 476 // RS232ByteDelay
#define PROP_CODEMAXLEN1D 477 // CodeMaxLen1D
#define PROP_CODEMINLEN1D 478 // CodeMinLen1D
#define PROP_CODEELEMENTAMEND 479 // CodeElementAmend
#define PROP_DECODEOPTIMIZE 480 // DecodeOptimize
#define PROP_OUTPUTDELAYCONTSCAN 481 // OutputDelayContScan
#define PROP_SAMEBARDELAYDR 482 // SameBarDelayDR
#define PROP_SWEEPINGSCANENHANCE 483 // SweepingScanEnhance
#define PROP_VIBRATORINDICATION 484 // VibratorIndication
#define PROP_SILENTMODE 485 // SilentMode
#define PROP_BEEPSOUNDMODE 486 // BeepSoundMode
#define PROP_INDICATIONTXDELAY 487 // IndicationTxDelay
#define PROP_VOICEDLY 488 // VoiceDly
#define PROP_INFRAREDSENSOR 489 // InfraredSensor
#define PROP_INFRAREDMODE 490 // InfraredMode
#define PROP_AUTODETEKEEPTIME 491 // AutoDeteKeepTime
#define PROP_AUTODETETEXTURE 492 // AutoDeteTexture
#define PROP_AUTODETELUM 493 // AutoDeteLum
#define PROP_AUTODETETXTUREINTER 494 // AutoDeteTxtureInter
#define PROP_AUTODETESLEEPFRMINTER 495 // AutoDeteSleepFrmInter
#define PROP_INFRAREDMSGSWITCH 496 // InfraredMsgSwitch
#define PROP_INFRAREDLUM 497 // InfraredLum
#define PROP_IRLUMDETESENLEVEL 498 // IRLumDeteSenLevel
#define PROP_DPMREAD 499 // DPMRead
#define PROP_COMPOSITEREAD 500 // CompositeRead
#define PROP_OCRRECOGNIZEMAIN 501 // OCRRecognizeMain
#define PROP_OCRRECOGNIZEPASSPORTMRZ 502 // OCRRecognizePassportMRZ
#define PROP_OCRRECOGNIZENATIONALCARDID 503 // OCRRecognizeNationalCardID
#define PROP_OCRTURKISHIDOUTPUTFORMAT 504 // OCRTurkishIDOutputFormat
#define PROP_OCRDOUBLECHECK 505 // OCRDoubleCheck
#define PROP_IVDDETETUBECAPMAIN 506 // IVDDeteTubeCapMain
#define PROP_IVDDETETUBEHEIGHT 507 // IVDDeteTubeHeight
#define PROP_AIMKEEPTIMEAFTERDECO 508 // AimKeepTimeAfterDeco
#define PROP_LIGHTDRIVECAPACITY 509 // LightDriveCapacity
#define PROP_LIGHTMAXPERLED 510 // LightMaxPerLED
#define PROP_LIGHTGROUP1CURRENT 511 // LightGroup1Current
#define PROP_LIGHTGROUP2CURRENT 512 // LightGroup2Current
#define PROP_AIMKEEPTIMEBEFOREDECO 513 // AimKeepTimeBeforeDeco
#define PROP_AUXILIARYLUM 514 // AuxiliaryLum
#define PROP_AUXILIARYLUMCOMPTIME 515 // AuxiliaryLumCompTime
#define PROP_IVDTUBEHIGHTINFO 516 // IVDTubeHightInfo
#define PROP_CODERSLTTXINDIDLYMODE 517 // CodeRsltTxIndiDlyMode
#define PROP_DECOSTAY 518 // DecoStay
#define PROP_PACKETOUTPUTDATACTR 519 // PacketOutputDataCtr
#define PROP_ADDWATERMARK 520 // AddWatermark
#define PROP_INSTANDINDICATE 521 // InstandIndicate
#define PROP_QRTERMINATORPOS 522 // QRTerminatorPos
#define PROP_CODEERROUTPUT2D 523 // CodeErrOutput2D
#define PROP_CODEERRLEVEL2D 524 // CodeErrLevel2D
#define PROP_CODEVERSION2D 525 // CodeVersion2D
#define PROP_CONTMODELUMLEVEL 526 // ContModeLumLevel
#define PROP_CONTMODENOACTIONDURA 527 // ContModeNoActionDura
#define PROP_TIMETOREBOOT 528 // TimeToReboot
#define PROP_IMAGEFLIPMODE 529 // ImageFlipMode
#define PROP_ATTACHCODEPOSOPTION 530 // AttachCodePosOption
#define PROP_LASTSUCCIMAGING 531 // LastSuccImaging
#define PROP_HILUM4HDCODE 532 // HiLum4HDCode
#define PROP_SYSRECOVERINDICATION 533 // SysRecoverIndication
#define PROP_OUTPUTMULTIPLE 534 // OutputMultiple
#define PROP_CONFIGBYCODEFB 535 // ConfigByCodeFb
#define PROP_QUERYCONFIGBYCODE 536 // QueryConfigByCode
#define PROP_RESULTCHECK 537 // ResultCheck
#define PROP_RAINBOWREAD 538 // RainbowRead
#define PROP_POWERUPREPORT 539 // PowerUpReport
#define PROP_EXPREBOOTINDICATION 540 // ExpRebootIndication
#define PROP_DECODEVERTEX 541 // DecodeVertex
#define PROP_AGINGTEST 542 // AgingTest
#define PROP_PREFIXTEXT 543 // PrefixText
#define PROP_PREFIXTEXTTRANSMISSION 544 // PrefixTextTransmission
#define PROP_SUFFIXTEXT 545 // SuffixText
#define PROP_PREAMBTEXT 546 // PreambText
#define PROP_POSTAMBTEXT 547 // PostambText
#define PROP_FN1SUBSTUTEXT 548 // FN1SubstuText
#define PROP_TRUNCLEADG5 549 // TruncLeadG5
#define PROP_REPEATG5 550 // RepeatG5
#define PROP_TRUNCENDG6 551 // TruncEndG6
#define PROP_REPEATG6 552 // RepeatG6
#define PROP_CODEIDPOS 553 // CodeIDPos
#define PROP_SUFFIXTEXTENABLE 554 // SuffixTextEnable
#define PROP_CODENAMEENABLE 555 // CodeNameEnable
#define PROP_PREAMBTEXTENABLE 556 // PreambTextEnable
#define PROP_POSTAMBTEXTENABLE 557 // PostambTextEnable
#define PROP_CODEIDTEXTENABLE 558 // CodeIDTextEnable
#define PROP_CODELENENABLE 559 // CodeLenEnable
#define PROP_NONEPRINTSTRENABLE 560 // NonePrintStrEnable
#define PROP_BANSPECIALKEYS 561 // BanSpecialKeys
#define PROP_TEXTEDIT 562 // TextEdit
#define PROP_INVOICECUSTSTR1 563 // InvoiceCustStr1
#define PROP_INVOICECUSTSTR2 564 // InvoiceCustStr2
#define PROP_INVOICECUSTSTR3 565 // InvoiceCustStr3
#define PROP_INVOICECUSTSTR4 566 // InvoiceCustStr4
#define PROP_BACKUPDECOIMG 567 // BackupDecoImg
#define PROP_ONTIMETRIGGERDECODE 568 // OnTimeTriggerDecode
#define PROP_UCIPGROUPRESETPUB 569 // UCIPGroupResetPUB
#define PROP_VISIONAPP 570 // VisionApp

// Enum values for IlluminationMode
typedef enum {
    ILLUMINATIONMODE_E_ALWAYS_OFF = 0,
    ILLUMINATIONMODE_E_ALWAYS_ON = 1,
    ILLUMINATIONMODE_E_FLASH = 2,
    ILLUMINATIONMODE_E_ON_WHEN_READING = 3,
} ILLUMINATIONMODE_E;

// Enum values for IlluminationLevel
typedef enum {
    ILLUMINATIONLEVEL_E_LOW = 0,
    ILLUMINATIONLEVEL_E_MID = 1,
    ILLUMINATIONLEVEL_E_HIGH = 2,
} ILLUMINATIONLEVEL_E;

// Enum values for LightGroupSelect
typedef enum {
    LIGHTGROUPSELECT_E_GROUP1 = 0,
    LIGHTGROUPSELECT_E_GROUP2 = 1,
} LIGHTGROUPSELECT_E;

// Enum values for DataFilterMode
typedef enum {
    DATAFILTERMODE_E_Disable = 0,
    DATAFILTERMODE_E_Normal = 1,
    DATAFILTERMODE_E_Advanced = 2,
} DATAFILTERMODE_E;

// Enum values for DataFilterFunc
typedef enum {
    DATAFILTERFUNC_E_FILTER = 0,
    DATAFILTERFUNC_E_DELETE = 1,
} DATAFILTERFUNC_E;

// Enum values for CodeSymb1D
typedef enum {
    CODESYMB1D_E_AS_PARA = 0,
    CODESYMB1D_E_DISABLE_ALL = 1,
    CODESYMB1D_E_ENABLE_ALL = 2,
} CODESYMB1D_E;

// Enum values for CodeSymb2D
typedef enum {
    CODESYMB2D_E_AS_PARA = 0,
    CODESYMB2D_E_DISABLE_ALL = 1,
    CODESYMB2D_E_ENABLE_ALL = 2,
    CODESYMB2D_E_PDF417_ONLY = 3,
    CODESYMB2D_E_QRCODE_ONLY = 4,
    CODESYMB2D_E_DATAMATRIX_ONLY = 5,
    CODESYMB2D_E_MAXICODE_ONLY = 6,
    CODESYMB2D_E_AZTECCODE_ONLY = 7,
    CODESYMB2D_E_CSCODE_ONLY = 8,
} CODESYMB2D_E;

// Enum values for MultiSymbRead
typedef enum {
    MULTISYMBREAD_E_DISABLE = 0,
    MULTISYMBREAD_E_ONLY_1D = 1,
    MULTISYMBREAD_E_ONLY_2D = 2,
    MULTISYMBREAD_E_BOTH_1D_2D = 3,
} MULTISYMBREAD_E;

// Enum values for CenteringRead
typedef enum {
    CENTERINGREAD_E_NONE = 0,
    CENTERINGREAD_E_VERTICAL = 1,
    CENTERINGREAD_E_NEAR = 2,
} CENTERINGREAD_E;

// Enum values for Code39Format
typedef enum {
    CODE39FORMAT_E_STANDARD = 0,
    CODE39FORMAT_E_FULL_ASCII = 1,
} CODE39FORMAT_E;

// Enum values for Code128TruZero
typedef enum {
    CODE128TRUZERO_E_DISABLE = 0,
    CODE128TRUZERO_E_ALL = 1,
    CODE128TRUZERO_E_FIRST = 2,
} CODE128TRUZERO_E;

// Enum values for CodabarStartStopType
typedef enum {
    CODABARSTARTSTOPTYPE_E_ABCD = 0,
    CODABARSTARTSTOPTYPE_E_abcd = 1,
    CODABARSTARTSTOPTYPE_E_TNE = 2,
    CODABARSTARTSTOPTYPE_E_tne = 3,
} CODABARSTARTSTOPTYPE_E;

// Enum values for Intl25ChkDigVer
typedef enum {
    INTL25CHKDIGVER_E_DISABLE = 0,
    INTL25CHKDIGVER_E_USS = 1,
    INTL25CHKDIGVER_E_OPCC = 2,
} INTL25CHKDIGVER_E;

// Enum values for Intl25LenRestr
typedef enum {
    INTL25LENRESTR_E_ANY = 0,
    INTL25LENRESTR_E_ONLY_44 = 1,
} INTL25LENRESTR_E;

// Enum values for Intl25BankFormatType
typedef enum {
    INTL25BANKFORMATTYPE_E_NO_TAB = 0,
    INTL25BANKFORMATTYPE_E_ALGUNS = 1,
    INTL25BANKFORMATTYPE_E_TODOS = 2,
} INTL25BANKFORMATTYPE_E;

// Enum values for EAN8SuplDig
typedef enum {
    EAN8SUPLDIG_E_NONE = 0,
    EAN8SUPLDIG_E_TWO_DIGITS = 1,
    EAN8SUPLDIG_E_FIVE_DIGITS = 2,
    EAN8SUPLDIG_E_TWO_OR_FIVE = 3,
    EAN8SUPLDIG_E_ONLY_WITH_SUPPLEMENTAL = 4,
} EAN8SUPLDIG_E;

// Enum values for EAN8TrunExp
typedef enum {
    EAN8TRUNEXP_E_NONE = 0,
    EAN8TRUNEXP_E_TRUNCATE = 1,
    EAN8TRUNEXP_E_EXPAND = 2,
} EAN8TRUNEXP_E;

// Enum values for EAN13SuplDig
typedef enum {
    EAN13SUPLDIG_E_NONE = 0,
    EAN13SUPLDIG_E_TWO_DIGITS = 1,
    EAN13SUPLDIG_E_FIVE_DIGITS = 2,
    EAN13SUPLDIG_E_TWO_OR_FIVE = 3,
    EAN13SUPLDIG_E_ONLY_WITH_SUPPLEMENTAL = 4,
} EAN13SUPLDIG_E;

// Enum values for UPCASuplDig
typedef enum {
    UPCASUPLDIG_E_NONE = 0,
    UPCASUPLDIG_E_TWO_DIGITS = 1,
    UPCASUPLDIG_E_FIVE_DIGITS = 2,
    UPCASUPLDIG_E_TWO_OR_FIVE = 3,
    UPCASUPLDIG_E_ONLY_WITH_SUPPLEMENTAL = 4,
} UPCASUPLDIG_E;

// Enum values for UPCATrunExp
typedef enum {
    UPCATRUNEXP_E_NONE = 0,
    UPCATRUNEXP_E_TRUNCATE = 1,
    UPCATRUNEXP_E_EXPAND = 2,
} UPCATRUNEXP_E;

// Enum values for UPCESuplDig
typedef enum {
    UPCESUPLDIG_E_NONE = 0,
    UPCESUPLDIG_E_TWO_DIGITS = 1,
    UPCESUPLDIG_E_FIVE_DIGITS = 2,
    UPCESUPLDIG_E_TWO_OR_FIVE = 3,
    UPCESUPLDIG_E_ONLY_WITH_SUPPLEMENTAL = 4,
} UPCESUPLDIG_E;

// Enum values for UPCETrunExp
typedef enum {
    UPCETRUNEXP_E_NONE = 0,
    UPCETRUNEXP_E_TRUNCATE = 1,
    UPCETRUNEXP_E_EXPAND_EAN13 = 2,
    UPCETRUNEXP_E_EXPAND_UPCA = 3,
} UPCETRUNEXP_E;

// Enum values for Code11ChkDigVer
typedef enum {
    CODE11CHKDIGVER_E_NONE = 0,
    CODE11CHKDIGVER_E_ONE = 1,
    CODE11CHKDIGVER_E_TWO = 2,
    CODE11CHKDIGVER_E_RESERVED = 3,
} CODE11CHKDIGVER_E;

// Enum values for GS1EXPAConversion
typedef enum {
    GS1EXPACONVERSION_E_NONE = 0,
    GS1EXPACONVERSION_E_UCC_EAN128 = 1,
} GS1EXPACONVERSION_E;

// Enum values for GS1DBConversion
typedef enum {
    GS1DBCONVERSION_E_NONE = 0,
    GS1DBCONVERSION_E_UCC_EAN128 = 1,
    GS1DBCONVERSION_E_UPC_EAN13 = 2,
} GS1DBCONVERSION_E;

// Enum values for GS1LIMIConversion
typedef enum {
    GS1LIMICONVERSION_E_NONE = 0,
    GS1LIMICONVERSION_E_UCC_EAN128 = 1,
    GS1LIMICONVERSION_E_UPC_EAN13 = 2,
} GS1LIMICONVERSION_E;

// Enum values for MSIPLChkDigVer
typedef enum {
    MSIPLCHKDIGVER_E_DISABLE = 0,
    MSIPLCHKDIGVER_E_ONE_MOD10 = 1,
    MSIPLCHKDIGVER_E_TWO_MOD10 = 2,
    MSIPLCHKDIGVER_E_MOD10_MOD11 = 3,
} MSIPLCHKDIGVER_E;

// Enum values for UCCEAN128TruZero
typedef enum {
    UCCEAN128TRUZERO_E_DISABLE = 0,
    UCCEAN128TRUZERO_E_ALL = 1,
    UCCEAN128TRUZERO_E_FIRST = 2,
} UCCEAN128TRUZERO_E;

// Enum values for UPCE1SuplDig
typedef enum {
    UPCE1SUPLDIG_E_NONE = 0,
    UPCE1SUPLDIG_E_TWO = 1,
    UPCE1SUPLDIG_E_FIVE = 2,
    UPCE1SUPLDIG_E_TWO_FIVE = 3,
    UPCE1SUPLDIG_E_ONLY = 4,
} UPCE1SUPLDIG_E;

// Enum values for UPCE1TrunExp
typedef enum {
    UPCE1TRUNEXP_E_NONE = 0,
    UPCE1TRUNEXP_E_TRUNCATE = 1,
    UPCE1TRUNEXP_E_EXPAND_EAN13 = 2,
    UPCE1TRUNEXP_E_EXPAND_UPCA = 3,
} UPCE1TRUNEXP_E;

// Enum values for QRCodeWebCodeForbid
typedef enum {
    QRCODEWEBCODEFORBID_E_ALLOW = 0,
    QRCODEWEBCODEFORBID_E_FORBID = 1,
    QRCODEWEBCODEFORBID_E_DOUYIN_ONLY = 2,
} QRCODEWEBCODEFORBID_E;

// Enum values for MICPDF417Conversion
typedef enum {
    MICPDF417CONVERSION_E_OFF = 0,
    MICPDF417CONVERSION_E_UCCEAN128 = 1,
    MICPDF417CONVERSION_E_UPCA_EAN13 = 2,
} MICPDF417CONVERSION_E;

// Enum values for PDF417Conversion
typedef enum {
    PDF417CONVERSION_E_NONE = 0,
    PDF417CONVERSION_E_UCC_EAN128 = 1,
    PDF417CONVERSION_E_UPC_EAN13 = 2,
} PDF417CONVERSION_E;

// Enum values for CHNFNCFrtCharConv
typedef enum {
    CHNFNCFRTCHARCONV_E_DISABLE = 0,
    CHNFNCFRTCHARCONV_E_ENABLE = 1,
    CHNFNCFRTCHARCONV_E_FIVE_TO_A = 2,
    CHNFNCFRTCHARCONV_E_SIX_TO_B = 3,
    CHNFNCFRTCHARCONV_E_SEVEN_TO_C = 4,
    CHNFNCFRTCHARCONV_E_EIGHT_TO_D = 5,
    CHNFNCFRTCHARCONV_E_NINE_TO_E = 6,
} CHNFNCFRTCHARCONV_E;

// Enum values for CHNFNCFrtCharSpec
typedef enum {
    CHNFNCFRTCHARSPEC_E_DISABLE = 0,
    CHNFNCFRTCHARSPEC_E_ZERO = 1,
    CHNFNCFRTCHARSPEC_E_FIVE_OR_A = 2,
    CHNFNCFRTCHARSPEC_E_SIX_OR_B = 3,
    CHNFNCFRTCHARSPEC_E_SEVEN_OR_C = 4,
    CHNFNCFRTCHARSPEC_E_EIGHT_OR_D = 5,
    CHNFNCFRTCHARSPEC_E_NINE_OR_E = 6,
    CHNFNCFRTCHARSPEC_E_ONE = 7,
    CHNFNCFRTCHARSPEC_E_TWO = 8,
    CHNFNCFRTCHARSPEC_E_THREE = 9,
    CHNFNCFRTCHARSPEC_E_FOUR = 10,
} CHNFNCFRTCHARSPEC_E;

// Enum values for NetLed
typedef enum {
    NETLED_E_LINK = 1,
    NETLED_E_TX = 2,
    NETLED_E_RX = 4,
} NETLED_E;

// Enum values for DataBatch
typedef enum {
    DATABATCH_E_NONE = 0,
    DATABATCH_E_OUT_RANGE = 1,
    DATABATCH_E_STANDARD = 2,
    DATABATCH_E_STANDARD_MANUAL = 3,
} DATABATCH_E;

// Enum values for ScanMode
typedef enum {
    SCANMODE_E_GoodReadOff = 0,
    SCANMODE_E_Mome = 1,
    SCANMODE_E_Alter = 2,
    SCANMODE_E_Cont = 3,
    SCANMODE_E_GoodReadOn = 4,
    SCANMODE_E_SciTrigger = 5,
    SCANMODE_E_AutoDete = 6,
    SCANMODE_E_AutoDeteIMG = 7,
    SCANMODE_E_AutoDeteIR_GoodReadOn = 8,
    SCANMODE_E_AutoDeteIR_GoodReadOff = 9,
    SCANMODE_E_AutoDeteIR_Mome = 10,
    SCANMODE_E_Desktop = 11,
    SCANMODE_E_ButtonCont = 12,
    SCANMODE_E_MultiSymbolsCont = 13,
} SCANMODE_E;

// Enum values for MovementScanMode
typedef enum {
    MOVEMENTSCANMODE_E_Standard = 0,
    MOVEMENTSCANMODE_E_Enhance = 1,
} MOVEMENTSCANMODE_E;

// Enum values for BeepVolume
typedef enum {
    BEEPVOLUME_E_LOW = 0,
    BEEPVOLUME_E_MID = 1,
    BEEPVOLUME_E_HIGH = 2,
    BEEPVOLUME_E_LOW2 = 3,
    BEEPVOLUME_E_LOW3 = 4,
    BEEPVOLUME_E_LOW4 = 5,
    BEEPVOLUME_E_LOW5 = 6,
    BEEPVOLUME_E_LOW6 = 7,
    BEEPVOLUME_E_LOW7 = 8,
    BEEPVOLUME_E_LOW8 = 9,
} BEEPVOLUME_E;

// Enum values for BeepTone
typedef enum {
    BEEPTONE_E_LEVEL0 = 0,
    BEEPTONE_E_LEVEL1 = 1,
    BEEPTONE_E_LEVEL2 = 2,
    BEEPTONE_E_LEVEL3 = 3,
} BEEPTONE_E;

// Enum values for CustomIOModeMain
typedef enum {
    CUSTOMIOMODEMAIN_E_Close = 0,
    CUSTOMIOMODEMAIN_E_InputHigh = 1,
    CUSTOMIOMODEMAIN_E_InputLow = 2,
    CUSTOMIOMODEMAIN_E_OutputHigh = 3,
    CUSTOMIOMODEMAIN_E_OutputLow = 4,
} CUSTOMIOMODEMAIN_E;

// Enum values for CustomIORisingAction
typedef enum {
    CUSTOMIORISINGACTION_E_None = 0,
    CUSTOMIORISINGACTION_E_StartDecode = 1,
    CUSTOMIORISINGACTION_E_EndDecode = 2,
} CUSTOMIORISINGACTION_E;

// Enum values for CustomIOFallingAction
typedef enum {
    CUSTOMIOFALLINGACTION_E_None = 0,
    CUSTOMIOFALLINGACTION_E_StartDecode = 1,
    CUSTOMIOFALLINGACTION_E_EndDecode = 2,
} CUSTOMIOFALLINGACTION_E;

// Enum values for CustomIOActiveCondition
typedef enum {
    CUSTOMIOACTIVECONDITION_E_None = 0,
    CUSTOMIOACTIVECONDITION_E_DecodeSuccess = 1,
    CUSTOMIOACTIVECONDITION_E_NoRead = 2,
    CUSTOMIOACTIVECONDITION_E_DecodeStarted = 3,
    CUSTOMIOACTIVECONDITION_E_DecodeStopped = 4,
    CUSTOMIOACTIVECONDITION_E_Command = 5,
    CUSTOMIOACTIVECONDITION_E_Timer = 6,
    CUSTOMIOACTIVECONDITION_E_Input1High = 7,
    CUSTOMIOACTIVECONDITION_E_Input2High = 8,
    CUSTOMIOACTIVECONDITION_E_Input3High = 9,
    CUSTOMIOACTIVECONDITION_E_Input4High = 10,
    CUSTOMIOACTIVECONDITION_E_Input1Low = 11,
    CUSTOMIOACTIVECONDITION_E_Input2Low = 12,
    CUSTOMIOACTIVECONDITION_E_Input3Low = 13,
    CUSTOMIOACTIVECONDITION_E_Input4Low = 14,
} CUSTOMIOACTIVECONDITION_E;

// Enum values for CustomIOInactiveCondition
typedef enum {
    CUSTOMIOINACTIVECONDITION_E_None = 0,
    CUSTOMIOINACTIVECONDITION_E_DecodeSuccess = 1,
    CUSTOMIOINACTIVECONDITION_E_NoRead = 2,
    CUSTOMIOINACTIVECONDITION_E_DecodeStarted = 3,
    CUSTOMIOINACTIVECONDITION_E_DecodeStopped = 4,
    CUSTOMIOINACTIVECONDITION_E_Command = 5,
    CUSTOMIOINACTIVECONDITION_E_Timer = 6,
    CUSTOMIOINACTIVECONDITION_E_Input1High = 7,
    CUSTOMIOINACTIVECONDITION_E_Input2High = 8,
    CUSTOMIOINACTIVECONDITION_E_Input3High = 9,
    CUSTOMIOINACTIVECONDITION_E_Input4High = 10,
    CUSTOMIOINACTIVECONDITION_E_Input1Low = 11,
    CUSTOMIOINACTIVECONDITION_E_Input2Low = 12,
    CUSTOMIOINACTIVECONDITION_E_Input3Low = 13,
    CUSTOMIOINACTIVECONDITION_E_Input4Low = 14,
} CUSTOMIOINACTIVECONDITION_E;

// Enum values for CharEncode
typedef enum {
    CHARENCODE_E_ASCII = 0,
    CHARENCODE_E_UTF8 = 1,
    CHARENCODE_E_Windows1251 = 2,
    CHARENCODE_E_SimpChinese = 3,
    CHARENCODE_E_TraChinese = 4,
    CHARENCODE_E_Windows1250 = 5,
    CHARENCODE_E_KOI8R = 6,
    CHARENCODE_E_Japanese = 7,
} CHARENCODE_E;

// Enum values for CharPrintCtr
typedef enum {
    CHARPRINTCTR_E_Disable = 0,
    CHARPRINTCTR_E_Printable = 1,
    CHARPRINTCTR_E_Alphanumeric = 2,
} CHARPRINTCTR_E;

// Enum values for CaseConv
typedef enum {
    CASECONV_E_Disable = 0,
    CASECONV_E_UpperDatOnly = 1,
    CASECONV_E_LowerDatOnly = 2,
    CASECONV_E_UpperWholeStr = 3,
    CASECONV_E_LowWholeStr = 4,
} CASECONV_E;

// Enum values for DecoRsltCheck
typedef enum {
    DECORSLTCHECK_E_Disable = 0,
    DECORSLTCHECK_E_EnableAll = 1,
    DECORSLTCHECK_E_HeadCheckOnly = 2,
    DECORSLTCHECK_E_TailCheckOnly = 3,
} DECORSLTCHECK_E;

// Enum values for USBEthernetCompositeType
typedef enum {
    USBETHERNETCOMPOSITETYPE_E_Keyboard = 0,
    USBETHERNETCOMPOSITETYPE_E_VCOM = 1,
} USBETHERNETCOMPOSITETYPE_E;

// Enum values for RS232FlowControl
typedef enum {
    RS232FLOWCONTROL_E_None = 0,
    RS232FLOWCONTROL_E_RtsCtsLow = 1,
    RS232FLOWCONTROL_E_RtsCtsHigh = 2,
    RS232FLOWCONTROL_E_XonXoff = 3,
    RS232FLOWCONTROL_E_AckNak = 4,
    RS232FLOWCONTROL_E_RtsLow = 5,
    RS232FLOWCONTROL_E_AckNakRtsCtsHigh = 6,
    RS232FLOWCONTROL_E_CtsScan = 7,
} RS232FLOWCONTROL_E;

// Enum values for RS232CharDelay
typedef enum {
    RS232CHARDELAY_E_Forbid = 0,
    RS232CHARDELAY_E_Delay5ms = 1,
    RS232CHARDELAY_E_Delay10ms = 2,
    RS232CHARDELAY_E_Delay20ms = 3,
    RS232CHARDELAY_E_Delay40ms = 4,
    RS232CHARDELAY_E_Delay80ms = 5,
} RS232CHARDELAY_E;

// Enum values for RS232BaudRate
typedef enum {
    RS232BAUDRATE_E_BR9600 = 0,
    RS232BAUDRATE_E_BR19200 = 1,
    RS232BAUDRATE_E_BR38400 = 2,
    RS232BAUDRATE_E_BR57600 = 3,
    RS232BAUDRATE_E_BR115200 = 4,
} RS232BAUDRATE_E;

// Enum values for RS232Parity
typedef enum {
    RS232PARITY_E_None = 0,
    RS232PARITY_E_Odd = 1,
    RS232PARITY_E_Even = 2,
} RS232PARITY_E;

// Enum values for RS232DataBits
typedef enum {
    RS232DATABITS_E_Bits8 = 0,
    RS232DATABITS_E_Bits7 = 1,
} RS232DATABITS_E;

// Enum values for RS232StopBits
typedef enum {
    RS232STOPBITS_E_Bits1 = 0,
    RS232STOPBITS_E_Bits2 = 1,
} RS232STOPBITS_E;

// Enum values for RS232HostType
typedef enum {
    RS232HOSTTYPE_E_Standard = 0,
    RS232HOSTTYPE_E_OPOS = 1,
    RS232HOSTTYPE_E_MDAux = 2,
} RS232HOSTTYPE_E;

// Enum values for RS232DecoDataFormat
typedef enum {
    RS232DECODATAFORMAT_E_Raw = 0,
    RS232DECODATAFORMAT_E_Packed = 1,
} RS232DECODATAFORMAT_E;

// Enum values for USBType
typedef enum {
    USBTYPE_E_PC = 0,
    USBTYPE_E_MAC = 1,
    USBTYPE_E_VISUAL_COM = 2,
    USBTYPE_E_SIMPLE_COM = 3,
    USBTYPE_E_OPOS = 4,
    USBTYPE_E_CUST_VISUAL_COM = 5,
    USBTYPE_E_IBM_HAND_HELD = 6,
    USBTYPE_E_HID_MSD = 7,
    USBTYPE_E_CUSTOM_BULK = 8,
    USBTYPE_E_CUSTOM_HID_KEYBOARD_HS_BULK = 9,
    USBTYPE_E_PC_LEGACY_KEYBOARD = 10,
    USBTYPE_E_VCOM_OPOS = 11,
    USBTYPE_E_CUSTOM_HID_INT_HS_BULK = 12,
    USBTYPE_E_CUSTOM_HID_KBD_HID_CUST = 13,
    USBTYPE_E_CUSTOM_HID_KBD_VISUAL_COM = 14,
    USBTYPE_E_NET = 15,
    USBTYPE_E_CUSTOM_HID_KBD_NET = 16,
    USBTYPE_E_CUSTOM_VISUAL_COM_NET = 17,
    USBTYPE_E_CUSTOM = 18,
} USBTYPE_E;

// Enum values for USBKeyboardLayout
typedef enum {
    USBKEYBOARDLAYOUT_E_CHN_USA = 0,
    USBKEYBOARDLAYOUT_E_TKYF = 1,
    USBKEYBOARDLAYOUT_E_TKYQ = 2,
    USBKEYBOARDLAYOUT_E_FRN = 3,
    USBKEYBOARDLAYOUT_E_ITA = 4,
    USBKEYBOARDLAYOUT_E_SPA = 5,
    USBKEYBOARDLAYOUT_E_SLK = 6,
    USBKEYBOARDLAYOUT_E_DMK = 7,
    USBKEYBOARDLAYOUT_E_JAP = 8,
    USBKEYBOARDLAYOUT_E_GER = 9,
    USBKEYBOARDLAYOUT_E_BEL = 10,
    USBKEYBOARDLAYOUT_E_RUS = 11,
    USBKEYBOARDLAYOUT_E_CZE = 12,
    USBKEYBOARDLAYOUT_E_THAI = 13,
    USBKEYBOARDLAYOUT_E_HUNGARY = 14,
    USBKEYBOARDLAYOUT_E_SWISS_GERMAN = 15,
    USBKEYBOARDLAYOUT_E_PORTUGUESE = 16,
    USBKEYBOARDLAYOUT_E_UKRAINIAN = 17,
    USBKEYBOARDLAYOUT_E_POLISH214 = 18,
    USBKEYBOARDLAYOUT_E_VIETNAM = 19,
    USBKEYBOARDLAYOUT_E_RUSSIAN_ANDROID = 20,
    USBKEYBOARDLAYOUT_E_VIETNAM_TELEX = 21,
} USBKEYBOARDLAYOUT_E;

// Enum values for USBKeyDly
typedef enum {
    USBKEYDLY_E_DELAY_0MS = 0,
    USBKEYDLY_E_DELAY_5MS = 1,
    USBKEYDLY_E_DELAY_10MS = 2,
    USBKEYDLY_E_DELAY_20MS = 3,
    USBKEYDLY_E_DELAY_40MS = 4,
    USBKEYDLY_E_DELAY_60MS = 5,
} USBKEYDLY_E;

// Enum values for USBNumbKey
typedef enum {
    USBNUMBKEY_E_DISABLE = 0,
    USBNUMBKEY_E_NUM = 1,
    USBNUMBKEY_E_ALT_KEYPAD = 2,
    USBNUMBKEY_E_ALT_KEYPAD_GBK = 3,
    USBNUMBKEY_E_RAW_HEX_IN_STRINGS = 4,
    USBNUMBKEY_E_UOS_UNICODE_IN_DEC = 5,
} USBNUMBKEY_E;

// Enum values for USBFuncKey
typedef enum {
    USBFUNCKEY_E_NON_PRINTABLE = 0,
    USBFUNCKEY_E_MINDEO = 1,
    USBFUNCKEY_E_CUSTOMIZE_PPN = 2,
    USBFUNCKEY_E_FORBID = 3,
    USBFUNCKEY_E_DATALOGIC_CTRL_CHAR_00 = 4,
} USBFUNCKEY_E;

// Enum values for USBHostPollingInter
typedef enum {
    USBHOSTPOLLINGINTER_E_INTERVAL_1MS = 0,
    USBHOSTPOLLINGINTER_E_INTERVAL_2MS = 1,
    USBHOSTPOLLINGINTER_E_INTERVAL_5MS = 2,
    USBHOSTPOLLINGINTER_E_INTERVAL_8MS = 3,
} USBHOSTPOLLINGINTER_E;

// Enum values for USBKeySendMode
typedef enum {
    USBKEYSENDMODE_E_DISCONTINUOUS = 0,
    USBKEYSENDMODE_E_CONTINUOUS = 1,
} USBKEYSENDMODE_E;

// Enum values for USBEnterMode
typedef enum {
    USBENTERMODE_E_ACCORDING_FUNC_KEY = 0,
    USBENTERMODE_E_FORCE_ALPHABET_ENTER = 1,
    USBENTERMODE_E_FORCE_NUMERIC_ENTER = 2,
    USBENTERMODE_E_FORCE_ALT_013_ENTER = 3,
} USBENTERMODE_E;

// Enum values for USBModKeysReleaseMode
typedef enum {
    USBMODKEYSRELEASEMODE_E_SAME_TIME = 0,
    USBMODKEYSRELEASEMODE_E_AFTER_NORMAL_KEYS = 1,
} USBMODKEYSRELEASEMODE_E;

// Enum values for InterfaceTypeMain
typedef enum {
    INTERFACETYPEMAIN_E_Auto = 0,
    INTERFACETYPEMAIN_E_RS232 = 1,
    INTERFACETYPEMAIN_E_USB = 2,
    INTERFACETYPEMAIN_E_RS232_ETH = 3,
    INTERFACETYPEMAIN_E_RS232_USB = 4,
    INTERFACETYPEMAIN_E_SOFTWARE = 5,
} INTERFACETYPEMAIN_E;

// Enum values for SweepingScanEnhance
typedef enum {
    SWEEPINGSCANENHANCE_E_LowSpeed = 0,
    SWEEPINGSCANENHANCE_E_MediumSpeed = 1,
    SWEEPINGSCANENHANCE_E_HighSpeed = 2,
} SWEEPINGSCANENHANCE_E;

// Enum values for BeepSoundMode
typedef enum {
    BEEPSOUNDMODE_E_MODE_0 = 0,
    BEEPSOUNDMODE_E_MODE_1 = 1,
    BEEPSOUNDMODE_E_MODE_2 = 2,
    BEEPSOUNDMODE_E_MODE_3 = 3,
    BEEPSOUNDMODE_E_MODE_4 = 4,
} BEEPSOUNDMODE_E;

// Enum values for InfraredMode
typedef enum {
    INFRAREDMODE_E_IN_STAND = 0,
    INFRAREDMODE_E_CONTINUE = 1,
} INFRAREDMODE_E;

// Enum values for AutoDeteLum
typedef enum {
    AUTODETELUM_E_ALWAYS_OFF = 0,
    AUTODETELUM_E_ON_IN_DARKNESS = 1,
    AUTODETELUM_E_ALWAYS_ON = 2,
} AUTODETELUM_E;

// Enum values for AutoDeteTxtureInter
typedef enum {
    AUTODETETXTUREINTER_E_INTERVAL_0S = 0,
    AUTODETETXTUREINTER_E_INTERVAL_5S = 1,
    AUTODETETXTUREINTER_E_INTERVAL_10S = 2,
    AUTODETETXTUREINTER_E_INTERVAL_30S = 3,
    AUTODETETXTUREINTER_E_INTERVAL_60S = 4,
    AUTODETETXTUREINTER_E_INTERVAL_INFINITY = 5,
} AUTODETETXTUREINTER_E;

// Enum values for AutoDeteSleepFrmInter
typedef enum {
    AUTODETESLEEPFRMINTER_E_INTERVAL_250MS = 0,
    AUTODETESLEEPFRMINTER_E_INTERVAL_500MS = 1,
    AUTODETESLEEPFRMINTER_E_INTERVAL_1000MS = 2,
} AUTODETESLEEPFRMINTER_E;

// Enum values for CompositeRead
typedef enum {
    COMPOSITEREAD_E_DISABLE = 0,
    COMPOSITEREAD_E_DATABAR_GS1_128 = 1,
    COMPOSITEREAD_E_ALL = 2,
} COMPOSITEREAD_E;

// Enum values for OCRRecognizeMain
typedef enum {
    OCRRECOGNIZEMAIN_E_AS_PARA = 0,
    OCRRECOGNIZEMAIN_E_DISABLE_ALL = 1,
    OCRRECOGNIZEMAIN_E_ENABLE_ALL = 2,
} OCRRECOGNIZEMAIN_E;

// Enum values for OCRTurkishIDOutputFormat
typedef enum {
    OCRTURKISHIDOUTPUTFORMAT_E_ORIGINAL = 0,
    OCRTURKISHIDOUTPUTFORMAT_E_CUSTOMIZED = 1,
} OCRTURKISHIDOUTPUTFORMAT_E;

// Enum values for CodeRsltTxIndiDlyMode
typedef enum {
    CODERSLTTXINDIDLYMODE_E_NONE = 0,
    CODERSLTTXINDIDLYMODE_E_ONLY_TX = 1,
    CODERSLTTXINDIDLYMODE_E_TX_AND_INDI = 2,
} CODERSLTTXINDIDLYMODE_E;

// Enum values for AddWatermark
typedef enum {
    ADDWATERMARK_E_DISABLE = 0,
    ADDWATERMARK_E_WITHOUT_BACKGROUND = 1,
    ADDWATERMARK_E_WITH_BACKGROUND = 2,
} ADDWATERMARK_E;

// Enum values for QRTerminatorPos
typedef enum {
    QRTERMINATORPOS_E_FIRST = 0,
    QRTERMINATORPOS_E_SECOND = 1,
} QRTERMINATORPOS_E;

// Enum values for ContModeLumLevel
typedef enum {
    CONTMODELUMLEVEL_E_LOW = 0,
    CONTMODELUMLEVEL_E_MID = 1,
    CONTMODELUMLEVEL_E_HIGH = 2,
} CONTMODELUMLEVEL_E;

// Enum values for TimeToReboot
typedef enum {
    TIMETOREBOOT_E_FORBID = 0,
    TIMETOREBOOT_E_SECS_30 = 1,
    TIMETOREBOOT_E_MINS_5 = 2,
    TIMETOREBOOT_E_MINS_15 = 3,
    TIMETOREBOOT_E_MINS_30 = 4,
    TIMETOREBOOT_E_HOUR_1 = 5,
    TIMETOREBOOT_E_HOURS_2 = 6,
    TIMETOREBOOT_E_HOURS_3 = 7,
    TIMETOREBOOT_E_HOURS_4 = 8,
    TIMETOREBOOT_E_HOURS_8 = 9,
    TIMETOREBOOT_E_HOURS_12 = 10,
    TIMETOREBOOT_E_HOURS_24 = 11,
    TIMETOREBOOT_E_HOURS_48 = 12,
} TIMETOREBOOT_E;

// Enum values for ImageFlipMode
typedef enum {
    IMAGEFLIPMODE_E_NONE = 0,
    IMAGEFLIPMODE_E_HORIZONTAL = 1,
    IMAGEFLIPMODE_E_VERTICAL = 2,
    IMAGEFLIPMODE_E_DIAGONAL = 3,
} IMAGEFLIPMODE_E;

// Enum values for AgingTest
typedef enum {
    AGINGTEST_E_OFF = 0,
    AGINGTEST_E_MODE_1 = 1,
    AGINGTEST_E_MODE_2 = 2,
    AGINGTEST_E_MODE_3 = 3,
    AGINGTEST_E_MODE_4 = 4,
} AGINGTEST_E;

// Enum values for CodeIDPos
typedef enum {
    CODEIDPOS_E_Before = 0,
    CODEIDPOS_E_After = 1,
} CODEIDPOS_E;

// Enum values for CodeIDTextEnable
typedef enum {
    CODEIDTEXTENABLE_E_Disable = 0,
    CODEIDTEXTENABLE_E_PropID = 1,
    CODEIDTEXTENABLE_E_AIMID = 2,
    CODEIDTEXTENABLE_E_Both = 3,
} CODEIDTEXTENABLE_E;

// Enum values for TextEdit
typedef enum {
    TEXTEDIT_E_Disable = 0,
    TEXTEDIT_E_Invoice = 1,
    TEXTEDIT_E_Evotrue = 2,
    TEXTEDIT_E_Prowill = 3,
    TEXTEDIT_E_Inspur = 4,
    TEXTEDIT_E_Elgin = 5,
    TEXTEDIT_E_Pulsa = 6,
    TEXTEDIT_E_GeLunBu = 7,
    TEXTEDIT_E_ShengXiaoBang = 8,
    TEXTEDIT_E_XinGuoDu_GSGL = 9,
    TEXTEDIT_E_InvoiceCust = 10,
} TEXTEDIT_E;

// Enum values for BackupDecoImg
typedef enum {
    BACKUPDECOIMG_E_OFF = 0,
    BACKUPDECOIMG_E_Both = 1,
    BACKUPDECOIMG_E_Succ = 2,
} BACKUPDECOIMG_E;

// Enum values for VisionApp
typedef enum {
    VISIONAPP_E_OFF = 0,
    VISIONAPP_E_OCR = 1,
    VISIONAPP_E_Class = 2,
    VISIONAPP_E_DeteFeature = 3,
} VISIONAPP_E;

#endif // __CPROPR_CFG_H__
