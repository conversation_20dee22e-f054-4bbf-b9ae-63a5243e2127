//=============================================================================
// Copyright (C), 2004-2025, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: mdcmd_proxy.h
// Author		: DongDL
// Created On	: 2025/07/04
// Description	: mdcmd_proxy.h
//
// History
// 1. V1.0, Created by DongDL. 2025/07/04
//=============================================================================

#ifndef _MDCMD_PROXY_H_
#define _MDCMD_PROXY_H_

typedef struct
{
	const void *ptr;
	unsigned int len;
} mdcommand_block_t;

typedef enum
{
	FLAG_PROCESS,
	FLAG_ACK,
	FLAG_NAK,
} mdcommand_flag_t;

int mdcommand_go(mdcommand_block_t *blocks, unsigned int count, unsigned int tmp, void *ctx,
	int (*cbk)(void *ctx, void *data_ptr, unsigned int data_size, mdcommand_flag_t flag));
int mdcommand_abt(int id);

#endif /* _MDCMD_PROXY_H_ */
