#ifndef CPROPR_MACRO_H
#define CPROPR_MACRO_H

/**
 * @brief Macro to check if an enum property value equals any of the specified values
 * @param NAME The property name (without PROP_ prefix)
 * @param ... Variable number of enum values to check (without NAME_E_ prefix)
 * 
 * Example usage:
 * @code
 * CPROPR_ENUM_EQ(COLOR, Blue, Green);
 * // If color is Blue or Green, value will be set to -1
 * @endcode
 */
#define CPROPR_ENUM_EQ(NAME, ...) \
	switch (cpropr_get_enum(PROP_##NAME)) \
	{ \
		CPROPR_EXPAND_CASES(NAME, __VA_ARGS__) \
		break; \
		default: *((int *)(value)) = -1; break; \
	}

/**
 * @brief Macro to check if an enum property value does NOT equal any of the specified values
 * @param NAME The property name (without PROP_ prefix)
 * @param ... Variable number of enum values to check (without NAME_E_ prefix)
 * 
 * Example usage:
 * @code
 * CPROPR_ENUM_NE(COLOR, Blue, Green);
 * // If color is NOT Blue or Green, value will be set to -1
 * @endcode
 */
#define CPROPR_ENUM_NE(NAME, ...) \
	switch (cpropr_get_enum(PROP_##NAME)) \
	{ \
		CPROPR_EXPAND_CASES(NAME, __VA_ARGS__) \
		*((int *)(value)) = -1; \
		break; \
		default: break; \
	}

/**
 * @brief Macro to check if a boolean property is TRUE
 * @param NAME The property name (without PROP_ prefix)
 * 
 * Example usage:
 * @code
 * CPROPR_BOOL_TR(AIM);
 * // If AIM is FALSE, value will be set to -1
 * @endcode
 */
#define CPROPR_BOOL_TR(NAME) \
	if (!cpropr_get_bool(PROP_##NAME)) *((int *)(value)) = -1;

/**
 * @brief Macro to check if a boolean property is FALSE
 * @param NAME The property name (without PROP_ prefix)
 * 
 * Example usage:
 * @code
 * CPROPR_BOOL_FA(AIM);
 * // If AIM is TRUE, value will be set to -1
 * @endcode
 */
#define CPROPR_BOOL_FA(NAME) \
	if (cpropr_get_bool(PROP_##NAME)) *((int *)(value)) = -1;

/**
 * @brief Complete hook check wrapper for ENUM_EQ
 * @param NAME The property name (without PROP_ prefix)
 * @param ... Variable number of enum values to check (without NAME_E_ prefix)
 * 
 * Example usage:
 * @code
 * CPROPR_CHECK_ENUM_EQ(COLOR, Blue, Green);
 * // Replaces entire hook check block for enum equality check
 * @endcode
 */
#define CPROPR_CHECK_ENUM_EQ(NAME, ...) \
	if (CPROPR_HOOK_CHECK == type) \
	{ \
		CPROPR_ENUM_EQ(NAME, __VA_ARGS__); \
		return; \
	}

/**
 * @brief Complete hook check wrapper for ENUM_NE
 * @param NAME The property name (without PROP_ prefix)
 * @param ... Variable number of enum values to check (without NAME_E_ prefix)
 * 
 * Example usage:
 * @code
 * CPROPR_CHECK_ENUM_NE(COLOR, Blue, Green);
 * // Replaces entire hook check block for enum inequality check
 * @endcode
 */
#define CPROPR_CHECK_ENUM_NE(NAME, ...) \
	if (CPROPR_HOOK_CHECK == type) \
	{ \
		CPROPR_ENUM_NE(NAME, __VA_ARGS__); \
		return; \
	}

/**
 * @brief Complete hook check wrapper for BOOL_TR
 * @param NAME The property name (without PROP_ prefix)
 * 
 * Example usage:
 * @code
 * CPROPR_CHECK_BOOL_TR(AIM);
 * // Replaces entire hook check block for boolean TRUE check
 * @endcode
 */
#define CPROPR_CHECK_BOOL_TR(NAME) \
	if (CPROPR_HOOK_CHECK == type) \
	{ \
		CPROPR_BOOL_TR(NAME); \
		return; \
	}

/**
 * @brief Complete hook check wrapper for BOOL_FA
 * @param NAME The property name (without PROP_ prefix)
 * 
 * Example usage:
 * @code
 * CPROPR_CHECK_BOOL_FA(AIM);
 * // Replaces entire hook check block for boolean FALSE check
 * @endcode
 */
#define CPROPR_CHECK_BOOL_FA(NAME) \
	if (CPROPR_HOOK_CHECK == type) \
	{ \
		CPROPR_BOOL_FA(NAME); \
		return; \
	}

/* Helper macros for case expansion */
#define CPROPR_EXPAND_CASES(name, ...) CPROPR_FOR_EACH(CPROPR_MAKE_CASE, name, __VA_ARGS__)

#define CPROPR_MAKE_CASE(name, VALUE)  case name##_E_##VALUE:

/* General FOR_EACH macro implementation */
#define CPROPR_FOR_EACH(macro, name, ...) \
	CPROPR_GET_FOR_EACH_HELPER(__VA_ARGS__, CPROPR_FOR_EACH_9, CPROPR_FOR_EACH_8, CPROPR_FOR_EACH_7, \
		CPROPR_FOR_EACH_6, CPROPR_FOR_EACH_5, CPROPR_FOR_EACH_4, CPROPR_FOR_EACH_3, CPROPR_FOR_EACH_2, \
		CPROPR_FOR_EACH_1)(macro, name, __VA_ARGS__)

#define CPROPR_GET_FOR_EACH_HELPER(a1, a2, a3, a4, a5, a6, a7, a8, a9, NAME, ...) NAME

// clang-format off
#define CPROPR_FOR_EACH_1(m, n, a1) m(n, a1)
#define CPROPR_FOR_EACH_2(m, n, a1, a2) m(n, a1) m(n, a2)
#define CPROPR_FOR_EACH_3(m, n, a1, a2, a3) m(n, a1) m(n, a2) m(n, a3)
#define CPROPR_FOR_EACH_4(m, n, a1, a2, a3, a4) m(n, a1) m(n, a2) m(n, a3) m(n, a4)
#define CPROPR_FOR_EACH_5(m, n, a1, a2, a3, a4, a5) m(n, a1) m(n, a2) m(n, a3) m(n, a4) m(n, a5)
#define CPROPR_FOR_EACH_6(m, n, a1, a2, a3, a4, a5, a6) m(n, a1) m(n, a2) m(n, a3) m(n, a4) m(n, a5) m(n, a6)
#define CPROPR_FOR_EACH_7(m, n, a1, a2, a3, a4, a5, a6, a7) m(n, a1) m(n, a2) m(n, a3) m(n, a4) m(n, a5) m(n, a6) m(n, a7)
#define CPROPR_FOR_EACH_8(m, n, a1, a2, a3, a4, a5, a6, a7, a8) m(n, a1) m(n, a2) m(n, a3) m(n, a4) m(n, a5) m(n, a6) m(n, a7) m(n, a8)
#define CPROPR_FOR_EACH_9(m, n, a1, a2, a3, a4, a5, a6, a7, a8, a9) m(n, a1) m(n, a2) m(n, a3) m(n, a4) m(n, a5) m(n, a6) m(n, a7) m(n, a8) m(n, a9)
// clang-format on

#endif /* CPROPR_MACRO_H */