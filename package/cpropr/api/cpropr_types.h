#ifndef __CPROPR_TYPES_H__
#define __CPROPR_TYPES_H__

// Property types
typedef enum
{
	CPROPR_TYPE_INT = 1,	// Integer type
	CPROPR_TYPE_FLOAT = 2,	// Float type
	CPROPR_TYPE_ENUM = 3,	// Enum type
	CPROPR_TYPE_STRING = 4, // String type (uses cpropr_bytes_t)
	CPROPR_TYPE_BOOL = 5,	// Boolean type
	CPROPR_TYPE_COMB = 6	// Combinable options type
} cpropr_type_t;

// Bytes type with flexible array
typedef struct
{
	unsigned char length;	 // Length of actual data
	char data[]; // Flexible array for data content
} cpropr_bytes_t;

// Value range union
typedef union
{
	struct
	{
		int min, max;
	} i; // Integer range
	struct
	{
		float min, max;
	} f; // Float range
	struct
	{						// Enum/Comb options
		const char **names; // Option names array
	} e;
	int length; // Maximum space for string/bytes
} cpropr_range_t;

// Value union
typedef union
{
	int i;			   // Integer value (includes int, bool, enum, comb)
	float f;		   // Float value
	cpropr_bytes_t *p; // bytes address
} cpropr_value_t;

// Property state
typedef enum
{
	CPROPR_STATE_INV = -1, // Invalid
	CPROPR_STATE_OKAY = 0, // Okay
	CPROPR_STATE_LOCK = 1, // Locked
} cpropr_state_t;

// Property flags
typedef enum
{
	CPROPR_FLAG_NONE = 0,		 // No special flags
	CPROPR_FLAG_RDONLY = 1 << 0, // Read only
	CPROPR_FLAG_WRONLY = 1 << 1, // Write only
	CPROPR_FLAG_HIDDEN = 1 << 2, // Hidden (not exported)
	CPROPR_FLAG_NOSAVE = 1 << 3, // Not saved
	CPROPR_FLAG_REBOOT = 1 << 4, // Takes effect after reboot
	CPROPR_FLAG_EXPERT = 1 << 5, // Expert mode
	CPROPR_FLAG_AUTORB = 1 << 6, // Auto reboot after change
} cpropr_flag_t;

// Hook types
typedef enum
{
	CPROPR_HOOK_READ,  // Read (before)
	CPROPR_HOOK_LOAD,  // Load from saved file (after)
	CPROPR_HOOK_WRITE, // Write (after)
	CPROPR_HOOK_FIXUP, // Fixup at runtime
	CPROPR_HOOK_CHECK, // Check state
} cpropr_hook_type_t;

// Description structure (documentation-related fields)
typedef struct
{
	const char *name;	// Property name
	const char *label;	// Display name
	const char *path;	// Property path
	const char *help;	// Help text
	const char **descs; // Option descriptions (for ENUM/COMB)
} cpropr_desc_t;

// Property item structure
typedef struct
{
	int id;														// Property ID
	const char *name;											// Property name
	cpropr_type_t type;											// Property type
	cpropr_flag_t flags;										// Property flags
	cpropr_range_t range;										// Value range
	cpropr_value_t defval;										// Default value
	const short *children;										// Children
	void (*hook)(int id, cpropr_hook_type_t type, void *value); // Hook function
} cpropr_item_t;

// Property node structure
typedef struct
{
	int id;
	cpropr_type_t type;
	cpropr_state_t state;
	union
	{
		int i;
		float f;
		cpropr_bytes_t b;
	} value;
} cpropr_node_t;

#endif // __CPROPR_TYPES_H__