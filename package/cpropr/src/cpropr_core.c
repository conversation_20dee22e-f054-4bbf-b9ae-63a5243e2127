#include "libcustom.h"
#include "cpropr_core.h"
#include "cpropr.ctx.h"
#include "cpropr.h"

// Global context (non-static)
cpropr_ctx_t g_ctx = {0};

int cpropr_init(const char *file)
{
	LOGD("Initializing property system...");

	// Get configuration items from generated header file
	g_ctx.items = _cpropr_items_;
	g_ctx.count = _cpropr_count_;

	// Rebuild each configuration item
	for (int i = 0; i < g_ctx.count; i++)
	{
		const cpropr_item_t *item = g_ctx.items[i];
		if (!item) continue;
		if (item->hook) item->hook(item->id, CPROPR_HOOK_FIXUP, 0);
	}

	// Calculate total memory size needed
	size_t total_size = 0;
	// Allocate pointer array, temporarily used as offsets
	g_ctx.values = clike_malloc(sizeof(void *) * g_ctx.count);
	clike_assert(!g_ctx.values, goto label_error);

	for (int i = 0; i < g_ctx.count; i++)
	{
		const cpropr_item_t *item = g_ctx.items[i];
		if (!item) continue;

		// Align to 4-byte boundary
		total_size = (total_size + 3) & ~3;
		g_ctx.values[i] = (void *)total_size; // Temporarily store offset

		switch (item->type)
		{
			case CPROPR_TYPE_INT:
			case CPROPR_TYPE_ENUM:
			case CPROPR_TYPE_COMB:
			case CPROPR_TYPE_BOOL: total_size += sizeof(int); break;
			case CPROPR_TYPE_FLOAT: total_size += sizeof(float); break;
			case CPROPR_TYPE_STRING: total_size += sizeof(cpropr_bytes_t) + item->range.length; break;
		}
	}

	// Allocate a continuous memory block
	void *values_block = clike_malloc(total_size);
	clike_assert(!values_block, goto label_free_values);

	// Initialize value for each configuration item
	for (int i = 0; i < g_ctx.count; i++)
	{
		const cpropr_item_t *item = g_ctx.items[i];
		if (!item) continue;

		void *value = (char *)values_block + (size_t)g_ctx.values[i];
		g_ctx.values[i] = value;

		switch (item->type)
		{
			case CPROPR_TYPE_INT:
			case CPROPR_TYPE_ENUM:
			case CPROPR_TYPE_COMB:
			case CPROPR_TYPE_BOOL: *(int *)value = item->defval.i; break;
			case CPROPR_TYPE_FLOAT: *(float *)value = item->defval.f; break;
			case CPROPR_TYPE_STRING:
			{
				cpropr_bytes_t *default_bytes = (cpropr_bytes_t *)item->defval.p;
				clike_copy(value, default_bytes, sizeof(cpropr_bytes_t) + default_bytes->length);
				break;
			}
		}
	}

	// Load from configuration file if provided
	if (file)
	{
		size_t len = strlen(file);
		if (len > 5)
		{
			// Must have at least x.json or x.bin length
			if (strcmp(file + len - 5, ".json") == 0) cpropr_import_json(file);
			else if (strcmp(file + len - 4, ".bin") == 0)
				cpropr_load_binary(file);
		}
	}

	LOGI("Property system initialized with %d items", g_ctx.count);
	return 0;

label_free_values:
	clike_free(g_ctx.values);
label_error:
	return -1;
}

int cpropr_get_int(int id)
{
	if ((unsigned)id >= g_ctx.count) return 0;
	const cpropr_item_t *item = g_ctx.items[id];
	if (!item) return 0;
	if (item->type != CPROPR_TYPE_INT) return 0;

	// Check write-only flag
	if (item->flags & CPROPR_FLAG_WRONLY) return 0;

	int value = *(int *)g_ctx.values[id];
	if (item->hook) { item->hook(id, CPROPR_HOOK_READ, &value); }
	return value;
}

int cpropr_set_int(int id, int value)
{
	if ((unsigned)id >= g_ctx.count) return -1;
	const cpropr_item_t *item = g_ctx.items[id];
	if (!item) return -1;
	if (item->type != CPROPR_TYPE_INT) return -1;

	// Check read-only flag
	if (item->flags & CPROPR_FLAG_RDONLY) return -1;

	*(int *)g_ctx.values[id] = value;

	if (item->hook) { item->hook(id, CPROPR_HOOK_WRITE, &value); }
	return 0;
}

int cpropr_get_enum(int id)
{
	if ((unsigned)id >= g_ctx.count) return 0;
	const cpropr_item_t *item = g_ctx.items[id];
	if (!item) return 0;
	if (item->type != CPROPR_TYPE_ENUM) return 0;

	// Check write-only flag
	if (item->flags & CPROPR_FLAG_WRONLY) return 0;

	int value = *(int *)g_ctx.values[id];
	if (item->hook) { item->hook(id, CPROPR_HOOK_READ, &value); }
	return value;
}

int cpropr_set_enum(int id, int value)
{
	if ((unsigned)id >= g_ctx.count) return -1;
	const cpropr_item_t *item = g_ctx.items[id];
	if (!item) return -1;
	if (item->type != CPROPR_TYPE_ENUM) return -1;

	// Check read-only flag
	if (item->flags & CPROPR_FLAG_RDONLY) return -1;

	*(int *)g_ctx.values[id] = value;

	if (item->hook) { item->hook(id, CPROPR_HOOK_WRITE, &value); }
	return 0;
}

int cpropr_get_comb(int id)
{
	if ((unsigned)id >= g_ctx.count) return 0;
	const cpropr_item_t *item = g_ctx.items[id];
	if (!item) return 0;
	if (item->type != CPROPR_TYPE_COMB) return 0;

	int value = *(int *)g_ctx.values[id];
	if (item->hook) { item->hook(id, CPROPR_HOOK_READ, &value); }
	return value;
}

int cpropr_set_comb(int id, int value)
{
	if ((unsigned)id >= g_ctx.count) return -1;
	const cpropr_item_t *item = g_ctx.items[id];
	if (!item) return -1;
	if (item->type != CPROPR_TYPE_COMB) return -1;

	// Check read-only flag
	if (item->flags & CPROPR_FLAG_RDONLY) return -1;

	*(int *)g_ctx.values[id] = value;

	if (item->hook) { item->hook(id, CPROPR_HOOK_WRITE, &value); }
	return 0;
}

void cpropr_free(void)
{
	if (g_ctx.values)
	{
		// Only need to free the memory of the first configuration item's value, as it's continuously allocated
		if (g_ctx.count > 0) clike_free(g_ctx.values[0]);
		clike_free(g_ctx.values);
	}
	g_ctx.values = NULL;
	g_ctx.items = NULL;
	g_ctx.count = 0;
}

float cpropr_get_float(int id)
{
	if ((unsigned)id >= g_ctx.count) return 0.0f;
	const cpropr_item_t *item = g_ctx.items[id];
	if (!item) return 0.0f;
	if (item->type != CPROPR_TYPE_FLOAT) return 0.0f;

	// Check write-only and hidden flag
	if (item->flags & (CPROPR_FLAG_WRONLY | CPROPR_FLAG_HIDDEN)) return 0.0f;

	float value = *(float *)g_ctx.values[id];
	if (item->hook) { item->hook(id, CPROPR_HOOK_READ, &value); }
	return value;
}

int cpropr_set_float(int id, float value)
{
	if ((unsigned)id >= g_ctx.count) return -1;
	const cpropr_item_t *item = g_ctx.items[id];
	if (!item) return -1;
	if (item->type != CPROPR_TYPE_FLOAT) return -1;

	// Check read-only flag
	if (item->flags & CPROPR_FLAG_RDONLY) return -1;

	*(float *)g_ctx.values[id] = value;
	if (item->hook) { item->hook(id, CPROPR_HOOK_WRITE, &value); }
	return 0;
}

int cpropr_get_bool(int id)
{
	if ((unsigned)id >= g_ctx.count) return 0;
	const cpropr_item_t *item = g_ctx.items[id];
	if (!item) return 0;
	if (item->type != CPROPR_TYPE_BOOL) return 0;

	// Check write-only and hidden flag
	if (item->flags & (CPROPR_FLAG_WRONLY | CPROPR_FLAG_HIDDEN)) return 0;

	int value = *(int *)g_ctx.values[id];
	if (item->hook) { item->hook(id, CPROPR_HOOK_READ, &value); }
	return value;
}

int cpropr_set_bool(int id, int value)
{
	if ((unsigned)id >= g_ctx.count) return -1;
	const cpropr_item_t *item = g_ctx.items[id];
	if (!item) return -1;
	if (item->type != CPROPR_TYPE_BOOL) return -1;

	// Check read-only flag
	if (item->flags & CPROPR_FLAG_RDONLY) return -1;

	*(int *)g_ctx.values[id] = value ? 1 : 0;
	if (item->hook) { item->hook(id, CPROPR_HOOK_WRITE, &value); }
	return 0;
}

cpropr_bytes_t *cpropr_get_string(int id)
{
	if ((unsigned)id >= g_ctx.count) return NULL;
	const cpropr_item_t *item = g_ctx.items[id];
	if (!item) return NULL;
	if (item->type != CPROPR_TYPE_STRING) return NULL;

	// Check flags
	if (item->flags & (CPROPR_FLAG_WRONLY | CPROPR_FLAG_HIDDEN)) return NULL;

	// Get bytes object from storage
	cpropr_bytes_t *bytes = g_ctx.values[id];

	// Call hook
	if (item->hook) item->hook(id, CPROPR_HOOK_READ, bytes);

	return bytes;
}

// Set string value using raw data and size
int cpropr_set_string(int id, const char *data, int size)
{
	if (!data) return -1;
	if ((unsigned)id >= g_ctx.count) return -1;
	const cpropr_item_t *item = g_ctx.items[id];
	if (!item) return -1;
	if (item->type != CPROPR_TYPE_STRING) return -1;

	// Check read-only flag
	if (item->flags & CPROPR_FLAG_RDONLY) return -1;

	// Copy data to bytes object
	cpropr_bytes_t *bytes = g_ctx.values[id];
	if (size == 0) size = strlen(data);						  // Use strlen if size is 0
	if (size > item->range.length) size = item->range.length; // Ensure we don't exceed allocated space
	bytes->length = size;
	memcpy(bytes->data, data, size);

	// Call hook if defined
	if (item->hook) item->hook(id, CPROPR_HOOK_WRITE, bytes);

	return 0;
}

cpropr_state_t cpropr_get_state(int id)
{
	if ((unsigned)id >= g_ctx.count) return CPROPR_STATE_INV;
	const cpropr_item_t *item = g_ctx.items[id];
	if (!item) return CPROPR_STATE_INV;
	cpropr_state_t state = CPROPR_STATE_OKAY;
	if (item->hook) item->hook(id, CPROPR_HOOK_CHECK, &state);
	return state;
}

int cpropr_get(int id, cpropr_type_t *typ, void *value, int limit)
{
	// Validate parameters
	if (!typ || !value || limit <= 0) return -1;
	if ((unsigned)id >= g_ctx.count) return -1;

	const cpropr_item_t *item = g_ctx.items[id];
	if (!item) return -1;

	// Check write-only flag
	if (item->flags & CPROPR_FLAG_WRONLY) return -1;

	*typ = item->type;

	switch (item->type)
	{
		case CPROPR_TYPE_INT:
		case CPROPR_TYPE_ENUM:
		case CPROPR_TYPE_COMB:
		case CPROPR_TYPE_BOOL:
		{
			if (limit < sizeof(int)) return -1;
			int int_value = *(int *)g_ctx.values[id];
			if (item->hook) item->hook(id, CPROPR_HOOK_READ, &int_value);
			*(int *)value = int_value;
			return sizeof(int);
		}
		case CPROPR_TYPE_FLOAT:
		{
			if (limit < sizeof(float)) return -1;
			float float_value = *(float *)g_ctx.values[id];
			if (item->hook) item->hook(id, CPROPR_HOOK_READ, &float_value);
			*(float *)value = float_value;
			return sizeof(float);
		}
		case CPROPR_TYPE_STRING:
		{
			if (limit < sizeof(cpropr_bytes_t)) return -1;
			cpropr_bytes_t *bytes = g_ctx.values[id];
			if (item->hook) item->hook(id, CPROPR_HOOK_READ, bytes);
			size_t total = sizeof(cpropr_bytes_t) + bytes->length;
			if (limit < total) return -1;
			clike_copy(value, bytes, total);
			return total;
		}
		default: return -1;
	}
}
