//=============================================================================
// Copyright (C), 2004-2025, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: cpropr_tree.c
// Author		: XuCF
// Created On	: 2025/05/19
// Description	: cpropr_tree.c
//
// History
// 1. V1.0, Created by XuCF. 2025/05/19
//=============================================================================

#include "libcustom.h"
#include "cpropr.h"
#include "cpropr_core.h"

#define _min_(a, b) ((a) < (b) ? (a) : (b))

int cpropr_read_tree(int id, cpropr_node_t *buffer, int limit)
{
	if (!buffer || limit <= 0) return -1;
	if ((unsigned)id >= g_ctx.count) return -1;
	cpropr_node_t *root = buffer, *roof = (cpropr_node_t *)((char *)buffer + limit);

	cpropr_item_t *item;
	cpropr_value_t *value;
	cpropr_node_t *node;
	const short *children;

	int top = 1 /* stack top */, curr /* current property id */, len /* temp copy length */;
	short stack[512];
	stack[0] = id;

	while (top > 0)
	{
		// pop current node
		curr = stack[--top];

		// find item and value
		item = g_ctx.items[curr], value = g_ctx.values[curr];
		if (!item || !value) continue;

		// check space
		node = buffer++; // move to next node
		if (buffer > roof) goto label_exit;

		// fill node
		node->id = curr;
		node->type = item->type;
		node->state = cpropr_get_state(curr);
		switch (item->type)
		{
			case CPROPR_TYPE_INT:
			case CPROPR_TYPE_BOOL:
			case CPROPR_TYPE_ENUM:
			case CPROPR_TYPE_COMB: node->value.i = value->i; break;
			case CPROPR_TYPE_FLOAT: node->value.f = value->f; break;
			case CPROPR_TYPE_STRING:
				len = _min_(value->p->length, (char *)roof - (char *)buffer);
				if (len == 0) continue;								 // no content, skip
				node->value.b.length = len;							 // copy length
				clike_copy(node->value.b.data, value->p->data, len); // copy content
				len = (len + 3) & ~3;								 // 4-byte alignment
				buffer = (cpropr_node_t *)((char *)buffer + len);	 // space for bytes content
				break;
		}

		// push children
		children = item->children;
		if (!children) continue; // no children
		while (*children != -1)
		{
			stack[top++] = *children;
			children++;
		}
	}
label_exit:
	return (int)((char *)buffer - (char *)root);
}
