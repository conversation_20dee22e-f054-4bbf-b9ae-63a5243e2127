#include "libcustom.h"
#include "cpropr_core.h"
#include "cpropr.h"

#define CPROPR_MAGIC 0x50524F43 // "CPRO" magic number

typedef struct
{
	int magic;
	int version;
	int count;
} cpropr_bin_header_t;

typedef struct
{
	int id;
	int size;
} cpropr_bin_item_t;

// Check if item needs to be saved
static int _check_need_save_(const cpropr_item_t *item, void *value)
{
	// Skip items with NOSAVE or WRONLY flags
	if ((CPROPR_FLAG_NOSAVE | CPROPR_FLAG_WRONLY | CPROPR_FLAG_RDONLY) & item->flags) return 0;
	// Read callback before check
	if (item->hook) item->hook(item->id, CPROPR_HOOK_READ, value);
	// Compare current value with default value
	switch (item->type)
	{
		case CPROPR_TYPE_INT:
		case CPROPR_TYPE_ENUM:
		case CPROPR_TYPE_COMB:
		case CPROPR_TYPE_BOOL: return *(int *)value != item->defval.i;
		case CPROPR_TYPE_FLOAT: return *(float *)value != item->defval.f;
		case CPROPR_TYPE_STRING:
		{
			// Get bytes object
			cpropr_bytes_t *bytes = (cpropr_bytes_t *)value;
			cpropr_bytes_t *def_bytes = (cpropr_bytes_t *)item->defval.p;
			// Compare length
			if (bytes->length != def_bytes->length) return 1;
			// Compare content
			if (bytes->length > 0) return memcmp(bytes->data, def_bytes->data, bytes->length) != 0;
			return 0;
		}
		default: return 0;
	}
}

// Write item header and value to file
static int _write_item_to_file(FILE *fp, const cpropr_item_t *item, void *value)
{
	// Calculate value size
	int size = 0;
	switch (item->type)
	{
		case CPROPR_TYPE_INT:
		case CPROPR_TYPE_ENUM:
		case CPROPR_TYPE_COMB:
		case CPROPR_TYPE_BOOL: size = sizeof(int); break;
		case CPROPR_TYPE_FLOAT: size = sizeof(float); break;
		case CPROPR_TYPE_STRING:
			size = ((cpropr_bytes_t *)value)->length;
			value = ((cpropr_bytes_t *)value)->data;
			break;
		default: break;
	}

	// Write item header
	cpropr_bin_item_t item_header = {.id = item->id, .size = size};
	if (fwrite(&item_header, sizeof(item_header), 1, fp) != 1) return -1;

	// Write value
	return fwrite(value, size, 1, fp) == 1 ? 0 : -1;
}

int cpropr_save_binary(const char *filename)
{
	FILE *fp = fopen(filename, "wb");
	clike_assert(!fp, goto label_error);

	// Reserve space for the file header
	fseek(fp, sizeof(cpropr_bin_header_t), SEEK_SET);

	// Write items and count valid entries
	int item_count = 0;
	for (int i = 0; i < g_ctx.count; i++)
	{
		const cpropr_item_t *item = g_ctx.items[i];
		if (!item) continue;
		void *value = g_ctx.values[i];
		if (!_check_need_save_(item, value)) continue;
		clike_assert(_write_item_to_file(fp, item, value) != 0, goto label_close);
		item_count++;
	}

	// Update header with actual count
	cpropr_bin_header_t header = {.magic = CPROPR_MAGIC, .version = 1, .count = item_count};
	fseek(fp, 0, SEEK_SET);
	clike_assert(fwrite(&header, sizeof(header), 1, fp) != 1, goto label_close);

	fclose(fp);
	return 0;

label_close:
	fclose(fp);
label_error:
	return -1;
}

// Read item from file and update value
static int _read_item_from_file(FILE *fp, cpropr_bin_item_t *item_header)
{
	int idx = item_header->id;
	if (idx < 0 || idx >= g_ctx.count)
	{
		fseek(fp, item_header->size, SEEK_CUR);
		return 0; // Return success to continue processing other items
	}

	const cpropr_item_t *item = g_ctx.items[idx];
	if (!item)
	{
		fseek(fp, item_header->size, SEEK_CUR);
		return 0; // Return success to continue processing other items
	}

	LOGI("Read item %s", item->name);
	void *value = g_ctx.values[idx];

	// Handle string type specially
	if (item->type == CPROPR_TYPE_STRING)
	{
		size_t read_size = (item->range.length < item_header->size) ? item->range.length : item_header->size;
		((cpropr_bytes_t *)value)->length = read_size;
		clike_assert(fread(((cpropr_bytes_t *)value)->data, read_size, 1, fp) != 1, return -1);
		if (read_size < item_header->size) fseek(fp, item_header->size - read_size, SEEK_CUR);
	}
	else { clike_assert(fread(value, item_header->size, 1, fp) != 1, return -1); }

	if (item->hook) item->hook(item->id, CPROPR_HOOK_LOAD, value);
	return 0;
}

int cpropr_load_binary(const char *filename)
{
	FILE *fp = fopen(filename, "rb");
	clike_assert(!fp, goto label_error);

	// Read header from file
	cpropr_bin_header_t header;
	clike_assert(fread(&header, sizeof(header), 1, fp) != 1 || header.magic != CPROPR_MAGIC, goto label_close);

	// Read items
	for (int i = 0; i < header.count; i++)
	{
		cpropr_bin_item_t item_header;
		clike_assert(fread(&item_header, sizeof(item_header), 1, fp) != 1, goto label_close);
		clike_assert(_read_item_from_file(fp, &item_header) != 0, goto label_close);
	}

	fclose(fp);
	return 0;

label_close:
	fclose(fp);
label_error:
	return -1;
}
