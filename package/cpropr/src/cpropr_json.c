#include "libcustom.h"
#include "cpropr_core.h"
#include "cpropr.h"

// Convert flags to string
static void _flags_to_string_(char *buf, size_t size, cpropr_flag_t flags)
{
	buf[0] = '\0';
	if (flags & CPROPR_FLAG_RDONLY) strncat(buf, "RO|", size - strlen(buf) - 1);
	if (flags & CPROPR_FLAG_WRONLY) strncat(buf, "WO|", size - strlen(buf) - 1);
	if (flags & CPROPR_FLAG_HIDDEN) strncat(buf, "HD|", size - strlen(buf) - 1);
	if (flags & CPROPR_FLAG_NOSAVE) strncat(buf, "NS|", size - strlen(buf) - 1);
	if (flags & CPROPR_FLAG_REBOOT) strncat(buf, "RB|", size - strlen(buf) - 1);
	if (flags & CPROPR_FLAG_EXPERT) strncat(buf, "EX|", size - strlen(buf) - 1);
	if (flags & CPROPR_FLAG_AUTORB) strncat(buf, "AB|", size - strlen(buf) - 1);
	if (!buf[0]) strncpy(buf, "NO", size - 1);
	else
		buf[strlen(buf) - 1] = '\0'; // Remove the last '|'
}

// Convert type to string
const char *_type_to_string_(cpropr_type_t type)
{
	switch (type)
	{
		case CPROPR_TYPE_INT: return "INT";
		case CPROPR_TYPE_FLOAT: return "FLOAT";
		case CPROPR_TYPE_BOOL: return "BOOL";
		case CPROPR_TYPE_STRING: return "STRING";
		case CPROPR_TYPE_ENUM: return "ENUM";
		case CPROPR_TYPE_COMB: return "COMB";
		default: return "UNKNOWN";
	}
}

// Internal function implementation
int cpropr_export_fp(FILE *fp)
{
	clike_assert(!fp, return -1);
	fprintf(fp, "{\n  \"properties\": [\n");

	int visible_count = 0;
	for (int i = 0; i < g_ctx.count; i++)
	{
		const cpropr_item_t *item = g_ctx.items[i];
		if (!item) continue;
		if (!(item->flags & CPROPR_FLAG_HIDDEN)) { visible_count++; }
	}

	int current = 0;
	for (int i = 0; i < g_ctx.count; i++)
	{
		const cpropr_item_t *item = g_ctx.items[i];
		if (!item) continue;
		void *value = g_ctx.values[i];

		// skip hidden properties
		if (item->flags & CPROPR_FLAG_HIDDEN) continue;
		current++;

		// fixed fields
		fprintf(fp, "    {\n");
		fprintf(fp, "      \"id\": %d,\n", item->id);
		fprintf(fp, "      \"name\": \"%s\",\n", item->name);
		fprintf(fp, "      \"type\": \"%s\",\n", _type_to_string_(item->type));
		char flags_str[64];
		_flags_to_string_(flags_str, sizeof(flags_str), item->flags);
		fprintf(fp, "      \"flags\": \"%s\",\n", flags_str);

		// range(or options) and default value
		switch (item->type)
		{
			case CPROPR_TYPE_INT:
				fprintf(fp, "      \"range\": {\"min\": %d, \"max\": %d},\n", item->range.i.min, item->range.i.max);
				fprintf(fp, "      \"default\": %d,\n", item->defval.i);
				break;
			case CPROPR_TYPE_FLOAT:
				fprintf(fp, "      \"range\": {\"min\": %.6f, \"max\": %.6f},\n", item->range.f.min, item->range.f.max);
				fprintf(fp, "      \"default\": %.6f,\n", item->defval.f);
				break;
			case CPROPR_TYPE_STRING:
			{
				// Get the default value from bytes_t structure
				cpropr_bytes_t *default_bytes = (cpropr_bytes_t *)item->defval.p;
				char buffer[128] = {0};
				clike_pbytes_enc(buffer, 128, default_bytes->data, default_bytes->length);
				fprintf(fp, "      \"length\": %d,\n", item->range.length);
				fprintf(fp, "      \"default\": \"%s\",\n", buffer);
				break;
			}
			case CPROPR_TYPE_BOOL:
				fprintf(fp, "      \"default\": %s,\n", item->defval.i ? "true" : "false");
				break;
			case CPROPR_TYPE_ENUM:
			case CPROPR_TYPE_COMB:
			{
				fprintf(fp, "      \"options\": [\n");
				int first = 1;
				for (int i = 0; item->range.e.names[i] != NULL; i++)
				{
					if (item->range.e.names[i] == (const char *)-1) continue;
					if (!first) fprintf(fp, ",\n");
					fprintf(fp, "        {\"name\": \"%s\", \"real\": %d}", item->range.e.names[i],
						item->type == CPROPR_TYPE_COMB ? (1 << i) : i);
					first = 0;
				}
				fprintf(fp, "\n      ],\n");
				fprintf(fp, "      \"default\": %d,\n", item->defval.i);
				break;
			}
		}

		// current value
		if (item->hook) item->hook(item->id, CPROPR_HOOK_READ, value);
		fprintf(fp, "      \"value\": ");
		switch (item->type)
		{
			case CPROPR_TYPE_INT: fprintf(fp, "%d", *(int *)value); break;
			case CPROPR_TYPE_FLOAT: fprintf(fp, "%.6f", *(float *)value); break;
			case CPROPR_TYPE_STRING:
			{
				cpropr_bytes_t *bytes = (cpropr_bytes_t *)value;
				char buffer[128] = {0};
				clike_pbytes_enc(buffer, 128, bytes->data, bytes->length);
				fprintf(fp, "\"%s\"", buffer);
				break;
			}
			case CPROPR_TYPE_BOOL: fprintf(fp, "%s", *(int *)value ? "true" : "false"); break;
			case CPROPR_TYPE_ENUM:
			case CPROPR_TYPE_COMB: fprintf(fp, "%d", *(int *)value); break;
		}
		fprintf(fp, ",\n");

		// state
		cpropr_state_t state = CPROPR_STATE_OKAY;
		if (item->hook) item->hook(item->id, CPROPR_HOOK_CHECK, &state);
		fprintf(fp, "      \"state\": %d\n", state);

		// end of the item
		if (current < visible_count) // not the last one
			fprintf(fp, "    },\n");
		else // the last one
			fprintf(fp, "    }\n");
	}

	fprintf(fp, "  ]\n}\n");
	return 0;
}

// Internal function implementation
int cpropr_import_fp(FILE *fp)
{
	clike_assert(!fp, return -1);
	// Read the entire file content
	fseek(fp, 0, SEEK_END);
	long size = ftell(fp);
	fseek(fp, 0, SEEK_SET);

	char *content = clike_malloc(size + 1);
	clike_assert(!content, return -1);

	size_t read_size = fread(content, 1, size, fp);
	content[read_size] = '\0';

	char *p = content;

	while (p)
	{
		// Find the id field
		p = strstr(p, "\"id\":");
		if (!p) break;

		p += 5;				   // Skip "id":
		while (*p == ' ') p++; // Skip spaces

		int id;
		if (sscanf(p, "%d", &id) != 1) continue;
		if ((unsigned)id >= g_ctx.count) continue;

		const cpropr_item_t *item = g_ctx.items[id];
		if (!item) continue;

		// Find the corresponding value field
		p = strstr(p, "\"value\":");
		if (!p) break;

		p += 8;				   // Skip "value":
		while (*p == ' ') p++; // Skip spaces

		// Parse the value based on type
		switch (item->type)
		{
			case CPROPR_TYPE_INT:
			{
				int value;
				if (sscanf(p, "%d", &value) == 1)
				{
					*(int *)g_ctx.values[id] = value;
					if (item->hook) { item->hook(id, CPROPR_HOOK_WRITE, &value); }
				}
				break;
			}
			case CPROPR_TYPE_BOOL:
			{
				int value = strncmp(p, "true", 4) == 0 ? 1 : 0;
				*(int *)g_ctx.values[id] = value;
				if (item->hook) { item->hook(id, CPROPR_HOOK_WRITE, &value); }
				break;
			}
			case CPROPR_TYPE_ENUM:
			case CPROPR_TYPE_COMB:
			{
				int value;
				if (sscanf(p, "%d", &value) == 1)
				{
					*(int *)g_ctx.values[id] = value;
					if (item->hook) { item->hook(id, CPROPR_HOOK_WRITE, &value); }
				}
				else if (*p == '{')
				{
					// If it's an object format, find the value field
					p = strstr(p, "\"value\":");
					if (p)
					{
						p += 8;				   // Skip "value":
						while (*p == ' ') p++; // Skip spaces
						if (sscanf(p, "%d", &value) == 1)
						{
							*(int *)g_ctx.values[id] = value;
							if (item->hook) { item->hook(id, CPROPR_HOOK_WRITE, &value); }
						}
					}
				}
				break;
			}
			case CPROPR_TYPE_FLOAT:
			{
				float value;
				if (sscanf(p, "%f", &value) == 1)
				{
					*(float *)g_ctx.values[id] = value;
					if (item->hook) { item->hook(id, CPROPR_HOOK_WRITE, &value); }
				}
				break;
			}
			case CPROPR_TYPE_STRING:
			{
				if (*p == '"')
				{
					p++; // Skip the starting quote
					char *end_quote = p;
					// Find the ending quote
					while ((*end_quote) && (*(end_quote + 1)) && (*end_quote - '"'))
						end_quote += (*end_quote == '\\') + 1;
					if (*end_quote == '"')
					{
						// Use the already allocated buffer
						cpropr_bytes_t *temp = (cpropr_bytes_t *)g_ctx.values[id];
						temp->length = clike_pbytes_dec(temp->data, item->range.length, p, end_quote - p);
						if (item->hook) { item->hook(id, CPROPR_HOOK_WRITE, temp); }
					}
				}
				break;
			}
		}
	}

	clike_free(content);
	return 0;
}

// Import/Export to file
int cpropr_export_json(const char *filename)
{
	FILE *fp = fopen(filename, "w");
	clike_assert(!fp, return -1);

	int ret = cpropr_export_fp(fp);
	fclose(fp);
	return ret;
}

int cpropr_import_json(const char *filename)
{
	FILE *fp = fopen(filename, "r");
	clike_assert(!fp, return -1);

	int ret = cpropr_import_fp(fp);
	fclose(fp);
	return ret;
}

// Import/Export to string
int cpropr_export_str(char *buffer, int size)
{
	int written = -1;
	clike_assert(!buffer || size <= 0, return -1);

	FILE *fp = tmpfile();
	clike_assert(!fp, return -1);

	int ret = cpropr_export_fp(fp);
	clike_assert(ret != 0, goto label_exit);

	rewind(fp);
	written = fread(buffer, 1, size - 1, fp);
	buffer[written] = '\0';

label_exit:
	fclose(fp);
	return written; // Return the exported length
}

int cpropr_import_str(const char *json)
{
	clike_assert(!json, return -1);

	FILE *fp = tmpfile();
	clike_assert(!fp, return -1);

	fputs(json, fp);
	rewind(fp);

	int ret = cpropr_import_fp(fp);
	fclose(fp);
	return ret;
}
