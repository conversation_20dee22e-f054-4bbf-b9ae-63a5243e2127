#include "libcustom.h"
#include "cpropr.h"
#include "cpropr_core.h"

// Convert combination flags to string representation
const char *_find_comb_name_(const cpropr_item_t *item, int value)
{
	static char flags_str[256]; // Buffer for combined flag values
	flags_str[0] = '\0';
	char *p = flags_str;
	int first = 1;

	// Check each bit
	const char **names = item->range.e.names;
	for (int bit = 0; names[bit] != NULL; bit++)
	{
		if (names[bit] == (const char *)-1) continue;
		if (value & (1 << bit))
		{
			if (!first)
			{
				strncat(p, "|", sizeof(flags_str) - strlen(p) - 1);
				p += strlen(p);
			}
			strncat(p, names[bit], sizeof(flags_str) - strlen(p) - 1);
			p += strlen(p);
			first = 0;
		}
	}
	
	// Return "UNKNOWN" if no flags set
	if (flags_str[0] == '\0') return "UNKNOWN";
	return flags_str;
}

// Get enum name by value
const char *_find_enum_name_(const cpropr_item_t *item, int value)
{
	const char **names = item->range.e.names;
	int count = 0;
	
	// Count valid enum entries
	while (names[count] != NULL) count++;

	if (value >= 0 && value < count) {
		if (names[value] == (const char *)-1) return "UNKNOWN";
		return names[value];
	}
	
	return "UNKNOWN";
}

// Legacy function for compatibility
const char *_find_enum_or_comb_name_(const cpropr_item_t *item, int value)
{
	if (item->type == CPROPR_TYPE_COMB)
		return _find_comb_name_(item, value);
	else // ENUM type
		return _find_enum_name_(item, value);
}

int cpropr_dump(void *writer)
{
	if (writer == NULL) return -1;

	int (*outputer)(const char *, ...) = writer;

	for (int i = 0; i < g_ctx.count; i++)
	{
		const cpropr_item_t *item = g_ctx.items[i];
		if (!item) continue;

		// Build flags string
		char flags[32] = "";
		if (item->flags & CPROPR_FLAG_RDONLY) strncat(flags, "RO", sizeof(flags) - strlen(flags) - 1);
		if (item->flags & CPROPR_FLAG_WRONLY) strncat(flags, "WO", sizeof(flags) - strlen(flags) - 1);
		if (item->flags & CPROPR_FLAG_HIDDEN) strncat(flags, "HD", sizeof(flags) - strlen(flags) - 1);
		if (item->flags & CPROPR_FLAG_EXPERT) strncat(flags, "EX", sizeof(flags) - strlen(flags) - 1);
		if (!flags[0]) strncpy(flags, "NO", sizeof(flags) - 1);

		outputer("%s(%s): ", item->name, flags);

		// Print value based on type
		switch (item->type)
		{
			case CPROPR_TYPE_INT:
				outputer("%d\n", cpropr_get_int(item->id)); // output int value
				break;
			case CPROPR_TYPE_FLOAT:
				outputer("%.2f\n", cpropr_get_float(item->id)); // output float value
				break;
			case CPROPR_TYPE_STRING:
			{
				cpropr_bytes_t *bytes = cpropr_get_string(item->id);
				if (!bytes) break;
				outputer("\"%*.s\"\n", bytes->length, bytes->data); // output string value
				break;
			}
			case CPROPR_TYPE_BOOL:
				outputer("%s\n", cpropr_get_bool(item->id) ? "true" : "false"); // output bool value
				break;
			case CPROPR_TYPE_ENUM:
				outputer("%s\n", _find_enum_name_(item, cpropr_get_enum(item->id))); // output enum value
				break;
			case CPROPR_TYPE_COMB:
				outputer("%s\n", _find_comb_name_(item, cpropr_get_comb(item->id))); // output comb value
				break;
		}
	}
	return 0;
}
