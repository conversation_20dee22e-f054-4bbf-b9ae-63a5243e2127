#include "libcustom.h"
#include "cpropr_core.h"
#include "cpropr.h"

int cpropr_disable_one(int id)
{
	if ((unsigned)id >= g_ctx.count) return -1;
	g_ctx.items[id] = NULL;
	return 0;
}

int _bit_position_(int opt)
{
	// Check if opt is 0 or not a power of 2
	if (opt <= 0 || (opt & (opt - 1)) != 0) return -1;
	// Find the position of the set bit
	int position = 0;
	while ((opt & 1) == 0)
	{
		opt >>= 1;
		position++;
	}
	return position;
}

int cpropr_disable_opt(int id, int opt)
{
	if ((unsigned)id >= g_ctx.count) return -1;
	cpropr_item_t *item = g_ctx.items[id];
	if (!item) return -1;
	if (CPROPR_TYPE_ENUM == item->type)
	{
		// disable the option
		item->range.e.names[opt] = (const char *)-1;
	}
	else if (CPROPR_TYPE_COMB == item->type)
	{
		opt = _bit_position_(opt);
		if (opt < 0) return -1;
		item->range.e.names[opt] = (const char *)-1;
	}
	else
	{
		return -1; // type not supported
	}
	return 0;
}

int cpropr_default_opt(int id, int vdef)
{
	if ((unsigned)id >= g_ctx.count) return -1;
	cpropr_item_t *item = g_ctx.items[id];
	if (!item) return -1;
	if (CPROPR_TYPE_ENUM == item->type || CPROPR_TYPE_COMB == item->type || CPROPR_TYPE_BOOL == item->type)
	{
		item->defval.i = vdef;
		return 0;
	}
	return -1; // type not supported
}

int cpropr_rebuild_int(int id, int vmin, int vmax, int vdef)
{
	if ((unsigned)id >= g_ctx.count) return -1;
	cpropr_item_t *item = g_ctx.items[id];
	if (!item) return -1;
	if (CPROPR_TYPE_INT != item->type) return -1; // type not supported
	item->range.i.min = vmin;
	item->range.i.max = vmax;
	item->defval.i = vdef;
	return 0;
}

int cpropr_rebuild_float(int id, float vmin, float vmax, float vdef)
{
	if ((unsigned)id >= g_ctx.count) return -1;
	cpropr_item_t *item = g_ctx.items[id];
	if (!item) return -1;
	if (CPROPR_TYPE_FLOAT != item->type) return -1; // type not supported
	item->range.f.min = vmin;
	item->range.f.max = vmax;
	item->defval.f = vdef;
	return 0;
}

int cpropr_rebuild_str(int id, int length, cpropr_bytes_t *vdef)
{
	if ((unsigned)id >= g_ctx.count) return -1;
	cpropr_item_t *item = g_ctx.items[id];
	if (!item) return -1;
	if (CPROPR_TYPE_STRING != item->type) return -1; // type not supported
	item->range.length = length;
	item->defval.p = vdef;
	return 0;
}
