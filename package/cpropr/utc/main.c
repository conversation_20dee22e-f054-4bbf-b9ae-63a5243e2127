#include "cpropr.h"
#include "libcustom.h"
#include "cpropr.cfg.h"

int main()
{
	// Initialize cpropr framework
	cpropr_init("package/cpropr/utc/default.json");

	// Test 1: Basic Property Operations
	clike_print("\n=== Test 1: Basic Property Operations ===\n");

	// Test boolean property
	clike_print("Testing boolean property (Aim):\n");
	cpropr_set_bool(PROP_AIM, 1);
	clike_print("Aim status: %s\n", cpropr_get_bool(PROP_AIM) ? "ON" : "OFF");

	// Test integer property
	clike_print("\nTesting integer property (Luminance):\n");
	cpropr_set_int(PROP_LUMINANCE, 60);
	clike_print("Luminance value: %d\n", cpropr_get_int(PROP_LUMINANCE));

	// Test float property
	clike_print("\nTesting float property (Temperature):\n");
	float temp = cpropr_get_float(PROP_TEMPERATURE);
	clike_print("Temperature: %.1f\n", temp);

	// Test enum property
	clike_print("\nTesting enum property (Color):\n");
	cpropr_set_enum(PROP_COLOR, COLOR_E_Red);
	clike_print("Color value: %d\n", cpropr_get_enum(PROP_COLOR));

	// Test comb property
	clike_print("\nTesting comb property (Indication):\n");
	cpropr_set_comb(PROP_INDICATION, INDICATION_E_LED);
	clike_print("Indication value: %d\n", cpropr_get_comb(PROP_INDICATION));

	// Test string property
	clike_print("\nTesting string property (Name):\n");
	cpropr_set_string(PROP_NAME, "TestDevice", 0);
	cpropr_bytes_t *name = cpropr_get_string(PROP_NAME);
	clike_print("Device name: %*.s\n", name->length, name->data);

	// Test 2: Property Flags
	clike_print("\n=== Test 2: Property Flags ===\n");

	// Test read-only property
	clike_print("Testing read-only property (Temperature):\n");
	if (cpropr_set_float(PROP_TEMPERATURE, 30.0f) != 0) clike_print("Failed to modify read-only property (expected)\n");

	// Test write-only property
	clike_print("\nTesting write-only property (Password):\n");
	cpropr_set_string(PROP_PASSWORD, "123456", 0);
	cpropr_bytes_t *pwd = cpropr_get_string(PROP_PASSWORD);
	clike_print("Password (should be empty): %p\n", pwd);

	// Test 3: Configuration Export/Import
	clike_print("\n=== Test 3: Configuration Export/Import ===\n");

	// Export to JSON
	clike_print("Exporting configuration to JSON...\n");
	cpropr_export_json("config.json");

	// Export to binary
	clike_print("Exporting configuration to binary...\n");
	cpropr_save_binary("config.bin");

	// Modify some properties
	clike_print("\nModifying some properties...\n");
	cpropr_set_int(PROP_LUMINANCE, 30);
	cpropr_set_enum(PROP_COLOR, COLOR_E_Green);

	// Load from JSON
	clike_print("\nLoading configuration from JSON...\n");
	cpropr_import_json("config.json");
	clike_print("Configuration after JSON import:\n");
	cpropr_dump(clike_print);

	// Modify properties again
	clike_print("\nModifying properties again...\n");
	cpropr_set_int(PROP_LUMINANCE, 40);
	cpropr_set_enum(PROP_COLOR, COLOR_E_White);

	// Load from binary
	clike_print("\nLoading configuration from binary...\n");
	cpropr_load_binary("config.bin");
	clike_print("Configuration after binary import:\n");
	cpropr_dump(clike_print);

	// Test 4: Read tree
	clike_print("\n=== Test 4: Read tree ===\n");
	const int _max_size_ = 1024;
	cpropr_node_t *tree = clike_malloc(_max_size_);
	int size = cpropr_read_tree(PROP_AIM, tree, _max_size_);
	for (cpropr_node_t *curr = tree; curr < (cpropr_node_t *)((char *)tree + size); curr++)
	{
		clike_print("  Id: %d\n", curr->id);
		clike_print("  Type: %d\n", curr->type);
		clike_print("  State: %d\n", curr->state);
		switch (curr->type)
		{
			case CPROPR_TYPE_BOOL:
			case CPROPR_TYPE_ENUM:
			case CPROPR_TYPE_COMB:
			case CPROPR_TYPE_INT: clike_print("  Value: %d\n", curr->value.i); break;
			case CPROPR_TYPE_FLOAT: clike_print("  Value: %.1f\n", curr->value.f); break;
			case CPROPR_TYPE_STRING:
			{
				int len = curr->value.b.length;
				clike_print("  Value: %*.s\n", len, curr->value.b.data);
				len = (len + 3) & ~3; // 4-byte alignment
				curr = (cpropr_node_t *)((char *)curr + len);
				break;
			}
		}
		clike_print("--------------------------------\n");
	}

	// Cleanup
	cpropr_free();
	return 0;
}
