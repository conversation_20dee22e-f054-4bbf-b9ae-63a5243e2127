// Auto generated by cpropr.py, DO NOT EDIT!

#ifndef __CPROPR_CTX_H__
#define __CPROPR_CTX_H__

#include "cpropr_types.h"
#include "cpropr.cfg.h"

// Hook function declarations
void _aim_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _luminance_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _temperature_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _color_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _indication_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _name_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _password_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _invalid_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _language_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _port_config_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _shape_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _volume_prop_hook_(int id, cpropr_hook_type_t type, void *value);
void _quality_prop_hook_(int id, cpropr_hook_type_t type, void *value);

// Options for Color
static const char *_color_names_[] = {
    "Red",
    "Green",
    "Blue",
    "White",
    0
};
// Options for Indication
static const char *_indication_names_[] = {
    "LED",
    "Buzzer",
    "Vibration",
    0
};
// Options for Language
static const char *_language_names_[] = {
    "EN",
    "ZH",
    0
};
// Options for PortConfig
static const char *_portconfig_names_[] = {
    "RS232",
    "RS485",
    "CAN",
    0
};
// Options for Shape
static const char *_shape_names_[] = {
    "Dot",
    "Cross",
    "Line",
    0
};
// Options for Quality
static const char *_quality_names_[] = {
    "Low",
    "Medium",
    "High",
    0
};
static cpropr_item_t *_cpropr_items_[] = {
    &(cpropr_item_t){
        .id = PROP_AIM,
        .name = "Aim",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NONE,
        .children = (short[]){12, -1},
        .hook = _aim_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_LUMINANCE,
        .name = "Luminance",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 100 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _luminance_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_TEMPERATURE,
        .name = "Temperature",
        .type = CPROPR_TYPE_FLOAT,
        .range = { .f = { -40.0, 85.0 } },
        .defval = { .f = 25.0 },
        .flags = CPROPR_FLAG_RDONLY,
        .children = 0,
        .hook = _temperature_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_COLOR,
        .name = "Color",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _color_names_ } },
        .defval = { .i = COLOR_E_White },
        .flags = CPROPR_FLAG_NOSAVE,
        .children = (short[]){14, -1},
        .hook = _color_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INDICATION,
        .name = "Indication",
        .type = CPROPR_TYPE_COMB,
        .range = { .e = { .names = _indication_names_ } },
        .defval = { .i = INDICATION_E_LED },
        .flags = CPROPR_FLAG_NONE,
        .children = (short[]){13, -1},
        .hook = _indication_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_NAME,
        .name = "Name",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 32 },
        .defval = { .p = (cpropr_bytes_t*)"\x06""Device" },
        .flags = CPROPR_FLAG_REBOOT,
        .children = 0,
        .hook = _name_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_PASSWORD,
        .name = "Password",
        .type = CPROPR_TYPE_STRING,
        .range = { .length = 16 },
        .defval = { .p = (cpropr_bytes_t*)"\x06""123456" },
        .flags = CPROPR_FLAG_WRONLY | CPROPR_FLAG_HIDDEN | CPROPR_FLAG_REBOOT,
        .children = 0,
        .hook = _password_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_INVALID,
        .name = "Invalid",
        .type = CPROPR_TYPE_BOOL,
        .defval = { .i = 0 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _invalid_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_LANGUAGE,
        .name = "Language",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _language_names_ } },
        .defval = { .i = LANGUAGE_E_EN },
        .flags = CPROPR_FLAG_REBOOT,
        .children = 0,
        .hook = _language_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_PORTACONFIG,
        .name = "PortAConfig",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _portconfig_names_ } },
        .defval = { .i = PORTCONFIG_E_RS232 },
        .flags = CPROPR_FLAG_REBOOT,
        .children = 0,
        .hook = _port_config_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_PORTBCONFIG,
        .name = "PortBConfig",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _portconfig_names_ } },
        .defval = { .i = PORTCONFIG_E_RS232 },
        .flags = CPROPR_FLAG_REBOOT,
        .children = 0,
        .hook = _port_config_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_PORTCCONFIG,
        .name = "PortCConfig",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _portconfig_names_ } },
        .defval = { .i = PORTCONFIG_E_RS232 },
        .flags = CPROPR_FLAG_REBOOT,
        .children = 0,
        .hook = _port_config_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_SHAPE,
        .name = "Shape",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _shape_names_ } },
        .defval = { .i = SHAPE_E_Dot },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _shape_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_VOLUME,
        .name = "Volume",
        .type = CPROPR_TYPE_INT,
        .range = { .i = { 0, 100 } },
        .defval = { .i = 50 },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _volume_prop_hook_,
    },
    &(cpropr_item_t){
        .id = PROP_QUALITY,
        .name = "Quality",
        .type = CPROPR_TYPE_ENUM,
        .range = { .e = { .names = _quality_names_ } },
        .defval = { .i = QUALITY_E_Medium },
        .flags = CPROPR_FLAG_NONE,
        .children = 0,
        .hook = _quality_prop_hook_,
    },
};

#define _cpropr_count_ (sizeof(_cpropr_items_)/sizeof(_cpropr_items_[0]))

#endif // __CPROPR_CTX_H__
