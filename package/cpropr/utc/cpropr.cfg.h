// Auto generated by cpropr.py, DO NOT EDIT!

#ifndef __CPROPR_CFG_H__
#define __CPROPR_CFG_H__

// Property IDs
#define PROP_AIM 0 // Aim
#define PROP_LUMINANCE 1 // Luminance
#define PROP_TEMPERATURE 2 // Temperature
#define PROP_COLOR 3 // Color
#define PROP_INDICATION 4 // Indication
#define PROP_NAME 5 // Name
#define PROP_PASSWORD 6 // Password
#define PROP_INVALID 7 // Invalid
#define PROP_LANGUAGE 8 // Language
#define PROP_PORTACONFIG 9 // PortAConfig
#define PROP_PORTBCONFIG 10 // PortBConfig
#define PROP_PORTCCONFIG 11 // PortCConfig
#define PROP_SHAPE 12 // Shape
#define PROP_VOLUME 13 // Volume
#define PROP_QUALITY 14 // Quality

// Enum values for Color
typedef enum {
    COLOR_E_Red = 0,
    COLOR_E_Green = 1,
    COLOR_E_Blue = 2,
    COLOR_E_White = 3,
} COLOR_E;

// Enum values for Indication
typedef enum {
    INDICATION_E_LED = 1,
    INDICATION_E_Buzzer = 2,
    INDICATION_E_Vibration = 4,
} INDICATION_E;

// Enum values for Language
typedef enum {
    LANGUAGE_E_EN = 0,
    LANGUAGE_E_ZH = 1,
} LANGUAGE_E;

// Enum values for PortConfig
typedef enum {
    PORTCONFIG_E_RS232 = 0,
    PORTCONFIG_E_RS485 = 1,
    PORTCONFIG_E_CAN = 2,
} PORTCONFIG_E;

// Enum values for Shape
typedef enum {
    SHAPE_E_Dot = 0,
    SHAPE_E_Cross = 1,
    SHAPE_E_Line = 2,
} SHAPE_E;

// Enum values for Quality
typedef enum {
    QUALITY_E_Low = 0,
    QUALITY_E_Medium = 1,
    QUALITY_E_High = 2,
} QUALITY_E;

#endif // __CPROPR_CFG_H__
