// Auto generated by cpropr.py, DO NOT EDIT!

#ifndef __CPROPR_DES_H__
#define __CPROPR_DES_H__

#include "cpropr_types.h"

// Path keys for multilingual support
static const char *_cpropr_pkeys_[][2] = {
    {"Cam", "EN: Camera;ZH: 相机"},
    {"Lum", "EN: Flashlight;ZH: 闪光灯"},
    {"Com", "EN: Communication;ZH: 通信"},
    {"PortA", "EN: Port A;ZH: 端口A"},
    {"PortB", "EN: Port B;ZH: 端口B"},
    {"PortC", "EN: Port C;ZH: 端口C"},
};
static const int _cpropr_pkeys_count_ = 6;

static const char *_color_descs_[] = {
    "EN: Red Light;ZH: 红光",
    "EN: Green Light;ZH: 绿光",
    "EN: Blue Light;ZH: 蓝光",
    "EN: White Light;ZH: 白光",
    0
};

static const char *_indication_descs_[] = {
    "EN: LED Indication;ZH: LED指示",
    "EN: Buzzer Indication;ZH: 蜂鸣器指示",
    "EN: Vibration Indication;ZH: 振动指示",
    0
};

static const char *_language_descs_[] = {
    "EN: English;ZH: 英文",
    "EN: Chinese;ZH: 中文",
    0
};

static const char *_portconfig_descs_[] = {
    "EN: RS232 Mode;ZH: RS232",
    "EN: RS485 Mode;ZH: RS485",
    "EN: CAN Mode;ZH: CAN",
    0
};

static const char *_shape_descs_[] = {
    "EN: Dot Shape;ZH: 点状",
    "EN: Cross Shape;ZH: 十字",
    "EN: Line Shape;ZH: 线形",
    0
};

static const char *_quality_descs_[] = {
    "EN: Low Quality;ZH: 低",
    "EN: Medium Quality;ZH: 中",
    "EN: High Quality;ZH: 高",
    0
};

static cpropr_desc_t *_cpropr_descs_[] = {
    &(cpropr_desc_t){
        .name = "Aim",
        .label = "EN: Aim enable;ZH: 瞄准功能",
        .path = "/",
        .help = "EN: Aim enable status;ZH: 瞄准功能状态",
        .descs = 0,
    },
    &(cpropr_desc_t){
        .name = "Luminance",
        .label = "EN: Luminance Level;ZH: 亮度等级",
        .path = "/",
        .help = "EN: Set luminance level;ZH: 设置亮度等级",
        .descs = 0,
    },
    &(cpropr_desc_t){
        .name = "Temperature",
        .label = "EN: Temperature;ZH: 温度",
        .path = "/",
        .help = "EN: Current device temperature in celsius;ZH: 当前设备温度（摄氏度）",
        .descs = 0,
    },
    &(cpropr_desc_t){
        .name = "Color",
        .label = "EN: Light Color;ZH: 光源颜色",
        .path = "/Cam/Lum",
        .help = "EN: Select light color;ZH: 选择光源颜色",
        .descs = _color_descs_,
    },
    &(cpropr_desc_t){
        .name = "Indication",
        .label = "EN: Status Indication;ZH: 状态指示",
        .path = "/",
        .help = "EN: Select status indication method;ZH: 选择状态指示方式",
        .descs = _indication_descs_,
    },
    &(cpropr_desc_t){
        .name = "Name",
        .label = "EN: Device Name;ZH: 设备名称",
        .path = "/",
        .help = "EN: Set device name;ZH: 设置设备名称",
        .descs = 0,
    },
    &(cpropr_desc_t){
        .name = "Password",
        .label = "EN: Device Password;ZH: 设备密码",
        .path = "/",
        .help = "EN: Set device password, write-only for security;ZH: 设置设备密码，仅写入以保证安全",
        .descs = 0,
    },
    &(cpropr_desc_t){
        .name = "Invalid",
        .label = "EN: Invalid Setting;ZH: 无效设置",
        .path = "/",
        .help = "EN: This setting will be disabled at runtime;ZH: 此设置将在运行时被禁用",
        .descs = 0,
    },
    &(cpropr_desc_t){
        .name = "Language",
        .label = "EN: System Language;ZH: 系统语言",
        .path = "/",
        .help = "EN: Select system display language;ZH: 选择系统显示语言",
        .descs = _language_descs_,
    },
    &(cpropr_desc_t){
        .name = "PortAConfig",
        .label = "EN: Port A Configuration;ZH: 端口A配置",
        .path = "/Com/PortA",
        .help = "EN: Configure communication port A;ZH: 配置通信端口A",
        .descs = _portconfig_descs_,
    },
    &(cpropr_desc_t){
        .name = "PortBConfig",
        .label = "EN: Port B Configuration;ZH: 端口B配置",
        .path = "/Com/PortB",
        .help = "EN: Configure communication port B;ZH: 配置通信端口B",
        .descs = _portconfig_descs_,
    },
    &(cpropr_desc_t){
        .name = "PortCConfig",
        .label = "EN: Port C Configuration;ZH: 端口C配置",
        .path = "/Com/PortC",
        .help = "EN: Configure communication port C;ZH: 配置通信端口C",
        .descs = _portconfig_descs_,
    },
    &(cpropr_desc_t){
        .name = "Shape",
        .label = "EN: Aim Shape;ZH: 瞄准形状",
        .path = "/",
        .help = "EN: Select aim shape when aim is enabled;ZH: 当瞄准功能启用时选择瞄准形状",
        .descs = _shape_descs_,
    },
    &(cpropr_desc_t){
        .name = "Volume",
        .label = "EN: Buzzer Volume;ZH: 蜂鸣器音量",
        .path = "/",
        .help = "EN: Set buzzer volume level;ZH: 设置蜂鸣器音量等级",
        .descs = 0,
    },
    &(cpropr_desc_t){
        .name = "Quality",
        .label = "EN: Image Quality;ZH: 图像质量",
        .path = "/",
        .help = "EN: Set image quality level (not available in blue light mode);ZH: 设置图像质量等级（蓝光模式下不可用）",
        .descs = _quality_descs_,
    },
};

#define _cpropr_desc_count_ (sizeof(_cpropr_descs_)/sizeof(_cpropr_descs_[0]))

#endif // __CPROPR_DES_H__
