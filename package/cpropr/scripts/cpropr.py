import os
from _cpropr_core import parse_all_configs
from _cpropr_code import generate_headers
from _cpropr_docu import generate_html_doc
from _cpropr_json import generate_json_config


def collect_source_files(application) -> tuple:
    srcs, la_files, cfg_h, ctx_h, des_h = [], [], None, None, None

    for item in application.get("SRCS", []):
        if not isinstance(item, str):
            continue
        if item.endswith(".c"):
            srcs.append(item)
        elif item.endswith(".la"):
            la_files.append(item)
        elif item.endswith("cpropr.cfg.h"):
            cfg_h = item
        elif item.endswith("cpropr.ctx.h"):
            ctx_h = item
        elif item.endswith("cpropr.des.h"):
            des_h = item

    return srcs, la_files, cfg_h, ctx_h, des_h


def _find_output_base(application):
    name = application.get("NAME", application.get("HEAD", "properties"))
    name, _ = os.path.splitext(name)
    return os.path.join(application.get("BUILD_PATH", "build"), "output", name)


def try_generate_doc(application, configs, paths):
    if not (application.get("ALL", False) or
            application.get("DOC", False) or
            application.get("DOCUMENT", False) or
            application.get("HTML", False)):
        return
    show_all = application.get("ADV", False)
    generate_html_doc(configs, paths, _find_output_base(application) + ".html", show_all)


def try_generate_json(application, configs, paths):
    if not (application.get("ALL", False) or
            application.get("JSON", False) or
            application.get("CONFIG", False)):
        return
    generate_json_config(configs, paths, _find_output_base(application) + ".json")


def mkcfg(application):
    """Main entry point for generating both code and documentation"""
    srcs, la_files, cfg_h, ctx_h, des_h = collect_source_files(application)

    if not cfg_h or not ctx_h:
        print("Error: Required header files not found in project configuration")
        return

    # Parse configs
    configs, paths = parse_all_configs(srcs, la_files, cfg_h)
    if not configs:
        print("No configuration properties found")
        return

    # Generate headers
    generate_headers(configs, paths, cfg_h, ctx_h, des_h)

    # Generate documentation if needed
    try_generate_doc(application, configs, paths)

    # Generate JSON config if needed
    try_generate_json(application, configs, paths)
