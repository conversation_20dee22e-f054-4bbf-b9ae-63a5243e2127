import re
import os
import toml
from typing import List, Dict, Any


class PropConfig:
    def __init__(self):
        self.name: str = ""
        self.label: str = ""
        self.type: str = ""
        self.range: tuple = None
        self.options: Dict = {}
        self.default: Any = None
        self.path: str = "/"
        self.help: str = ""
        self.length: int = 0
        self.flags: str = "CPROPR_FLAG_NONE"
        self.hook: str = ""
        self.base: str = ""
        self.deps: str = ""
        self.children: List[int] = []


# Helper functions for parsing
def _parse_comment_line(line: str, prop: PropConfig):
    if line.startswith(' *  '):
        return None, line.strip('* ')

    line = line.strip('/* ')
    if not line.startswith('@'):
        return None, None

    key, value = [x.strip() for x in line[1:].split(':', 1)]

    if key == 'PROP':
        prop.name = value
    elif key == 'TYPE':
        prop.type = value
    elif key == 'DEFAULT':
        prop.default = value.strip('"')
    elif key == 'RANGE':
        prop.range = _parse_range(value, prop.type)
    elif key == 'LENGTH':
        prop.length = int(value)
    elif key == 'FLAG':
        prop.flags = _parse_flags(value)
    elif key == 'DEPS':
        prop.deps = value.strip()

    return key, value


def _parse_range(value: str, prop_type: str) -> tuple:
    min_val, max_val = value.split(',')
    if prop_type == 'FLOAT':
        return (float(min_val), float(max_val))
    return (int(min_val), int(max_val))


def _parse_flags(value: str) -> str:
    flags = []

    for flag in value.strip().split('|'):
        flag = flag.strip()
        if not flag:
            continue
        flags.append(f"CPROPR_FLAG_{flag}")

    return " | ".join(flags) if flags else "CPROPR_FLAG_NONE"


def _parse_options(lines: List[str]) -> Dict[str, str]:
    options = {}
    current_key = None
    current_desc = []

    for line in lines:
        line = line.strip()
        if not line:
            continue

        if line.startswith('+'):
            if current_key and current_desc:
                options[current_key] = ";".join(current_desc)

            current_key, desc = [x.strip() for x in line[1:].split(':', 1)]
            current_desc = [desc] if desc else []
        elif line:
            current_desc.append(line)

    if current_key and current_desc:
        options[current_key] = ";".join(current_desc)

    return options


def _parse_multilang_string(text: str) -> Dict[str, str]:
    if isinstance(text, dict):
        return text

    result = {}
    if not text:
        return result

    for part in text.split(";"):
        if ":" in part:
            lang, content = part.split(":", 1)
            result[lang.strip().upper()] = content.strip()
        else:
            result["EN"] = part.strip()

    return result


def _process_multiline(prop: PropConfig, key: str, lines: List[str]):
    if not lines:
        return

    content = ";".join(lines)
    if key == 'OPTIONS':
        prop.options = _parse_options(lines)
    elif key == 'LABEL':
        prop.label = content
    elif key == 'PATH':
        prop.path = content
    elif key == 'HELP':
        prop.help = content

    lines.clear()


# Single block parsing
def _parse_single_block(text: str) -> PropConfig:
    prop = PropConfig()
    lines = text.split('\n')
    current_key = None
    temp_lines = []

    for line in lines:
        key, value = _parse_comment_line(line, prop)
        if value is None:
            break
        if key is None:
            temp_lines.append(value)
            continue

        if current_key:
            _process_multiline(prop, current_key, temp_lines)
            current_key = None

        if key in ['LABEL', 'PATH', 'HELP', 'OPTIONS']:
            current_key = key
            if value:
                temp_lines.append(value)

    if current_key:
        _process_multiline(prop, current_key, temp_lines)

    hook_match = re.search(r'void (\w+)\(.+\)', text)
    if hook_match:
        prop.hook = hook_match.group(1)

    return prop


# Variant expansion
def _expand_variants(prop: PropConfig) -> List[PropConfig]:
    match = re.match(r'^(.+?)\[([^\]]+)\](.*)$', prop.name)
    if not match:
        prop.base = prop.name
        return [prop]

    base, variants, suffix = match.groups()
    variant_list = [v.strip() for v in variants.split(',')]
    expanded = []

    for var in variant_list:
        new_prop = PropConfig()
        new_prop.__dict__ = prop.__dict__.copy()
        new_prop.name = f"{base}{var}{suffix}"
        new_prop.base = f"{base}{suffix}"

        new_prop.label = new_prop.label.replace("%i", var)
        new_prop.path = new_prop.path.replace("%i", var)
        new_prop.help = new_prop.help.replace("%i", var)
        new_prop.deps = new_prop.deps.replace("%i", var)

        expanded.append(new_prop)

    return expanded


# Main comment parsing
def parse_config_comments(c_file: str) -> List[PropConfig]:
    with open(c_file, 'r', encoding="gbk", errors="ignore") as f:
        content = f.read()

    configs = []
    blocks = re.finditer(r'/\* @PROP:.*?\n\}', content, re.DOTALL)

    for block in blocks:
        prop = _parse_single_block(block.group())
        if not prop.name:
            print(f"Bad block:\n{block.group(0)}")
            continue
        configs.extend(_expand_variants(prop))

    return configs


# Data conversion functions
def _convert_to_dicts(configs: List[PropConfig]):
    for cfg in configs:
        if isinstance(cfg, str):
            continue
        cfg.label = _parse_multilang_string(cfg.label)
        cfg.help = _parse_multilang_string(cfg.help)
        opts = cfg.options
        for key, text in opts.items():
            opts[key] = _parse_multilang_string(text)


# Language processing functions
def _write_language_file(la_file: str, configs: List[PropConfig], paths: Dict[str, Dict[str, str]], marker: str):
    with open(la_file, 'w', encoding='utf-8') as f:
        f.write("[PathKeys]\n")
        for key, value in paths.items():
            text = value.get(marker, "")
            f.write(f'{key} = "{text}"\n')

        converted = set()
        for cfg in configs:
            if isinstance(cfg, str):
                continue
            name = re.sub(r'\[.*?\]', '', cfg.name)
            f.write(f"[{name}]\n")
            f.write(f'LABEL = "{cfg.label.get(marker, "")}"\n')
            f.write(f'HELP = "{cfg.help.get(marker, "")}"\n')
            if cfg.type not in ['ENUM', 'COMB']:
                continue
            if cfg.base in converted:
                continue
            converted.add(cfg.base)
            f.write(f"[{name}.Options]\n")
            for key, value in cfg.options.items():
                f.write(f'{key} = "{value.get(marker, "")}"\n')

    print(f"Generated language file: {la_file}")


def _merge_languages(configs: List[PropConfig], paths: Dict[str, Dict[str, str]], la_files: List[str]):
    def _update_lang_(data: Dict, value: str):
        if not value:
            return
        if marker not in data:
            data[marker] = value
        elif data[marker] != value:
            print(f"Warning: {cfg.name} {marker} has different values")

    for la_file in la_files:
        marker = os.path.splitext(os.path.basename(la_file))[0].upper()
        lang_data = toml.load(la_file)

        paths_data = lang_data.pop('PathKeys', {})
        for key, data in paths.items():
            _update_lang_(data, paths_data.get(key, ""))

        converted = set()
        for cfg in configs:
            if isinstance(cfg, str):
                continue
            cfg_data = lang_data.get(cfg.name, None)
            if cfg_data is None:
                continue
            _update_lang_(cfg.label, cfg_data.get('LABEL', ""))
            _update_lang_(cfg.help, cfg_data.get('HELP', ""))
            opt_data = cfg_data.get('Options', None)
            if opt_data is None:
                continue
            if cfg.type not in ['ENUM', 'COMB']:
                continue
            if cfg.base in converted:
                continue
            converted.add(cfg.base)
            opts = cfg.options
            for key, data in opts.items():
                _update_lang_(data, opt_data.get(key, ""))

        _write_language_file(la_file, configs, paths, marker)


# Legacy order maintenance functions
def _find_legacy_order(cfg_h: str) -> List[str]:
    prop_names = []

    with open(cfg_h, 'r', errors="ignore") as f:
        for line in f:
            if line.startswith('// '):
                line = line[3:]
            item = re.match(r'#define PROP_(\w+)', line)
            if item:
                prop_names.append(item.group(1))

    return prop_names


def _maintain_legacy_order(configs: List[PropConfig], cfg_h: str) -> List[PropConfig]:
    if not cfg_h or not os.path.exists(cfg_h):
        return configs

    cur_names = [cfg.name.upper() for cfg in configs]
    old_names = _find_legacy_order(cfg_h)
    name_to_cfg = {cfg.name.upper(): cfg for cfg in configs}

    ordered_configs = []

    def _add_old_property(name):
        if name in name_to_cfg:
            ordered_configs.append(name_to_cfg[name])
        else:
            print(f"Property {name} deleted")
            ordered_configs.append(name)

    def _add_new_property(name):
        if name not in old_names:
            print(f"Property {name} added")
            ordered_configs.append(name_to_cfg[name])

    [_add_old_property(name) for name in old_names]
    [_add_new_property(name) for name in cur_names]

    return ordered_configs


# Path processing functions
def _fix_legacy_path_format(configs: List[PropConfig]):
    for cfg in configs:
        if isinstance(cfg, str):
            continue
        if not cfg.path or cfg.path == "/":
            continue
        if not cfg.path.strip().startswith("EN:"):
            continue
        en_path, left_path = cfg.path.split(";", 1)
        abbreviated_path = en_path.strip()[3::].strip()
        if abbreviated_path.startswith("/Hide"):
            abbreviated_path = "/Reserved"
        cfg.path = f"{abbreviated_path};EN:{abbreviated_path};{left_path}"


# Path extraction
def _extract_path_keys(configs: List[PropConfig]) -> Dict[str, Dict[str, str]]:
    keys = {}
    for cfg in configs:
        if isinstance(cfg, str):
            continue
        if ";" in cfg.path:
            path, desc = cfg.path.split(";", 1)
            desc = _parse_multilang_string(desc)
            cfg.path = path
        else:
            path, desc = cfg.path, {}
        pclips = [p for p in path.split("/") if p]
        if not pclips:
            continue
        for key, value in desc.items():
            dclips = [p for p in value.split("/") if p]
            assert len(dclips) == len(pclips), f"Path and description length mismatch: {path} {value}"
            desc[key] = dclips
        for i, clip in enumerate(pclips):
            lang = {}
            for key, value in desc.items():
                lang[key] = value[i]
            keys[clip] = lang
    return keys


# Main entry point
def parse_all_configs(srcs: List[str], langs: List[str], cfg_h: str) -> List[PropConfig]:
    configs = []
    for src in sorted(srcs):
        configs.extend(parse_config_comments(src))
    configs = _maintain_legacy_order(configs, cfg_h)
    _fix_legacy_path_format(configs)
    paths = _extract_path_keys(configs)
    _convert_to_dicts(configs)
    _merge_languages(configs, paths, langs)
    return configs, paths
