import os
from typing import List, Dict
from _cpropr_core import PropConfig


# Helper functions for text processing
def _get_display_text(text) -> str:
    if isinstance(text, str):
        return text
    
    if not isinstance(text, dict) or not text:
        return ""
    
    if "EN" in text and text["EN"]:
        return text["EN"]
    
    for lang, content in text.items():
        if content:
            return content
    
    return ""


def _format_multilang_text(text) -> str:
    if isinstance(text, str):
        return text
    
    if not isinstance(text, dict) or not text:
        return ""
    
    if len(text) == 1:
        return list(text.values())[0]
    
    html = ['<div class="multilang">']
    for lang, content in text.items():
        if content:
            html.append(f'<span class="lang-tag">{lang}</span>{content}<br>')
    html.append('</div>')
    
    return ''.join(html)


def _clean_flag_display(flags: str) -> str:
    return flags.replace("CPROPR_FLAG_", "")


def _is_hidden(cfg: PropConfig) -> bool:
    return "CPROPR_FLAG_HIDDEN" in cfg.flags


def _build_multilang_path(path: str, paths: Dict[str, Dict[str, str]]) -> Dict[str, str]:
    if not path or path == "/":
        return {}
    
    path_parts = [p for p in path.split("/") if p]
    if not path_parts:
        return {}
    
    lang_template = None
    for part in path_parts:
        if part in paths and paths[part]:
            lang_template = paths[part]
            break
    
    if not lang_template:
        return {"EN": path}
    
    multilang_paths = {}
    for lang in lang_template.keys():
        translated_parts = []
        for part in path_parts:
            if part in paths and lang in paths[part] and paths[part][lang]:
                translated_parts.append(paths[part][lang])
            else:
                translated_parts.append(part)
        multilang_paths[lang] = "/" + "/".join(translated_parts)
    
    return multilang_paths


# HTML generation helper functions
def _add_property_field(html: List[str], label: str, value: str):
    html.append('<div class="property-label">' + label + ':</div>')
    html.append('<div class="property-value">' + value + '</div>')


def _get_html_header() -> str:
    return """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configuration Properties Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
        }
        .property {
            border: 1px solid #ddd;
            margin: 20px 0;
            border-radius: 6px;
            overflow: hidden;
        }
        .property-header {
            background: #3498db;
            color: white;
            padding: 15px;
            font-weight: bold;
            font-size: 18px;
        }
        .property-content {
            padding: 20px;
        }
        .property-info {
            display: grid;
            grid-template-columns: 150px 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        .property-label {
            font-weight: bold;
            color: #2c3e50;
        }
        .property-value {
            color: #34495e;
        }
        .type-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .type-int { background: #e74c3c; color: white; }
        .type-float { background: #f39c12; color: white; }
        .type-bool { background: #27ae60; color: white; }
        .type-string { background: #9b59b6; color: white; }
        .type-enum { background: #34495e; color: white; }
        .type-comb { background: #16a085; color: white; }
        .options-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .options-table th,
        .options-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .options-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .multilang {
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .lang-tag {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            margin-right: 8px;
        }
        .toc {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 30px;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 5px 0;
        }
        .toc a {
            text-decoration: none;
            color: #3498db;
        }
        .toc a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Configuration Properties Documentation</h1>
"""


def _get_html_footer() -> str:
    return """
    </div>
</body>
</html>
"""


# Property HTML generation
def _generate_property_html(cfg: PropConfig, paths: Dict[str, Dict[str, str]], show_all: bool = False) -> str:
    html = []
    
    html.append(f'<div class="property" id="{cfg.name}">')
    html.append(f'<div class="property-header">{cfg.name}</div>')
    html.append('<div class="property-content">')
    
    html.append('<div class="property-info">')
    
    _add_property_field(html, "Label", _format_multilang_text(cfg.label))
    _add_property_field(html, "Type", f'<span class="type-badge type-{cfg.type.lower()}">{cfg.type}</span>')
    
    if cfg.path and cfg.path != "/":
        multilang_path = _build_multilang_path(cfg.path, paths)
        _add_property_field(html, "Path", _format_multilang_text(multilang_path))
    
    if cfg.default is not None:
        _add_property_field(html, "Default", f'<code>{cfg.default}</code>')
    
    if cfg.range:
        _add_property_field(html, "Range", f'{cfg.range[0]} ~ {cfg.range[1]}')
    
    if cfg.type == "STRING" and cfg.length > 0:
        _add_property_field(html, "Max Length", str(cfg.length))
    
    if cfg.flags != "CPROPR_FLAG_NONE":
        clean_flags = _clean_flag_display(cfg.flags)
        _add_property_field(html, "Flags", f'<code>{clean_flags}</code>')
    
    if cfg.hook and show_all:
        _add_property_field(html, "Hook", f'<code>{cfg.hook}()</code>')
    
    html.append('</div>')
    
    if cfg.help:
        html.append('<div class="property-label">Description:</div>')
        html.append(f'<div class="property-value">{_format_multilang_text(cfg.help)}</div>')
    
    if cfg.type in ['ENUM', 'COMB'] and cfg.options:
        html.append('<div class="property-label">Options:</div>')
        html.append('<table class="options-table">')
        html.append('<tr><th>Value</th><th>Description</th></tr>')
        
        for key, desc in cfg.options.items():
            html.append(f'<tr>')
            html.append(f'<td><code>{key}</code></td>')
            html.append(f'<td>{_format_multilang_text(desc)}</td>')
            html.append('</tr>')
        
        html.append('</table>')
    
    html.append('</div>')
    html.append('</div>')
    
    return '\n'.join(html)


# Body HTML generation
def _get_html_body(configs: List[PropConfig], paths: Dict[str, Dict[str, str]], show_all: bool = False) -> str:
    html = []
    
    visible_configs = [cfg for cfg in configs if show_all or not _is_hidden(cfg)]
    
    html.append('<div class="toc">')
    html.append('<h2>Table of Contents</h2>')
    html.append('<ul>')
    
    for cfg in visible_configs:
        html.append(f'<li><a href="#{cfg.name}">{cfg.name}</a> - {_get_display_text(cfg.label)}</li>')
    
    html.append('</ul>')
    html.append('</div>')
    
    for cfg in visible_configs:
        html.append(_generate_property_html(cfg, paths, show_all))
    
    return '\n'.join(html)


# Main entry point
def generate_html_doc(configs: List[PropConfig], paths: Dict[str, Dict[str, str]], output: str, show_all: bool = False):
    valid_configs = [cfg for cfg in configs if isinstance(cfg, PropConfig)]
    with open(output, 'w', encoding='utf-8') as f:
        f.write(_get_html_header())
        f.write(_get_html_body(valid_configs, paths, show_all))
        f.write(_get_html_footer())
    print(f"Generated documentation: {output}") 