import json
import os
from typing import List, Dict, Any
from _cpropr_core import PropConfig


# Helper functions for data conversion
def _convert_multilang_field(field) -> Any:
    if isinstance(field, dict):
        return field if field else None
    return field if field else None


def _convert_flags(flags: str) -> List[str]:
    if flags == "CPROPR_FLAG_NONE":
        return []
    
    flag_list = []
    for flag in flags.split(" | "):
        clean_flag = flag.replace("CPROPR_FLAG_", "").strip()
        if clean_flag:
            flag_list.append(clean_flag)
    
    return flag_list


def _convert_numeric_default(default_val: str, prop_type: str):
    if prop_type == "FLOAT":
        return float(default_val)
    return int(default_val)


def _convert_enum_default(default_val: str):
    values = [v.strip().strip('"') for v in default_val.split('|')]
    return values[0] if len(values) == 1 else values


def _convert_options(options: Dict[str, Any]) -> List[Dict[str, Any]]:
    option_list = []
    
    for key, desc in options.items():
        option_data = {
            "value": key,
            "description": _convert_multilang_field(desc)
        }
        option_list.append(option_data)
    
    return option_list


def _convert_path_keys(paths: Dict[str, Dict[str, str]]) -> Dict[str, Any]:
    if not paths:
        return {}
    
    path_keys = {}
    for key, lang_dict in paths.items():
        if lang_dict:
            path_keys[key] = lang_dict
        else:
            path_keys[key] = key
    
    return path_keys


# Type-specific data processing
def _add_type_specific_data(prop_data: Dict, cfg: PropConfig):
    if cfg.type == "STRING":
        if cfg.length > 0:
            prop_data["max_length"] = cfg.length
        if cfg.default:
            prop_data["default"] = cfg.default
    
    elif cfg.type in ["INT", "FLOAT"]:
        if cfg.range:
            prop_data["range"] = {
                "min": cfg.range[0],
                "max": cfg.range[1]
            }
        if cfg.default is not None:
            prop_data["default"] = _convert_numeric_default(cfg.default, cfg.type)
    
    elif cfg.type == "BOOL":
        if cfg.default is not None:
            prop_data["default"] = cfg.default.lower() == "true"
    
    elif cfg.type in ["ENUM", "COMB"]:
        if cfg.options:
            prop_data["options"] = _convert_options(cfg.options)
        if cfg.default:
            prop_data["default"] = _convert_enum_default(cfg.default)


def _add_optional_fields(prop_data: Dict, cfg: PropConfig):
    if cfg.hook:
        prop_data["hook"] = cfg.hook
    
    if cfg.base:
        prop_data["base"] = cfg.base
    
    if cfg.deps:
        prop_data["dependencies"] = [dep.strip() for dep in cfg.deps.split(',')]
    
    if cfg.children:
        prop_data["children"] = cfg.children


# Single config conversion
def _convert_single_config(cfg: PropConfig, index: int) -> Dict[str, Any]:
    prop_data = {
        "id": index,
        "name": cfg.name,
        "type": cfg.type,
        "label": _convert_multilang_field(cfg.label),
        "path": _convert_multilang_field(cfg.path),
        "help": _convert_multilang_field(cfg.help),
        "flags": _convert_flags(cfg.flags)
    }
    
    _add_type_specific_data(prop_data, cfg)
    _add_optional_fields(prop_data, cfg)
    
    return prop_data


# Main conversion functions
def _convert_configs_to_json(configs: List[PropConfig], paths: Dict[str, Dict[str, str]]) -> Dict[str, Any]:
    result = {
        "version": "1.0",
        "generated_by": "cpropr",
        "total_properties": len(configs),
        "path_keys": _convert_path_keys(paths),
        "properties": []
    }
    
    for i, cfg in enumerate(configs):
        prop_data = _convert_single_config(cfg, i)
        result["properties"].append(prop_data)
    
    return result


def generate_json_config(configs: List[PropConfig], paths: Dict[str, Dict[str, str]], output: str):    
    valid_configs = [cfg for cfg in configs if isinstance(cfg, PropConfig)]
    json_data = _convert_configs_to_json(valid_configs, paths)
    
    with open(output, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, indent=2, ensure_ascii=False)
    
    print(f"Generated JSON config: {output}") 