from typing import List, Dict
from _cpropr_core import PropConfig


# Helper functions for data processing
def _resolve_dependencies(configs: List[PropConfig]):
    name_to_id = {}
    for i, cfg in enumerate(configs):
        if isinstance(cfg, PropConfig):
            name_to_id[cfg.name] = i
    
    for i, cfg in enumerate(configs):
        if not isinstance(cfg, PropConfig) or not cfg.deps:
            continue
        
        for dep_name in [d.strip() for d in cfg.deps.split(',')]:
            if dep_name in name_to_id:
                dep_id = name_to_id[dep_name]
                configs[dep_id].children.append(i)


def _find_unique_enums(configs: List[PropConfig]) -> List[PropConfig]:
    unique_enums = []
    found = set()
    for cfg in configs:
        if isinstance(cfg, str):
            continue
        if cfg.type not in ['ENUM', 'COMB']:
            continue
        if cfg.base in found:
            continue
        found.add(cfg.base)
        unique_enums.append(cfg)
    return unique_enums


def _dict_to_multilang_string(data_dict: Dict[str, str]) -> str:
    if isinstance(data_dict, str):
        return data_dict
    
    if len(data_dict.values()) == 1:
        return list(data_dict.values())[0]
    
    parts = []
    for lang, text in data_dict.items():
        parts.append(f"{lang}: {text}")
    
    return ";".join(parts) if parts else ""


def _decode_c_string(s: str) -> bytes:
    if not s:
        return b""
    
    result = bytearray()
    i = 0
    while i < len(s):
        if s[i] != '\\' or i + 1 >= len(s):
            result.append(ord(s[i]))
            i += 1
            continue
        
        i += 1
        if s[i] == 'x' and i + 2 < len(s):
            hex_val = int(s[i+1:i+3], 16)
            result.append(hex_val)
            i += 3
        else:
            escape_chars = {'a': '\a', 'b': '\b', 't': '\t', 'n': '\n', 'v': '\v', 'f': '\f', 'r': '\r'}
            result.append(ord(escape_chars.get(s[i], s[i])))
            i += 1
    
    return bytes(result)


# CFG header generation functions
def _write_enum_definitions(f, configs: List[PropConfig]):
    unique_enums = _find_unique_enums(configs)
    
    for cfg in unique_enums:
        enum_id = cfg.base
        enum_name = f"{enum_id.upper()}_E"
        
        f.write(f"// Enum values for {enum_id}\n")
        f.write(f"typedef enum {{\n")
        
        for i, (key, _) in enumerate(cfg.options.items()):
            value = 1 << i if cfg.type == 'COMB' else i
            f.write(f"    {enum_name}_{key} = {value},\n")
        
        f.write(f"}} {enum_name};\n\n")


def _generate_cfg_header(configs: List[PropConfig], output: str):
    with open(output, 'w', encoding='utf-8') as f:
        f.write("// Auto generated by cpropr.py, DO NOT EDIT!\n\n")
        f.write("#ifndef __CPROPR_CFG_H__\n")
        f.write("#define __CPROPR_CFG_H__\n\n")
        
        f.write("// Property IDs\n")
        for i, cfg in enumerate(configs):
            if isinstance(cfg, str):
                f.write(f"// #define PROP_{cfg} {i}\n")
            else:
                f.write(f"#define PROP_{cfg.name.upper()} {i} // {cfg.name}\n")
        f.write("\n")
        
        _write_enum_definitions(f, configs)
        
        f.write("#endif // __CPROPR_CFG_H__\n")
    
    print(f"Generated header file: {output}")


# CTX header generation functions
def _write_hook_declarations(f, configs: List[PropConfig]):
    hooks = []
    for cfg in configs:
        if isinstance(cfg, str) or not cfg.hook:
            continue
        if cfg.hook not in hooks:
            hooks.append(cfg.hook)
    
    if not hooks:
        return
    
    f.write("// Hook function declarations\n")
    for hook in hooks:
        f.write(f"void {hook}(int id, cpropr_hook_type_t type, void *value);\n")
    f.write("\n")


def _write_option_tables(f, configs: List[PropConfig]):
    unique_enums = _find_unique_enums(configs)
    
    for cfg in unique_enums:
        enum_id = cfg.base
        option_name = enum_id.lower()
        
        f.write(f"// Options for {enum_id}\n")
        f.write(f"static const char *_{option_name}_names_[] = {{\n")
        for key in cfg.options.keys():
            f.write(f'    "{key}",\n')
        f.write("    0\n};\n")


def _write_type_specific_data(f, cfg: PropConfig):
    if cfg.type == "STRING":
        f.write(f'        .range = {{ .length = {cfg.length} }},\n')
        if cfg.default is None:
            f.write(f'        .defval = {{ .p = 0 }},\n')
        elif cfg.default:
            str_len = len(_decode_c_string(cfg.default))
            length_bytes = f'\\x{str_len & 0xFF:02x}'
            f.write(f'        .defval = {{ .p = (cpropr_bytes_t*)"{length_bytes}""{cfg.default}" }},\n')
        else:
            f.write(f'        .defval = {{ .p = (cpropr_bytes_t*)"\\x00" }},\n')
    elif cfg.type == "INT":
        f.write(f'        .range = {{ .i = {{ {cfg.range[0]}, {cfg.range[1]} }} }},\n')
        f.write(f'        .defval = {{ .i = {cfg.default} }},\n')
    elif cfg.type == "FLOAT":
        f.write(f'        .range = {{ .f = {{ {cfg.range[0]}, {cfg.range[1]} }} }},\n')
        f.write(f'        .defval = {{ .f = {cfg.default} }},\n')
    elif cfg.type == "BOOL":
        f.write(f'        .defval = {{ .i = {1 if cfg.default.lower() == "true" else 0} }},\n')
    elif cfg.type in ['ENUM', 'COMB']:
        enum_id = cfg.base
        option_name = enum_id.lower()
        enum_name = f"{enum_id.upper()}_E"
        
        f.write(f'        .range = {{ .e = {{ .names = _{option_name}_names_ }} }},\n')
        
        default_values = [x.strip() for x in cfg.default.strip('"').split('|')]
        default_val = ' | '.join([f"{enum_name}_{x}" for x in default_values])
        f.write(f'        .defval = {{ .i = {default_val} }},\n')


def _write_property_array(f, configs: List[PropConfig]):
    f.write("static cpropr_item_t *_cpropr_items_[] = {\n")
    
    for cfg in configs:
        if isinstance(cfg, str):
            f.write("    0,\n")
            continue
        
        f.write("    &(cpropr_item_t){\n")
        f.write(f'        .id = PROP_{cfg.name.upper()},\n')
        f.write(f'        .name = "{cfg.name}",\n')
        f.write(f'        .type = CPROPR_TYPE_{cfg.type},\n')
        
        _write_type_specific_data(f, cfg)
        
        f.write(f'        .flags = {cfg.flags},\n')
        
        if cfg.children:
            children = ', '.join(map(str, cfg.children)) + ', -1'
            f.write(f'        .children = (short[]){{{children}}},\n')
        else:
            f.write('        .children = 0,\n')
        
        f.write(f'        .hook = {cfg.hook or "0"},\n')
        f.write("    },\n")
    
    f.write("};\n\n")


def _generate_ctx_header(configs: List[PropConfig], output: str):
    with open(output, 'w', encoding='utf-8') as f:
        f.write("// Auto generated by cpropr.py, DO NOT EDIT!\n\n")
        f.write("#ifndef __CPROPR_CTX_H__\n")
        f.write("#define __CPROPR_CTX_H__\n\n")
        f.write('#include "cpropr_types.h"\n')
        f.write('#include "cpropr.cfg.h"\n\n')
        
        _write_hook_declarations(f, configs)
        _write_option_tables(f, configs)
        _write_property_array(f, configs)
        
        f.write("#define _cpropr_count_ (sizeof(_cpropr_items_)/sizeof(_cpropr_items_[0]))\n\n")
        f.write("#endif // __CPROPR_CTX_H__\n")
    
    print(f"Generated context file: {output}")


# DES header generation functions
def _write_path_keys_array(f, paths: Dict[str, Dict[str, str]]):
    if not paths:
        f.write("// No path keys found\n")
        f.write("static const char *_cpropr_pkeys_[][2] = { {0, 0} };\n")
        f.write("static const int _cpropr_pkeys_count_ = 0;\n\n")
        return
    
    f.write("// Path keys for multilingual support\n")
    f.write("static const char *_cpropr_pkeys_[][2] = {\n")
    
    for key, lang_dict in paths.items():
        multilang_str = _dict_to_multilang_string(lang_dict)
        f.write(f'    {{"{key}", "{multilang_str}"}},\n')
    
    f.write("};\n")
    f.write(f"static const int _cpropr_pkeys_count_ = {len(paths)};\n\n")


def _write_option_descriptions(f, configs: List[PropConfig]):
    unique_enums = _find_unique_enums(configs)
    
    for cfg in unique_enums:
        enum_id = cfg.base
        option_name = enum_id.lower()
        
        f.write(f"static const char *_{option_name}_descs_[] = {{\n")
        for key, desc_data in cfg.options.items():
            desc_str = _dict_to_multilang_string(desc_data)
            f.write(f'    "{desc_str}",\n')
        f.write("    0\n};\n\n")


def _write_description_array(f, configs: List[PropConfig]):
    f.write("static cpropr_desc_t *_cpropr_descs_[] = {\n")
    
    for cfg in configs:
        if isinstance(cfg, str):
            f.write("    0,\n")
            continue
        
        f.write("    &(cpropr_desc_t){\n")
        f.write(f'        .name = "{cfg.name}",\n')
        f.write(f'        .label = "{_dict_to_multilang_string(cfg.label)}",\n')
        f.write(f'        .path = "{_dict_to_multilang_string(cfg.path)}",\n')
        f.write(f'        .help = "{_dict_to_multilang_string(cfg.help)}",\n')
        
        if cfg.type in ['ENUM', 'COMB'] and cfg.options:
            enum_id = cfg.base
            option_name = enum_id.lower()
            f.write(f'        .descs = _{option_name}_descs_,\n')
        else:
            f.write('        .descs = 0,\n')
        
        f.write("    },\n")
    
    f.write("};\n\n")


def _generate_des_header(configs: List[PropConfig], paths: Dict[str, Dict[str, str]], output: str):
    if not output:
        return

    with open(output, 'w', encoding='utf-8') as f:
        f.write("// Auto generated by cpropr.py, DO NOT EDIT!\n\n")
        f.write("#ifndef __CPROPR_DES_H__\n")
        f.write("#define __CPROPR_DES_H__\n\n")
        f.write('#include "cpropr_types.h"\n\n')
        
        _write_path_keys_array(f, paths)
        _write_option_descriptions(f, configs)
        _write_description_array(f, configs)
        
        f.write("#define _cpropr_desc_count_ (sizeof(_cpropr_descs_)/sizeof(_cpropr_descs_[0]))\n\n")
        f.write("#endif // __CPROPR_DES_H__\n")
    
    print(f"Generated description file: {output}")


# Main entry point
def generate_headers(configs: List[PropConfig], paths: Dict[str, Dict[str, str]], cfg_h: str, ctx_h: str, des_h: str):
    _resolve_dependencies(configs)
    _generate_cfg_header(configs, cfg_h)
    _generate_ctx_header(configs, ctx_h)
    _generate_des_header(configs, paths, des_h) 