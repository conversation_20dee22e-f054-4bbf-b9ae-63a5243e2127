//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: MdNetwork.h
// Author		: DongDL
// Created On	: 2024/11/25
// Description	: MdNetwork.h
//
// History
// 1. V1.0, Created by DongDL. 2024/11/25
//=============================================================================

#ifndef _MDNETWORK_H
#define _MDNETWORK_H

#include "libcustom.h"

#define MDNetwork_IPSet_FORCE (0x1)
#define MDNetwork_IPSet_DHCP  (0x1 << 1)
#define MDNetwork_IPSet_LLA	  (0x1 << 2)

void MdNetwork_Init(void);
s32 MdNetwork_IPSet(u32 ip, u32 ip_cfg);
s32 MdNetwork_IPGet(u32 *ip, u32 *ip_cfg);
s32 MdNetwork_MacSet(byt *mac);
s32 MdNetwork_MacGet(byt *mac);
s32 MdNetwork_NetMaskSet(u32 mask);
s32 MdNetwork_NetMaskGet(u32 *mask);
s32 MdNetwork_GwSet(u32 gw);
s32 MdNetwork_GwGet(u32 *gw);
s32 MdNetwork_IPHex2Str(u32 s, bin *d, u32 l);
s32 MdNetwork_IPStr2Hex(str s, u32 *d);

#endif //_MDNETWORK_H
