[[lines]]
type = "level"
name = "lumin1"
edge = ["lumin1_off", "lumin1_on"]
site = 1.2

[[lines]]
type = "level"
name = "lumin2"
edge = ["lumin2_off", "lumin2_on"]
site = 1.2

[[lines]]
type = "level"
name = "lumin"
edge = ["lumin_off", "lumin_on"]
site = 1.2

[[lines]]
type = "level"
name = "irf-out"
edge = ["irf_off", "irf_on"]
site = 1.2

[[lines]]
type = "pulse"
name = "irf-get"
edge = ["irf_get"]
site = 1.2

[[lines]]
type = "level"
name = "aim"
edge = ["aim_off", "aim_on"]
site = 1.2

[[lines]]
type = "level"
name = "trigger"
edge = ["trigger_down", "trigger_up"]
site = 1.2

[[lines]]
type = "pulse"
name = "sof"
edge = ["cam_sof"]
site = 1.2

[[lines]]
type = "pulse"
name = "eof"
edge = ["cam_eof"]
site = 0

[[lines]]
type = "level"
name = "cam-state"
edge = ["cam_err", "cam_off", "cam_stb", "cam_idl", "cam_rdy", "cam_img"]
site = 1.2

[[lines]]
type = "level"
name = "camstop"
edge = ["camstop_bot", "camstop_top"]
site = 1.2

[[lines]]
type = "level"
name = "power"
edge = ["pm_enter", "pm_exit"]
site = 1.2

[[lines]]
type = "pulse"
name = "drv_iofcb"
edge = ["drv_iofcb"]
site = 1.2

[[lines]]
type = "pulse"
name = "drv_iorcb"
edge = ["drv_iorcb"]
site = 0

[[lines]]
type = "level"
name = "trig1-state"
edge = ["trigev_non", "trigev_rel", "trigev_pre", "trigev_bth"]
site = 1.2

[[lines]]
type = "level"
name = "domelight_r"
edge = ["domelight_r_off", "domelight_r_on"]
site = 1.2

[[lines]]
type = "level"
name = "domelight_bg"
edge = ["domelight_bg_off", "domelight_bg_on"]
site = 1.2