//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: clexer.h
// Author		: XuCF
// Created On	: 2024/11/22
// Description	: clexer.h
//
// History
// 1. V1.0, Created by XuCF. 2024/11/22
//=============================================================================

#ifndef _CLEXER_H
#define _CLEXER_H

#include "libcustom.h"

#define LEX

typedef struct clexer_struct clexer_t;

clexer_t *clexer_create(any states, any moves, any hooks);
void clexer_delete(clexer_t *thiz);

any clexer_input(clexer_t *thiz, byt *data, int len, int *use);
non clexer_reset(clexer_t *thiz);

#endif //_CLEXER_H
