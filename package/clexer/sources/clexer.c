//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: clexer.c
// Author		: XuCF
// Created On	: 2024/11/22
// Description	: clexer.c
//
// History
// 1. V1.0, Created by XuCF. 2024/11/22
//=============================================================================

#include "clexer.h"

struct clexer_struct
{
	unsigned int *states;
	unsigned int **moves;
	void **hooks;
	int state;
};

clexer_t *clexer_create(any states, any moves, any hooks)
{
	clexer_t *thiz = clike_new(clexer_t);
	thiz->states = states;
	thiz->moves = moves;
	thiz->hooks = hooks;
	thiz->state = 0;
	return thiz;
}

void clexer_delete(clexer_t *thiz)
{
	clike_free(thiz);
}

#define _action_get_typ_(a) ((a) & 0xc0000000)
#define _action_get_acc_(a) ((a) & 0x3fffffff)
#define _action_get_one_(a) ((a) & 0x000000ff)
#define _action_get_sta_(a) (((a) & 0x3fffff00) >> 8)
#define _action_get_mov_(a) (((a) & 0x3fff0000) >> 16)
#define _action_get_cnt_(a) ((a) & 0x0000ffff)

#define _entrym_get_blo_(e) ((e) & 0x000000ff)
#define _entrym_get_bhi_(e) (((e) & 0x0000ff00) >> 8)
#define _entrym_get_sta_(e) (((e) & 0xffff0000) >> 16)

#define _ACTION_TYP_MOV_	(0u << 30u)
#define _ACTION_TYP_ACC_	(1u << 30u)
#define _ACTION_TYP_ONE_	(2u << 30u)
#define _ACTION_TYP_ANY_	(3u << 30u)

any clexer_input(clexer_t *thiz, byt *data, int len, int *use)
{
	any func = 0;
	byt *ptr = data, *top = data + len;
	unsigned int action, *entrys, cnt, i, entrym;
label_loop:
	action = thiz->states[thiz->state];
	LOGD("action=%08x, state=%d", action, thiz->state);
	switch (_action_get_typ_(action))
	{
		case _ACTION_TYP_MOV_:
			if (ptr >= top) goto label_exit;
			entrys = thiz->moves[_action_get_mov_(action)];
			cnt = _action_get_cnt_(action);
			LOGD("entrys=%d,%d", _action_get_mov_(action), cnt);
			for (i = 0; i < cnt; i++)
			{
				entrym = entrys[i];
				LOGD("entrym=%08x, byte=%02x", entrym, *ptr);
				if (_entrym_get_blo_(entrym) > *ptr) continue;
				if (_entrym_get_bhi_(entrym) < *ptr) continue;
				thiz->state = _entrym_get_sta_(entrym);
				ptr++;
				goto label_loop;
			}
			thiz->state = 0;
			goto label_exit;
		case _ACTION_TYP_ACC_:
			thiz->state = 0;
			*use = ptr - data;
			func = thiz->hooks[_action_get_acc_(action)];
			goto label_exit;
		case _ACTION_TYP_ONE_:
			if (ptr >= top) goto label_exit;
			if (*ptr == _action_get_one_(action))
			{
				thiz->state = _action_get_sta_(action);
				ptr++;
			}
			else
			{
				thiz->state = 0;
				goto label_exit;
			}
			break;
		case _ACTION_TYP_ANY_: goto label_exit; break;
		default: goto label_exit;
	}
	goto label_loop;

label_exit:
	*use = ptr - data;
	return func;
}

non clexer_reset(clexer_t *thiz)
{
	thiz->state = 0;
}