#!python
import os
import re
import sys

TYPE_MOV = 0 << 30
TYPE_ACC = 1 << 30
TYPE_ONE = 2 << 30
TYPE_ANY = 3 << 30

TYPE_BIT = 3 << 30
ITEM_BIT = 0x3fffffff
BYTE_BIT = 0xff
MOVE_BIT = 0x3fffff00


def _vbyt_(byt):
    if type(byt) == int:
        out = chr(byt)
        if out.isprintable():
            return out
        else:
            return f"{byt:02x}h"
    if type(byt) == bytes:
        return byt.decode()


def _dump_fa_(fa):
    for cur, byt, nxt, oop in fa:
        act = f"G{nxt:02}" if oop < 0 else f"A{oop:02}"
        byt = _vbyt_(byt)
        print(f"{cur:02} {byt} {act}")


def _dump_tb_(tb, fp=sys.stdout):

    def _find_all_byts_():
        out = set()
        for row in tb:
            if type(row) != list:
                continue
            for byt, _ in row:
                out.add(byt)
        return sorted(list(out))

    def _check_merge_(one, two):
        for row in tb:
            if type(row) != list:
                continue
            ref, cnt = -1, 0
            for byt, nxt in row:
                if byt == one or byt == two:
                    if ref < 0:
                        ref = nxt
                    elif ref != nxt:
                        return False
                    cnt += 1
            if cnt == 1:
                return False
        return True

    obyts = _find_all_byts_()
    nbyts = [[obyts[0]]]
    for i in range(len(obyts) - 1):
        bcur, bnxt = obyts[i], obyts[i + 1]
        if bcur + 1 == bnxt and _check_merge_(bcur, bnxt):
            nbyts[-1].append(bnxt)
        else:
            nbyts.append([bnxt])

    def _write_line_(data, head):
        line = " | ".join(data)
        line = f"| {head} | {line} |\n"
        fp.write(line)

    cnt = len(nbyts) * 6 - 3

    fp.write("/" + "*" * (cnt + 8) + "\n")

    clips = []
    for bset in nbyts:
        if len(bset) == 1:
            byt = bset[0]
            clips.append(f"{_vbyt_(byt)}".center(3))
        else:
            start, stop = bset[0], bset[-1]
            clips.append(f"{_vbyt_(start)}-{_vbyt_(stop)}")
    _write_line_(clips, "  ")

    def _find_nbyts_idx_(byt):
        for i, bset in enumerate(nbyts):
            if byt in bset:
                return i

    for i, row in enumerate(tb):
        head = f"{i:02}"
        if type(row) != list:
            clip = f"A{row:02}"
            clip = clip.center(cnt, "-")
            _write_line_([clip], head)
            continue
        clips = ["   "] * len(nbyts)
        for byt, nxt in row:
            clips[_find_nbyts_idx_(byt)] = f"T{nxt:02}"
        _write_line_(clips, head)

    fp.write("*" * (cnt + 8) + "/\n")


def _output_tb_(tb, fp=sys.stdout):
    def _write_move(clips, idx):
        nclips, start, stop, ref = [], clips[0][0], clips[0][0], clips[0][1]
        for byt, nxt in clips[1::]:
            if byt == stop + 1 and nxt == ref:
                stop = byt
                continue
            nclips.append((start, stop, ref))
            start, stop, ref = byt, byt, nxt
        nclips.append((start, stop, ref))
        fp.write(f"static const unsigned int _lex_move_{idx}_[] = {{\n")
        for start, stop, nxt in nclips:
            value = start | (stop << 8) | (nxt << 16)
            if start != stop:
                fp.write(f"\t0x{value:08x}, // Pick {_vbyt_(start)}-{_vbyt_(stop)} To {nxt}\n")
            else:
                fp.write(f"\t0x{value:08x}, // Pick {_vbyt_(start)} To {nxt}\n")
        fp.write(f"}};\n")
        return len(nclips)

    states, move, total, accept = [], 0, 0, 0
    for i, row in enumerate(tb):
        if type(row) != list:
            entry = row | TYPE_ACC
            accept += 1
        elif len(row) == 1:
            byt, nxt = row[0]
            entry = byt | nxt << 8 | TYPE_ONE
        else:
            entry = _write_move(row, move)
            total += entry
            entry, move = move << 16 | entry | TYPE_MOV, move + 1
        states.append(entry)
    total += len(states)

    fp.write(f"static const unsigned int *_lex_moves_[] = {{\n")
    for i in range(move):
        fp.write(f"\t_lex_move_{i}_,\n")
    fp.write(f"}};\n")

    fp.write(f"static const unsigned int _lex_states_[] = {{\n")
    for i, entry in enumerate(states):
        typx = TYPE_BIT & entry
        if TYPE_ACC == typx:
            idx = ITEM_BIT & entry
            fp.write(f"\t0x{entry:08x}, // {i:02} ACC {idx}\n")
        elif TYPE_ONE == typx:
            byte = BYTE_BIT & entry
            stat = (MOVE_BIT & entry) >> 8
            fp.write(f"\t0x{entry:08x}, // {i:02} ONE {_vbyt_(byte)} -> {stat}\n")
        else:
            fp.write(f"\t0x{entry:08x}, // {i:02} MOV {entry & 0xffff} @ {entry >> 16}\n")
    fp.write(f"}};\n")

    fp.write(f"// Total {total} u32 + {move+accept} ptr used.\n")


def _closure_(nfa, state, rbyt):
    ostat, accs = set(), set()
    for cur, byt, nxt, acc in nfa:
        if byt == rbyt and cur in state:
            ostat.add(nxt)
            accs.add(acc)
    assert len(accs) == 1
    return tuple(ostat), accs.pop()


def _find_nxt_byts_(nfa, state):
    byts = set()
    for cur, byt, _, _ in nfa:
        if cur in state:
            byts.add(byt)
    return byts


def _build_dfa_(nfa):
    states, dfa, i = [(0, )], [], 0
    while i < len(states):
        state = states[i]
        for rbyt in _find_nxt_byts_(nfa, state):
            sure, acc = _closure_(nfa, state, rbyt)
            if sure not in states:
                states.append(sure)
            dfa.append((i, rbyt, states.index(sure), acc))
        i = i + 1
    return dfa


def _build_nfa_(rex):
    nfa = []
    nxt = 0
    for idx, item in enumerate(rex):
        cur, clip, i = 0, None, 0
        while i < len(item):
            if item[i::].startswith(b"+"):
                nfa.append([cur, clip, cur, -1])
                i = i + 1
                continue
            if item[i::].startswith(b"*"):
                cur = nfa[-1][0]
                nfa[-1][2] = nxt - 1
                i = i + 1
                continue

            if item[i::].startswith(b"\\d"):
                clip, i = b"0123456789", i+2
            elif item[i::].startswith(b"\\h"):
                clip, i = b"0123456789abcdefABCDEF", i+2
            else:
                clip, i = item[i:i+1], i + 1

            nxt += 1
            nfa.append([cur, clip, nxt, -1])
            cur = nxt

        nfa[-1][3] = idx

    tmp = []
    for cur, clip, nxt, oop in nfa:
        for byt in clip:
            tmp.append([cur, byt, nxt, oop])
    return tmp


def _build_xtb_(dfa):
    idx = 0
    for cur, _, nxt, _ in dfa:
        idx = max(cur, nxt, idx)
    xtb = [[] for _ in range(idx + 1)]
    for cur, byt, nxt, acc in dfa:
        xtb[cur].append((byt, nxt))
        if acc >= 0:
            xtb[nxt] = acc
    for row in xtb:
        if type(row) != list:
            continue
        row.sort(key=lambda x: x[0])
    return xtb


def _build_all_(rex):
    nfa = _build_nfa_(rex)
    dfa = _build_dfa_(nfa)
    xtb = _build_xtb_(dfa)
    return xtb


def _latin1_encode_(input_str):
    try:
        # Use the `encode('latin1').decode('unicode_escape')` trick to handle escape sequences
        processed_str = input_str.encode('latin1').decode('unicode_escape')
        return processed_str.encode('latin1')
    except Exception as e:
        raise ValueError(f"Failed to convert string to bytes: {e}")


def _latin1_decode_(input_bytes):
    try:
        # Use `latin1` to decode raw bytes, then encode special characters with `unicode_escape`
        return input_bytes.decode('latin1').encode('unicode_escape').decode('latin1')
    except Exception as e:
        raise ValueError(f"Failed to convert bytes to string: {e}")


_match_pt_ = re.compile(r"LEX \w+ (\w+)\([\w\*, ]+\) /\* (.+) \*/")


def _find_rex_in_c_(path):
    rules, funcs = [], []
    with open(path, "r", errors="ignore") as fp:
        for line in fp:
            mat = _match_pt_.match(line)
            if not mat:
                continue
            funcs.append(mat.group(1))
            rules.append(_latin1_encode_(mat.group(2)))
    return rules, funcs


def lextb(application):
    srcs, lex = [], None
    for item in application["SRCS"]:
        if item.endswith(".c") and os.access(item, os.R_OK):
            srcs.append(item)
        elif lex is None and item.endswith(".lex.h"):
            lex = item

    if not lex or not srcs:
        return

    rules, funcs = [], []
    for src in sorted(srcs):
        vrules, vfuncs = _find_rex_in_c_(src)
        rules.extend(vrules)
        funcs.extend(vfuncs)

    xtb = _build_all_(rules)

    with open(lex, "w") as fp:
        _dump_tb_(xtb, fp)

        for i, (item, func) in enumerate(zip(rules, funcs)):
            item = _latin1_decode_(item)
            fp.write(f"void {func}(); // {i:02} {item}\n")

        fp.write("static const void *_lex_hooks_[] = {\n")
        for i, (item, func) in enumerate(zip(rules, funcs)):
            item = _latin1_decode_(item)
            fp.write(f"\t{func}, // {i:02} {item}\n")
        fp.write("};\n")

        _output_tb_(xtb, fp)
