/*****************************************************************
|    | 0ah | 0dh | 16h |  %  | 0-9 |  D  |  E  |  M  |  O  |  T  |
| 00 |     |     | T01 |     |     |     |     |     |     |     |
| 01 |     |     |     |     |     |     |     |     |     | T02 |
| 02 |     | T03 |     |     |     |     |     |     |     |     |
| 03 |     |     |     | T04 |     |     |     |     |     |     |
| 04 |     |     |     |     |     | T05 |     |     |     |     |
| 05 |     |     |     |     |     |     | T06 |     |     |     |
| 06 |     |     |     |     |     |     |     | T07 |     |     |
| 07 |     |     |     |     |     |     |     |     | T08 |     |
| 08 |     |     |     |     | T09 |     |     |     |     |     |
| 09 | T10 |     |     |     | T09 |     |     |     |     |     |
| 10 | ---------------------------A00--------------------------- |
*****************************************************************/
void _demo_command_handle_(); // 00 \x16T\r%DEMO\\d+\n
static const void *_lex_hooks_[] = {
	_demo_command_handle_, // 00 \x16T\r%DEMO\\d+\n
};
static const unsigned int _lex_move_0_[] = {
	0x00093930, // Pick 0-9 To 9
};
static const unsigned int _lex_move_1_[] = {
	0x000a0a0a, // Pick 0ah To 10
	0x00093930, // Pick 0-9 To 9
};
static const unsigned int *_lex_moves_[] = {
	_lex_move_0_,
	_lex_move_1_,
};
static const unsigned int _lex_states_[] = {
	0x80000116, // 00 ONE 16h -> 1
	0x80000254, // 01 ONE T -> 2
	0x8000030d, // 02 ONE 0dh -> 3
	0x80000425, // 03 ONE % -> 4
	0x80000544, // 04 ONE D -> 5
	0x80000645, // 05 ONE E -> 6
	0x8000074d, // 06 ONE M -> 7
	0x8000084f, // 07 ONE O -> 8
	0x00000001, // 08 MOV 1 @ 0
	0x00010002, // 09 MOV 2 @ 1
	0x40000000, // 10 ACC 0
};
// Total 14 u32 + 3 ptr used.
