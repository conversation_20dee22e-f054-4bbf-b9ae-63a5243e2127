//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: entry.c
// Author		: XuCF
// Created On	: 2024/11/22
// Description	: entry.c
//
// History
// 1. V1.0, Created by XuCF. 2024/11/22
//=============================================================================

#include "clexer.h"
#include "demo.lex.h"

int main(int argc, const char **args)
{
	str test = "\x16\x54\x0d%DEMO123456\n";

	int ret;
	void (*func)(any, int);
	clexer_t *lexer = clexer_create((any)_lex_states_, (any)_lex_moves_, (any)_lex_hooks_);
	func = clexer_input(lexer, (byt *)test, strlen(test), &ret);
	if (func) func((any)test, strlen(test));
	clexer_delete(lexer);
	return 0;
}
