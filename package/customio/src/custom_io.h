//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: custom_io.h
// Author		: XuCF
// Created On	: 2025/02/06
// Description	: custom_io.h
//
// History
// 1. V1.0, Created by XuCF. 2025/02/06
//=============================================================================

#ifndef _CUSTOM_IO_H
#define _CUSTOM_IO_H

#include "cpropr.cfg.h"

#define CUSTOMIO_ACTION_NONE		 CUSTOMIORISINGACTION_E_None
#define CUSTOMIO_ACTION_START_DECODE CUSTOMIORISINGACTION_E_StartDecode
#define CUSTOMIO_ACTION_STOP_DECODE	 CUSTOMIORISINGACTION_E_EndDecode
#define CUSTOMIO_ACTION_TRIG_FRAMES	 CUSTOMIORISINGACTION_E_TriggerNFrame
void customio_setup(void (*input_callback)(int event));

#define CUSTOMIO_CONDIT_NONE		   CUSTOMIOACTIVECONDITION_E_None
#define CUSTOMIO_CONDIT_DECODE_SUCCESS CUSTOMIOACTIVECONDITION_E_DecodeSuccess
#define CUSTOMIO_CONDIT_NOREAD		   CUSTOMIOACTIVECONDITION_E_NoRead
#define CUSTOMIO_CONDIT_DECODE_STARTED CUSTOMIOACTIVECONDITION_E_DecodeStarted
#define CUSTOMIO_CONDIT_DECODE_STOPPED CUSTOMIOACTIVECONDITION_E_DecodeStopped
void customio_output(int event);

#endif //_CUSTOM_IO_H
