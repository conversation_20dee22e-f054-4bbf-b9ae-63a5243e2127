//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: custom_io_demo.c
// Author		: XuCF
// Created On	: 2025/02/07
// Description	: Demo for custom IO module usage
//
// History
// 1. V1.0, Created by XuCF. 2025/02/07
//=============================================================================

#include "libcustom.h"
#include "custom_io.h"

// Global variables for demo
static int g_is_decoding = 0;

// Simulate decode process
static void SimulateDecodeProcess(void)
{
	for (int i = 0; i < 20; i++)
	{
		clike_delay_ms(100);
		if (!g_is_decoding) break;
		g_is_decoding = 0;

		// Simulate random decode result
		int success = (rand() % 2);

		if (success)
		{
			clike_print("Decode success\n");
			customio_output(CUSTOMIO_CONDIT_DECODE_SUCCESS);
		}
		else
		{
			clike_print("Decode failed (NoRead)\n");
			customio_output(CUSTOMIO_CONDIT_NOREAD);
		}
	}
}

// Simulate start decode
static void SimulateStartDecode(void)
{
	if (!g_is_decoding)
	{
		g_is_decoding = 1;
		clike_print("Start decoding...\n");

		// Notify decode started
		customio_output(CUSTOMIO_CONDIT_DECODE_STARTED);

		// Start decode process
		SimulateDecodeProcess();
	}
}

// Simulate stop decode
static void SimulateStopDecode(void)
{
	if (g_is_decoding)
	{
		g_is_decoding = 0;
		clike_print("Stop decoding\n");

		// Notify decode stopped
		customio_output(CUSTOMIO_CONDIT_DECODE_STOPPED);
	}
}

// IO input event callback
static void MyCustomIO_InputCallback(int event)
{
	switch (event)
	{
		case CUSTOMIO_ACTION_START_DECODE:
			clike_print("Received start decode event\n");
			SimulateStartDecode();
			break;

		case CUSTOMIO_ACTION_STOP_DECODE:
			clike_print("Received stop decode event\n");
			SimulateStopDecode();
			break;

		case CUSTOMIO_ACTION_NONE:
			clike_print("Received but not used\n");
			break;

		default:
			clike_print("Unknown input event: %d\n", event);
			break;
	}
}

// Demo program entry
void MyCustomIODemo_Main(void)
{
	clike_print("Custom IO demo started\n");

	// Register input callback
	customio_setup(MyCustomIO_InputCallback);

	clike_print("Custom IO initialized, waiting for input events...\n");
	clike_print("Tips:\n");
	clike_print("1. Configure IO input to trigger decode start/stop\n");
	clike_print("2. Decode results will be output through IO\n");
}
