//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: clike_macro.h
// Author		: XuCF
// Created On	: 2024/03/05
// Description	: clike_macro.h
//
// History
// 1. V1.0, Created by XuCF. 2024/03/05
//=============================================================================

#ifndef _CLIKE_MACRO_H
#define _CLIKE_MACRO_H

// clike
#ifdef __cplusplus
#define Cmpt extern "C"
#else
#define Cmpt
#endif
#define cmpt Cmpt

// object
typedef struct
{
	void *(*__vtb__)(int id);
} VTable;
#define ClassDeclare(Name) typedef struct Name##Type Name
#define ClassRealize(Name) struct Name##Type
#define ClassDefined(Name)                                                                                             \
	typedef struct Name##Type Name;                                                                                    \
	struct Name##Type
#define VTableBuild(Name, _obj_) ((VTable *)(_obj_))->__vtb__ = __##Name##_VTable__
cmpt void *__DefaultImpl__(void *thiz, ...);

// assert
#define Assert(_cond_, _action_)                                                                                       \
	if (_cond_)                                                                                                        \
	{                                                                                                                  \
		LOGE("Assert Fail %s %d", __FILE__, __LINE__);                                                                 \
		_action_;                                                                                                      \
	}
#define clike_assert   Assert

// tips
#define ARRAY_CNT(_a_) (sizeof(_a_) / sizeof((_a_)[0]))
#define nop			   (void)0

// decorate
#define clike_inline   __attribute__((always_inline)) inline

// constant
#define Null		   0
#define None		   0

#endif //_CLIKE_MACRO_H