//=============================================================================
// Copyright (C), 2004-2025, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: cqueue.h
// Author		: XuCF
// Created On	: 2024/04/25
// Description	: cqueue.h
//
// History
// 1. V1.0, Created by XuCF. 2024/04/25
//=============================================================================

#include "libcustom.h"

#if defined(CQUEUE_TYPE)
#define typ CQUEUE_TYPE
#else
#define typ byt
#endif

typedef struct
{
	int volu;
	int wpos;
	int rpos;
} cqueue_t;

static inline cqueue_t *cqueue_create(int volu)
{
	cqueue_t *thiz;
	// align volu
	int n = 16, m = 16;
	while (m > 1)
	{
		// check m bits start at bit n
		if (volu & (((1 << m) - 1) << n))
		{
			// high bits is not zero
			m >>= 1, n = n + m;
		}
		else
		{
			// high bits is zero
			m >>= 1, n = n - m;
		}
	}
	volu = ((1 << n) - 1) | volu;
	// build cqueue
	thiz = clike_malloc(sizeof(typ) * (volu + 1) + sizeof(cqueue_t));
	thiz->volu = volu;
	thiz->rpos = thiz->wpos = 0;
	return thiz;
}

static inline void cqueue_delete(cqueue_t *thiz)
{
	clike_free(thiz);
}

static inline int cqueue_size(cqueue_t *thiz)
{
	return (thiz->wpos - thiz->rpos) & thiz->volu;
}

static inline int cqueue_left(cqueue_t *thiz)
{
	return (thiz->rpos - thiz->wpos - 1) & thiz->volu;
}

static inline int cqueue_push(cqueue_t *thiz, typ *buf, int num)
{
	typ *room;
	int left;
	if (cqueue_left(thiz) < num) return -1;
	room = (typ *)(thiz + 1);
	left = thiz->volu - thiz->wpos + 1;
	if (left >= num)
	{
		// copy once
		clike_copy(room + thiz->wpos, buf, sizeof(typ) * num);
	}
	else
	{
		// copy twice
		clike_copy(room + thiz->wpos, buf, sizeof(typ) * left);
		clike_copy(room, buf + left, sizeof(typ) * (num - left));
	}
	thiz->wpos = (thiz->wpos + num) & thiz->volu;
	return num;
}

static inline int cqueue_take(cqueue_t *thiz, typ *buf, int num)
{
	typ *room;
	int left;
	int size = cqueue_size(thiz);
	if (size == 0) return 0;
	if ((num < 0) || (size < num)) num = size;
	room = (typ *)(thiz + 1);
	left = thiz->volu - thiz->rpos + 1;
	if (left >= num)
	{
		// copy once
		clike_copy(buf, room + thiz->rpos, sizeof(typ) * num);
	}
	else
	{
		// copy twice
		clike_copy(buf, room + thiz->rpos, sizeof(typ) * left);
		clike_copy(buf + left, room, sizeof(typ) * (num - left));
	}
	thiz->rpos = (thiz->rpos + num) & thiz->volu;
	return num;
}

static inline int cqueue_push_one(cqueue_t *thiz, typ one)
{
	if (cqueue_left(thiz) < 1) return -1;
	((typ *)(thiz + 1))[thiz->wpos] = one;
	thiz->wpos = (thiz->wpos + 1) & thiz->volu;
	return 0;
}

static inline int cqueue_take_one(cqueue_t *thiz, typ *pt)
{
	if (cqueue_size(thiz) == 0) return 0;
	*pt = ((typ *)(thiz + 1))[thiz->rpos];
	thiz->rpos = (thiz->rpos + 1) & thiz->volu;
	return 1;
}

static inline int cqueue_drop(cqueue_t *thiz, int num)
{
	int size = cqueue_size(thiz);
	if (size == 0) return 0;
	if ((num < 0) || (size < num)) num = size;
	thiz->rpos = (thiz->rpos + num) & thiz->volu;
	return num;
}

static inline int cqueue_drop_all(cqueue_t *thiz)
{
	thiz->rpos = thiz->wpos = 0;
	return 0;
}
