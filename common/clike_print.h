//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: clike_print.h
// Author		: XuCF
// Created On	: 2024/03/05
// Description	: clike_print.h
//
// History
// 1. V1.0, Created by XuCF. 2024/03/05
//=============================================================================

#ifndef _CLIKE_PRINT_H
#define _CLIKE_PRINT_H

#if (ULOGL > 4)
#define LOGV(fmt, arg...) clike_print("[V][%s] " fmt "\n", __FUNCTION__, ##arg)
#else
#define LOGV(...) (void)0
#endif

#if (ULOGL > 3)
#define LOGD(fmt, arg...) clike_print("[D][%s] " fmt "\n", __FUNCTION__, ##arg)
#else
#define LOGD(...) (void)0
#endif

#if (ULOGL > 2)
#define LOGI(fmt, arg...) clike_print("[I][%s] " fmt "\n", __FUNCTION__, ##arg)
#else
#define LOGI(...) (void)0
#endif

#if (ULOGL > 1)
#define LOGW(fmt, arg...) clike_print("[W][%s] " fmt "\n", __FUNCTION__, ##arg)
#else
#define LOGW(...) (void)0
#endif

#if (ULOGL > 0)
#define LOGE(fmt, arg...) clike_print("[E][%s] " fmt "\n", __FUNCTION__, ##arg)
#else
#define LOGE(...) (void)0
#endif

#endif //_CLIKE_PRINT_H