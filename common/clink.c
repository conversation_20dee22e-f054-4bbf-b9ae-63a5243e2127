//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: clist.c
// Author		: XuCF
// Created On	: 2023/12/08
// Description	: clist.c
//
// History
// 1. V1.0, Created by XuCF. 2023/12/08
//=============================================================================

#include "clink.h"

non slink_attach_next(slink_t *thiz, slink_t *node)
{
	node->next = thiz->next;
	thiz->next = node;
}

slink_t *slink_detach_next(slink_t *thiz)
{
	slink_t *next = thiz->next;
	thiz->next = next->next;
	return next;
}

non dlink_attach_prev(dlink_t *thiz, dlink_t *node)
{
	node->next = thiz;
	node->prev = thiz->prev;
	thiz->prev->next = node;
	thiz->prev = node;
}

non dlink_attach_next(dlink_t *thiz, dlink_t *node)
{
	node->next = thiz->next;
	node->prev = thiz;
	node->next->prev = node;
	thiz->next = node;
}

dlink_t *dlink_detach_next(dlink_t *thiz)
{
	dlink_t *next = thiz->next;
	thiz->next = next->next;
	thiz->next->prev = thiz;
	return next;
}

dlink_t *dlink_detach_prev(dlink_t *thiz)
{
	dlink_t *prev = thiz->prev;
	thiz->prev = prev->prev;
	thiz->prev->next = thiz;
	return prev;
}

dlink_t *dlink_detach_curr(dlink_t *thiz)
{
	thiz->next->prev = thiz->prev;
	thiz->prev->next = thiz->next;
	return thiz;
}
