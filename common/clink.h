//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: clink.h
// Author		: XuCF
// Created On	: 2023/12/08
// Description	: clink.h
//
// History
// 1. V1.0, Created by XuCF. 2023/12/08
//=============================================================================

#ifndef _CLINK_H
#define _CLINK_H

#include "libcustom.h"

typedef struct _slink_node_ slink_t;
struct _slink_node_
{
	slink_t *next;
};

typedef struct _dlink_node_ dlink_t;
struct _dlink_node_
{
	dlink_t *next;
	dlink_t *prev;
};

non slink_attach_next(slink_t *thiz, slink_t *node);
non dlink_attach_next(dlink_t *thiz, dlink_t *node);
non dlink_attach_prev(dlink_t *thiz, dlink_t *node);

slink_t *slink_detach_next(slink_t *thiz);
dlink_t *dlink_detach_next(dlink_t *thiz);
dlink_t *dlink_detach_prev(dlink_t *thiz);
dlink_t *dlink_detach_curr(dlink_t *thiz);

#define clink_next(l) (((slink_t *)(l))->next)
#define clink_prev(l) (((dlink_t *)(l))->prev)

clike_inline non slink_reset(slink_t *thiz)
{
	thiz->next = thiz;
}

clike_inline non dlink_reset(dlink_t *thiz)
{
	thiz->next = thiz->prev = thiz;
}

#define clink_empty(l) (clink_next(l) == (slink_t *)(l))

#define slink_pop(l, a...)                                                                                             \
	({                                                                                                                 \
		any _p;                                                                                                        \
		if (clink_empty(l))                                                                                            \
		{                                                                                                              \
			a;                                                                                                         \
			_p = 0;                                                                                                    \
		}                                                                                                              \
		else                                                                                                           \
			_p = slink_detach_next((slink_t *)(l));                                                                    \
		_p;                                                                                                            \
	})

#define slink_add slink_attach_next
#define slink_put slink_attach_next

clike_inline non slink_link_spc(slink_t *thiz, any mem, int size, int num)
{
	int i;
	slink_reset(thiz);
	for (i = 0; i < num; i++)
	{
		slink_add(thiz, mem);
		mem += size;
	}
}
#define slink_link(l, m, t, n) slink_link_spc(l, m, sizeof(t), n)

#define slink_new(b, t, n)                                                                                             \
	({                                                                                                                 \
		any _p = clike_malloc(sizeof(b) + sizeof(t) * (n));                                                            \
		slink_link(_p, _p + sizeof(b), t, n);                                                                          \
		_p;                                                                                                            \
	})

#define clink_nfor(l, n) for ((n) = (any)clink_next(l); (n) != (any)(l); (n) = (any)clink_next(n))
#define clink_pfor(l, n) for ((n) = (any)clink_prev(l); (n) != (any)(l); (n) = (any)clink_prev(n))
#define slink_iter(l, p, n)                                                                                            \
	for ((p) = (any)(l), (n) = (any)clink_next(p); (n) != (any)(l); (p) = (n), (n) = (any)clink_next(p))

#define clink_nget(l, n, c, a)                                                                                         \
	do {                                                                                                               \
		(n) = (any)clink_next(l);                                                                                      \
		while (1)                                                                                                      \
		{                                                                                                              \
			if ((n) == (any)(l))                                                                                       \
			{                                                                                                          \
				a;                                                                                                     \
				break;                                                                                                 \
			}                                                                                                          \
			if (c) break;                                                                                              \
			(n) = (any)clink_next(n);                                                                                  \
		}                                                                                                              \
	} while (0)

#define clink_pget(l, n, c, a)                                                                                         \
	do {                                                                                                               \
		(n) = (any)clink_prev(l);                                                                                      \
		while (1)                                                                                                      \
		{                                                                                                              \
			if ((n) == (any)(l))                                                                                       \
			{                                                                                                          \
				a;                                                                                                     \
				break;                                                                                                 \
			}                                                                                                          \
			if (c) break;                                                                                              \
			(n) = (any)clink_prev(n);                                                                                  \
		}                                                                                                              \
	} while (0)

#define slink_drop(l, n, c, a)                                                                                         \
	do {                                                                                                               \
		slink_t *_p = (any)(l);                                                                                        \
		(n) = (any)clink_next(_p);                                                                                     \
		while (1)                                                                                                      \
		{                                                                                                              \
			if ((n) == (any)(l))                                                                                       \
			{                                                                                                          \
				a;                                                                                                     \
				break;                                                                                                 \
			}                                                                                                          \
			if (c)                                                                                                     \
			{                                                                                                          \
				slink_detach_next(_p);                                                                                 \
				break;                                                                                                 \
			}                                                                                                          \
			_p = (any)(n), (n) = (any)clink_next(_p);                                                                  \
		}                                                                                                              \
	} while (0)
#endif //_CLINK_H