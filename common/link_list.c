//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: link_list.c
// Author		: XuCF
// Created On	: 2022/11/18
// Description	: link_list.c
//
// History
// 1. V1.0, Created by XuCF. 2022/11/18
//=============================================================================

#include "link_list.h"
#include <malloc.h>

#define link_list_assert(cond, act)                                                                                    \
	if (cond) { act; }

static link_list_node_t *link_list_find_node_by_obj(link_list_t *thiz, void *obj)
{
	link_list_assert(obj == 0, goto label_exit);
	for (link_list_node_t *node = thiz->room->next; node != thiz->room; node = node->next)
	{
		if (node->obj == obj) { return node; }
	}
label_exit:
	return 0;
}

static link_list_node_t *link_list_find_node_by_pos(link_list_t *thiz, int pos)
{
	link_list_node_t *node = 0;
	// check
	link_list_assert(pos >= thiz->size, goto label_exit);
	link_list_assert(pos < -thiz->size, goto label_exit);
	// find
	node = thiz->room;
	if (pos >= 0)
	{
		for (int i = 0; i < pos; i++) { node = node->next; }
	}
	else
	{
		for (int i = 0; i > pos; i--) { node = node->prev; }
	}
label_exit:
	return node;
}

static void link_list_del_node(link_list_t *thiz, link_list_node_t *node)
{
	// del
	node->prev->next = node->next;
	node->next->prev = node->prev;
	node->next = node->prev = 0;
	node->obj = 0;
	thiz->size--;
}

int link_list_init(link_list_t *thiz, int volu)
{
	thiz->volu = volu + 1;
	thiz->size = 1;
	thiz->room = (link_list_node_t *)malloc(thiz->volu * sizeof(link_list_node_t));
	thiz->room->next = thiz->room->prev = thiz->room;
	thiz->room->obj = (void *)0xdeadbeef;
	for (int i = 1; i < thiz->volu; i++) { thiz->room[i].obj = 0; }
	return 0;
}

int link_list_free(link_list_t *thiz)
{
	free(thiz->room);
	return 0;
}

int link_list_ins(link_list_t *thiz, void *obj, int idx)
{
	int ret = -1;

	// check
	link_list_assert(obj == 0, goto label_exit);
	link_list_assert(thiz->size >= thiz->volu, goto label_exit);

	// get tail
	link_list_node_t *tail = link_list_find_node_by_pos(thiz, idx);
	link_list_assert(tail == 0, goto label_exit);

	// take idle node
	link_list_node_t *node = 0;
	for (int i = 1; i < thiz->volu; i++)
	{
		if (thiz->room[i].obj == 0)
		{
			node = &thiz->room[i];
			break;
		}
	}
	link_list_assert(node == 0, ret = -1; goto label_exit);

	// add to tail
	node->prev = tail;
	node->next = tail->next;
	tail->next->prev = node;
	tail->next = node;
	node->obj = obj;
	thiz->size++;

label_exit:
	return ret;
}

int link_list_add(link_list_t *thiz, void *obj)
{
	return link_list_ins(thiz, obj, -1);
}

int link_list_del(link_list_t *thiz, void *obj)
{
	int ret = -1;
	// get
	link_list_node_t *node = link_list_find_node_by_obj(thiz, obj);
	link_list_assert(node == 0, goto label_exit);
	// del
	link_list_del_node(thiz, node);
	ret = 0;
label_exit:
	return ret;
}

void *link_list_get(link_list_t *thiz, int idx)
{
	void *obj = 0;
	// get node
	link_list_node_t *node = link_list_find_node_by_pos(thiz, idx + 1);
	link_list_assert(node == 0, goto label_exit);
	// get obj
	obj = node->obj;
label_exit:
	return obj;
}

void *link_list_pop(link_list_t *thiz, int idx)
{
	void *obj = 0;
	// get node
	link_list_node_t *node = link_list_find_node_by_pos(thiz, idx + 1);
	link_list_assert(node == 0, goto label_exit);
	// get obj
	obj = node->obj;
	// del
	link_list_del_node(thiz, node);
label_exit:
	return obj;
}

int link_list_count(link_list_t *thiz)
{
	return thiz->size - 1;
}

link_list_node_t *link_list_head_node(link_list_t *thiz)
{
	return thiz->room->next;
}
link_list_node_t *link_list_tail_node(link_list_t *thiz)
{
	return thiz->room->prev;
}
link_list_node_t *link_list_anch_node(link_list_t *thiz)
{
	return thiz->room;
}