//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: clike_basic.h
// Author		: XuCF
// Created On	: 2024/03/05
// Description	: clike_basic.h
//
// History
// 1. V1.0, Created by XuCF. 2024/03/05
//=============================================================================

#ifndef _CLIKE_BASIC_H
#define _CLIKE_BASIC_H

#include <inttypes.h>

typedef void *any;				// point for anything
typedef const char *str;		// string
typedef void non;				// none
typedef char chr;				// character
typedef char bin;				// binary
typedef unsigned char byt;		// byte
typedef short s16;				// signed 16bit number
typedef unsigned short u16;		// unsigned 16bit number
typedef int s32;				// signed 32bit number
typedef unsigned int u32;		// unsigned 32bit number
typedef long long s64;			// signed 64bit number
typedef unsigned long long u64; // unsigned 64bit number
typedef intptr_t snv;			// signed native
typedef uintptr_t unv;			// unsigned native
typedef float f32;				// float
typedef double f64;				// double

int clike_print_imp(const char *fmt, ...);
int clike_dump_hex(void *data, int size);
int clike_dump_chr(void *data, int size);
int clike_pbar(int total, int curr, const char *head, ...);
int clike_xbytes_enc(char *xbytes, int limit, const char *cbytes, int size);
int clike_xbytes_dec(char *cbytes, int limit, const char *xbytes, int size);
int clike_pbytes_enc(char *pbytes, int limit, const char *cbytes, int size);
int clike_pbytes_dec(char *cbytes, int limit, const char *pbytes, int size);
int clike_ubytes_enc(char *ubytes, int limit, const char *cbytes, int size);
int clike_ubytes_dec(char *cbytes, int limit, const char *ubytes, int size);

unsigned int clike_time_us(void);
unsigned long long clike_time_us_full(void);
unsigned int clike_time_ms(void);
unsigned long long clike_time_ms_full(void);
unsigned int clike_tick(void);
unsigned long long clike_tick_full(void);
unsigned int clike_delay_us(unsigned int us);
unsigned int clike_delay_ms(unsigned int ms);

void *clike_malloc(unsigned long size);
void clike_free(void *mem);

void clike_copy(void *dst, const void *src, unsigned long size);
void clike_copy_in(void *dst, const void *src, unsigned long size);
void clike_copy_out(void *dst, const void *src, unsigned long size);

void clike_memset(void *dst, int val, unsigned long size);

long clike_disable_int(void);
void clike_enable_int(long cpsr);

#define clike_new(_typ_) (_typ_ *)clike_malloc(sizeof(_typ_))

#endif //_CLIKE_BASIC_H
