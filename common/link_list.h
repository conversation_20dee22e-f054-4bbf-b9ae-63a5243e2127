//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: link_list.h
// Author		: XuCF
// Created On	: 2022/11/18
// Description	: link_list.h
//
// History
// 1. V1.0, Created by XuCF. 2022/11/18
//=============================================================================

#ifndef _LINK_LIST_H
#define _LINK_LIST_H

struct link_list_node_struct;
typedef struct link_list_node_struct link_list_node_t;
struct link_list_struct;
typedef struct link_list_struct link_list_t;

struct link_list_node_struct
{
	void *obj;
	link_list_node_t *prev;
	link_list_node_t *next;
};

struct link_list_struct
{
	link_list_node_t *room;
	int volu;
	int size;
};

int link_list_init(link_list_t *thiz, int volu);
int link_list_free(link_list_t *thiz);

int link_list_ins(link_list_t *thiz, void *obj, int pos);
int link_list_add(link_list_t *thiz, void *obj);
int link_list_del(link_list_t *thiz, void *obj);
void *link_list_get(link_list_t *thiz, int idx);
void *link_list_pop(link_list_t *thiz, int idx);

int link_list_count(link_list_t *thiz);

link_list_node_t *link_list_head_node(link_list_t *thiz);
link_list_node_t *link_list_tail_node(link_list_t *thiz);
link_list_node_t *link_list_anch_node(link_list_t *thiz);

#define LinkListForNext(type, name, list)                                                                              \
	for (type *node = (type *)((list)->room->next), *name = ((link_list_node_t *)node)->obj;                           \
		 (list)->room != ((link_list_node_t *)node);                                                                   \
		 node = (type *)(((link_list_node_t *)node)->next), name = ((link_list_node_t *)node)->obj)

#endif //_LINK_LIST_H