//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: clike_basic.c
// Author		: XuCF
// Created On	: 2024/08/27
// Description	: clike_basic.c
//
// History
// 1. V1.0, Created by XuCF. 2024/08/27
//=============================================================================

#define _CRT_SECURE_NO_WARNINGS
#include "libcustom.h"

int clike_dump_hex(void *data, int size)
{
	const unsigned char colum = 15;
	unsigned char *bytes = data;
	clike_print("--hex---:");
	for (int i = 0; i <= colum; i++)
	{
		// print head
		clike_print(" %02x", i);
	}
	for (int i = 0; i < size; i++)
	{
		if ((colum & i) == 0)
		{
			clike_print("\n%08x: %02x", i, bytes[i]);
			continue;
		}
		clike_print(" %02x", bytes[i]);
	}
	clike_print("\n");
	return 0;
}

int clike_dump_chr(void *data, int size)
{
	unsigned char *bytes = data;
	for (int i = 0; i < size; i++)
	{
		unsigned char ch = bytes[i];
		// process special escape
		switch (ch)
		{
			case '\a': clike_print("\\a"); continue;
			case '\b': clike_print("\\b"); continue;
			case '\t': clike_print("\\t"); continue;
			case '\n': clike_print("\\n"); continue;
			case '\v': clike_print("\\v"); continue;
			case '\f': clike_print("\\f"); continue;
			case '\r': clike_print("\\r"); continue;
			case '\\': clike_print("\\\\"); continue;
			case '\"': clike_print("\\\""); continue;
			default: break;
		}
		// process ascii
		if (ch >= 0x20 && ch < 0x7F)
		{
			clike_print("%c", ch);
			continue;
		}
		// process other
		clike_print("\\x%02x", ch);
	}
	return 0;
}

int clike_pbar(int total, int curr, const char *head, ...)
{
	const int line_width = 60; // Total progress bar width
	char line_buf[128] = {0};  // Line buffer
	int pos = 0;

	// Process variable arguments for header
	va_list args;
	va_start(args, head);
	pos += vsnprintf(line_buf + pos, sizeof(line_buf) - pos, head, args);
	va_end(args);

	// Add separator
	pos += snprintf(line_buf + pos, sizeof(line_buf) - pos, ": [");

	// Calculate progress bar length
	int bar_width = line_width - pos - 8; // Subtract header, separator and percentage length
	int filled = bar_width * curr / total;

	// Add progress bar
	char *bar_start = line_buf + pos;
	clike_memset(bar_start, '=', filled);
	clike_memset(bar_start + filled, ' ', bar_width - filled);
	pos += bar_width;

	// Add percentage or Done
	if (curr >= total) // Done
		pos += snprintf(line_buf + pos, sizeof(line_buf) - pos, "] 100%%\n");
	else // Percentage
		pos += snprintf(line_buf + pos, sizeof(line_buf) - pos, "] %3d%%\r", (int)((float)curr * 100 / total));

	// Output entire line at once
	fwrite(line_buf, 1, pos, stdout);
	fflush(stdout);
	return 0;
}

int clike_xbytes_enc(char *xbytes, int limit, const char *cbytes, int size)
{
	if (!xbytes || !cbytes || limit <= 0 || size < 0) return -1;
	int count = 0;
	for (int i = 0; i < size && count < limit; i++)
	{
		unsigned char ch = (unsigned char)cbytes[i];
		// process special escape
		switch (ch)
		{
			case '\a': count += snprintf(xbytes + count, limit - count, "\\a"); continue;
			case '\b': count += snprintf(xbytes + count, limit - count, "\\b"); continue;
			case '\t': count += snprintf(xbytes + count, limit - count, "\\t"); continue;
			case '\n': count += snprintf(xbytes + count, limit - count, "\\n"); continue;
			case '\v': count += snprintf(xbytes + count, limit - count, "\\v"); continue;
			case '\f': count += snprintf(xbytes + count, limit - count, "\\f"); continue;
			case '\r': count += snprintf(xbytes + count, limit - count, "\\r"); continue;
			case '\\': count += snprintf(xbytes + count, limit - count, "\\\\"); continue;
			case '\"': count += snprintf(xbytes + count, limit - count, "\\\""); continue;
			default: break;
		}
		// process ascii
		if (ch >= 0x20 && ch < 0x7F)
		{
			xbytes[count++] = ch;
			continue;
		}
		// process other
		if (count + 4 > limit) break;
		count += snprintf(xbytes + count, limit - count, "\\x%02x", ch);
	}
	if (count < limit) xbytes[count] = '\0';
	return count;
}

int clike_xbytes_dec(char *cbytes, int limit, const char *xbytes, int size)
{
	if (!cbytes || !xbytes || limit <= 0 || size < 0) return -1;
	int count = 0;
	for (int i = 0; i < size && count < limit; i++)
	{
		if (xbytes[i] != '\\' || i + 1 >= size)
		{
			cbytes[count++] = xbytes[i];
			continue;
		}
		// process escape
		i++; // skip backslash
		if (i >= size) break;
		switch (xbytes[i])
		{
			case 'a': cbytes[count++] = '\a'; break;
			case 'b': cbytes[count++] = '\b'; break;
			case 't': cbytes[count++] = '\t'; break;
			case 'n': cbytes[count++] = '\n'; break;
			case 'v': cbytes[count++] = '\v'; break;
			case 'f': cbytes[count++] = '\f'; break;
			case 'r': cbytes[count++] = '\r'; break;
			case 'x':
				if (i + 2 < size)
				{
					char hex[3] = {xbytes[i + 1], xbytes[i + 2], 0};
					unsigned int value;
					if (sscanf(hex, "%x", &value) == 1)
					{
						cbytes[count++] = (char)value;
						i += 2; // skip hex
					}
					else { cbytes[count++] = 'x'; }
				}
				else { cbytes[count++] = 'x'; }
				break;
			default: cbytes[count++] = xbytes[i]; break;
		}
	}
	if (count < limit) cbytes[count] = '\0';
	return count;
}

int clike_pbytes_enc(char *pbytes, int limit, const char *cbytes, int size)
{
	if (!pbytes || !cbytes || limit <= 0 || size < 0) return -1;
	int count = 0;
	for (int i = 0; i < size && count < limit; i++)
	{
		unsigned char ch = (unsigned char)cbytes[i];
		// process special escape
		switch (ch)
		{
			case '\a': count += snprintf(pbytes + count, limit - count, "\\a"); continue;
			case '\b': count += snprintf(pbytes + count, limit - count, "\\b"); continue;
			case '\t': count += snprintf(pbytes + count, limit - count, "\\t"); continue;
			case '\n': count += snprintf(pbytes + count, limit - count, "\\n"); continue;
			case '\v': count += snprintf(pbytes + count, limit - count, "\\v"); continue;
			case '\f': count += snprintf(pbytes + count, limit - count, "\\f"); continue;
			case '\r': count += snprintf(pbytes + count, limit - count, "\\r"); continue;
			case '\\': count += snprintf(pbytes + count, limit - count, "\\\\"); continue;
			case '\"': count += snprintf(pbytes + count, limit - count, "\\\""); continue;
			case '%': count += snprintf(pbytes + count, limit - count, "%%%%"); continue;
			default: break;
		}
		// process ascii
		if (ch >= 0x20 && ch < 0x7F)
		{
			pbytes[count++] = ch;
			continue;
		}
		// process other
		if (count + 4 > limit) break;
		count += snprintf(pbytes + count, limit - count, "%%%02x", ch);
	}
	if (count < limit) pbytes[count] = '\0';
	return count;
}

int clike_pbytes_dec(char *cbytes, int limit, const char *pbytes, int size)
{
	if (!cbytes || !pbytes || limit <= 0 || size < 0) return -1;
	int count = 0;
	for (int i = 0; i < size && count < limit; i++)
	{
		if (pbytes[i] == '%' && i + 1 < size)
		{
			if (pbytes[i + 1] == '%')
			{
				cbytes[count++] = '%';
				i++; // skip second %
				continue;
			}
			if (i + 2 < size)
			{
				char hex[3] = {pbytes[i + 1], pbytes[i + 2], 0};
				unsigned int value;
				if (sscanf(hex, "%x", &value) == 1)
				{
					cbytes[count++] = (char)value;
					i += 2; // skip hex
				}
				else { cbytes[count++] = pbytes[i]; }
			}
			else { cbytes[count++] = pbytes[i]; }
		}
		else if (pbytes[i] == '\\' && i + 1 < size)
		{
			i++; // skip backslash
			if (i >= size) break;
			switch (pbytes[i])
			{
				case 'a': cbytes[count++] = '\a'; break;
				case 'b': cbytes[count++] = '\b'; break;
				case 't': cbytes[count++] = '\t'; break;
				case 'n': cbytes[count++] = '\n'; break;
				case 'v': cbytes[count++] = '\v'; break;
				case 'f': cbytes[count++] = '\f'; break;
				case 'r': cbytes[count++] = '\r'; break;
				case '\\': cbytes[count++] = '\\'; break;
				default: cbytes[count++] = pbytes[i]; break;
			}
		}
		else { cbytes[count++] = pbytes[i]; }
	}
	if (count < limit) cbytes[count] = '\0';
	return count;
}

int clike_ubytes_enc(char *ubytes, int limit, const char *cbytes, int size)
{
	if (!ubytes || !cbytes || limit <= 0 || size < 0) return -1;
	int count = 0;
	for (int i = 0; i < size && count < limit; i++)
	{
		unsigned char ch = (unsigned char)cbytes[i];
		// process special escape
		switch (ch)
		{
			case '\a': count += snprintf(ubytes + count, limit - count, "\\a"); continue;
			case '\b': count += snprintf(ubytes + count, limit - count, "\\b"); continue;
			case '\t': count += snprintf(ubytes + count, limit - count, "\\t"); continue;
			case '\n': count += snprintf(ubytes + count, limit - count, "\\n"); continue;
			case '\v': count += snprintf(ubytes + count, limit - count, "\\v"); continue;
			case '\f': count += snprintf(ubytes + count, limit - count, "\\f"); continue;
			case '\r': count += snprintf(ubytes + count, limit - count, "\\r"); continue;
			case '\\': count += snprintf(ubytes + count, limit - count, "\\\\"); continue;
			case '\"': count += snprintf(ubytes + count, limit - count, "\\\""); continue;
			default: break;
		}
		// process ascii
		if (ch >= 0x20 && ch < 0x7F)
		{
			ubytes[count++] = ch;
			continue;
		}
		// process other
		if (count + 6 > limit) break;
		count += snprintf(ubytes + count, limit - count, "\\u%04x", ch);
	}
	// end symbol
	if (count < limit) ubytes[count] = '\0';
	return count;
}

int clike_ubytes_dec(char *cbytes, int limit, const char *ubytes, int size)
{
	if (!cbytes || !ubytes || limit <= 0 || size < 0) return -1;
	int count = 0;
	for (int i = 0; i < size && count < limit; i++)
	{
		if (ubytes[i] != '\\' || i + 1 >= size)
		{
			cbytes[count++] = ubytes[i];
			continue;
		}
		// process escape
		i++; // skip backslash
		if (i >= size) break;
		switch (ubytes[i])
		{
			case 'a': cbytes[count++] = '\a'; break;
			case 'b': cbytes[count++] = '\b'; break;
			case 't': cbytes[count++] = '\t'; break;
			case 'n': cbytes[count++] = '\n'; break;
			case 'v': cbytes[count++] = '\v'; break;
			case 'f': cbytes[count++] = '\f'; break;
			case 'r': cbytes[count++] = '\r'; break;
			case 'u':
				if (i + 4 < size)
				{
					char hex[5] = {ubytes[i + 1], ubytes[i + 2], ubytes[i + 3], ubytes[i + 4], 0};
					unsigned int value;
					if (sscanf(hex, "%04x", &value) == 1)
					{
						cbytes[count++] = (char)value;
						i += 4; // skip hex
					}
					else { cbytes[count++] = 'u'; }
				}
				else { cbytes[count++] = 'u'; }
				break;
			default: cbytes[count++] = ubytes[i]; break;
		}
	}
	if (count < limit) cbytes[count] = '\0';
	return count;
}
