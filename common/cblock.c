//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: cblock.c
// Author		: XuCF
// Created On	: 2024/12/19
// Description	: Memory pool with fixed block size and dynamic expansion
//
// History
// 1. V1.0, Created by XuCF. 2024/12/19
//=============================================================================

#include "cblock.h"
#include "clink.h"

#define _ALIGN_SIZE_ 8

// Internal chunk structure
typedef struct
{
	slink_t node;	 // Link node for chunk list
	int block_count; // Number of blocks in this chunk
					 // Memory blocks follow immediately after this header
} _chunk_header_t;

// Memory pool structure
typedef struct cblock_t
{
	slink_t free_list;	   // Free block list head
	slink_t chunk_list;	   // Chunk list head for cleanup
	int block_size;		   // Size of each block (aligned)
	int chunk_capacity;	   // Blocks per chunk (doubles each expansion)
	int total_blocks;	   // Total allocated blocks
	int free_blocks;	   // Current free blocks
	int chunks_count;	   // Number of chunks allocated
	int get_count;		   // Total get operations
	int put_count;		   // Total put operations
	cblock_init_func_t fi; // Block initialization function
	cblock_free_func_t ff; // Block cleanup function
} cblock_t;

// Align size to 8 bytes boundary
static int _align_size_(int size)
{
	return (size + _ALIGN_SIZE_ - 1) & ~(_ALIGN_SIZE_ - 1);
}

// Allocate new chunk and add blocks to free list
static int _expand_pool_(cblock_t *thiz)
{
	int chunk_size = sizeof(_chunk_header_t) + thiz->block_size * thiz->chunk_capacity;
	_chunk_header_t *chunk = (_chunk_header_t *)clike_malloc(chunk_size);
	clike_assert(!chunk, return -1);
	clike_memset(chunk, 0, chunk_size);

	// Initialize chunk header
	chunk->block_count = thiz->chunk_capacity;

	// Add chunk to chunk list
	slink_attach_next(&thiz->chunk_list, &chunk->node);

	// Add all blocks in this chunk to free list
	char *block_ptr = (char *)(chunk + 1);
	for (int i = 0; i < thiz->chunk_capacity; i++)
	{
		// Call initialization function if provided
		if (thiz->fi) { thiz->fi((any)block_ptr); }
		slink_attach_next(&thiz->free_list, (slink_t *)block_ptr);
		block_ptr += thiz->block_size;
	}

	// Update statistics
	thiz->total_blocks += thiz->chunk_capacity;
	thiz->free_blocks += thiz->chunk_capacity;
	thiz->chunks_count++;

	// Double capacity for next expansion
	thiz->chunk_capacity *= 2;

	return 0;
}

cblock_t *cblock_new_(int block_size, int init_capacity, cblock_init_func_t fi, cblock_free_func_t ff)
{
	clike_assert(0 >= block_size, return NULL);
	clike_assert(0 >= init_capacity, return NULL);

	cblock_t *thiz = clike_malloc(sizeof(cblock_t));
	clike_assert(!thiz, return NULL);

	// Initialize pool structure
	slink_reset(&thiz->free_list);
	slink_reset(&thiz->chunk_list);
	thiz->block_size = _align_size_(block_size);
	thiz->chunk_capacity = init_capacity;
	thiz->total_blocks = 0;
	thiz->free_blocks = 0;
	thiz->chunks_count = 0;
	thiz->get_count = 0;
	thiz->put_count = 0;
	thiz->fi = fi;
	thiz->ff = ff;

	// Allocate initial chunk
	clike_assert(0 > _expand_pool_(thiz), goto cleanup);

	return thiz;

cleanup:
	clike_free(thiz);
	return NULL;
}

non cblock_del(cblock_t *thiz)
{
	clike_assert(!thiz, return);

	// Call cleanup function for all blocks if provided (only when destroying pool)
	if (thiz->ff)
	{
		slink_t *chunk_node;
		clink_nfor(&thiz->chunk_list, chunk_node)
		{
			_chunk_header_t *chunk = (_chunk_header_t *)chunk_node;
			char *block_ptr = (char *)(chunk + 1);

			// Call cleanup function for each block in this chunk
			for (int i = 0; i < chunk->block_count; i++)
			{
				thiz->ff((any)block_ptr);
				block_ptr += thiz->block_size;
			}
		}
	}

	// Free all chunks
	slink_t *chunk_node;
	while (!clink_empty(&thiz->chunk_list))
	{
		chunk_node = slink_detach_next(&thiz->chunk_list);
		_chunk_header_t *chunk = (_chunk_header_t *)chunk_node;
		clike_free(chunk);
	}

	clike_free(thiz);
}

any cblock_get(cblock_t *thiz)
{
	clike_assert(!thiz, return NULL);

	// Expand pool if no free blocks available
	if (clink_empty(&thiz->free_list)) { clike_assert(0 > _expand_pool_(thiz), return NULL); }

	// Get block from free list
	slink_t *block = slink_detach_next(&thiz->free_list);

	// Update statistics
	thiz->free_blocks--;
	thiz->get_count++;

	return (any)block;
}

non cblock_put(cblock_t *thiz, any block)
{
	clike_assert(!thiz, return);
	clike_assert(!block, return);

	// Add block back to free list
	slink_attach_next(&thiz->free_list, (slink_t *)block);

	// Update statistics
	thiz->free_blocks++;
	thiz->put_count++;
}

non cblock_dump_ex(cblock_t *thiz, cblock_dump_func_t df)
{
	clike_assert(!thiz, return);
	clike_assert(!df, return);
	df("=== Memory Pool Statistics ===\n");
	df("Block size: %d bytes (aligned)\n", thiz->block_size);
	df("Total blocks: %d\n", thiz->total_blocks);
	df("Free blocks: %d\n", thiz->free_blocks);
	df("Used blocks: %d\n", thiz->total_blocks - thiz->free_blocks);
	df("Chunks allocated: %d\n", thiz->chunks_count);
	df("Next chunk capacity: %d\n", thiz->chunk_capacity);
	df("Get operations: %d\n", thiz->get_count);
	df("Put operations: %d\n", thiz->put_count);
	df("Init/Free functions: %s/%s\n", thiz->fi ? "Yes" : "No", thiz->ff ? "Yes" : "No");
	df("Memory usage: %zu bytes\n",
		thiz->chunks_count * sizeof(_chunk_header_t) + thiz->total_blocks * thiz->block_size + sizeof(cblock_t));
	df("===============================\n");
}

non cblock_dump(cblock_t *thiz)
{
	clike_assert(!thiz, return);
	cblock_dump_ex(thiz, (cblock_dump_func_t)clike_print);
}
