//=============================================================================
// Copyright (C), 2004-2020, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: object.c
// Author		: XuCF
// Created On	: 2020/11/12
// Description	: object.c
//
// History
// 1. V1.0, Created by XuCF. 2020/11/12
//=============================================================================

#include "object.h"

s32 Super_Construct(Super *thiz, Operate operate)
{
	thiz->operate = operate;
	return 0;
}

s32 Super_Operate(Super *thiz, u32 what, void *param1, u32 param2)
{
	return thiz->operate(thiz, what, param1, param2);
}

void *__DefaultImpl__(void *thiz, ...)
{
	LOGW("%p", thiz);
	return 0;
}
