//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: cblock.h
// Author		: XuCF
// Created On	: 2024/12/19
// Description	: Memory pool with fixed block size and dynamic expansion
//
// History
// 1. V1.0, Created by XuCF. 2024/12/19
//=============================================================================

#ifndef _CBLOCK_H
#define _CBLOCK_H

#include "libcustom.h"

typedef struct cblock_t cblock_t;
typedef non (*cblock_init_func_t)(any block);	 // Block initialization function
typedef non (*cblock_free_func_t)(any block);	 // Block cleanup function
typedef non (*cblock_dump_func_t)(str fmt, ...); // Dump function

// Main functions
cblock_t *cblock_new_(int block_size, int init_capacity, cblock_init_func_t fi, cblock_free_func_t ff);
non cblock_del(cblock_t *thiz);
any cblock_get(cblock_t *thiz);
non cblock_put(cblock_t *thiz, any block);

// Dump functions
non cblock_dump_ex(cblock_t *thiz, cblock_dump_func_t df);
non cblock_dump(cblock_t *thiz);

// Convenient macros for type-safe usage
#define cblock_new(type, init_capacity)				cblock_new_(sizeof(type), init_capacity, NULL, NULL)
#define cblock_new_adv(type, init_capacity, fi, ff) cblock_new_(sizeof(type), init_capacity, fi, ff)

#endif // _CBLOCK_H
