//==============================================================================
// Copyright (C), 2004-2020, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: para_code_macro.h
// Author		: HuangYK
// Created On	: 2020 Jan 14, Tue
// Description	: Define Parameter Code Options Macro.
//
// History
// 0. V1.0, Created by HuangYK, 2020 Jan 14, Tue
// 1. V1.1, Update by TanJW, 2020 Apr 14, Tue
//
//==============================================================================

#ifndef _PARA_CODE_OPTIONS_MACRO_H_
#define _PARA_CODE_OPTIONS_MACRO_H_

// global code read enable setting
#define MDFLAG_RESERVED										0

#define MDFLAG_CodeRead_Disabled							0		// 失能全局条码识读
#define MDFLAG_CodeRead_Enabled								1		// 使能全局条码识读

//解码成功提示
#define MDFLAG_INDICA_DecodeSucceed_Disable					0
#define MDFLAG_INDICA_DecodeSucceed_Enable					1
#define MDFLAG_INDICA_DecodeSucceed_Default					MDFLAG_INDICA_DecodeSucceed_Enable

//Write serial number to USB descriptor switch
#define MDFLAG_SERIAL_NUM_2_USBD_SWITCH_OFF					0
#define MDFLAG_SERIAL_NUM_2_USBD_SWITCH_ON					1

//USB mass storage device enum as CD-ROM or Udisk switch
#define MDFLAG_USB_MSD_SWITCH_CDROM							0
#define MDFLAG_USB_MSD_SWITCH_UDISK							1

// 以下宏为系统中没有使用，也找不到对应的参数号的宏
///////////////////////////////////Start////////////////////////////////////////

// 01xx. Interface相关
#define MDFLAG_Interface_Res1_Default						0
#define MDFLAG_Interface_Res2_Default						0

// 02xx. KeyBoard相关
// 02xx.1 此项有可能与“0204”相关，有可能更新了取值范围后，就没有沿用本套宏
#define MDFLAG_KB_FuncKey_Disable							0
#define MDFLAG_KB_FuncKey_Enable							1
#define MDFLAG_KB_FuncKey_Default							MDFLAG_RESERVED

// 02xx.2 此项有可能与“0207”相关
#define MDFLAG_KB_InterKeyDelay_Default						0
#define MDFLAG_KB_Res2_Default								0

// 03xx. RS232相关的宏没有

// 04xx. Scan Mode相关
// 04xx.1
#define MDFLAG_SCAN_Decoder4_Disable						0
#define MDFLAG_SCAN_Decoder4_Enable							1
#define MDFLAG_SCAN_Decoder4_Default						MDFLAG_SCAN_Decoder4_Disable

// 04xx.2
#define MDFLAG_SCAN_PwrDelay_Default						1

// 04xx.3
#define MDFLAG_SCAN_Res1_Default							0
#define MDFLAG_SCAN_Res2_Default							0

// 05xx. Indication相关
#define MDFLAG_INDICA_Res1_Default							2
#define MDFLAG_INDICA_Res2_Default							0

// 06xx. 自动感应相关
// 06xx.1
#define MDFLAG_Infrad_SnsrStdbyDura_Default					0x16

// 06xx.2
#define MDFLAG_Infrad_Res1_Default							0
#define MDFLAG_Infrad_Res2_Default							0

// 07xx. 无线相关的宏没有

// 08xx. 无线相关的宏没有

// 09xx. USB相关
// 09xx.1
#define MDFLAG_USB_Speed_Default							3

// 09xx.2
#define MDFLAG_USB_Mode_Default								0

// 10xx. 条码识读设置相关的宏没有

// 11xx. UPCA码相关
#define MDFLAG_UPCA_Res1_Default							0
#define MDFLAG_UPCA_Res2_Default							0

// 12xx. UPCE码相关
#define MDFLAG_UPCE_Res1_Default							0
#define MDFLAG_UPCE_Res2_Default							0

// 13xx. EAN13码相关
#define MDFLAG_EAN13_Res1_Default							0
#define MDFLAG_EAN13_Res2_Default							0

// 14xx. EAN8码相关
#define MDFLAG_EAN8_Res1_Default							0
#define MDFLAG_EAN8_Res2_Default							0

// 15xx. Code 39码相关
#define MDFLAG_CODE39_Res1_Disable							0
#define MDFLAG_CODE39_Res1_Enable							1
#define MDFLAG_CODE39_Res1_Default							MDFLAG_CODE39_Res1_Disable


// 16xx. 交叉25码相关
#define MDFLAG_INTL25_Res1_Default							0
#define MDFLAG_INTL25_Res2_Default							0

// 17xx. 工业25码相关
#define MDFLAG_INDS25_Res1_Default							0
#define MDFLAG_INDS25_Res2_Default							0

// 18xx. 矩阵25码相关
#define MDFLAG_MATR25_Res1_Default							0
#define MDFLAG_MATR25_Res2_Default							0

// 19xx. 库德巴码相关
#define MDFLAG_CODABAR_Res1_Default							0
#define MDFLAG_CODABAR_Res2_Default							0

// 20xx. 128码相关
#define MDFLAG_CODE128_Res2_Default							0

// 21xx. code-93码相关
#define MDFLAG_CODE93_Res1_Default							0
#define MDFLAG_CODE93_Res2_Default							0

// 22xx. code-11码相关
// 22xx.1 CodeID1与CodeID2
#define MDFLAG_CODE11_CodeID1_Default						'V'
#define MDFLAG_CODE11_CodeID2_Default						'V'

// 22xx.2
#define MDFLAG_CODE11_Res1_Default							0
#define MDFLAG_CODE11_Res2_Default							0

// 23xx. MSI/Plessey码相关
#define MDFLAG_MSIPL_Res1_Default							0
#define MDFLAG_MSIPL_Res2_Default							0

// 24xx. UK/Plessey码相关
// 24xx.1 CodeID1与CodeID2
#define MDFLAG_UKPL_CodeID1_Default							'U'
#define MDFLAG_UKPL_CodeID2_Default							'U'

// 24xx.2
#define MDFLAG_UKPL_Res1_Default							0
#define MDFLAG_UKPL_Res2_Default							0

// 25xx. UCC/EAN 128码相关
#define MDFLAG_UCCEAN128_Res2_Default						0

// 26xx. 中国邮政码相关
#define MDFLAG_CHNPST_Res1_Default							0
#define MDFLAG_CHNPST_Res2_Default							0

// 27xx. GS1 DataBar码相关
// 27xx.1 CodeID1与CodeID2
#define MDFLAG_GS1DB_CodeID1_Default						'R'
#define MDFLAG_GS1DB_CodeID2_Default						'R'

// 27xx.2
#define MDFLAG_GS1DB_Res2_Default							0

// 28xx. GS1 DataBar Limited码相关
// 28xx.1 CodeID1与CodeID2
#define MDFLAG_GS1LIMI_CodeID1_Default						'R'
#define MDFLAG_GS1LIMI_CodeID2_Default						'R'

// 28xx.2
#define MDFLAG_GS1LIMI_Res2_Default							0

// 29xx. GS1 DataBar Expanded码相关
// 29xx.1 CodeID1与CodeID2
#define MDFLAG_GS1EXPA_CodeID1_Default						'R'
#define MDFLAG_GS1EXPA_CodeID2_Default						'R'

// 29xx.2
#define MDFLAG_GS1EXPA_Res2_Default							0

// 30xx. PDF417码相关
// 30xx.1 CodeID1与CodeID2
#define MDFLAG_PDF417_CodeID1_Default						'P'
#define MDFLAG_PDF417_CodeID2_Default						'P'

// 30xx.2
#define MDFLAG_PDF417_Res1_Default							0
#define MDFLAG_PDF417_Res2_Default							0

// 31xx. MicroPDF417
// 31xx.1 CodeID1与CodeID2
#define MDFLAG_MICPDF417_CodeID1_Default					'P'
#define MDFLAG_MICPDF417_CodeID2_Default					'P'

// 31xx.2
#define MDFLAG_MICPDF417_Res1_Default						0
#define MDFLAG_MICPDF417_Res2_Default						0

// 32xx. 中国财政码相关
// 32xx.1 CodeID1与CodeID2
#define MDFLAG_CHNFNC_CodeID1_Default						'Y'
#define MDFLAG_CHNFNC_CodeID2_Default						'Y'

// 33xx. ISBT128码无相关宏

// 34xx. UPCE1码相关
// 34xx.1 CodeID1与CodeID2
#define MDFLAG_UPCE1_CodeID1_Default						'D'	// mindeo version
#define MDFLAG_UPCE1_CodeID2_Default						'D'	// mindeo version

// 34xx.2
#define MDFLAG_UPCE1_Res1_Default							0
#define MDFLAG_UPCE1_Res2_Default							0

// 80xx. 字符串设置相关
// 80xx.1
#define MDFLAG_STRSET_Res2									'A'

// 80xx.2
#define MDFLAG_STRSET_CharSubstu1_0							0
#define MDFLAG_STRSET_CharSubstu1_1							0

// 80xx.3
#define MDFLAG_STRSET_CharSubstu2_0							0
#define MDFLAG_STRSET_CharSubstu2_1							0

// 81xx. 字符串位置设置相关
#define MDFLAG_STRPOS_Res1_Default							0
#define MDFLAG_STRPOS_Res2_Default							0

// 82xx. 字符串传送
#define MDFLAG_STRTRANS_Res2_Default						0

////////////////////////////////////End/////////////////////////////////////////


// “0010”，Dec，扫码设置参数功能；
#define MDFLAG_SetParaScan_Disable							0		// 失能扫码设置参数功能
#define MDFLAG_SetParaScan_Enable							1		// 使能扫码设置参数功能
#define MDFLAG_SetParaScan_Default							MDFLAG_SetParaScan_Enable

// “0101”，Dec，接口类型功能；
#define MDFLAG_Interface_Port_AUTO 							0		// 自动检测电缆线使用的是 RS-232 串口线、USB 线或PS2连接线
#define MDFLAG_Interface_Port_RS232							1		// 只能识别RS-232接口的电缆线
#define MDFLAG_Interface_Port_USB							2		// 只能识别USB接口的电缆线
// 若需要使用PS2接口，请使用“自动识别”（0101D00）
#define MDFLAG_Interface_Port_RS232_ETH						3		// 只能识别RS-232+以太网接口的电缆线
#define MDFLAG_Interface_Port_RS232_USB						4		// 只能识别RS-232+USB接口的电缆线
#define MDFLAG_Interface_Port_SOFTWARE						5		// 只能识别软件接口的电缆线
#define MDFLAG_Interface_Port_Default						MDFLAG_Interface_Port_AUTO

// “0201”，Dec，KeyBoard接口，接口类型功能；
#define MDFLAG_KB_Type_IBMAT_PS2							0		// IBM AT，PS/2接口类型
#define MDFLAG_KB_Type_MAC									1		// Apple Mac及兼容机类型
#define MDFLAG_KB_Type_Default								MDFLAG_KB_Type_IBMAT_PS2

// “0202”，Dec，KeyBoard接口，键盘布局功能；
#define MDFLAG_KB_Layout_CHN_USA							0		// 美式键盘布局
#define MDFLAG_KB_Layout_TKYF								1		// 土耳其F式键盘布局
#define MDFLAG_KB_Layout_TKYQ								2		// 土耳其Q式键盘布局
#define MDFLAG_KB_Layout_FRN								3		// 法式键盘布局
#define MDFLAG_KB_Layout_ITA								4		// 意大利式键盘布局
#define MDFLAG_KB_Layout_SPA								5		// 西班牙式键盘布局
#define MDFLAG_KB_Layout_SLK								6		// 斯洛伐克式键盘布局
#define MDFLAG_KB_Layout_DMK								7		// 丹麦式键盘布局
#define MDFLAG_KB_Layout_JAP								8		// 日式键盘布局
#define MDFLAG_KB_Layout_GER								9		// 德式键盘布局
#define MDFLAG_KB_Layout_BEL								10		// 比利时式键盘布局
#define MDFLAG_KB_Layout_RUS								11		// 俄罗斯式键盘布局
#define MDFLAG_KB_Layout_CZE								12		// 捷克式键盘布局
#define MDFLAG_KB_Layout_Thai								13		// 泰文键盘布局
#define MDFLAG_KB_Layout_Hungary							14		// 匈牙利键盘布局
#define MDFLAG_KB_Layout_Swiss_German						15		// 瑞士德语键盘布局
#define MDFLAG_KB_Layout_Default							MDFLAG_KB_Layout_CHN_USA

// “0203”，Dec，KeyBoard接口，主机端口速率（时钟周期）功能；
#define MDFLAG_KB_Speed_60us								0		// 主机端口速率（时钟周期）60 微秒
#define MDFLAG_KB_Speed_70us								1		// 主机端口速率（时钟周期）70 微秒
#define MDFLAG_KB_Speed_80us								2		// 主机端口速率（时钟周期）80 微秒
#define MDFLAG_KB_Speed_90us								3		// 主机端口速率（时钟周期）90 微秒
#define MDFLAG_KB_Speed_100us								4		// 主机端口速率（时钟周期）100微秒
#define MDFLAG_KB_Speed_200us								5		// 主机端口速率（时钟周期）200微秒
#define MDFLAG_KB_Speed_Default								MDFLAG_KB_Speed_60us

// “0204”，Dec，KeyBoard接口，功能键（组合键）时延选项；
#define MDFLAG_KB_FuncKey_Delay_0ms							0		// KeyBoard接口，功能键（组合键）时延0 毫秒
#define MDFLAG_KB_FuncKey_Delay_10ms						1		// KeyBoard接口，功能键（组合键）时延10毫秒
#define MDFLAG_KB_FuncKey_Delay_20ms						2		// KeyBoard接口，功能键（组合键）时延20毫秒
#define MDFLAG_KB_FuncKey_Delay_40ms						3		// KeyBoard接口，功能键（组合键）时延40毫秒
#define MDFLAG_KB_FuncKey_Delay_80ms						4		// KeyBoard接口，功能键（组合键）时延80毫秒
#define MDFLAG_KB_FuncKey_Delay_Default						MDFLAG_KB_FuncKey_Delay_0ms

// “0205”，Dec，KeyBoard接口，数字键功能；
#define MDFLAG_KB_NumKey_Disable							0		// 输出字母和数字时，传送字母键盘对应的键码
#define MDFLAG_KB_NumKey_Num								1		// 输出数字时，将传送数字小键盘对应的键码
#define MDFLAG_KB_NumKey_AltKypd							2		// 输出字符时，将模拟 Alt＋数字键的方式，输出字符的 Unicode 编码
#define MDFLAG_KB_NumKey_AltKypd_GBK						3		// 输出字符时，将模拟 Alt＋数字键的方式，输出字符的 GBK 编码
#define MDFLAG_KB_NumKey_AltKypd_BIG5						4		// 输出字符时，将模拟 Alt＋数字键的方式，输出字符的 BIG5 编码
#define MDFLAG_KB_NumKey_AltKypd_THAI						5		// 输出字符时，将模拟 Alt＋数字键的方式，输出字符的 THAI（泰文）编码
#define MDFLAG_KB_NumKey_Default							MDFLAG_KB_NumKey_Disable

// “0206”，Dec，KeyBoard接口，上电检验功能；
#define MDFLAG_KB_PwOnSim_Disable							0		// 禁止上电检验功能
#define MDFLAG_KB_PwOnSim_Enable							1		// 使能上电检验功能
#define MDFLAG_KB_PwOnSim_Default							MDFLAG_KB_PwOnSim_Disable

// “0207”，Dec，KeyBoard接口，相邻字符时延功能；
#define MDFLAG_KB_InterCharDelay_0ms						0		// KeyBoard接口，相邻字符时延0 毫秒
#define MDFLAG_KB_InterCharDelay_5ms						1		// KeyBoard接口，相邻字符时延5 毫秒
#define MDFLAG_KB_InterCharDelay_10ms						2		// KeyBoard接口，相邻字符时延10毫秒
#define MDFLAG_KB_InterCharDelay_20ms						3		// KeyBoard接口，相邻字符时延20毫秒
#define MDFLAG_KB_InterCharDelay_40ms						4		// KeyBoard接口，相邻字符时延40毫秒
#define MDFLAG_KB_InterCharDelay_80ms						5		// KeyBoard接口，相邻字符时延80毫秒
#define MDFLAG_KB_InterCharDelay_Default					MDFLAG_KB_InterCharDelay_0ms

// “0208”，Dec，KeyBoard接口，相邻字节时延功能；
#define MDFLAG_KB_BlkTransDelay_1ms							0		// KeyBoard接口，相邻字节时延1毫秒
#define MDFLAG_KB_BlkTransDelay_2ms							1		// KeyBoard接口，相邻字节时延2毫秒
#define MDFLAG_KB_BlkTransDelay_4ms							2		// KeyBoard接口，相邻字节时延4毫秒
#define MDFLAG_KB_BlkTransDelay_8ms							3		// KeyBoard接口，相邻字节时延8毫秒
#define MDFLAG_KB_BlkTransDelay_Default						MDFLAG_KB_BlkTransDelay_1ms

// “0209”，Dec，KeyBoard接口，大写锁定翻转功能；
#define MDFLAG_KB_CapStatus_OFF								0		// 禁止大写锁定翻转功能
#define MDFLAG_KB_CapStatus_ON								1		// 使能大写锁定翻转功能
#define MDFLAG_KB_CapStatus_Default							MDFLAG_KB_CapStatus_OFF

// “0210”，Dec，KeyBoard接口，大写锁定忽略功能；
#define MDFLAG_KB_AutoCapStatus_OFF							0		// 禁止大写锁定忽略功能
#define MDFLAG_KB_AutoCapStatus_ON							1		// 使能大写锁定翻转功能
#define MDFLAG_KB_AutoCapStatus_Default						MDFLAG_KB_AutoCapStatus_OFF

// “0301”，Dec，RS232接口，流量控制功能；
#define MDFLAG_RS232_FlowControl_None						0		// 只使用 TxD 和 RxD 信号进行通信而不使用任何硬件或软件握手协议
#define MDFLAG_RS232_FlowControl_RTSCTS_Low					1		// 当影像扫描器准备向主机传送条码数据时，它必须先发送 RTS 信号，等待主机发出 CTS 信号，然后进行正常数据通信。如超时或主机没有反馈 CTS 信号，影像扫描器蜂鸣器将发出特殊鸣叫警告。根据主机空闲时，提供 RTS 的电平高低，做相应设置（主机空闲：RTS 低电平）
#define MDFLAG_RS232_FlowControl_RTSCTS_High				2		// 当影像扫描器准备向主机传送条码数据时，它必须先发送 RTS 信号，等待主机发出 CTS 信号，然后进行正常数据通信。如超时或主机没有反馈 CTS 信号，影像扫描器蜂鸣器将发出特殊鸣叫警告。根据主机空闲时，提供 RTS 的电平高低，做相应设置（主机空闲：RTS 高电平）
#define MDFLAG_RS232_FlowControl_XONXOFF					3		// 当主机不能接受数据时，它会发送一个 XOFF 字符通知影像扫描器暂停传送；直到影像扫描器收到一个 XON 字符时，传送继续
#define MDFLAG_RS232_FlowControl_ACKNAK						4		// 数据传输完毕后，影像扫描器将等待主机反馈一个 ACK（应答）或者 NAK（无应答）信号。当收到一个 NAK 信号，影像扫描器会重新发送数据并等待一个 ACK 或者 NAK 信号。当连续收到三次NAK 信号时，影像扫描器将不再尝试发送当前数据，同时蜂鸣器和 LED 灯会发出提示信号
#define MDFLAG_RS232_FlowControl_RTS_Low					5		// 注：选项1-5供ES命令体系专用，如果使用uE命令体系，此选项无效
#define MDFLAG_RS232_FlowControl_ACKNAK_RTSCTS_High			6		// 注：此选项供uE命令体系专用，如果使用ES命令体系，此选项无效
#define MDFLAG_RS232_FlowControl_CTS_SCAN					7		// 设置此选项后，当CTS为有效电平时，开始解码；无效电平时，结束解码
#define MDFLAG_RS232_FlowControl_Default					MDFLAG_RS232_FlowControl_None

// “0302”，Dec，RS232接口，相邻字符时延功能；
#define MDFLAG_RS232_InterCharDelay_Forbid					0		// 禁止相邻字符时延
#define MDFLAG_RS232_InterCharDelay_5Msecs					1		// 相邻字符时延为5 毫秒
#define MDFLAG_RS232_InterCharDelay_10Msecs					2		// 相邻字符时延为10毫秒
#define MDFLAG_RS232_InterCharDelay_20Msecs					3		// 相邻字符时延为20毫秒
#define MDFLAG_RS232_InterCharDelay_40Msecs					4		// 相邻字符时延为40毫秒
#define MDFLAG_RS232_InterCharDelay_80Msecs					5		// 相邻字符时延为80毫秒
#define MDFLAG_RS232_InterCharDelay_Default					MDFLAG_RS232_InterCharDelay_Forbid

// “0303”，Dec，RS232接口，相邻字节时延功能；（系统上未使用）
// 由于系统上未使用此参数，此处暂时不定义Unit
#define MDFLAG_RS232_BlkTransDelay_1ms						0		// RS232接口，相邻字节时延1毫秒？代码上注释是1ms但是未找到依据
#define MDFLAG_RS232_BlkTransDelay_Default					MDFLAG_RS232_BlkTransDelay_1ms

// “0304”，Dec，RS232接口，反馈时延功能（系统中使用的单位有10ms与100ms，此处暂时定义单位为10ms）；
#define MDFLAG_RS232_ResponseDelay_Unit_10ms				10		// RS232接口，反馈时延单位10ms
#define MDFLAG_RS232_ResponseDelay_0ms						0		// RS232接口，反馈时延0毫秒

// 在system_cfg.h中，定义默认值为20，但是实际没有使用到，系统上使用的默认值为0
// 去掉默认值20不知道是否会带来其他负面影响，此处暂时保留
#define	MDFLAG_RS232_ResponseDelay_Default					20
//#define MDFLAG_RS232_ResponseDelay_Default					MDFLAG_RS232_ResponseDelay_0ms

// “0305”，Dec，RS232接口，波特率功能；
#define MDFLAG_RS232_BuadRate_300							0		// 波特率为300
#define MDFLAG_RS232_BuadRate_600							1		// 波特率为600
#define MDFLAG_RS232_BuadRate_1200							2		// 波特率为1200
#define MDFLAG_RS232_BuadRate_2400							3		// 波特率为2400
#define MDFLAG_RS232_BuadRate_4800							4		// 波特率为4800
#define MDFLAG_RS232_BuadRate_9600							5		// 波特率为9600
#define MDFLAG_RS232_BuadRate_19200							6		// 波特率为19200
#define MDFLAG_RS232_BuadRate_38400							7		// 波特率为38400
#define MDFLAG_RS232_BuadRate_57600							8		// 波特率为57600
#define MDFLAG_RS232_BuadRate_115200						9		// 波特率为115200
#define MDFLAG_RS232_BuadRate_230400						10		// 波特率为230400
#define MDFLAG_RS232_BuadRate_345600						11		// 波特率为345600
#define MDFLAG_RS232_BuadRate_460800						12		// 波特率为460800
#define MDFLAG_RS232_BuadRate_576000						13		// 波特率为576000
#define MDFLAG_RS232_BuadRate_691200						14		// 波特率为691200
#define MDFLAG_RS232_BuadRate_806400						15		// 波特率为806400
#define MDFLAG_RS232_BuadRate_921600						16		// 波特率为921600
#define MDFLAG_RS232_BuadRate_1036800						17		// 波特率为1036800
#define MDFLAG_RS232_BuadRate_1152000						18		// 波特率为1152000
#define MDFLAG_RS232_BuadRate_1382400						19		// 波特率为1382400
#define MDFLAG_RS232_BuadRate_1497600						20		// 波特率为1497600
#define MDFLAG_RS232_BuadRate_Default						MDFLAG_RS232_BuadRate_9600

// “0306”，Dec，RS232接口，奇偶校验功能；
#define MDFLAG_RS232_Parity_None							0		// 无奇偶校验
#define MDFLAG_RS232_Parity_Odd								1		// 校验方式为奇校验
#define MDFLAG_RS232_Parity_Even							2		// 校验方式为偶校验
#define MDFLAG_RS232_Parity_Default							MDFLAG_RS232_Parity_None

// “0307”，Dec，RS232接口，数据位功能；
#define MDFLAG_RS232_DataBits_8								0		// 扫描器传输的数据中数据字符位数为8位
#define MDFLAG_RS232_DataBits_7								1		// 扫描器传输的数据中数据字符位数为7位
#define MDFLAG_RS232_DataBits_Default						MDFLAG_RS232_DataBits_8

// “0308”，Dec，RS232接口，停止位功能；
#define MDFLAG_RS232_StopBits_1								0		// 扫描器传输的数据中停止位的位数为1位
#define MDFLAG_RS232_StopBits_2								1		// 扫描器传输的数据中停止位的位数为2位
#define MDFLAG_RS232_StopBits_Default						MDFLAG_RS232_StopBits_1

// “0309”，目前系统上未使用

// “0310”，Dec，RS232接口，主机类型功能；
#define MDFLAG_RS232_DeviceType_Standard					0		// 阅读器与主机通过标准RS-232 接口进行通信
#define MDFLAG_RS232_DeviceType_OPOS						1		// 使用OPOS/JPOS的数据协议进行通信
#define MDFLAG_RS232_DeviceType_MDAux						2		// 使用辅助端口进行通信
#define MDFLAG_RS232_DeviceType_Default						MDFLAG_RS232_DeviceType_Standard

// “0311”，Dec，RS232接口，解码数据包传输功能；
#define MDFLAG_RS232_DecoDataFormat_Raw						0		// 解码数据是以原始格式（ASCII 码）传输。当解码数据以原始格式传输时，ACK/NAK握手协议是否使能都不会影响数据传输
#define MDFLAG_RS232_DecoDataFormat_Packed					1		// 解码数据以打包格式传输。详见“DECODE_DATA解码数据”
#define MDFLAG_RS232_DecoDataFormat_Default					MDFLAG_RS232_DecoDataFormat_Raw

// “0312”，Dec，RS232接口，主机字符时延功能（单位：10ms）；
#define MDFLAG_RS232_HsCharTimeout_Unit_10ms				10		// RS232接口，主机字符时延单位10ms
#define MDFLAG_RS232_HsCharTimeout_200ms					20		// RS232接口，主机字符时延200ms
#define MDFLAG_RS232_HsCharTimeout_Default					MDFLAG_RS232_HsCharTimeout_200ms

// “0401”，Dec，扫描模式功能；
#define MDFLAG_SCAN_Mode_GoodReadOff						0		// 单次按键触发模式
#define MDFLAG_SCAN_Mode_Mome								1		// 按键保持模式
#define MDFLAG_SCAN_Mode_Alter								2		// 开关持续模式
#define MDFLAG_SCAN_Mode_Cont								3		// 持续模式
#define MDFLAG_SCAN_Mode_GoodReadOn							4		// 单次按键保持模式
#define MDFLAG_SCAN_Mode_sciTrigger							5		// 命令控制触发模式
#define MDFLAG_SCAN_Mode_AutoDete							6		// 自动感应-单次按键保持模式
#define MDFLAG_SCAN_Mode_AutoDeteIMG						7		// 自动感应-单次按键触发模式
#define MDFLAG_SCAN_Mode_AutoDeteIR_GoodReadOn				8		// 红外感应-单次按键保持
#define MDFLAG_SCAN_Mode_AutoDeteIR_GoodReadOff				9		// 红外感应-单次按键触发
#define MDFLAG_SCAN_Mode_AutoDeteIR_Mome					10		// 红外感应-按键保持
#define MDFLAG_SCAN_Mode_Desktop							11		// 桌面模式
#define MDFLAG_SCAN_Mode_ButtonCont							12		// 按键持续模式
#define MDFLAG_SCAN_Mode_MultiSymbolsCont					20		// 多信号保持模式
#define MDFLAG_SCAN_Mode_NONE								0xFF	// 无
#define MDFLAG_SCAN_Mode_Default							MDFLAG_SCAN_Mode_Mome

// “0402”，Dec，保持时长功能（单位：1s）；
// 保持时长功能即解码持续时间，由于MP8000，MP8300，MP8600与其他产品的时间长短不一样，
// 且“0402D11”~“0402D15”都是表示时间60min，此处暂时不进行处理
#define	MDFLAG_SCAN_StdbyDur_Default						4		// 这个定义是在原来的system_cfg.h中定义，现在暂时搬移到此处

// “0403”，Hex，1D重码有效时延功能（单位：50ms）；
#define	MDFLAG_SCAN_SamBarDelay_Default						10		// 在原来的system_cfg.h中定义，下面的是新增的，对1D与2D加以区分
#define MDFLAG_SCAN_1D_SamBarDelay_Unit_50ms				50		// 1D重码有效时延单位50ms
#define MDFLAG_SCAN_1D_SamBarDelay_400ms					8		// 1D重码有效时延400ms
#define MDFLAG_SCAN_1D_SamBarDelay_Default					MDFLAG_1D_SamBarDelay_400ms

// “0404”，Dec，多重确认功能；
#define MDFLAG_SCAN_DoubConfirm_None						0		// 关闭多重确认
#define MDFLAG_SCAN_DoubConfirm_1							1		// 1重确认
#define MDFLAG_SCAN_DoubConfirm_2							2		// 2重确认
#define MDFLAG_SCAN_DoubConfirm_3							3		// 3重确认
#define MDFLAG_SCAN_DoubConfirm_4							4		// 4重确认
#define MDFLAG_SCAN_DoubConfirm_5							5		// 5重确认
#define MDFLAG_SCAN_DoubConfirm_6							6		// 6重确认
#define MDFLAG_SCAN_DoubConfirm_7							7		// 7重确认
#define MDFLAG_SCAN_DoubConfirm_8							8		// 8重确认
#define MDFLAG_SCAN_DoubConfirm_9							9		// 9重确认
#define MDFLAG_SCAN_DoubConfirm_Default						MDFLAG_SCAN_DoubConfirm_None

// “0405”，Dec，一维条码全局最大码字长度功能；
#define MDFLAG_SCAN_GlbMaxLen_99							99		// 一维条码全局最大码字长度99
#define MDFLAG_SCAN_GlbMaxLen_Default						MDFLAG_SCAN_GlbMaxLen_99

// “0406”，Dec，一维条码全局最小码字长度功能；
#define MDFLAG_SCAN_GlbMinLen_4								4		// 一维条码全局最小码字长度4
#define MDFLAG_SCAN_GlbMinLen_Default						MDFLAG_SCAN_GlbMinLen_4

// “0407”，Dec，全局插入字符串组功能；
#define MDFLAG_SCAN_GlbInsertGrpSel_0						0		// 不进行全局插入字符串组
#define MDFLAG_SCAN_GlbInsertGrpSel_Default					MDFLAG_SCAN_GlbInsertGrpSel_0

// “0408”，Dec，条码宽度校正功能；
#define MDFLAG_SCAN_EleAmend_Disable						0		// 禁止条码宽度校正功能
#define MDFLAG_SCAN_EleAmend_Enable							1		// 使能条码宽度校正功能
#define MDFLAG_SCAN_EleAmend_Default						MDFLAG_SCAN_EleAmend_Enable

// “0409”，Dec，字符输出控制功能；
#define MDFLAG_SCAN_PrtCharOnly_Disable						0		// 不进行控制，数据正常输出
#define MDFLAG_SCAN_PrtCharOnly_Printable					1		// 仅输出可打印字符，位于ASCII码表中0x00-0x1F的内容即不可打印字符将不输出
#define MDFLAG_SCAN_PrtCharOnly_Alphanumeric				2		// 仅输出字母与数字字符
#define MDFLAG_SCAN_PrtCharOnly_Default						MDFLAG_SCAN_PrtCharOnly_Disable

// “0410”，Dec，纠错优化解码功能；
#define MDFLAG_SCAN_DecodeOpt_Disable						0		// 失能纠错优化解码功能
#define MDFLAG_SCAN_DecodeOpt_Enable						1		// 使能纠错优化解码功能
#define MDFLAG_SCAN_DecodeOpt_Default						MDFLAG_SCAN_DecodeOpt_Enable

// “0411”，Hex，连续扫描数据输出时延功能；
// 系统上未查找到使用，暂时不定义Unit
#define	MDFLAG_DatOutDlyContMod_0ms							0		// 连续扫描数据输出时延0ms
#define	MDFLAG_DatOutDlyContMod_Default						MDFLAG_DatOutDlyContMod_0ms

// “0412”，Dec，扫描模式，从Idle模式进入浅度休眠模式（Light Sleep）的时延
#define MDFLAG_IDLE_TO_LIGHT_SLEEP_Delay_1s					0		//1 Second
#define MDFLAG_IDLE_TO_LIGHT_SLEEP_Delay_5s					1		//5 Second
#define MDFLAG_IDLE_TO_LIGHT_SLEEP_Delay_10s				2		//10 Second
#define MDFLAG_IDLE_TO_LIGHT_SLEEP_Delay_30s				3		//30 Second
#define MDFLAG_IDLE_TO_LIGHT_SLEEP_Delay_1m					4		//1 Minute
#define MDFLAG_IDLE_TO_LIGHT_SLEEP_Delay_5m					5		//5 Minute
#define MDFLAG_IDLE_TO_LIGHT_SLEEP_Delay_10m				6		//10 Minute
#define MDFLAG_IDLE_TO_LIGHT_SLEEP_Delay_30m				7		//30 Minute
#define MDFLAG_IDLE_TO_LIGHT_SLEEP_Delay_1h					8		//1 hour
#define MDFLAG_IDLE_TO_LIGHT_SLEEP_Delay_3h					9		//3 hour
#define MDFLAG_IDLE_TO_LIGHT_SLEEP_Delay_0s					10		//0 Second
#define MDFLAG_IDLE_TO_LIGHT_SLEEP_Delay_50ms				11		//50 Milli-Second
#define MDFLAG_IDLE_TO_LIGHT_SLEEP_Delay_100ms				12		//100 Milli-Second
#define MDFLAG_IDLE_TO_LIGHT_SLEEP_Delay_200ms				13		//200 Milli-Second
#define MDFLAG_IDLE_TO_LIGHT_SLEEP_Delay_500ms				14		//500 Milli-Second
#define MDFLAG_IDLE_TO_LIGHT_SLEEP_Delay_Default			MDFLAG_IDLE_TO_LIGHT_SLEEP_Delay_5m

// “0413”，Dec，字符编码系统功能；
#define	MDFLAG_SCAN_CharEncode_ASCII						0		// 使用ASCII字符编码系统
#define MDFLAG_SCAN_CharEncode_UTF8							1		// 使用UTF-8码字符编码系统
#define MDFLAG_SCAN_CharEncode_Windows1251					2		// 使用Windows-1251字符编码系统
#define MDFLAG_SCAN_CharEncode_SimpChinese					3		// 使用简体中文字符编码系统
#define MDFLAG_SCAN_CharEncode_TraChinese					4		// 使用繁体中文字符编码系统
#define MDFLAG_SCAN_CharEncode_Windows1250					5		// 使用Windows-1250字符编码系统
#define MDFLAG_SCAN_CharEncode_KOI8R						6		// 使用KOI8R字符编码系统
#define MDFLAG_SCAN_CharEncode_Japanese						7		// 使用日文字符编码系统
#define MDFLAG_SCAN_CharEncode_Default						MDFLAG_SCAN_CharEncode_ASCII

// “0414”，Dec，边解边传功能；
#define MDFLAG_SCAN_BackTx_Disable							0		// 失能边解边传功能
#define MDFLAG_SCAN_BackTx_Enable							1		// 使能边解边传功能
#define MDFLAG_SCAN_BackTx_Adapt_50ms						2		// 自适应边解边传功能，自适应阈值为50ms
#define MDFLAG_SCAN_BackTx_Adapt_100ms						3		// 自适应边解边传功能，自适应阈值为100ms
#define MDFLAG_SCAN_BackTx_Adapt_150ms						4		// 自适应边解边传功能，自适应阈值为150ms
#define MDFLAG_SCAN_BackTx_Adapt_200ms						5		// 自适应边解边传功能，自适应阈值为200ms
#define MDFLAG_SCAN_BackTx_Adapt_250ms						6		// 自适应边解边传功能，自适应阈值为250ms
#define MDFLAG_SCAN_BackTx_Adapt_300ms						7		// 自适应边解边传功能，自适应阈值为300ms
#define MDFLAG_SCAN_BackTx_Default							MDFLAG_SCAN_BackTx_Disable

// “0415”，Hex，二维重码时延功能（单位：50ms）；
#define MDFLAG_SCAN_2D_SamBarDelay_Unit_50ms				50		// 二维重码时延单位50ms
#define MDFLAG_SCAN_2D_SamBarDelay_400ms					8		// 二维重码时延400ms
#define MDFLAG_SCAN_2D_SamBarDelay_Default					MDFLAG_2D_SamBarDelay_400ms

// “0416”，Dec，扫描模式，休眠模式的选项：休眠模式的最深等级；
// 等级说明：
//    1. 若设置成MDFLAG_LOW_POWER_MODE_OFF，系统只能运行在，正常工作模式；
//    2. 若设置成MDFLAG_LOW_POWER_MODE_IDLE，系统能运行在，正常工作模式-->Idle模式；
//    3. 若设置成MDFLAG_LOW_POWER_MODE_LIGHT_SLEEP，系统能运行在，正常工作模式-->Idle模式-->LIGHT_SLEEP模式；
//    4. 若设置成MDFLAG_LOW_POWER_MODE_DEEP_SLEEP，系统能运行在，正常工作模式-->Idle模式-->LIGHT_SLEEP模式-->DEEP_SLEEP模式；
#define MDFLAG_LOW_POWER_MODE_OFF							0
#define MDFLAG_LOW_POWER_MODE_DEEP_SLEEP					1
#define MDFLAG_LOW_POWER_MODE_IDLE							2
#define MDFLAG_LOW_POWER_MODE_LIGHT_SLEEP					3
#define MDFLAG_LOW_POWER_MODE_Default						MDFLAG_LOW_POWER_MODE_IDLE

// “0417”，Dec，SCI串口通信接口指令类型功能；
#define MDFLAG_SCI_CMD_TYPE_NONE							0		// 不支持SCI命令功能
#define MDFLAG_SCI_CMD_TYPE_ME5_ES4_SERIES					1		// 支持 ME5 和 ES4 SCI命令功能
#define MDFLAG_SCI_CMD_TYPE_UE966_SERIES					2		// 支持 uE966 SCI命令功能
#define MDFLAG_SCI_CMD_TYPE_DEFAULT							MDFLAG_SCI_CMD_TYPE_ME5_ES4_SERIES

// “0418”，Dec，辅助接口设备类型功能；
#define MDFLAG_SCAN_AuxDeviceType_Mindeo					0		// 民德
#define MDFLAG_SCAN_AuxDeviceType_Datalogic					1		// Datalogic
#define MDFLAG_SCAN_AuxDeviceType_Motorola					2		// 摩托罗拉
#define MDFLAG_SCAN_AuxDeviceType_Honeywell					3		// 霍尼韦尔
#define MDFLAG_SCAN_AuxDeviceType_Default					MDFLAG_SCAN_AuxDeviceType_Mindeo

// “0419”，Dec，输出参数配置信息功能；
#define MDFLAG_ParaSetCodeOutput_Disable					0		// 失能输出参数配置信息功能
#define MDFLAG_ParaSetCodeOutput_Enable						1		// 使能输出参数配置信息功能
#define MDFLAG_ParaSetCodeOutput_Default					MDFLAG_ParaSetCodeOutput_Disable

// “0420”，Dec，解码结果被串口命令打断后是否重传开关功能；
#define MDFLAG_SciCmd_InterruptRsltTxResend_Disable			0		// 失能解码结果被串口命令打断后重传开关功能
#define MDFLAG_SciCmd_InterruptRsltTxResend_Enable			1		// 使能解码结果被串口命令打断后重传开关功能
#define MDFLAG_SciCmd_InterruptRsltTxResend_Default			MDFLAG_SciCmd_InterruptRsltTxResend_Enable

// “0421”，Dec，传送“NR”字符功能；
#define MDFLAG_SendNoRead_Disable							0		// 失能传送“NR”字符功能
#define MDFLAG_SendNoRead_Enable							1		// 使能传送“NR”字符功能
#define MDFLAG_SendNoRead_Default							MDFLAG_SendNoRead_Disable

// “0422”，Hex，DoubleRead重码时延功能（单位：50ms）；
#define MDFLAG_SamBarDly_DR_Unit_50ms						50		// DoubleRead重码时延单位50ms
#define MDFLAG_SamBarDly_DR_400ms							8		// DoubleRead重码时延400ms
#define MDFLAG_SamBarDly_DR_Default							MDFLAG_SamBarDly_DR_400ms

// “0423”，Hex，辅助端口DoubleRead重码时延功能（单位：50ms）；
#define MDFLAG_SamBarDly_DR_Aux_Unit_50ms					50		// 辅助端口DoubleRead重码时延单位50ms
#define MDFLAG_SamBarDly_DR_Aux_600ms						12		// 辅助端口DoubleRead重码时延600ms
#define MDFLAG_SamBarDly_DR_Aux_Default						MDFLAG_SamBarDly_DR_Aux_600ms

// “0424”，Dex，工作模式功能；
#define MDFLAG_WORK_MODE_STANDARD							0		// 标准工作模式
#define MDFLAG_WORK_MODE_GROUP								1		// 群组工作模式
#define MDFLAG_WORK_MODE_DEFAULT							MDFLAG_WORK_MODE_STANDARD

// “0425”，Hex，NoRead自定义字符功能；
// NoRead自定义字符，默认取值范围0x00~0xff，如默认值为“%%NoRead”
#define MDFLAG_NoReadCustomChar_NoRead						{'%','%','N','o','R','e','a','d'}
#define MDFLAG_NoReadCustomChar_Default						MDFLAG_NoReadCustomChar_NoRead

// “0426”，扫描模式，从浅度休眠模式（Light Sleep）进入深度度休眠模式（Deep Sleep）的时延
#define MDFLAG_LIGHT_SLEEP_TO_DEEP_SLEEP_Delay_1s			0		//1 Second
#define MDFLAG_LIGHT_SLEEP_TO_DEEP_SLEEP_Delay_5s			1		//5 Second
#define MDFLAG_LIGHT_SLEEP_TO_DEEP_SLEEP_Delay_10s			2		//10 Second
#define MDFLAG_LIGHT_SLEEP_TO_DEEP_SLEEP_Delay_30s			3		//30 Second
#define MDFLAG_LIGHT_SLEEP_TO_DEEP_SLEEP_Delay_1m			4		//1 Minute
#define MDFLAG_LIGHT_SLEEP_TO_DEEP_SLEEP_Delay_5m			5		//5 Minute
#define MDFLAG_LIGHT_SLEEP_TO_DEEP_SLEEP_Delay_10m			6		//10 Minute
#define MDFLAG_LIGHT_SLEEP_TO_DEEP_SLEEP_Delay_30m			7		//30 Minute
#define MDFLAG_LIGHT_SLEEP_TO_DEEP_SLEEP_Delay_1h			8		//1 hour
#define MDFLAG_LIGHT_SLEEP_TO_DEEP_SLEEP_Delay_3h			9		//3 hour
#define MDFLAG_LIGHT_SLEEP_TO_DEEP_SLEEP_Delay_0s			10		//0 Second
#define MDFLAG_LIGHT_SLEEP_TO_DEEP_SLEEP_Delay_50ms			11		//50 Milli-Second
#define MDFLAG_LIGHT_SLEEP_TO_DEEP_SLEEP_Delay_100ms		12		//100 Milli-Second
#define MDFLAG_LIGHT_SLEEP_TO_DEEP_SLEEP_Delay_200ms		13		//200 Milli-Second
#define MDFLAG_LIGHT_SLEEP_TO_DEEP_SLEEP_Delay_500ms		14		//500 Milli-Second
#define MDFLAG_LIGHT_SLEEP_TO_DEEP_SLEEP_Delay_Default		MDFLAG_LIGHT_SLEEP_TO_DEEP_SLEEP_Delay_0s

// “0427”，Dec，按键升级时延选项；
#define MDFLAG_BUTTON_UPGRADE_DELAY_20ms					0		// 按键时延为20ms
#define MDFLAG_BUTTON_UPGRADE_DELAY_15s						1		// 按键时延为15s
#define MDFLAG_BUTTON_UPGRADE_DELAY_30s						2		// 按键时延为30s
#define MDFLAG_BUTTON_UPGRADE_DELAY_Default					MDFLAG_BUTTON_UPGRADE_DELAY_20ms

// “0428”，Dec，掠过式扫码体验增强选项；
#define MDFLAG_SWEEPING_SCAN_ENHAN_LowSweepingSpeed			0		// 低掠过速度，无增强
#define MDFLAG_SWEEPING_SCAN_ENHAN_MediumSweepingSpeed		1		// 中掠过速度
#define MDFLAG_SWEEPING_SCAN_ENHAN_HighSweepingSpeed		2		// 高掠过速度
#define MDFLAG_SWEEPING_SCAN_ENHAN_Default					MDFLAG_SWEEPING_SCAN_ENHAN_LowSweepingSpeed

// “0429”，Dec，按键切换蜂鸣器音调功能选项；
#define MDFLAG_BUTTON_CHANGE_BEEPER_TONE_Disable			0		// 禁止按键切换蜂鸣器音调功能
#define MDFLAG_BUTTON_CHANGE_BEEPER_TONE_Enable				1		// 使能按键切换蜂鸣器音调功能
#define MDFLAG_BUTTON_CHANGE_BEEPER_TONE_Default			MDFLAG_BUTTON_CHANGE_BEEPER_TONE_Disable

// “0430”，Dec，解码成功静默时延功能选项；
#define MDFLAG_GOOD_READ_DELAY_UNIT_MS						500		// 解码成功静默时延单位
#define MDFLAG_GOOD_READ_DELAY_NONE							0		// 禁止解码成功静默延时功能
#define MDFLAG_GOOD_READ_DELAY_Default						MDFLAG_GOOD_READ_DELAY_NONE

// “0431”，Dec，移动扫码模式选项；
#define MDFLAG_MOVEMENT_SCAN_MODE_Standard					0		// 标准模式
#define MDFLAG_MOVEMENT_SCAN_MODE_Enhance					1		// 增强模式
#define MDFLAG_MOVEMENT_SCAN_MODE_Default					MDFLAG_MOVEMENT_SCAN_MODE_Standard

// “0432”，Dec，直射补光灯与穹顶补光灯选择控制选项；
#define MDFLAG_LUM_SEL_Direct								0		// 仅直射补光灯
#define MDFLAG_LUM_SEL_DomeWhite							1		// 仅穹顶补光灯白色
#define MDFLAG_LUM_SEL_DomeRed								2		// 仅穹顶补光灯红色
#define MDFLAG_LUM_SEL_DomeBlue								3		// 仅穹顶补光灯蓝色
#define MDFLAG_LUM_SEL_DomeGreen							4		// 仅穹顶补光灯绿色
#define MDFLAG_LUM_SEL_DirectDomeWhite						5		// 直射补光灯与穹顶补光灯白色切换
#define MDFLAG_LUM_SEL_DirectDomeRed						6		// 直射补光灯与穹顶补光灯红色切换
#define MDFLAG_LUM_SEL_DirectDomeBlue						7		// 直射补光灯与穹顶补光灯蓝色切换
#define MDFLAG_LUM_SEL_DirectDomeGreen						8		// 直射补光灯与穹顶补光灯绿色切换
#define MDFLAG_LUM_SEL_DomeWhiteRed							9		// 穹顶补光灯白色与穹顶补光灯红色切换
#define MDFLAG_LUM_SEL_DomeWhiteBlue						10		// 穹顶补光灯白色与穹顶补光灯蓝色切换
#define MDFLAG_LUM_SEL_DomeWhiteGreen						11		// 穹顶补光灯白色与穹顶补光灯绿色切换
#define MDFLAG_LUM_SEL_DomeRedBlue							12		// 穹顶补光灯红色与穹顶补光灯蓝色切换
#define MDFLAG_LUM_SEL_DomeRedGreen							13		// 穹顶补光灯红色与穹顶补光灯绿色切换
#define MDFLAG_LUM_SEL_DomeBlueGreen						14		// 穹顶补光灯蓝色与穹顶补光灯绿色切换
#define MDFLAG_LUM_SEL_Default								MDFLAG_LUM_SEL_Direct

// “0433”，Dec，直射补光灯与穹顶补光灯切换时间间隔；
#define MDFLAG_DIR_AND_DOME_LUM_SW_ELP_0s					0		// 0 秒
#define MDFLAG_DIR_AND_DOME_LUM_SW_ELP_1s					1		// 1 秒
#define MDFLAG_DIR_AND_DOME_LUM_SW_ELP_2s					2		// 2 秒
#define MDFLAG_DIR_AND_DOME_LUM_SW_ELP_3s					3		// 3 秒
#define MDFLAG_DIR_AND_DOME_LUM_SW_ELP_4s					4		// 4 秒
#define MDFLAG_DIR_AND_DOME_LUM_SW_ELP_5s					5		// 5 秒
#define MDFLAG_DIR_AND_DOME_LUM_SW_ELP_6s					6		// 6 秒
#define MDFLAG_DIR_AND_DOME_LUM_SW_ELP_7s					7		// 7 秒
#define MDFLAG_DIR_AND_DOME_LUM_SW_ELP_8s					8		// 8 秒
#define MDFLAG_DIR_AND_DOME_LUM_SW_ELP_9s					9		// 9 秒
#define MDFLAG_DIR_AND_DOME_LUM_SW_ELP_10s					10		// 10 秒
#define MDFLAG_DIR_AND_DOME_LUM_SW_ELP_Default				MDFLAG_DIR_AND_DOME_LUM_SW_ELP_2s

// “0434”，Dec，解码场景适配；
#define MDFLAG_DECO_SCENE_ADAPT_SimpleTexture				0		// 简单纹理场景
#define MDFLAG_DECO_SCENE_ADAPT_ComplexTexture				1		// 复杂纹理场景
#define MDFLAG_DECO_SCENE_ADAPT_Default						MDFLAG_DECO_SCENE_ADAPT_SimpleTexture

// “0435”，Dec，定时触发解码时长；
#define MDFLAG_ON_TIME_TRIG_DECO_UNIT_MS					100		// 定时触发解码时长单位：100ms
#define MDFLAG_ON_TIME_TRIG_DECO_0ms						0		// 0ms
#define MDFLAG_ON_TIME_TRIG_DECO_100ms						1		// 100ms
#define MDFLAG_ON_TIME_TRIG_DECO_200ms						2		// 200ms
#define MDFLAG_ON_TIME_TRIG_DECO_300ms						3		// 300ms
// ...
#define MDFLAG_ON_TIME_TRIG_DECO_9800ms						98		// 9800ms
#define MDFLAG_ON_TIME_TRIG_DECO_9900ms						99		// 9900ms
#define MDFLAG_ON_TIME_TRIG_DECO_Default					MDFLAG_ON_TIME_TRIG_DECO_0ms

// “0501”，Dec，上电指示功能；
#define MDFLAG_INDICA_PwrAlert_Disable						0		// 禁止上电指示功能，扫描器上电自启后喇叭和LED灯不会发出提示信息
#define MDFLAG_INDICA_PwrAlert_Enable						1		// 使能上电指示功能，扫描器上电自启后喇叭和LED灯会发出提示信息
#define MDFLAG_INDICA_PwrAlert_Default						MDFLAG_INDICA_PwrAlert_Enable

// “0502”，Dec，LED指示功能；
#define MDFLAG_INDICA_Led_Disable							0		// 解码成功后LED灯不会闪烁
#define MDFLAG_INDICA_Led_Enable							1		// 解码成功后LED灯会闪烁一下
#define MDFLAG_INDICA_Led_Default							MDFLAG_INDICA_Led_Enable

// “0503”，Dec，蜂鸣器指示功能；
#define MDFLAG_INDICA_Beep_Disable							0		// 解码成功后蜂鸣器不会鸣叫
#define MDFLAG_INDICA_Beep_Enable							1		// 解码成功后蜂鸣器会鸣叫一声
#define MDFLAG_INDICA_Beep_Default							MDFLAG_INDICA_Beep_Enable

// “0504”，Dec，蜂鸣器鸣叫时长功能（单位：10ms）；
#define MDFLAG_INDICA_BeepTime_Unit_10ms					10		// 蜂鸣器鸣叫时长单位10ms
#define MDFLAG_INDICA_BeepTime_50ms							5		// 蜂鸣器鸣叫时长50ms
#define MDFLAG_INDICA_BeepTime_60ms							6		// 蜂鸣器鸣叫时长60ms
#define MDFLAG_INDICA_BeepTime_100ms						10		// 蜂鸣器鸣叫时长100ms
#define MDFLAG_INDICA_BeepTime_Default						MDFLAG_INDICA_BeepTime_50ms

// “0505”，Dec，蜂鸣器音量（占空比）功能（参数3~9仅对MP8608有效）；
#define MDFLAG_INDICA_Beep_Vol_Low							0		// 蜂鸣器以较低音量进行提示
#define MDFLAG_INDICA_Beep_Vol_Mid							1		// 蜂鸣器以适中音量进行提示
#define MDFLAG_INDICA_Beep_Vol_High							2		// 蜂鸣器以较高音量进行提示
#define MDFLAG_INDICA_Beep_Vol_Low_2						3		// 蜂鸣器以更低音量进行提示（低-2，15%）
#define MDFLAG_INDICA_Beep_Vol_Low_3						4		// 蜂鸣器以更低音量进行提示（低-3，10%）
#define MDFLAG_INDICA_Beep_Vol_Low_4						5		// 蜂鸣器以更低音量进行提示（低-4，8%）
#define MDFLAG_INDICA_Beep_Vol_Low_5						6		// 蜂鸣器以更低音量进行提示（低-5，6%）
#define MDFLAG_INDICA_Beep_Vol_Low_6						7		// 蜂鸣器以更低音量进行提示（低-6，4%）
#define MDFLAG_INDICA_Beep_Vol_Low_7						8		// 蜂鸣器以更低音量进行提示（低-7，2%）
#define MDFLAG_INDICA_Beep_Vol_Low_8						9		// 蜂鸣器以更低音量进行提示（低-8，1%）
#define MDFLAG_INDICA_Beep_Vol_Default						MDFLAG_INDICA_Beep_Vol_High

// “0506”，Dec，蜂鸣器音调功能；
#define MDFLAG_INDICA_Beep_Tone_Level_0						0		// 蜂鸣器0级音调进行提示
#define MDFLAG_INDICA_Beep_Tone_Level_1						1		// 蜂鸣器1级音调进行提示
#define MDFLAG_INDICA_Beep_Tone_Level_2						2		// 蜂鸣器2级音调进行提示
#define MDFLAG_INDICA_Beep_Tone_Level_3						3		// 蜂鸣器3级音调进行提示
#define MDFLAG_INDICA_Beep_Tone_Level_Default				MDFLAG_INDICA_Beep_Tone_Level_2

// “0507”，Dec，振动器指示功能；
#define MDFLAG_INDICA_Vibrator_Disable						0		// 解码成功后不进行振动
#define MDFLAG_INDICA_Vibrator_Enable						1		// 解码成功后会振动一下
#define MDFLAG_INDICA_Vibrator_Default						MDFLAG_INDICA_Vibrator_Enable

// “0508”，Dec，电源指示灯功能；
#define MDFLAG_Indica_PowerLEDCtrl_Forbid					0		// 禁止控制电源指示灯
#define MDFLAG_Indica_PowerLEDCtrl_Allow					1		// 允许控制电源指示灯
#define MDFLAG_Indica_PowerLEDCtrl_Default					MDFLAG_Indica_PowerLEDCtrl_Allow

// “0509”，Dec，指示，静音模式控制功能；
#define MDFLAG_Indica_SilentMode_Off						0		// 静音模式关闭
#define MDFLAG_Indica_SilentMode_On							1		// 静音模式开启
#define MDFLAG_Indica_SilentMode_Default					MDFLAG_Indica_SilentMode_Off

// “0510”，Dec，指示，音效模式控制功能；
#define MDFLAG_Indica_Beep_Sound_Mode_0						0		// 蜂鸣器音效模式 0，0505 默认值 + 0506 默认值
#define MDFLAG_Indica_Beep_Sound_Mode_1						1		// 蜂鸣器音效模式 1，2 级音调，中音量提示
#define MDFLAG_Indica_Beep_Sound_Mode_2						2		// 蜂鸣器音效模式 2，0 级音调，高音量提示
#define MDFLAG_Indica_Beep_Sound_Mode_3						3		// 蜂鸣器音效模式 3，0 级音调，中音量提示
#define MDFLAG_Indica_Beep_Sound_Mode_4						4		// 蜂鸣器音效模式 4，一维码解码成功提示“0505 默认值 + 0506 默认值”，二维码解码成功提示“0 级音调，高音量提示”。
#define MDFLAG_Indica_Beep_Sound_Mode_Default				MDFLAG_Indica_Beep_Sound_Mode_0

// “0511”，Dec，指示，解码成功提示，解码结果输出距离上一个解码成功提示与解码结果输出时延；
#define MDFLAG_IndicaTx_Dly_Unit_10ms						10		// 解码成功提示，传输解码结果距离上一个解码成功提示与传输解码输出时延单位：10ms
#define MDFLAG_IndicaTx_Dly_0ms								0		// 解码成功提示，传输解码结果距离上一个解码成功提示与传输解码输出时延：0ms
#define MDFLAG_IndicaTx_Dly_10ms							1		// 解码成功提示，传输解码结果距离上一个解码成功提示与传输解码输出时延：10ms
#define MDFLAG_IndicaTx_Dly_20ms							2		// 解码成功提示，传输解码结果距离上一个解码成功提示与传输解码输出时延：20ms
#define MDFLAG_IndicaTx_Dly_30ms							3		// 解码成功提示，传输解码结果距离上一个解码成功提示与传输解码输出时延：30ms
#define MDFLAG_IndicaTx_Dly_40ms							4		// 解码成功提示，传输解码结果距离上一个解码成功提示与传输解码输出时延：40ms
#define MDFLAG_IndicaTx_Dly_50ms							5		// 解码成功提示，传输解码结果距离上一个解码成功提示与传输解码输出时延：50ms
#define MDFLAG_IndicaTx_Dly_60ms							6		// 解码成功提示，传输解码结果距离上一个解码成功提示与传输解码输出时延：60ms
// ...
#define MDFLAG_IndicaTx_Dly_99990ms							9999	// 解码成功提示，传输解码结果距离上一个解码成功提示与传输解码输出时延：99990ms
#define MDFLAG_IndicaTx_Dly_Default							MDFLAG_IndicaTx_Dly_0ms

// “0512”，Dec，指示，语音播放重新计算延时功能；
#define MDFLAG_Indica_Voice_RstCnt_Dly_Unit_100ms			100		// 语音播报完成后重置计算单位时间 100ms，重置计数
#define MDFLAG_Indica_Voice_RstCnt_Dly_None					0		// 关闭语音播放功能
#define MDFLAG_Indica_Voice_RstCnt_Dly_100ms				1		// 语音播报完成后 100ms，重置计数
#define MDFLAG_Indica_Voice_RstCnt_Dly_200ms				2		// 语音播报完成后 200ms，重置计数
// ...
#define MDFLAG_Indica_Voice_RstCnt_Dly_2000ms				20		// 语音播报完成后 1000ms，重置计数
#define MDFLAG_Indica_Voice_RstCnt_Dly_9900ms				99		// 语音播报完成后 9900ms，重置计数
#define MDFLAG_Indica_Voice_RstCnt_Dly_Default				MDFLAG_Indica_Voice_RstCnt_Dly_None

// “0601”，Dec，自动感应红外传感器功能；
#define MDFLAG_Infrad_Sensor_Disable						0		// 失能红外传感器功能
#define MDFLAG_Infrad_Sensor_Enable							1		// 使能红外传感器功能
#define MDFLAG_Infrad_Sensor_Default						MDFLAG_Infrad_Sensor_Disable

// “0602”，Dec，自动感应操作模式功能；
#define MDFLAG_Infrad_Mode_InStand							0		// 在架
#define MDFLAG_Infrad_Mode_Continue							1		// 不在架
#define MDFLAG_Infrad_Mode_Default							MDFLAG_Infrad_Mode_InStand

// “0603”，Dec，自动感应保持时长功能（单位：1s）；
#define MDFLAG_AutoDete_StdbyDura_Unit_1s					1000	// 自动感应保持时长单位1s
#define MDFLAG_AutoDete_StdbyDura_4s						4		// 自动感应保持时长4s
#define MDFLAG_AutoDete_StdbyDura_Default					MDFLAG_AutoDete_StdbyDura_4s

// “0604”，Dec，(非AI产品下)自动感应纹理检测开关功能，或(AI产品下)自动感应灵敏度开关功能；
#define MDFLAG_AutoDete_TextureSwitch_Disable				0		// (非AI产品下)失能自动感应纹理检测开关功能
#define MDFLAG_AutoDete_TextureSwitch_Enable				1		// (非AI产品下)使能自动感应纹理检测开关功能
#define MDFLAG_AutoDete_Sensitivity_High					0		// (AI产品下)自动感应灵敏度 - 高灵敏度
#define MDFLAG_AutoDete_Sensitivity_Low						1		// (AI产品下)自动感应灵敏度 - 低灵敏度
#define MDFLAG_AutoDete_TextureSwitch_Default				MDFLAG_AutoDete_TextureSwitch_Disable

// “0605”，Dec，自动感应，照明开关功能；
#define MDFLAG_ADIMG_LUMIN_AlwaysOff						0		// 补光灯常灭
#define MDFLAG_ADIMG_LUMIN_OnInDarkness						1		// 弱光下开启照明
#define MDFLAG_ADIMG_LUMIN_AlwaysOn							2		// 补光灯常亮
#define MDFLAG_ADIMG_LUMIN_Default							MDFLAG_ADIMG_LUMIN_AlwaysOn

// “0606”，Dec，自动感应纹理检测启动时间间隔功能；
#define MDFLAG_AutoDete_TxtureInter_0s						0		// 纹理检测启动时间间隔0s
#define MDFLAG_AutoDete_TxtureInter_5s						1		// 纹理检测启动时间间隔5s
#define MDFLAG_AutoDete_TxtureInter_10s						2		// 纹理检测启动时间间隔10s
#define MDFLAG_AutoDete_TxtureInter_30s						3		// 纹理检测启动时间间隔30s
#define MDFLAG_AutoDete_TxtureInter_60s						4		// 纹理检测启动时间间隔60s
#define MDFLAG_AutoDete_TxtureInter_Infinity				5		// 纹理检测启动时间间隔无穷大
#define MDFLAG_AutoDete_TxtureInter_Default					MDFLAG_AutoDete_TxtureInter_10s

// “0607”，Dec，休眠自动感应图像拍摄时间间隔功能；
#define MDFLAG_AutoDete_SleepFrmInter_250ms					1		// 休眠自动感应图像拍摄时间间隔250ms
#define MDFLAG_AutoDete_SleepFrmInter_500ms					2		// 休眠自动感应图像拍摄时间间隔500ms
#define MDFLAG_AutoDete_SleepFrmInter_1000ms				3		// 休眠自动感应图像拍摄时间间隔1000ms
#define MDFLAG_AutoDete_SleepFrmInter_Default				MDFLAG_AutoDete_SleepFrmInter_250ms

// “0608”，Dec，红外自动感应模式报文上传开关功能；
#define MDFLAG_AutoDete_InfraredMsgSwitch_Disable			0		// 失能红外自动感应模式报文上传开关功能
#define MDFLAG_AutoDete_InfraredMsgSwitch_Enable			1		// 使能红外自动感应模式报文上传开关功能
#define MDFLAG_AutoDete_InfraredMsgSwitch_Default			MDFLAG_AutoDete_InfraredMsgSwitch_Disable

// “0609”，Dec，红外感应过程中的补光灯控制；
#define MDFLAG_AutoDete_InfraredLumCtrl_Disable				0		// 红外感应过程中，禁止开启补光灯
#define MDFLAG_AutoDete_InfraredLumCtrl_Enable				1		// 红外感应过程中，允许开启补光灯
#define MDFLAG_AutoDete_InfraredLumCtrl_Default				MDFLAG_AutoDete_InfraredLumCtrl_Disable

// “0610”，Dec，红外补光灯感应灵敏度等级；
#define MDFLAG_AutoDete_IRLumDete_SenLevel_0				0		// 灵敏度等级 0 亮斑与视窗大小百分比：0%
#define MDFLAG_AutoDete_IRLumDete_SenLevel_10				1		// 灵敏度等级 1 亮斑与视窗大小百分比：10%
#define MDFLAG_AutoDete_IRLumDete_SenLevel_20				2		// 灵敏度等级 2 亮斑与视窗大小百分比：20%
#define MDFLAG_AutoDete_IRLumDete_SenLevel_30				3		// 灵敏度等级 3 亮斑与视窗大小百分比：30%
#define MDFLAG_AutoDete_IRLumDete_SenLevel_40				4		// 灵敏度等级 4 亮斑与视窗大小百分比：40%
#define MDFLAG_AutoDete_IRLumDete_SenLevel_50				5		// 灵敏度等级 5 亮斑与视窗大小百分比：50%
#define MDFLAG_AutoDete_IRLumDete_SenLevel_60				6		// 灵敏度等级 6 亮斑与视窗大小百分比：60%
#define MDFLAG_AutoDete_IRLumDete_SenLevel_70				7		// 灵敏度等级 7 亮斑与视窗大小百分比：70%
#define MDFLAG_AutoDete_IRLumDete_SenLevel_80				8		// 灵敏度等级 8 亮斑与视窗大小百分比：80%
#define MDFLAG_AutoDete_IRLumDete_SenLevel_90				9		// 灵敏度等级 9 亮斑与视窗大小百分比：90%
#define MDFLAG_AutoDete_IRLumDete_SenLevel_100				10		// 灵敏度等级 10 亮斑与视窗大小百分比：100%
#define MDFLAG_AutoDete_IRLumDete_SenLevel_Default			MDFLAG_AutoDete_IRLumDete_SenLevel_10

// “0700”，Dec，无线产品参数，无线网络类型功能；
#define MDFLAG_Wireless_NetworkType_PointCoordinator		0		// 点协式无线网络类型
#define MDFLAG_Wireless_NetworkType_DistributionCoordinator	1		// 分布式无线网络类型
#define MDFLAG_Wireless_NetworkType_Default					MDFLAG_Wireless_NetworkType_DistributionCoordinator

// “0701”，Dec，无线产品参数，无线网络信道功能；
#define MDFLAG_Wireless_433_Channel_6						6		// 无线网络6号信道
#define MDFLAG_Wireless_433_Channel_Default					MDFLAG_Wireless_433_Channel_6

// “0702”，Dec，无线产品参数，无线手持ID功能；
#define MDFLAG_Wireless_HandheldID_1						1		// 无线产品参数，无线手持ID为1
#define MDFLAG_Wireless_HandheldID_Default					MDFLAG_Wireless_HandheldID_1

// “0703”，Dec，无线产品参数，无线发送输出功率功能；
#define MDFLAG_Wireless_PowerGain_10dbm						1		// 输出功率增益为 +10dbm
#define MDFLAG_Wireless_PowerGain_7dbm						2		// 输出功率增益为 +7dbm
#define MDFLAG_Wireless_PowerGain_5dbm						3		// 输出功率增益为 +5dbm
#define MDFLAG_Wireless_PowerGain_0dbm						4		// 输出功率增益为 0dbm
#define MDFLAG_Wireless_PowerGain_s5dbm						5		// 输出功率增益为 -5dbm
#define MDFLAG_Wireless_PowerGain_s10dbm					6		// 输出功率增益为 -10dbm
#define MDFLAG_Wireless_PowerGain_s15dbm					7		// 输出功率增益为 -15dbm
#define MDFLAG_Wireless_PowerGain_s23dbm					8		// 输出功率增益为 -23dbm
#define MDFLAG_Wireless_PowerGain_Default					MDFLAG_Wireless_PowerGain_10dbm

// “0704”，Dec，无线产品参数，无线跳频设置功能；
#define MDFLAG_Wireless_FreqHoppingCtr_Disable				0		// 失能无线跳频设置功能
#define MDFLAG_Wireless_FreqHoppingCtr_Enable				1		// 使能无线跳频设置功能
#define MDFLAG_Wireless_FreqHoppingCtr_Default				MDFLAG_Wireless_FreqHoppingCtr_Disable

// “0705”，Dec，无线产品参数，数据批处理功能控制；
#define MDFLAG_Wireless_DataBatch_None						0		// 关闭
#define MDFLAG_Wireless_DataBatch_OutRange					1		// 区外存储
#define MDFLAG_Wireless_DataBatch_Standard					2		// 标准存储
#define MDFLAG_Wireless_DataBatch_StandManual				3		// 标准存储（数据需手动清除）
#define MDFLAG_Wireless_DataBatch_Default					MDFLAG_Wireless_DataBatch_None

// “0706”，Dec，无线产品参数，无线连接状态提示音功能；
#define MDFLAG_Wireless_ReconnectBeep_None					0		// 关闭提示
#define MDFLAG_Wireless_ReconnectBeep_Once					1		// 仅提示一次（连接状态发生改变时）
#define MDFLAG_Wireless_ReconnectBeep_Repeat				2		// 重复提示（设备未连接时）
#define MDFLAG_Wireless_ReconnectBeep_Default				MDFLAG_Wireless_ReconnectBeep_Once

// “0707”，Hex，无线产品参数，无线连接检测间隔（单位：1s）；
#define MDFLAG_Wireless_ConnectCheckDelay_Unit_1s			1000	// 无线连接检测间隔单位1s
#define MDFLAG_Wireless_ConnectCheckDelay_3s				3		// 无线连接检测间隔3s
#define MDFLAG_Wireless_ConnectCheckDelay_Default			MDFLAG_Wireless_ConnectCheckDelay_3s

// “0708”，Dec，无线产品参数，关机时保存数据功能；
#define MDFLAG_Wireless_LowPowerSaveData_Disable			0		// 失能关机时保存数据功能
#define MDFLAG_Wireless_LowPowerSaveData_Enable				1		// 使能关机时保存数据功能
#define MDFLAG_Wireless_LowPowerSaveData_Default			MDFLAG_Wireless_LowPowerSaveData_Enable

// “0709”，Dec，无线产品参数，低电量提示控制；
#define MDFLAG_Wireless_LowPowerIndication_OFF				0		// 关闭提示
#define MDFLAG_Wireless_LowPowerIndication_ON				1		// 开启提示
#define MDFLAG_Wireless_LowPowerIndication_Default			MDFLAG_Wireless_LowPowerIndication_ON

// “0710”，Hex，无线产品参数，进入休眠延时功能（单位：1min）（暂时已不生效）；
#define MDFLAG_Wireless_EnterSleepDelay_Unit_1min			60000	// 无线产品参数，进入休眠延时单位1min
#define MDFLAG_Wireless_EnterSleepDelay_15min				0x0f	// 进入休眠延时15min
#define MDFLAG_Wireless_EnterSleepDelay_Default				MDFLAG_Wireless_EnterSleepDelay_15min

// “0711”，Hex，无线产品参数，缓存数据发送延时功能（单位：1min）（暂未实现）；
#define MDFLAG_Wireless_SendBuffDataDelay_Unit_1min			60000	// 无线产品参数，缓存数据发送延时单位1min
#define MDFLAG_Wireless_SendBuffDataDelay_15min				0x0f	// 发送缓存数据时延15min
#define MDFLAG_Wireless_SendBuffDataDelay_Default			MDFLAG_Wireless_SendBuffDataDelay_15min

// “0712”，Dec，无线产品参数，手持枪无线通信地址；
// 手持枪无线通信地址，默认取值范围0000~9999
#define MDFLAG_Wireless_433_HHUAddress_0					0
#define MDFLAG_Wireless_433_HHUAddress_1					0
#define MDFLAG_Wireless_433_HHUAddress_Default				(MDFLAG_Wireless_433_HHUAddress_0, MDFLAG_Wireless_433_HHUAddress_1)

// “0713”，Dec，无线产品参数，无线传输延时（单位：1s）；
#define MDFLAG_Wireless_SendDataDelay_Unit_1s				1000	// 无线产品参数，无线传输延时单位1s
#define MDFLAG_Wireless_SendDataDelay_2s					2		// 无线传输延时2s
#define MDFLAG_Wireless_SendDataDelay_Default				MDFLAG_Wireless_SendDataDelay_2s

// “0714”，Dec，无线产品参数，基座有线传输结果通知（暂未实现）；
#define MDFLAG_Wireless_WiredFeedback_Disable				0		// 失能基座有线传输结果通知
#define MDFLAG_Wireless_WiredFeedback_Enable				1		// 使能基座有线传输结果通知
#define MDFLAG_Wireless_WiredFeedback_Default				MDFLAG_Wireless_WiredFeedback_Disable

// “0715”，Dec，无线产品参数，手持终端持续待机超时关机功能；
#define MDFLAG_Wireless_IdleTimeoutShutdown_Forbid			0		// 禁止待机超时关机
#define MDFLAG_Wireless_IdleTimeoutShutdown_5min			1		// 持续待机5分钟后关机
#define MDFLAG_Wireless_IdleTimeoutShutdown_10min			2		// 持续待机10分钟后关机
#define MDFLAG_Wireless_IdleTimeoutShutdown_20min			3		// 持续待机20分钟后关机
#define MDFLAG_Wireless_IdleTimeoutShutdown_30min			4		// 持续待机30分钟后关机
#define MDFLAG_Wireless_IdleTimeoutShutdown_1Hours			5		// 持续待机1小时后关机
#define MDFLAG_Wireless_IdleTimeoutShutdown_3Hours			6		// 持续待机3小时后关机
#define MDFLAG_Wireless_IdleTimeoutShutdown_8Hours			7		// 持续待机8小时后关机
#define MDFLAG_Wireless_IdleTimeoutShutdown_10Hours			8		// 持续待机10小时后关机
#define MDFLAG_Wireless_IdleTimeoutShutdown_12Hours			9		// 持续待机12小时后关机
#define MDFLAG_Wireless_IdleTimeoutShutdown_Default			MDFLAG_Wireless_IdleTimeoutShutdown_Forbid

// “0716”，Hex，无线产品参数，手持枪地址（高位）；（已被0712取代）
#define MDFLAG_Wireless_HandheldAddrHigh_None				0x00
#define MDFLAG_Wireless_HandheldAddrHigh_Default			MDFLAG_Wireless_HandheldAddrHigh_None

// “0717”，Dec，无线产品参数，基座地址（低位），不直接进行参数设置，是绑定、加入时保存基座信息。
#define MDFLAG_Wireless_CradleAddrLow_None					0x00
#define MDFLAG_Wireless_CradleAddrLow_Default				MDFLAG_Wireless_CradleAddrLow_None

// “0718”，Dec，无线产品参数，基座地址（高位），不直接进行参数设置，是绑定、加入时保存基座信息。
#define MDFLAG_Wireless_CradleAddrHigh_None					0x00
#define MDFLAG_Wireless_CradleAddrHigh_Default				MDFLAG_Wireless_CradleAddrHigh_None

// “0719”，系统暂未使用

// “0720”，Dec，无线产品参数，VALID/INVALID/LOCK/UNLOCK 协议；
#define MDFLAG_Wireless_ProtocolVILU_Disable				0		// 失能VALID/INVALID/LOCK/UNLOCK 协议
#define MDFLAG_Wireless_ProtocolVILU_Enable					1		// 使能VALID/INVALID/LOCK/UNLOCK 协议
#define MDFLAG_Wireless_ProtocolVILU_Default				MDFLAG_Wireless_ProtocolVILU_Enable

// “0721”，Dec，无线产品参数，等待指令时延功能（单位：1ms）；
#define MDFLAG_Wireless_WaitCommandDelay_Unit_1ms			1		// 等待指令时延单位1ms
#define MDFLAG_Wireless_WaitCommandDelay_0ms				0		// 等待指令时延0ms
#define MDFLAG_Wireless_WaitCommandDelay_Default			MDFLAG_Wireless_WaitCommandDelay_0ms

// “0740”，Dec，无线产品参数，蓝牙工作模式功能；
#define MDFLAG_Wireless_BT_Mode_HID							0		// HID键盘
#define MDFLAG_Wireless_BT_Mode_SPP							1		// SPP
#define MDFLAG_Wireless_BT_Mode_BASE						2		// 基座
#define MDFLAG_Wireless_BT_Mode_LES							3		// LES
#define MDFLAG_Wireless_BT_Mode_Default						MDFLAG_Wireless_BT_Mode_BASE

// “0741”，Dec，无线产品参数，蓝牙键盘布局功能；
#define MDFLAG_Wireless_BT_KBLayout_USA						0		// 美式键盘布局
#define MDFLAG_Wireless_BT_KBLayout_Turkish_F				1		// 土耳其F式键盘布局
#define MDFLAG_Wireless_BT_KBLayout_Turkish_Q				2		// 土耳其Q式键盘布局
#define MDFLAG_Wireless_BT_KBLayout_French					3		// 法式键盘布局
#define MDFLAG_Wireless_BT_KBLayout_Italian					4		// 意大利式键盘布局
#define MDFLAG_Wireless_BT_KBLayout_Spanish					5		// 西班牙式键盘布局
#define MDFLAG_Wireless_BT_KBLayout_Slovak					6		// 斯洛伐克式键盘布局
#define MDFLAG_Wireless_BT_KBLayout_Denmark					7		// 丹麦式键盘布局
#define MDFLAG_Wireless_BT_KBLayout_Japanese				8		// 日式键盘布局
#define MDFLAG_Wireless_BT_KBLayout_German					9		// 德式键盘布局
#define MDFLAG_Wireless_BT_KBLayout_Belgian					10		// 比利时式键盘布局
#define MDFLAG_Wireless_BT_KBLayout_Russian					11		// 俄罗斯式键盘布局
#define MDFLAG_Wireless_BT_KBLayout_Czech					12		// 捷克式键盘布局
#define MDFLAG_Wireless_BT_KBLayout_Thai					13		// 泰文式键盘布局
#define MDFLAG_Wireless_BT_KBLayout_Hungary					14		// 匈牙利式键盘布局
#define MDFLAG_Wireless_BT_KBLayout_Default					MDFLAG_Wireless_BT_KBLayout_USA

// “0742”，Dec，无线产品参数，蓝牙传输延迟功能（单位：1ms）；
#define MDFLAG_Wireless_BT_KBDelay_Unit_1ms					1		// 蓝牙传输延迟单位1ms
#define MDFLAG_Wireless_BT_KBDelay_0ms						0		// 蓝牙传输延迟0ms
#define MDFLAG_Wireless_BT_KBDelay_Default					MDFLAG_Wireless_BT_KBDelay_0ms

// “0743”，Dec，无线产品参数，无线连接检测功能；
#define MDFLAG_Wireless_ConnectDetec_Forbid					0		// 禁止无线连接检测
#define MDFLAG_Wireless_ConnectDetec_Allow					1		// 允许无线连接检测
#define MDFLAG_Wireless_ConnectDetec_Default				MDFLAG_Wireless_ConnectDetec_Allow

// “0744”，Dec，无线产品参数，手持枪地址自动分配功能；
#define MDFLAG_Wireless_433_HHUAddrAuto_DISABLE				0		// 禁止无线连接检测
#define MDFLAG_Wireless_433_HHUAddrAuto_ENABLE				1		// 允许无线连接检测
#define MDFLAG_Wireless_433_HHUAddrAuto_Default				MDFLAG_Wireless_433_HHUAddrAuto_ENABLE

// “0745”，Dec，无线产品参数，手持枪BT HID模式下，字符间传输时延功能（单位：1ms）；
#define MDFLAG_Wireless_BT_HID_ComPortSped_Unit_1ms			1		// 手持枪BT HID模式下，字符间传输时延单位1ms
#define MDFLAG_Wireless_BT_HID_ComPortSped_10ms				10		// 手持枪BT HID模式下，字符间传输时延10ms
#define MDFLAG_Wireless_BT_HID_ComPortSped_Default			MDFLAG__Wireless_BT_HID_ComPortSped_10ms

// “0746”，Dec，无线产品参数，低功耗模式下无线模块的控制功能；
#define MDFLAG_Wireless_LowPowerWirelessCtrl_Forbid			0		// 禁止关闭无线模块
#define MDFLAG_Wireless_LowPowerWirelessCtrl_30Secs			1		// 30秒后关闭无线模块
#define MDFLAG_Wireless_LowPowerWirelessCtrl_15Mins			2		// 15分钟后关闭无线模块
#define MDFLAG_Wireless_LowPowerWirelessCtrl_30Mins			3		// 30分钟后关闭无线模块
#define MDFLAG_Wireless_LowPowerWirelessCtrl_1Hour			4		// 1小时后关闭无线模块
#define MDFLAG_Wireless_LowPowerWirelessCtrl_2Hours			5		// 2小时后关闭无线模块
#define MDFLAG_Wireless_LowPowerWirelessCtrl_3Hours			6		// 3小时后关闭无线模块
#define MDFLAG_Wireless_LowPowerWirelessCtrl_4Hours			7		// 4小时后关闭无线模块
#define MDFLAG_Wireless_LowPowerWirelessCtrl_8Hours			8		// 8小时后关闭无线模块
#define MDFLAG_Wireless_LowPowerWirelessCtrl_10Hours		9		// 10小时后关闭无线模块
#define MDFLAG_Wireless_LowPowerWirelessCtrl_12Hours		10		// 12小时后关闭无线模块
#define MDFLAG_Wireless_LowPowerWirelessCtrl_Default		MDFLAG_Wireless_LowPowerWirelessCtrl_Forbid

// “0747”，Dec，无线产品参数，433MHz手持枪的无线组网协议类型
#define MDFLAG_Wireless_433_RF_Protocol_ALOHA				0		// 无线组网协议，ALOHA
#define MDFLAG_Wireless_433_RF_Protocol_TDM_M1				1		// 无线组网协议，TDM-M1
#define MDFLAG_Wireless_433_RF_Protocol_Default				MDFLAG_Wireless_433_RF_Protocol_ALOHA

// “0748”，Dec，无线产品参数，无线信道自动分配功能；
#define MDFLAG_Wireless_ChannelAutoAssign_Disable			0		// 失能 无线信道自动分配功能
#define MDFLAG_Wireless_ChannelAutoAssign_Enable			1		// 使能 无线信道自动分配功能
#define MDFLAG_Wireless_ChannelAutoAssign_Default			MDFLAG_Wireless_ChannelAutoAssign_Enable

// “0798”，Hex，无线产品参数，基座通信地址；（暂未使用，还需要重新完善其功能）
// 基座通信地址，默认取值范围0x00~0xff

// “0799”，Dec，无线产品参数，433无线频段的国家码；(此处宏定义的值，必须与无线模块 CC1110 固件中的相关定义严格统一)
#define MDFLAG_Wireless_433_RegionCode_Europe				0		// 欧洲地区
#define MDFLAG_Wireless_433_RegionCode_China				1		// 中国地区
#define MDFLAG_Wireless_433_RegionCode_Turkey				2		// 土耳其地区
#define MDFLAG_Wireless_433_RegionCode_Default				MDFLAG_Wireless_433_RegionCode_China

// “0804”，Dec，无线产品参数，基座无线输出功率水平；
#define MDFLAG_WirelessCradle_PowerLevel_1					1		// 1级水平
#define MDFLAG_WirelessCradle_PowerLevel_2					2		// 2级水平
#define MDFLAG_WirelessCradle_PowerLevel_3					3		// 3级水平
#define MDFLAG_WirelessCradle_PowerLevel_4					4		// 4级水平
#define MDFLAG_WirelessCradle_PowerLevel_5					5		// 5级水平
#define MDFLAG_WirelessCradle_PowerLevel_6					6		// 6级水平
#define MDFLAG_WirelessCradle_PowerLevel_7					7		// 7级水平
#define MDFLAG_WirelessCradle_PowerLevel_8					8		// 8级水平
#define MDFLAG_WirelessCradle_PowerLevel_Default			MDFLAG_WirelessCradle_PowerLevel_1

// “0901”，Dec，USB接口，USB设备类型；
/* USBType（0901）这个参数，为了保证自定义设备用选项99，需要将最大值设为99。
 * 但是中间的参数是无效的，这时需要添加一个有效性的判断。
 * 宏定义MDFLAG_USB_ValidMax用来界定除99外有效选项的上限。
 * 注意添加新的选项是，要配合修改MDFLAG_USB_ValidMax。*/
	#define MDFLAG_USB_Type_PC								0		// 扫描器会被识别为 USB HID键盘仿真设备
	#define MDFLAG_USB_Type_MAC								1		// 扫描器会被识别为仅支持Apple Mac的HID键盘仿真设备（当前的实现已与参数0合并，即与HID键盘无异）
#if (MACR_PRODUCT_FORM_EMBEDDED == 1)
	#define MDFLAG_USB_CustVisualCOM						2		// 扫描器端口会被识别为用户自定义USB虚拟串口
#else
	#define MDFLAG_USB_VisualCOM							2		// 扫描器端口会被识别为USB虚拟串口
#endif
	#define MDFLAG_USB_Simple_COM							3		// Simple COM Port Emulation
	#define MDFLAG_USB_OPOS									4		// 自定义HID设备，收发数据格式符合OPOS/JPOS规范。

#if (MACR_PRODUCT_FORM_EMBEDDED == 1)
	#define MDFLAG_USB_VisualCOM							5		// 扫描器端口会被识别为USB虚拟串口
#else
	#define MDFLAG_USB_CustVisualCOM						5		// 扫描器端口会被识别为用户自定义USB虚拟串口
#endif
	#define MDFLAG_USB_IBM_Hand_Held						6		// USB_IBM_Hand_Held
	#define MDFLAG_USB_HIDMSD								7		// 扫描器会被识别为一个USB存储设备，使用的内存空间来自设备的Flash区；同时该设备工作为HID键盘
	#define MDFLAG_USB_CustomBulkTransfer					8		// 客户定制功能
	#define MDFLAG_USB_CustomHIDKeyboardHSBulk				9		// 客户定制功能
	#define MDFLAG_USB_Type_PC_Legacy_Keyboard				10		// USB HID键盘(兼容使用旧版本协议的主机)
	#define MDFLAG_USB_VCOM_OPOS							11      // 自定义VCOM设备类型，收发数据格式符合OPOS/JPOS规范。
	#define MDFLAG_USB_CustomHIDIntHSBulk					12		// 自定义复合设备类型，自定义HID中断传输 + 自定义高速批量传输
	#define MDFLAG_USB_CustomHIDKbdHIDCust					13		// 自定义复合设备类型，HID键盘 + HID自定义传输
	#define MDFLAG_USB_CustomHIDKbdVisualCOM				14		// 自定义复合设备类型，HID键盘 + 虚拟串口
	#define MDFLAG_USB_Net									15		// USB 虚拟网口
	#define MDFLAG_USB_CustomHIDKbdNet						16		// 自定义复合设备类型，HID键盘 + Net
	#define MDFLAG_USB_CustomVisualCOMNet					17		// 自定义复合设备类型，虚拟串口 + Net

#if (MACR_PRODUCT_FORM_EMBEDDED == 1)
	#if ((MACR_PRODUCT_AD_IR100 == 1) || (MACR_PRODUCT_AD_IR101 == 1))
		#define MDFLAG_USB_ValidMax							MDFLAG_USB_CustomVisualCOMNet
	#else
		#define MDFLAG_USB_ValidMax							MDFLAG_USB_VisualCOM
	#endif
#else
	#define MDFLAG_USB_ValidMax								MDFLAG_USB_CustomHIDKbdVisualCOM
#endif
	#define MDFLAG_USB_Custom								99		// 自定义端口，可用于自研拍图软件或高速升级，需配合相应的高速驱动

	#define MDFLAG_USB_Type_Default							MDFLAG_USB_Type_PC

// “0902”，Dec，USB接口，USB键盘类型；
#define MDFLAG_USB_Layout_CHN_USA							0		// 美式键盘布局
#define MDFLAG_USB_Layout_TKYF								1		// 土耳其F式键盘布局
#define MDFLAG_USB_Layout_TKYQ								2		// 土耳其Q时键盘布局
#define MDFLAG_USB_Layout_FRN								3		// 法式键盘布局
#define MDFLAG_USB_Layout_ITA								4		// 意大利式键盘布局
#define MDFLAG_USB_Layout_SPA								5		// 西班牙式键盘布局
#define MDFLAG_USB_Layout_SLK								6		// 斯洛伐克式键盘布局
#define MDFLAG_USB_Layout_DMK								7		// 丹麦式键盘布局
#define MDFLAG_USB_Layout_JAP								8		// 日式键盘布局
#define MDFLAG_USB_Layout_GER								9		// 德式键盘布局
#define MDFLAG_USB_Layout_BEL								10		// 比利时式键盘布局
#define MDFLAG_USB_Layout_RUS								11		// 俄罗斯式键盘布局
#define MDFLAG_USB_Layout_CZE								12		// 捷克式键盘布局
#define MDFLAG_USB_Layout_THAI								13		// 泰式键盘布局
#define MDFLAG_USB_Layout_Hungary							14		// 匈牙利式键盘布局
#define MDFLAG_USB_Layout_Swiss_German						15		// 瑞士德语键盘布局
#define MDFLAG_USB_Layout_Portuguese						16		// 葡萄牙文键盘布局
#define MDFLAG_USB_Layout_Ukrainian							17		// 乌克兰语键盘布局
#define MDFLAG_USB_Layout_Polish214							18		// 波兰语键盘布局
#define MDFLAG_USB_Layout_Vietnam							19		// 越南语键盘布局
#define MDFLAG_USB_Layout_RussianAndroid					20		// 适配安卓设备的俄罗斯键盘布局
#define MDFLAG_USB_Layout_VietnamTelexInputMethod			21		// 越南语键盘布局，搭配Telex输入法使用
#define MDFLAG_USB_Layout_LatinAmericaSpanish				22		// 拉丁美洲西班牙键盘布局，需要配合win10输入法西班牙语（拉丁美洲）输入法(ESP)使用
#define MDFLAG_USB_Layout_Default							MDFLAG_USB_Layout_CHN_USA

// “0903”，Dec，USB接口，相邻键值发送时延功能；
#define MDFLAG_USB_InterKeyDly_0ms							0		// USB接口，相邻键值发送时延0ms
#define MDFLAG_USB_InterKeyDly_5ms							1		// USB接口，相邻键值发送时延5ms
#define MDFLAG_USB_InterKeyDly_10ms							2		// USB接口，相邻键值发送时延10ms
#define MDFLAG_USB_InterKeyDly_20ms							3		// USB接口，相邻键值发送时延20ms
#define MDFLAG_USB_InterKeyDly_40ms							4		// USB接口，相邻键值发送时延40ms
#define MDFLAG_USB_InterKeyDly_60ms							5		// USB接口，相邻键值发送时延60ms
#define MDFLAG_USB_InterKeyDly_Default						MDFLAG_USB_InterKeyDly_0ms

// “0904”，Dec，USB接口，数字键功能；
#define MDFLAG_USB_NumKey_Disable							0		// 输出字母和数字时，将传送字母键盘对应的键码
#define MDFLAG_USB_NumKey_Num								1		// 输出数字时，将传送数字小键盘对应的键码
#define MDFLAG_USB_NumKey_AltKypd							2		// 输出字母和数字时，将模拟 Alt＋数字键的方式
#define MDFLAG_USB_NumKey_AltKypd_GBK						3		// 输出字母和数字时，将模拟 Alt＋数字键的方式，然后根据GBK编码输出对应的字符
#define MDFLAG_USB_NumKey_AltKypd_BIG5						4		// 输出字母和数字时，将模拟 Alt＋数字键的方式，然后根据BIG5编码输出对应的字符
#define MDFLAG_USB_NumKey_AltKypd_THAI						5		// 输出字母和数字时，将模拟 Alt＋数字键的方式，然后根据THAI编码输出对应的字符
#define MDFLAG_USB_NumKey_RawHexInStrings					6		// 直接输出编码对应的16进制值
#define MDFLAG_USB_NumKey_Uos_UnicodeInDec					7		// 模拟数字键的方式，然后根据Unicode编码输出对应字符，以十进制的方式( 统信Uos系统专用 )
#define MDFLAG_USB_NumKey_Default							MDFLAG_USB_NumKey_Disable

// “0905”，Dec，USB接口，组合键功能；
#define MDFLAG_USB_FuncKey_Range_00H_1FH_NonPrintable_Char	0		// [00H - 1FH]范围的字符，按非打印字符方式输出
#define MDFLAG_USB_FuncKey_Mindeo							1		// [00H - 1FH]范围的字符，按MINDEO定义的功能键方式输出
#define MDFLAG_USB_FuncKey_Customize_PPN					2		// [00H - 1FH]范围的字符，按PPN码定制映射方式输出
#define MDFLAG_USB_FuncKey_Range_00H_1FH_Forbid				3		// [00H - 1FH]范围的字符，禁止输出
#define MDFLAG_USB_FuncKey_DatalogicCtrlChar00				4		// [00H - 1FH]、[80H - A0H]范围的字符，按Datalogic Control Character 00映射方式输出
#define MDFLAG_USB_FuncKey_Default							MDFLAG_USB_FuncKey_Mindeo

// “0906”，Dec，USB接口，USB主机轮询间隔时间；
#define MDFLAG_USB_HOST_POLLING_INTER_1ms					0		// 主机端口轮询间隔为1ms
#define MDFLAG_USB_HOST_POLLING_INTER_2ms					1		// 主机端口轮询间隔为2ms
#define MDFLAG_USB_HOST_POLLING_INTER_5ms					2		// 主机端口轮询间隔为5ms
#define MDFLAG_USB_HOST_POLLING_INTER_8ms					3		// 主机端口轮询间隔为8ms
#define MDFLAG_USB_HOST_POLLING_INTER_Default				MDFLAG_USB_HOST_POLLING_INTER_1ms

// “0907”，Dec，USB接口，键盘键值发送方式；
// 不连续的键值发送方式：在每发送一个键值后，会插入一个Release键，然后在发送下一个键值
// 连续的键值发送方式：在每发送一个键值后，紧接着发送下一个键值，
//					 当字符间间隔+主机轮询时间大于等于主机键盘重复延时（32ms）时，才会在键值后面插入一个Release键
#define MDFLAG_USB_HID_Keypacket_Send_Mode_Discontinuous	0		// 不连续的键值发送方式
#define MDFLAG_USB_HID_Keypacket_Send_Mode_Continuous		1		// 连续的键值发送方式
#define MDFLAG_USB_HID_Keypacket_Send_Mode_Default			MDFLAG_USB_HID_Keypacket_Send_Mode_Discontinuous

// “0908”，USB接口，0x0D映射的回车键方式；
#define MDFLAG_USB_ENTER_MODE_AccordingFuncKeyConfig		0		// 遵循当前0905参数值的功能键映射
#define MDFLAG_USB_ENTER_MODE_ForceAlplabetEnter			1		// 字母键盘回车方式
#define MDFLAG_USB_ENTER_MODE_ForceNumericEnter				2		// 数字键盘回车方式
#define MDFLAG_USB_ENTER_MODE_ForceAlt013Enter				3		// alt+013回车 方式
#define MDFLAG_USB_ENTER_MODE_Default						MDFLAG_USB_ENTER_MODE_AccordingFuncKeyConfig

// “0909”，USB接口，键盘模式下组合键的释放方式；
#define MDFLAG_USB_Mod_Keys_Release_Mode_Same_Time			0		// 组合键同时释放全部按键
#define MDFLAG_USB_Mod_Keys_Release_Mode_After_Normal_Keys	1		// 组合键先释放普通按键，延迟释放修饰键
#define MDFLAG_USB_Mod_Keys_Release_Mode_Default			MDFLAG_USB_Mod_Keys_Release_Mode_Same_Time

// “1001”，Dec，二维条码，识读功能；
// 取值范围0~9，需要确认详细参数的意义
#define MDFLAG_2D_SymbologiesRead_AsPara					0		// 遵循各类2D条码的自定义识读设定
#define MDFLAG_2D_SymbologiesRead_DisableAll				1		// 失能全部2D条码的识读设定
#define MDFLAG_2D_SymbologiesRead_EnableAll					2		// 使能全部2D条码的识读设定
#define MDFLAG_2D_SymbologiesRead_PDF417					3		// 仅PDF417开启
#define MDFLAG_2D_SymbologiesRead_QRCode					4		// 仅QR码开启
#define MDFLAG_2D_SymbologiesRead_DataMatrix				5		// 仅Data Matrix开启
#define MDFLAG_2D_SymbologiesRead_MaxiCode					6		// 仅MaxiCode开启
#define MDFLAG_2D_SymbologiesRead_AztecCode					7		// 仅AztecCode开启
#define MDFLAG_2D_SymbologiesRead_CSCode					8		// 仅汉信码开启
#define MDFLAG_2D_SymbologiesRead_9							9		// 目前还没有查找到“1001D09”的具体意义
#define MDFLAG_2D_SymbologiesRead_Default					MDFLAG_2D_SymbologiesRead_AsPara

// “1002”，Dec，直接零部件标识条码，识读功能；
#define MDFLAG_DPM_Read_Disable								0		// 失能识读接零部件标识条码
#define MDFLAG_DPM_Read_Enable								1		// 使能识读接零部件标识条码
#define MDFLAG_DPM_Read_Default								MDFLAG_DPM_Read_Disable

// “1003”，Dec，同图多条码，识读功能；
#define MDFLAG_Multi_Symbols_Disable						0		// 失能同图多条码识读
#define MDFLAG_Multi_Symbols_Only_1D						1		// 同图多条码只识读 1D 条码
#define MDFLAG_Multi_Symbols_Only_2D						2		// 同图多条码只识读 2D 条码
#define MDFLAG_Multi_Symbols_1D_And_2D						3		// 同图多条码同时识读 1D + 2D 条码
#define MDFLAG_Multi_Symbols_Default						MDFLAG_Multi_Symbols_Disable

// “1004”，Dec，垂直居中解码模式，识读功能；
#define MDFLAG_CenteringDecoMode_None						0		// 关闭中心解码模式
#define MDFLAG_CenteringDecoMode_Vertical					1		// 垂直居中解码模式
#define MDFLAG_CenteringDecoMode_Near						2		// 邻近中心解码模式
#define MDFLAG_CenteringDecoMode_Default					MDFLAG_CenteringDecoMode_None

// “1005”，Dec，一维条码，识读功能；
#define MDFLAG_1D_SymbologiesRead_AsPara					0		// 遵循各类1D条码的自定义识读设定
#define MDFLAG_1D_SymbologiesRead_DisableAll				1		// 失能全部1D条码的识读设定
#define MDFLAG_1D_SymbologiesRead_EnableAll					2		// 使能全部1D条码的识读设定
#define MDFLAG_1D_SymbologiesRead_Default					MDFLAG_1D_SymbologiesRead_AsPara

// “1006”，Dec，同图多码，固定条码数量选项；
#define MDFLAG_Multi_Symbols_ConsNum_Op_Disable				0		// 失能同图多码固定条码数量，即解到多少就输出多少
#define MDFLAG_Multi_Symbols_ConsNum_Op_Enable				1		// 使能同图多码固定条码数量，即解到小于预期数量不输出，等于预期数量输出，大于预期数量按预期数量输出丢弃其他
#define MDFLAG_Multi_Symbols_ConsNum_Op_Default				MDFLAG_Multi_Symbols_ConsNum_Op_Disable

// “1007”，Dec，移动屏（手机屏）条码，识读功能；
#define MDFLAG_Mobile_Screen_Read_Disable					0		// 失能识读移动屏（手机屏）条码
#define MDFLAG_Mobile_Screen_Read_Enable					1		// 使能识读移动屏（手机屏）条码
#define MDFLAG_Mobile_Screen_Read_Default					MDFLAG_Mobile_Screen_Read_Enable

// “1010”，Dec，AI 复原模型控制选项；
#define MDFLAG_AI_Restore_Model_Disable						0		// 失能 AI 复原模型
#define MDFLAG_AI_Restore_Model_Enable						1		// 使能 AI 复原模型
#define MDFLAG_AI_Restore_Model_Default						MDFLAG_AI_Restore_Model_Disable

// “1011”，Dec，AI ROI 模型控制选项；
#define MDFLAG_AI_ROI_Model_320x320							0		// 320x320 AI ROI 模型
#define MDFLAG_AI_ROI_Model_512x640							1		// 512x640 AI ROI 模型
#define MDFLAG_AI_ROI_Model_Default							MDFLAG_AI_ROI_Model_320x320

// “1012”，Dec，高温保护阈值控制选项；
#define MDFLAG_High_Temp_Protect_Threshold_Disable			0		// 高温保护阈值禁用
#define MDFLAG_High_Temp_Protect_Threshold_85Entry70Exit	8570	// 85℃触发高温保护，70℃解除高温保护
#define MDFLAG_High_Temp_Protect_Threshold_Default			MDFLAG_High_Temp_Protect_Threshold_Disable

// UPCA
// “1101”，Dec，UPC-A码相关，UPC-A识读功能；
#define MDFLAG_UPCA_Read_Disable							0		// 失能识读UPC-A码
#define MDFLAG_UPCA_Read_Enable								1		// 使能识读UPC-A码
#define MDFLAG_UPCA_Read_Default							MDFLAG_CodeRead_Enabled

// “1102”，Dec，UPC-A码相关，校验符确认功能；
#define MDFLAG_UPCA_ChkDigVer_Disable						0		// 失能检测校验符
#define MDFLAG_UPCA_ChkDigVer_Enable						1		// 使能检测校验符
#define MDFLAG_UPCA_ChkDigVer_Default						MDFLAG_UPCA_ChkDigVer_Enable

// “1103”，Dec，UPC-A码相关，校验符传送功能；
#define MDFLAG_UPCA_ChkDigTrans_Disable						0		// 失能传送校验符
#define MDFLAG_UPCA_ChkDigTrans_Enable						1		// 使能传送校验符
#define MDFLAG_UPCA_ChkDigTrans_Default						MDFLAG_UPCA_ChkDigTrans_Enable

// “1104”，Hex，UPC-A码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“1104H41”字符A，H41
#define MDFLAG_UPCA_CodeID_A								('A')
#define MDFLAG_UPCA_CodeID_Default							MDFLAG_UPCA_CodeID_A

// “1105”，Dec，UPC-A码相关，插入字符串组功能；
#define MDFLAG_UPCA_InsertGrpSel_None						0		// 不插入字符串组
#define MDFLAG_UPCA_InsertGrpSel_Default					MDFLAG_UPCA_InsertGrpSel_None

// “1106”，Dec，UPC-A码相关，附加码功能；
#define MDFLAG_UPCA_SuplDig_None							0		// 无附加码功能
#define MDFLAG_UPCA_SuplDig_2								1		// 可以识读带2位附加码的UPC-A码
#define MDFLAG_UPCA_SuplDig_5								2		// 可以识读带5位附加码的UPC-A条码
#define MDFLAG_UPCA_SuplDig_25								3		// 可以识读带2位或5位附加码的UPC-A码
#define MDFLAG_UPCA_SuplDig_Only							4		// 只解带附加码的UPC-A条码
#define MDFLAG_UPCA_SuplDig_Default							MDFLAG_UPCA_SuplDig_None

// “1107”，Dec，UPC-A码相关，截去、扩展功能；
#define MDFLAG_UPCA_TrunExp_None							0		// 不进行截去、扩展
#define MDFLAG_UPCA_TrunExp_Trun							1		// 截去前导“0”，UPC-A数据字符的前导一位或多位“0”将被截去
#define MDFLAG_UPCA_TrunExp_Exp								2		// 输出数据扩展成 13位的 EAN-13 码
#define MDFLAG_UPCA_TrunExp_Default							MDFLAG_UPCA_TrunExp_None

// “1108”，Hex，UPC-A码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_UPCA_AimID_0									0xFF		// 最多支持8个字符
#define MDFLAG_UPCA_AimID_1									0xFF
#define MDFLAG_UPCA_AimID_2									0xFF
#define MDFLAG_UPCA_AimID_3									0xFF
#define MDFLAG_UPCA_AimID_4									0xFF
#define MDFLAG_UPCA_AimID_5									0xFF
#define MDFLAG_UPCA_AimID_6									0xFF
#define MDFLAG_UPCA_AimID_7									0xFF
#define MDFLAG_UPCA_AimID_Default							(MDFLAG_UPCA_AimID_0, MDFLAG_UPCA_AimID_1, MDFLAG_UPCA_AimID_2, MDFLAG_UPCA_AimID_3, \
															 MDFLAG_UPCA_AimID_4, MDFLAG_UPCA_AimID_5, MDFLAG_UPCA_AimID_6, MDFLAG_UPCA_AimID_7)

// UPCE
// “1201”，Dec，UPC-E码相关，UPC-E识读功能；
#define MDFLAG_UPCE_Read_Disable							0		// 失能识读UPC-E码
#define MDFLAG_UPCE_Read_Enable								1		// 使能识读UPC-E码
#define MDFLAG_UPCE_Read_Default							MDFLAG_CodeRead_Enabled

// “1202”，Dec，UPC-E码相关，校验符确认功能；
#define MDFLAG_UPCE_ChkDigVer_Disable						0		// 失能检测校验符
#define MDFLAG_UPCE_ChkDigVer_Enable						1		// 使能检测校验符
#define MDFLAG_UPCE_ChkDigVer_Default						MDFLAG_UPCE_ChkDigVer_Enable

// “1203”，Dec，UPC-E码相关，校验符传送功能；
#define MDFLAG_UPCE_ChkDigTrans_Disable						0		// 失能传送校验符
#define MDFLAG_UPCE_ChkDigTrans_Enable						1		// 使能传送校验符
#define MDFLAG_UPCE_ChkDigTrans_Default						MDFLAG_UPCE_ChkDigTrans_Enable

// “1204”，Hex，UPC-E码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“1204H44”字符D，H44
#define MDFLAG_UPCE_CodeID_D								('D')
#define MDFLAG_UPCE_CodeID_Default							MDFLAG_UPCE_CodeID_D

// “1205”，Dec，UPC-E码相关，插入字符串组功能；
#define MDFLAG_UPCE_InsertGrpSel_None						0		// 不插入字符串组
#define MDFLAG_UPCE_InsertGrpSel_Default					MDFLAG_UPCE_InsertGrpSel_None

// “1206”，Dec，UPC-E码相关，附加码功能；
#define MDFLAG_UPCE_SuplDig_None							0		// 无附加码功能
#define MDFLAG_UPCE_SuplDig_2								1		// 可以识读带2位附加码的UPC-E码
#define MDFLAG_UPCE_SuplDig_5								2		// 可以识读带5位附加码的UPC-E码
#define MDFLAG_UPCE_SuplDig_25								3		// 可以识读带2位或5位附加码的UPC-E码
#define MDFLAG_UPCE_SuplDig_Only							4		// 只解带附加码的UPC-E条码
#define MDFLAG_UPCE_SuplDig_Default							MDFLAG_UPCE_SuplDig_None

// “1207”，Dec，UPC-E码相关，截去、扩展功能；
#define MDFLAG_UPCE_TrunExp_None							0		// 不进行截去、扩展
#define MDFLAG_UPCE_TrunExp_Trun							1		// 截去前导“0”，UPC-A数据字符的前导一位或多位“0”将被截去
#define MDFLAG_UPCE_TrunExp_ExpE13							2		// 输出数据扩展成 13位的 EAN-13 码
#define MDFLAG_UPCE_TrunExp_ExpUA							3		// 输出数据扩展成 12位的 UPC-A 码
#define MDFLAG_UPCE_TrunExp_Default							MDFLAG_UPCE_TrunExp_None

// “1208”，Hex，UPC-E码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_UPCE_AimID_0									0xFF		// 最多支持8个字符
#define MDFLAG_UPCE_AimID_1									0xFF
#define MDFLAG_UPCE_AimID_2									0xFF
#define MDFLAG_UPCE_AimID_3									0xFF
#define MDFLAG_UPCE_AimID_4									0xFF
#define MDFLAG_UPCE_AimID_5									0xFF
#define MDFLAG_UPCE_AimID_6									0xFF
#define MDFLAG_UPCE_AimID_7									0xFF
#define MDFLAG_UPCE_AimID_Default							(MDFLAG_UPCE_AimID_0, MDFLAG_UPCE_AimID_1, MDFLAG_UPCE_AimID_2, MDFLAG_UPCE_AimID_3, \
															 MDFLAG_UPCE_AimID_4, MDFLAG_UPCE_AimID_5, MDFLAG_UPCE_AimID_6, MDFLAG_UPCE_AimID_7)

// EAN-13
// “1301”，Dec，EAN-13码相关，EAN-13识读功能；
#define MDFLAG_EAN13_Read_Disable							0		// 失能识读EAN-13码
#define MDFLAG_EAN13_Read_Enable							1		// 使能识读EAN-13码
#define MDFLAG_EAN13_Read_Default							MDFLAG_CodeRead_Enabled

// “1302”，Dec，EAN-13码相关，校验符确认功能；
#define MDFLAG_EAN13_ChkDigVer_Disable						0		// 失能检测校验符
#define MDFLAG_EAN13_ChkDigVer_Enable						1		// 使能检测校验符
#define MDFLAG_EAN13_ChkDigVer_Default						MDFLAG_EAN13_ChkDigVer_Enable

// “1303”，Dec，EAN-13码相关，校验符传送功能；
#define MDFLAG_EAN13_ChkDigTrans_Disable					0		// 失能传送校验符
#define MDFLAG_EAN13_ChkDigTrans_Enable						1		// 使能传送校验符
#define MDFLAG_EAN13_ChkDigTrans_Default					MDFLAG_EAN13_ChkDigTrans_Enable

// “1304”，Hex，EAN13码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“1304H41”字符A，H41
#define MDFLAG_EAN13_CodeID_A								('A')
#define MDFLAG_EAN13_CodeID_Default							MDFLAG_EAN13_CodeID_A

// “1305”，Dec，EAN-13码相关，插入字符串组功能；
#define MDFLAG_EAN13_InsertGrpSel_None						0		// 不插入字符串组
#define MDFLAG_EAN13_InsertGrpSel_Default					MDFLAG_EAN13_InsertGrpSel_None

// “1306”，Dec，EAN-13码相关，附加码功能；
#define MDFLAG_EAN13_SuplDig_None							0		// 无附加码功能
#define MDFLAG_EAN13_SuplDig_2								1		// 可以识读带2位附加码的EAN-13码
#define MDFLAG_EAN13_SuplDig_5								2		// 可以识别带5位附加码的EAN-13码
#define MDFLAG_EAN13_SuplDig_25								3		// 可以识别带2位或5位附加码的EAN-13码
#define MDFLAG_EAN13_SuplDig_Only							4		// 只解带附加码的EAN13条码
#define MDFLAG_EAN13_SuplDig_Default						MDFLAG_EAN13_SuplDig_None

// “1307”，Dec，EAN-13码相关，ISBN/ISSN转换功能；
#define MDFLAG_EAN13_ISBSN_Disable							0		// 失能转换
#define MDFLAG_EAN13_ISBSN_Enable							1		// 使能转换，ISBN 是对前导码为“978”的 EAN-13 码进行转换得到 10 位字符数据；ISSN 是对前导码为“977”的 EAN-13码进行转换得到的 8 位字符数据。
#define MDFLAG_EAN13_ISBSN_Default							MDFLAG_EAN13_ISBSN_Disable

// “1309”，Hex，EAN13码，ISBN/ISSN自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“1104H42”字符B，H42
#define MDFLAG_EAN13_ISSN_ISBN_ID_B							('B')
#define MDFLAG_EAN13_ISSN_ISBN_ID_Default					MDFLAG_EAN13_ISSN_ISBN_ID_B

// “1310”，Hex，EAN13码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_EAN13_AimID_0								0xFF		// 最多支持8个字符
#define MDFLAG_EAN13_AimID_1								0xFF
#define MDFLAG_EAN13_AimID_2								0xFF
#define MDFLAG_EAN13_AimID_3								0xFF
#define MDFLAG_EAN13_AimID_4								0xFF
#define MDFLAG_EAN13_AimID_5								0xFF
#define MDFLAG_EAN13_AimID_6								0xFF
#define MDFLAG_EAN13_AimID_7								0xFF
#define MDFLAG_EAN13_AimID_Default							(MDFLAG_EAN13_AimID_0, MDFLAG_EAN13_AimID_1, MDFLAG_EAN13_AimID_2, MDFLAG_EAN13_AimID_3, \
															 MDFLAG_EAN13_AimID_4, MDFLAG_EAN13_AimID_5, MDFLAG_EAN13_AimID_6, MDFLAG_EAN13_AimID_7)

// EAN-8
// “1401”，Dec，EAN-8码相关，EAN-8识读功能；
#define MDFLAG_EAN8_Read_Disable							0		// 失能识读EAN-8码
#define MDFLAG_EAN8_Read_Enable								1		// 使能识读EAN-8码
#define MDFLAG_EAN8_Read_Default							MDFLAG_CodeRead_Enabled

// “1402”，Dec，EAN-8码相关，校验符确认功能；
#define MDFLAG_EAN8_ChkDigVer_Disable						0		// 失能检测校验符
#define MDFLAG_EAN8_ChkDigVer_Enable						1		// 使能检测校验符
#define MDFLAG_EAN8_ChkDigVer_Default						MDFLAG_EAN8_ChkDigVer_Enable

// “1403”，Dec，EAN-8码相关，校验符传送功能；
#define MDFLAG_EAN8_ChkDigTrans_Disable						0		// 失能传送校验符
#define MDFLAG_EAN8_ChkDigTrans_Enable						1		// 使能传送校验符
#define MDFLAG_EAN8_ChkDigTrans_Default						MDFLAG_EAN8_ChkDigTrans_Enable

// “1404”，Hex，EAN-8码，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“1404H43”字符C，H43
#define MDFLAG_EAN8_CodeID_C								('C')
#define MDFLAG_EAN8_CodeID_Default							MDFLAG_EAN8_CodeID_C

// “1405”，Dec，EAN-8码相关，插入字符串组功能；
#define MDFLAG_EAN8_InsertGrpSel_None						0		// 不插入字符串组
#define MDFLAG_EAN8_InsertGrpSel_Default					MDFLAG_EAN8_InsertGrpSel_None

// “1406”，Dec，EAN-8码相关，附加码功能；
#define MDFLAG_EAN8_SuplDig_None							0		// 无附加码功能
#define MDFLAG_EAN8_SuplDig_2								1		// 可以识读带2位附加码的EAN-8码
#define MDFLAG_EAN8_SuplDig_5								2		// 可以识别带5位附加码的EAN-8码
#define MDFLAG_EAN8_SuplDig_25								3		// 可以识别带2位或5位附加码的EAN-8码
#define MDFLAG_EAN8_SuplDig_Only							4		// 只解带附加码的EAN-8条码
#define MDFLAG_EAN8_SuplDig_Default							MDFLAG_EAN8_SuplDig_None

// “1407”，Dec，EAN-8码相关，截去、扩展功能；
#define MDFLAG_EAN8_TrunExp_None							0		// 不进行截去、扩展
#define MDFLAG_EAN8_TrunExp_Trun							1		// 截去前导“0”，UPC-E1数据字符的前导一位或多位“0”将被截去
#define MDFLAG_EAN8_TrunExp_Exp								2		// 输出数据扩展成 13位的 EAN-13 码
#define MDFLAG_EAN8_TrunExp_Default							MDFLAG_EAN8_TrunExp_None

// “1408”，Hex，EAN-8码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_EAN8_AimID_0									0xFF		// 最多支持8个字符
#define MDFLAG_EAN8_AimID_1									0xFF
#define MDFLAG_EAN8_AimID_2									0xFF
#define MDFLAG_EAN8_AimID_3									0xFF
#define MDFLAG_EAN8_AimID_4									0xFF
#define MDFLAG_EAN8_AimID_5									0xFF
#define MDFLAG_EAN8_AimID_6									0xFF
#define MDFLAG_EAN8_AimID_7									0xFF
#define MDFLAG_EAN8_AimID_Default							(MDFLAG_EAN8_AimID_0, MDFLAG_EAN8_AimID_1, MDFLAG_EAN8_AimID_2, MDFLAG_EAN8_AimID_3, \
															 MDFLAG_EAN8_AimID_4, MDFLAG_EAN8_AimID_5, MDFLAG_EAN8_AimID_6, MDFLAG_EAN8_AimID_7)

// Code 39
// “1501”，Dec，Code 39码相关，识读功能；
#define MDFLAG_CODE39_Read_Disable							0		// 失能识读Code 39码
#define MDFLAG_CODE39_Read_Enable							1		// 使能识读Code 39码
#define MDFLAG_CODE39_Read_Default							MDFLAG_CodeRead_Enabled

// “1502”，Dec，Code 39码相关，校验符确认功能；
#define MDFLAG_CODE39_ChkDigVer_Disable						0		// 失能检测校验符
#define MDFLAG_CODE39_ChkDigVer_Enable						1		// 使能检测校验符
#define MDFLAG_CODE39_ChkDigVer_Default						MDFLAG_CODE39_ChkDigVer_Disable

// “1503”，Dec，Code 39码相关，校验符传送功能；
#define MDFLAG_CODE39_ChkDigTrans_Disable					0		// 失能传送校验符
#define MDFLAG_CODE39_ChkDigTrans_Enable					1		// 使能传送校验符
#define MDFLAG_CODE39_ChkDigTrans_Default					MDFLAG_CODE39_ChkDigTrans_Disable

// “1504”，Dec，Code 39码相关，最大码字长度功能；
#define MDFLAG_CODE39_MaxLen_99								99		// 最大码字长度99
#define MDFLAG_CODE39_MaxLen_Default						MDFLAG_CODE39_MaxLen_99

// “1505”，Dec，Code 39码相关，最小码字长度功能；
#define MDFLAG_CODE39_MinLen_1								1		// 最小码字长度1
#define MDFLAG_CODE39_MinLen_Default						MDFLAG_CODE39_MinLen_1

// “1506”，Hex，Code39码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“1506H4D”字符M，H4D
#define MDFLAG_Code39_CodeID_M								('M')
#define MDFLAG_Code39_CodeID_Default						MDFLAG_Code39_CodeID_M

// “1507”，Dec，Code 39码相关，插入字符串组功能；
#define MDFLAG_CODE39_InsertGrpSel_None						0		// 不插入字符串组
#define MDFLAG_CODE39_InsertGrpSel_Default					MDFLAG_CODE39_InsertGrpSel_None

// “1508”，Dec，Code 39码相关，数据传输格式功能；
#define MDFLAG_CODE39_Format_Standard						0		// 输出ASCII码的一一对应的字面翻译字符
#define MDFLAG_CODE39_Format_FullASCII						1		// 输出字符串组合后的ASCII码字符串。组合方式是将每两个字符编码成一个ASCII码，两个字符由特殊字符（$，+，%，/）中的一个和26个英文字母（A－Z）中的一个构成（暂时不懂是什么意思）
#define MDFLAG_CODE39_Format_Default						MDFLAG_CODE39_Format_Standard

// “1509”，Dec，Code 39码相关，起始符/终止符传送功能；
#define MDFLAG_CODE39_StEdTrans_Disable						0		// 不传送起始符/终止符
#define MDFLAG_CODE39_StEdTrans_Enable						1		// 传送起始符/终止符
#define MDFLAG_CODE39_StEdTrans_Default						MDFLAG_CODE39_StEdTrans_Disable

// “1510”，Dec，Code 39码相关，"*"可作数据字符功能；
#define MDFLAG_Code39_AsteAsChar_Disable					0		// 失能"*"可作数据字符选项
#define MDFLAG_Code39_AsteAsChar_Enable						1		// 使能"*"可作数据字符选项
#define MDFLAG_Code39_AsteAsChar_Default					MDFLAG_Code39_AsteAsChar_Disable

// “1511”，Dec，Code 39码相关，39码转32码功能；
#define MDFLAG_CODE39_Code32_Disable						0		// 失能39码转32码选项
#define MDFLAG_CODE39_Code32_Enable							1		// 使能39码转32码选项
#define MDFLAG_CODE39_Code32_Default						MDFLAG_CODE39_Code32_Disable

// “1512”，Dec，Code 39码相关，39_32码的前缀"A"传送功能；
#define MDFLAG_CODE39_Code32Pre_Disable						0		// 失能39_32码的前缀"A"传送
#define MDFLAG_CODE39_Code32Pre_Enable						1		// 使能39_32码的前缀"A"传送
#define MDFLAG_CODE39_Code32Pre_Default						MDFLAG_CODE39_Code32Pre_Disable

// “1513”，Dec，Code 39码相关，Trioptic 39 码识读功能；
#define MDFLAG_CODE39_Trioptic_Disable						0		// 失能识读Trioptic 39码
#define MDFLAG_CODE39_Trioptic_Enable						1		// 使能识读Trioptic 39码
#define MDFLAG_CODE39_Trioptic_Default						MDFLAG_CODE39_Trioptic_Disable

// “1514”，Dec，Code 39码相关，Trioptic 39 码起始符/终止符传送功能；
#define MDFLAG_CODE39_TOStEdTrans_Disable					0		// 失能传送Trioptic 39 码起始符/终止符
#define MDFLAG_CODE39_TOStEdTrans_Enable					1		// 使能传送Trioptic 39 码起始符/终止符
#define MDFLAG_CODE39_TOStEdTrans_Default					MDFLAG_CODE39_TOStEdTrans_Disable

// “1515”，Hex，Code 39码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_CODE39_AimID_0								0xFF		// 最多支持8个字符
#define MDFLAG_CODE39_AimID_1								0xFF
#define MDFLAG_CODE39_AimID_2								0xFF
#define MDFLAG_CODE39_AimID_3								0xFF
#define MDFLAG_CODE39_AimID_4								0xFF
#define MDFLAG_CODE39_AimID_5								0xFF
#define MDFLAG_CODE39_AimID_6								0xFF
#define MDFLAG_CODE39_AimID_7								0xFF
#define MDFLAG_CODE39_AimID_Default							(MDFLAG_CODE39_AimID_0, MDFLAG_CODE39_AimID_1, MDFLAG_CODE39_AimID_2, MDFLAG_CODE39_AimID_3, \
															 MDFLAG_CODE39_AimID_4, MDFLAG_CODE39_AimID_5, MDFLAG_CODE39_AimID_6, MDFLAG_CODE39_AimID_7)

// Interleaved 2 of 5
// “1601”，Dec，交叉25码相关，识读功能；
#define MDFLAG_INTL25_Read_Disable							0		// 失能识读交叉25码
#define MDFLAG_INTL25_Read_Enable							1		// 使能能识读交叉25码
#define MDFLAG_INTL25_Read_Default							MDFLAG_CodeRead_Enabled

// “1602”，Dec，交叉25码相关，校验符确认功能；
#define MDFLAG_INTL25_ChkDigVer_Disable						0		// 不检测校验符
#define MDFLAG_INTL25_ChkDigVer_USS							1		// 采用USS检测校验符
#define MDFLAG_INTL25_ChkDigVer_OPCC						2		// 采用OPCC检测校验符
#define MDFLAG_INTL25_ChkDigVer_Default						MDFLAG_INTL25_ChkDigVer_Disable

// “1603”，Dec，交叉25码相关，校验符传送功能；
#define MDFLAG_INTL25_ChkDigTrans_Disable					0		// 失能传送校验符
#define MDFLAG_INTL25_ChkDigTrans_Enable					1		// 使能传送校验符
#define MDFLAG_INTL25_ChkDigTrans_Default					MDFLAG_INTL25_ChkDigTrans_Disable

// “1604”，Dec，交叉25码相关，最大码字长度功能；
#define MDFLAG_INTL25_MaxLen_99								99		// 最大码字长度99
#define MDFLAG_INTL25_MaxLen_Default						MDFLAG_INTL25_MaxLen_99

// “1605”，Dec，交叉25码相关，最小码字长度功能；
#define MDFLAG_INTL25_MinLen_6								6		// 最小码字长度6
#define MDFLAG_INTL25_MinLen_Default						MDFLAG_INTL25_MinLen_6

// “1606”，Hex，交叉25码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“1606H49”字符I，H49
#define MDFLAG_Intl25_CodeID_I								('I')
#define MDFLAG_Intl25_CodeID_Default						MDFLAG_Intl25_CodeID_I

// “1607”，Dec，交叉25码相关，插入字符串组功能；
#define MDFLAG_INTL25_InsertGrpSel_None						0		// 不插入字符串组
#define MDFLAG_INTL25_InsertGrpSel_Default					MDFLAG_INTL25_InsertGrpSel_None

// “1608”，系统上暂时没有定义

// “1609”，Dec，交叉25码相关，字符长度限制功能；
#define MDFLAG_INTL25_LenRestr_Any							0		// 不限制长度
#define MDFLAG_INTL25_LenRestr_44CharOnly					1		// 仅限44个字符
#define MDFLAG_INTL25_LenRestr_Default						MDFLAG_INTL25_LenRestr_Any

// “1610”，Dec，交叉25码相关，银行格式转换功能；
#define MDFLAG_INTL25_BankFormatConv_Disable				0		// 失能银行格式转换
#define MDFLAG_INTL25_BankFormatConv_Enable					1		// 使能银行格式转换
#define MDFLAG_INTL25_BankFormatConv_Default				MDFLAG_INTL25_BankFormatConv_Disable

// “1611”，Dec，交叉25码相关，银行格式类型功能；
#define MDFLAG_INTL25_BankFormatType_NoTabInsert			0		// 不插入Tab键
#define MDFLAG_INTL25_BankFormatType_ALGUNS					1		// 按照ALGUNS规则插入若干个Tab键
#define MDFLAG_INTL25_BankFormatType_TODOS					2		// 按照TODOS OS规则插入若干个Tab键
#define MDFLAG_INTL25_BankFormatType_Default				MDFLAG_INTL25_BankFormatType_NoTabInsert

// “1612”，Hex，交叉25码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_INTL25_AimID_0								0xFF		// 最多支持8个字符
#define MDFLAG_INTL25_AimID_1								0xFF
#define MDFLAG_INTL25_AimID_2								0xFF
#define MDFLAG_INTL25_AimID_3								0xFF
#define MDFLAG_INTL25_AimID_4								0xFF
#define MDFLAG_INTL25_AimID_5								0xFF
#define MDFLAG_INTL25_AimID_6								0xFF
#define MDFLAG_INTL25_AimID_7								0xFF
#define MDFLAG_INTL25_AimID_Default							(MDFLAG_INTL25_AimID_0, MDFLAG_INTL25_AimID_1, MDFLAG_INTL25_AimID_2, MDFLAG_INTL25_AimID_3, \
															 MDFLAG_INTL25_AimID_4, MDFLAG_INTL25_AimID_5, MDFLAG_INTL25_AimID_6, MDFLAG_INTL25_AimID_7)

// Industrial 2 of 5
// “1701”，Dec，工业25码相关，识读功能；
#define MDFLAG_INDS25_Read_Disable							0		// 失能识读工业25码
#define MDFLAG_INDS25_Read_Enable							1		// 使能识读工业25码
#define MDFLAG_INDS25_Read_Default							MDFLAG_CodeRead_Disabled

// “1702”，Dec，工业25码相关，最大码字长度功能；
#define MDFLAG_INDS25_MaxLen_99								99		// 最大码字长度99
#define MDFLAG_INDS25_MaxLen_Default						MDFLAG_INDS25_MaxLen_99

// “1703”，Dec，工业25码相关，最小码字长度功能；
#define MDFLAG_INDS25_MinLen_4								4		// 最小码字长度4
#define MDFLAG_INDS25_MinLen_Default						MDFLAG_INDS25_MinLen_4

// “1704”，Hex，工业25码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“1705H48”字符H，H48
#define MDFLAG_Indus25_CodeID_H								('H')
#define MDFLAG_Indus25_CodeID_Default						MDFLAG_Indus25_CodeID_H

// “1705”，Dec，工业25码相关，插入字符串组功能；
#define MDFLAG_INDS25_InsertGrpSel_None						0		// 不插入字符串组
#define MDFLAG_INDS25_InsertGrpSel_Default					MDFLAG_INDS25_InsertGrpSel_None

// “1706”，Hex，工业25码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_INDS25_AimID_0								0xFF		// 最多支持8个字符
#define MDFLAG_INDS25_AimID_1								0xFF
#define MDFLAG_INDS25_AimID_2								0xFF
#define MDFLAG_INDS25_AimID_3								0xFF
#define MDFLAG_INDS25_AimID_4								0xFF
#define MDFLAG_INDS25_AimID_5								0xFF
#define MDFLAG_INDS25_AimID_6								0xFF
#define MDFLAG_INDS25_AimID_7								0xFF
#define MDFLAG_INDS25_AimID_Default							(MDFLAG_INDS25_AimID_0, MDFLAG_INDS25_AimID_1, MDFLAG_INDS25_AimID_2, MDFLAG_INDS25_AimID_3, \
															 MDFLAG_INDS25_AimID_4, MDFLAG_INDS25_AimID_5, MDFLAG_INDS25_AimID_6, MDFLAG_INDS25_AimID_7)

// Matrix 2 of 5
// “1801”，Dec，矩阵25码相关，识读功能；
#define MDFLAG_MATR25_Read_Disable							0		// 失能识读矩阵25码
#define MDFLAG_MATR25_Read_Enable							1		// 使能识读矩阵25码
#define MDFLAG_MATR25_Read_Default							MDFLAG_CodeRead_Enabled

// “1802”，Dec，矩阵25码相关，校验符确认功能；
#define MDFLAG_MATR25_ChkDigVer_Disable						0		// 失能检测校验符
#define MDFLAG_MATR25_ChkDigVer_Enable						1		// 使能检测校验符
#define MDFLAG_MATR25_ChkDigVer_Default						MDFLAG_MATR25_ChkDigVer_Disable

// “1803”，Dec，矩阵25码相关，校验符传送功能；
#define MDFLAG_MATR25_ChkDigTrans_Disable					0		// 失能传送校验符
#define MDFLAG_MATR25_ChkDigTrans_Enable					1		// 使能传送校验符
#define MDFLAG_MATR25_ChkDigTrans_Default					MDFLAG_MATR25_ChkDigTrans_Disable

// “1804”，Dec，矩阵25码相关，最大码字长度功能；
#define MDFLAG_MATR25_MaxLen_99								99		// 最大码字长度99
#define MDFLAG_MATR25_MaxLen_Default						MDFLAG_MATR25_MaxLen_99

// “1805”，Dec，矩阵25码相关，最小码字长度功能；
#define MDFLAG_MATR25_MinLen_6								6		// 最小码字长度6
#define MDFLAG_MATR25_MinLen_Default						MDFLAG_MATR25_MinLen_6

// “1806”，Hex，矩阵25码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“1806H58”字符X，H58
#define MDFLAG_Matrx25_CodeID_X								('X')
#define MDFLAG_Matrx25_CodeID_Default						MDFLAG_Matrx25_CodeID_X

// “1807”，Dec，矩阵25码相关，插入字符串组功能；
#define MDFLAG_MATR25_InsertGrpSel_None						0		// 不插入字符串组
#define MDFLAG_MATR25_InsertGrpSel_Default					MDFLAG_MATR25_InsertGrpSel_None

// “1808”，Hex，矩阵25码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_MATR25_AimID_0								0xFF		// 最多支持8个字符
#define MDFLAG_MATR25_AimID_1								0xFF
#define MDFLAG_MATR25_AimID_2								0xFF
#define MDFLAG_MATR25_AimID_3								0xFF
#define MDFLAG_MATR25_AimID_4								0xFF
#define MDFLAG_MATR25_AimID_5								0xFF
#define MDFLAG_MATR25_AimID_6								0xFF
#define MDFLAG_MATR25_AimID_7								0xFF
#define MDFLAG_MATR25_AimID_Default							(MDFLAG_MATR25_AimID_0, MDFLAG_MATR25_AimID_1, MDFLAG_MATR25_AimID_2, MDFLAG_MATR25_AimID_3, \
															 MDFLAG_MATR25_AimID_4, MDFLAG_MATR25_AimID_5, MDFLAG_MATR25_AimID_6, MDFLAG_MATR25_AimID_7)

// codabar
// “1901”，Dec，库德巴码相关，识读功能；
#define MDFLAG_CODABAR_Read_Disable							0		// 失能识读库德巴码
#define MDFLAG_CODABAR_Read_Enable							1		// 使能识读库德巴码
#define MDFLAG_CODABAR_Read_Default							MDFLAG_CodeRead_Enabled

// “1902”，Dec，库德巴码相关，校验符确认功能；
#define MDFLAG_CODABAR_ChkDigVer_Disable					0		// 失能检测校验符
#define MDFLAG_CODABAR_ChkDigVer_Enable						1		// 使能检测校验符
#define MDFLAG_CODABAR_ChkDigVer_Default					MDFLAG_CODABAR_ChkDigVer_Disable

// “1903”，Dec，库德巴码相关，校验符传送功能；
#define MDFLAG_CODABAR_ChkDigTrans_Disable					0		// 失能传送校验符
#define MDFLAG_CODABAR_ChkDigTrans_Enable					1		// 使能传送校验符
#define MDFLAG_CODABAR_ChkDigTrans_Default					MDFLAG_CODABAR_ChkDigTrans_Disable

// “1904”，Dec，库德巴码相关，最大码字长度功能；
#define MDFLAG_CODABAR_MaxLen_99							99		// 最大码字长度99
#define MDFLAG_CODABAR_MaxLen_Default						MDFLAG_CODABAR_MaxLen_99

// “1905”，Dec，库德巴码相关，最小码字长度功能；
#define MDFLAG_CODABAR_MinLen_4								4		// 最小码字长度4
#define MDFLAG_CODABAR_MinLen_Default						MDFLAG_CODABAR_MinLen_4

// “1906”，Hex，库德巴码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“1906H4E”字符N，H4E
#define MDFLAG_Codabar_CodeID_N								('N')
#define MDFLAG_Codabar_CodeID_Default						MDFLAG_Codabar_CodeID_N

// “1907”，Dec，库德巴码相关，插入字符串组功能；
#define MDFLAG_CODABAR_InsertGrpSel_None					0		// 不插入字符串组
#define MDFLAG_CODABAR_InsertGrpSel_Default					MDFLAG_CODABAR_InsertGrpSel_None

// “1908”，Dec，库德巴码相关，起始符/终止符类型功能；
#define MDFLAG_CODABAR_StEdType_ABCD						0		// 起始符/终止符为ABCD
#define MDFLAG_CODABAR_StEdType_abcd						1		// 起始符/终止符为abcd
#define MDFLAG_CODABAR_StEdType_TNE							2		// 起始符为ABCD，终止符为TN*E
#define MDFLAG_CODABAR_StEdType_tne							3		// 起始符为abcd，终止符为tn*e
#define MDFLAG_CODABAR_StEdType_Default						MDFLAG_CODABAR_StEdType_ABCD

// “1909”，Dec，库德巴码相关，起始符/终止符传送功能；
#define MDFLAG_CODABAR_StEdTrans_Disable					0		// 失能传送起始符/终止符
#define MDFLAG_CODABAR_StEdTrans_Enable						1		// 使能传送起始符/终止符
#define MDFLAG_CODABAR_StEdTrans_Default					MDFLAG_CODABAR_StEdTrans_Disable

// “1910”，Dec，库德巴码相关，起始符/终止符相同功能；
#define MDFLAG_CODABAR_StartEndCharEq_Disable				0		// 失能起始符/终止符相同
#define MDFLAG_CODABAR_StartEndCharEq_Enable				1		// 使能起始符/终止符相同
#define MDFLAG_CODABAR_StartEndCharEq_Default				MDFLAG_CODABAR_StartEndCharEq_Disable

// “1911”，Hex，库德巴码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_CODABAR_AimID_0								0xFF		// 最多支持8个字符
#define MDFLAG_CODABAR_AimID_1								0xFF
#define MDFLAG_CODABAR_AimID_2								0xFF
#define MDFLAG_CODABAR_AimID_3								0xFF
#define MDFLAG_CODABAR_AimID_4								0xFF
#define MDFLAG_CODABAR_AimID_5								0xFF
#define MDFLAG_CODABAR_AimID_6								0xFF
#define MDFLAG_CODABAR_AimID_7								0xFF
#define MDFLAG_CODABAR_AimID_Default						(MDFLAG_CODABAR_AimID_0, MDFLAG_CODABAR_AimID_1, MDFLAG_CODABAR_AimID_2, MDFLAG_CODABAR_AimID_3, \
															 MDFLAG_CODABAR_AimID_4, MDFLAG_CODABAR_AimID_5, MDFLAG_CODABAR_AimID_6, MDFLAG_CODABAR_AimID_7)
// code128
// “2001”，Dec，128码相关，识读功能；
#define MDFLAG_CODE128_Read_Disable							0		// 失能识读128码
#define MDFLAG_CODE128_Read_Enable							1		// 使能识读128码
#define MDFLAG_CODE128_Read_Default							MDFLAG_CodeRead_Enabled

// “2002”，Dec，128码相关，校验符确认功能；
#define MDFLAG_CODE128_ChkDigVer_Disable					0		// 失能检测校验符
#define MDFLAG_CODE128_ChkDigVer_Enable						1		// 使能检测校验符
#define MDFLAG_CODE128_ChkDigVer_Default					MDFLAG_CODE128_ChkDigVer_Enable

// “2003”，Dec，128码相关，校验符传送功能；
#define MDFLAG_CODE128_ChkDigTrans_Disable					0		// 失能传送校验符
#define MDFLAG_CODE128_ChkDigTrans_Enable					1		// 使能传送校验符
#define MDFLAG_CODE128_ChkDigTrans_Default					MDFLAG_CODE128_ChkDigTrans_Disable

// “2004”，Dec，128码相关，最大码字长度功能；
#define MDFLAG_CODE128_MaxLen_99							99		// 最大码字长度99
#define MDFLAG_CODE128_MaxLen_Default						MDFLAG_CODE128_MaxLen_99

// “2005”，Dec，128码相关，最小码字长度功能；
#define MDFLAG_CODE128_MinLen_1								1		// 最小码字长度1
#define MDFLAG_CODE128_MinLen_Default						MDFLAG_CODE128_MinLen_1

// “2006”，Hex，128码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“2006H4B”字符K，H4B
#define MDFLAG_Code128_CodeID_K								('K')
#define MDFLAG_Code128_CodeID_Default						MDFLAG_Code128_CodeID_K

// “2007”，Dec，128码相关，插入字符串组功能；
#define MDFLAG_CODE128_InsertGrpSel_None					0		// 不插入字符串组
#define MDFLAG_CODE128_InsertGrpSel_Default					MDFLAG_CODE128_InsertGrpSel_None

// “2008”，Dec，128码相关，截去前导0功能；
#define MDFLAG_CODE128_TruZero_Disable						0		// 禁止截去前导“0”
#define MDFLAG_CODE128_TruZero_All							1		// 截去全部前导“0”
#define MDFLAG_CODE128_TruZero_First						2		// 仅截去第一位“0”
#define MDFLAG_CODE128_TruZero_Default						MDFLAG_CODE128_TruZero_Disable

// “2009”，Hex，128码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_CODE128_AimID_0									0xFF		// 最多支持8个字符
#define MDFLAG_CODE128_AimID_1									0xFF
#define MDFLAG_CODE128_AimID_2									0xFF
#define MDFLAG_CODE128_AimID_3									0xFF
#define MDFLAG_CODE128_AimID_4									0xFF
#define MDFLAG_CODE128_AimID_5									0xFF
#define MDFLAG_CODE128_AimID_6									0xFF
#define MDFLAG_CODE128_AimID_7									0xFF
#define MDFLAG_CODE128_AimID_Default							(MDFLAG_CODE128_AimID_0, MDFLAG_CODE128_AimID_1, MDFLAG_CODE128_AimID_2, MDFLAG_CODE128_AimID_3, \
																 MDFLAG_CODE128_AimID_4, MDFLAG_CODE128_AimID_5, MDFLAG_CODE128_AimID_6, MDFLAG_CODE128_AimID_7)

// code-93
// “2101”，Dec，code-93码相关，识读功能；
#define MDFLAG_CODE93_Read_Disable							0		// 失能识读code-93码
#define MDFLAG_CODE93_Read_Enable							1		// 使能识读code-93码
#define MDFLAG_CODE93_Read_Default							MDFLAG_CodeRead_Enabled

// “2102”，Dec，code-93码相关，校验符确认功能；
#define MDFLAG_CODE93_ChkDigVer_Disable						0		// 失能检测校验符
#define MDFLAG_CODE93_ChkDigVer_Enable						1		// 使能检测校验符
#define MDFLAG_CODE93_ChkDigVer_Default						MDFLAG_CODE93_ChkDigVer_Enable

// “2103”，Dec，code-93码相关，校验符传送功能；
#define MDFLAG_CODE93_ChkDigTrans_Disable					0		// 失能传送校验符
#define MDFLAG_CODE93_ChkDigTrans_Enable					1		// 使能传送校验符
#define MDFLAG_CODE93_ChkDigTrans_Default					MDFLAG_CODE93_ChkDigTrans_Disable

// “2104”，Dec，code-93码相关，最大码字长度功能；
#define MDFLAG_CODE93_MaxLen_99								99		// 最大码字长度99
#define MDFLAG_CODE93_MaxLen_Default						MDFLAG_CODE93_MaxLen_99

// “2105”，Dec，code-93码相关，最小码字长度功能；
#define MDFLAG_CODE93_MinLen_1								1		// 最小码字长度1
#define MDFLAG_CODE93_MinLen_Default						MDFLAG_CODE93_MinLen_1

// “2106”，Hex，Code-93码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“2106H4C”字符L，H4C
#define MDFLAG_Code93_CodeID_L								('L')
#define MDFLAG_Code93_CodeID_Default						MDFLAG_Code93_CodeID_L

// “2107”，Dec，code-93码相关，插入字符串组功能；
#define MDFLAG_CODE93_InsertGrpSel_None						0		// 不插入字符串组
#define MDFLAG_CODE93_InsertGrpSel_Default					MDFLAG_CODE93_InsertGrpSel_None

// “2108”，Hex，code-93码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_CODE93_AimID_0								0xFF		// 最多支持8个字符
#define MDFLAG_CODE93_AimID_1								0xFF
#define MDFLAG_CODE93_AimID_2								0xFF
#define MDFLAG_CODE93_AimID_3								0xFF
#define MDFLAG_CODE93_AimID_4								0xFF
#define MDFLAG_CODE93_AimID_5								0xFF
#define MDFLAG_CODE93_AimID_6								0xFF
#define MDFLAG_CODE93_AimID_7								0xFF
#define MDFLAG_CODE93_AimID_Default							(MDFLAG_CODE93_AimID_0, MDFLAG_CODE93_AimID_1, MDFLAG_CODE93_AimID_2, MDFLAG_CODE93_AimID_3, \
															 MDFLAG_CODE93_AimID_4, MDFLAG_CODE93_AimID_5, MDFLAG_CODE93_AimID_6, MDFLAG_CODE93_AimID_7)

// code-11
// “2201”，Dec，code-11码相关，识读功能；
#define MDFLAG_CODE11_Read_Disable							0		// 失能识读code-11码
#define MDFLAG_CODE11_Read_Enable							1		// 使能识读code-11码
#define MDFLAG_CODE11_Read_Default							MDFLAG_CodeRead_Disabled

// “2202”，Dec，code-11码相关，校验符确认功能；
#define MDFLAG_CODE11_ChkDigVer_Disable						0		// 失能检测校验符
#define MDFLAG_CODE11_ChkDigVer_One							1		// 检测1位（check_C）校验符
#define MDFLAG_CODE11_ChkDigVer_Two							2		// 检测2位（check_C与check_K）校验符
#define MDFLAG_CODE11_ChkDigVer_Res							3		// 保留
#define MDFLAG_CODE11_ChkDigVer_Default						MDFLAG_CODE11_ChkDigVer_One

// “2203”，Dec，code-11码相关，校验符传送功能；
#define MDFLAG_CODE11_ChkDigTrans_Disable					0		// 失能传送校验符
#define MDFLAG_CODE11_ChkDigTrans_Enable					1		// 使能传送校验符
#define MDFLAG_CODE11_ChkDigTrans_Default					MDFLAG_CODE11_ChkDigTrans_Disable

// “2204”，Dec，code-11码相关，最大码字长度功能；
#define MDFLAG_CODE11_MaxLen_99								99		// 表示最大码字长度99
#define MDFLAG_CODE11_MaxLen_Default						MDFLAG_CODE11_MaxLen_99

// “2205”，Dec，code-11码相关，最小码字长度功能；
#define MDFLAG_CODE11_MinLen_4								4		// 最小码字长度4
#define MDFLAG_CODE11_MinLen_Default						MDFLAG_CODE11_MinLen_4

// “2206”，Hex，code-11码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“2206H56”字符V，H56
#define MDFLAG_Code11_CodeID_V								('V')
#define MDFLAG_Code11_CodeID_Default						MDFLAG_Code11_CodeID_V

// “2207”，Dec，code-11码相关，插入字符串组功能；
#define MDFLAG_CODE11_InsertGrpSel_None						0		// 不插入字符串组
#define MDFLAG_CODE11_InsertGrpSel_Default					MDFLAG_CODE11_InsertGrpSel_None

// “2208”，Hex，code-11码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_CODE11_AimID_0								0xFF		// 最多支持8个字符
#define MDFLAG_CODE11_AimID_1								0xFF
#define MDFLAG_CODE11_AimID_2								0xFF
#define MDFLAG_CODE11_AimID_3								0xFF
#define MDFLAG_CODE11_AimID_4								0xFF
#define MDFLAG_CODE11_AimID_5								0xFF
#define MDFLAG_CODE11_AimID_6								0xFF
#define MDFLAG_CODE11_AimID_7								0xFF
#define MDFLAG_CODE11_AimID_Default							(MDFLAG_CODE11_AimID_0, MDFLAG_CODE11_AimID_1, MDFLAG_CODE11_AimID_2, MDFLAG_CODE11_AimID_3, \
															 MDFLAG_CODE11_AimID_4, MDFLAG_CODE11_AimID_5, MDFLAG_CODE11_AimID_6, MDFLAG_CODE11_AimID_7)

// MSI/Plessey
// “2301”，Dec，MSI/Plessey码相关，识读功能；
#define MDFLAG_MSIPL_Read_Disable							0		// 失能识读MSI/Plessey码
#define MDFLAG_MSIPL_Read_Enable							1		// 使能识读MSI/Plessey码
#define MDFLAG_MSIPL_Read_Default							MDFLAG_CodeRead_Disabled

// “2302”，Dec，MSI/Plessey码相关，校验符确认功能；
#define MDFLAG_MSIPL_ChkDigVer_Disable						0		// 失能检测校验符
#define MDFLAG_MSIPL_ChkDigVer_One							1		// 检测1位（模10）校验符
#define MDFLAG_MSIPL_ChkDigVer_Two1010						2		// 检测2位（模10）（模10）校验符
#define MDFLAG_MSIPL_ChkDigVer_Two1011						3		// 检测2位（模10）（模11）校验符
#define MDFLAG_MSIPL_ChkDigVer_Default						MDFLAG_MSIPL_ChkDigVer_Disable

// “2303”，Dec，MSI/Plessey码相关，校验符传送功能；
#define MDFLAG_MSIPL_ChkDigTrans_Disable					0		// 失能传送校验符
#define MDFLAG_MSIPL_ChkDigTrans_Enable						1		// 使能传送校验符
// 默认值是Enable是在原来的system_cfg.h中定义，但是在系统system_cfg_std.c中定义默认值为Disable，
// 不知道是否会影响到解码器正常工作，此处暂时保留
#define MDFLAG_MSIPL_ChkDigTrans_Default					MDFLAG_MSIPL_ChkDigTrans_Enable
//#define MDFLAG_MSIPL_ChkDigTrans_Default					MDFLAG_MSIPL_ChkDigTrans_Disable

// “2304”，Dec，MSI/Plessey码相关，最大码字长度功能；
#define MDFLAG_MSIPL_MaxLen_99								99		// 最大码字长度99
#define MDFLAG_MSIPL_MaxLen_Default							MDFLAG_MSIPL_MaxLen_99

// “2305”，Dec，MSI/Plessey码相关，最小码字长度功能；
#define MDFLAG_MSIPL_MinLen_4								4		// 最小码字长度4
#define MDFLAG_MSIPL_MinLen_Default							MDFLAG_MSIPL_MinLen_4

// “2306”，Hex，MSI/Plessey码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“2306H4F”字符O，H4F
#define MDFLAG_MSIplesy_CodeID_O							('O')
#define MDFLAG_MSIplesy_CodeID_Default						MDFLAG_MSIplesy_CodeID_O

// “2307”，Dec，MSI/Plessey码相关，插入字符串组功能；
#define MDFLAG_MSIPL_InsertGrpSel_None						0		// 不插入字符串组
#define MDFLAG_MSIPL_InsertGrpSel_Default					MDFLAG_MSIPL_InsertGrpSel_None

// “2308”，Hex，MSI/Plessey码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_MSIPL_AimID_0								0xFF		// 最多支持8个字符
#define MDFLAG_MSIPL_AimID_1								0xFF
#define MDFLAG_MSIPL_AimID_2								0xFF
#define MDFLAG_MSIPL_AimID_3								0xFF
#define MDFLAG_MSIPL_AimID_4								0xFF
#define MDFLAG_MSIPL_AimID_5								0xFF
#define MDFLAG_MSIPL_AimID_6								0xFF
#define MDFLAG_MSIPL_AimID_7								0xFF
#define MDFLAG_MSIPL_AimID_Default							(MDFLAG_MSIPL_AimID_0, MDFLAG_MSIPL_AimID_1, MDFLAG_MSIPL_AimID_2, MDFLAG_MSIPL_AimID_3, \
															 MDFLAG_MSIPL_AimID_4, MDFLAG_MSIPL_AimID_5, MDFLAG_MSIPL_AimID_6, MDFLAG_MSIPL_AimID_7)

// UK/Plessey
// “2401”，Dec，UK/Plessey码相关，识读功能；
#define MDFLAG_UKPL_Read_Disable							0		// 失能识读UK/Plessey码
#define MDFLAG_UKPL_Read_Enable								1		// 使能识读UK/Plessey码
#define MDFLAG_UKPL_Read_Default							MDFLAG_CodeRead_Disabled

// “2402”，Dec，UK/Plessey码相关，校验符确认功能；
#define MDFLAG_UKPL_ChkDigVer_Disable						0		// 失能检测校验符
#define MDFLAG_UKPL_ChkDigVer_Enable						1		// 使能检测校验符
#define MDFLAG_UKPL_ChkDigVer_Default						MDFLAG_UKPL_ChkDigVer_Enable

// “2403”，Dec，UK/Plessey码相关，校验符传送功能；
#define MDFLAG_UKPL_ChkDigTrans_Disable						0		// 失能传送校验符
#define MDFLAG_UKPL_ChkDigTrans_Enable						1		// 使能传送校验符
#define MDFLAG_UKPL_ChkDigTrans_Default						MDFLAG_UKPL_ChkDigTrans_Disable

// “2404”，Dec，UK/Plessey码相关，最大码字长度功能；
#define MDFLAG_UKPL_MaxLen_99								99		// 最大码字长度99
#define MDFLAG_UKPL_MaxLen_Default							MDFLAG_UKPL_MaxLen_99

// “2405”，Dec，UK/Plessey码相关，最小码字长度功能；
#define MDFLAG_UKPL_MinLen_1								1		// 最小码字长度1
#define MDFLAG_UKPL_MinLen_Default							MDFLAG_UKPL_MinLen_1

// “2406”，Hex，UK/Plessey码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“2406H55”字符U，H55
#define MDFLAG_UKplesy_CodeID_U								('U')
#define MDFLAG_UKplesy_CodeID_Default						MDFLAG_UKplesy_CodeID_U

// “2407”，Dec，UK/Plessey码相关，插入字符串组功能；
#define MDFLAG_UKPL_InsertGrpSel_None						0		// 不插入字符串组
#define MDFLAG_UKPL_InsertGrpSel_Default					MDFLAG_UKPL_InsertGrpSel_None

// “2408”，Hex，UK/Plessey码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_UKPL_AimID_0									0xFF		// 最多支持8个字符
#define MDFLAG_UKPL_AimID_1									0xFF
#define MDFLAG_UKPL_AimID_2									0xFF
#define MDFLAG_UKPL_AimID_3									0xFF
#define MDFLAG_UKPL_AimID_4									0xFF
#define MDFLAG_UKPL_AimID_5									0xFF
#define MDFLAG_UKPL_AimID_6									0xFF
#define MDFLAG_UKPL_AimID_7									0xFF
#define MDFLAG_UKPL_AimID_Default							(MDFLAG_UKPL_AimID_0, MDFLAG_UKPL_AimID_1, MDFLAG_UKPL_AimID_2, MDFLAG_UKPL_AimID_3, \
															 MDFLAG_UKPL_AimID_4, MDFLAG_UKPL_AimID_5, MDFLAG_UKPL_AimID_6, MDFLAG_UKPL_AimID_7)

// UCC/EAN 128
// “2501”，Dec，UCC/EAN 128码相关，识读功能；
#define MDFLAG_UCCEAN128_Read_Disable						0		// 失能识读UCC/EAN 128码
#define MDFLAG_UCCEAN128_Read_Enable						1		// 使能识读UCC/EAN 128码
#define MDFLAG_UCCEAN128_Read_Default						MDFLAG_CodeRead_Enabled

// “2502”，Dec，UCC/EAN 128码相关，校验符确认功能；
#define MDFLAG_UCCEAN128_ChkDigVer_Disable					0		// 失能检测校验符
#define MDFLAG_UCCEAN128_ChkDigVer_Enable					1		// 使能检测校验符
#define MDFLAG_UCCEAN128_ChkDigVer_Default					MDFLAG_UCCEAN128_ChkDigVer_Enable

// “2503”，Dec，UCC/EAN 128码相关，校验符传送功能；
#define MDFLAG_UCCEAN128_ChkDigTrans_Disable				0		// 失能传送校验符
#define MDFLAG_UCCEAN128_ChkDigTrans_Enable					1		// 使能传送校验符
#define MDFLAG_UCCEAN128_ChkDigTrans_Default				MDFLAG_UCCEAN128_ChkDigTrans_Disable

// “2504”，Dec，UCC/EAN 128码相关，最大码字长度功能；
#define MDFLAG_UCCEAN128_MaxLen_99							99		// 最大码字长度99
#define MDFLAG_UCCEAN128_MaxLen_Default						MDFLAG_UCCEAN128_MaxLen_99

// “2505”，Dec，UCC/EAN 128码相关，最小码字长度功能；
#define MDFLAG_UCCEAN128_MinLen_1							1		// 最小码字长度1
#define MDFLAG_UCCEAN128_MinLen_Default						MDFLAG_UCCEAN128_MinLen_1

// “2506”，Hex，UCC/EAN 128码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“2506H4B”字符K，H4B
#define MDFLAG_UCCEAN128_CodeID_K							('K')
#define MDFLAG_UCCEAN128_CodeID_Default						MDFLAG_UCCEAN128_CodeID_K

// “2507”，Dec，UCC/EAN 128码相关，插入字符串组功能；
#define MDFLAG_UCCEAN128_InsertGrpSel_None					0		// 不插入字符串组
#define MDFLAG_UCCEAN128_InsertGrpSel_Default				MDFLAG_UCCEAN128_InsertGrpSel_None

// “2508”，Dec，UCC/EAN 128码相关，截去前导“0”功能；
#define MDFLAG_UCCEAN128_TruZero_Disable					0		// 禁止，不截去前导“0”
#define MDFLAG_UCCEAN128_TruZero_All						1		// 128码数据字符的前导全部的“0”将被截去
#define MDFLAG_UCCEAN128_TruZero_First						2		// 128码数据字符的前导一位的“0”将被截去
#define MDFLAG_UCCEAN128_TruZero_Default					MDFLAG_UCCEAN128_TruZero_Disable

// “2509”，Hex，UCC/EAN 128码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_UCCEAN128_AimID_0							0xFF		// 最多支持8个字符
#define MDFLAG_UCCEAN128_AimID_1							0xFF
#define MDFLAG_UCCEAN128_AimID_2							0xFF
#define MDFLAG_UCCEAN128_AimID_3							0xFF
#define MDFLAG_UCCEAN128_AimID_4							0xFF
#define MDFLAG_UCCEAN128_AimID_5							0xFF
#define MDFLAG_UCCEAN128_AimID_6							0xFF
#define MDFLAG_UCCEAN128_AimID_7							0xFF
#define MDFLAG_UCCEAN128_AimID_Default						(MDFLAG_UCCEAN128_AimID_0, MDFLAG_UCCEAN128_AimID_1, MDFLAG_UCCEAN128_AimID_2, MDFLAG_UCCEAN128_AimID_3, \
															 MDFLAG_UCCEAN128_AimID_4, MDFLAG_UCCEAN128_AimID_5, MDFLAG_UCCEAN128_AimID_6, MDFLAG_UCCEAN128_AimID_7)

// China Post
// “2601”，Dec，中国邮政码相关，识读功能；
#define MDFLAG_CHNPST_Read_Disable							0		// 失能识读中国邮政码
#define MDFLAG_CHNPST_Read_Enable							1		// 使能识读中国邮政码
#define MDFLAG_CHNPST_Read_Default							MDFLAG_CodeRead_Enabled

// “2602”，Dec，中国邮政码相关，校验符确认功能；
#define MDFLAG_CHNPST_ChkDigVer_Disable						0		// 失能检测校验符
#define MDFLAG_CHNPST_ChkDigVer_Enable						1		// 使能检测校验符
#define MDFLAG_CHNPST_ChkDigVer_Default						MDFLAG_CHNPST_ChkDigVer_Disable

// “2603”，Dec，中国邮政码相关，校验符传送功能；
#define MDFLAG_CHNPST_ChkDigTrans_Disable					0		// 失能传送校验符
#define MDFLAG_CHNPST_ChkDigTrans_Enable					1		// 使能传送校验符
#define MDFLAG_CHNPST_ChkDigTrans_Default					MDFLAG_CHNPST_ChkDigTrans_Disable

// “2604”，Dec，中国邮政码相关，最大码字长度功能；
#define MDFLAG_CHNPST_MaxLen_11								11		// 最大码字长度11
#define MDFLAG_CHNPST_MaxLen_Default						MDFLAG_CHNPST_MaxLen_11

// “2605”，Dec，中国邮政码相关，最小码字长度功能；
#define MDFLAG_CHNPST_MinLen_11								11		// 最小码字长度11
#define MDFLAG_CHNPST_MinLen_Default						MDFLAG_CHNPST_MinLen_11

// “2606”，Hex，中国邮政码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“2606H54”字符T，H54
#define MDFLAG_ChnPost_CodeID_T								('T')
#define MDFLAG_ChnPost_CodeID_Default						MDFLAG_ChnPost_CodeID_T

// “2607”，Dec，中国邮政码相关，插入字符串组功能；
#define MDFLAG_CHNPST_InsertGrpSel_None						0		// 不插入字符串组
#define MDFLAG_CHNPST_InsertGrpSel_Default					MDFLAG_CHNPST_InsertGrpSel_None

// “2608”，Hex，中国邮政码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_CHNPST_AimID_0								0xFF		// 最多支持8个字符
#define MDFLAG_CHNPST_AimID_1								0xFF
#define MDFLAG_CHNPST_AimID_2								0xFF
#define MDFLAG_CHNPST_AimID_3								0xFF
#define MDFLAG_CHNPST_AimID_4								0xFF
#define MDFLAG_CHNPST_AimID_5								0xFF
#define MDFLAG_CHNPST_AimID_6								0xFF
#define MDFLAG_CHNPST_AimID_7								0xFF
#define MDFLAG_CHNPST_AimID_Default							(MDFLAG_CHNPST_AimID_0, MDFLAG_CHNPST_AimID_1, MDFLAG_CHNPST_AimID_2, MDFLAG_CHNPST_AimID_3, \
															 MDFLAG_CHNPST_AimID_4, MDFLAG_CHNPST_AimID_5, MDFLAG_CHNPST_AimID_6, MDFLAG_CHNPST_AimID_7)

// GS1 DataBar
// “2701”，Dec，GS1 DataBar相关，识读功能；
#define MDFLAG_GS1DB_Read_Disable							0		// 失能识读GS1 DataBar
#define MDFLAG_GS1DB_Read_Enable							1		// 使能识读GS1 DataBar
#define MDFLAG_GS1DB_Read_Default							MDFLAG_CodeRead_Enabled

// “2702”，Hex，GS1 DataBar相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“2702H52”字符R，H52
#define MDFLAG_GS1DB_CodeID_R								('R')
#define MDFLAG_GS1DB_CodeID_Default							MDFLAG_GS1DB_CodeID_R

// “2703”，Dec，GS1 DataBar相关，插入字符串组功能；
#define MDFLAG_GS1DB_InsertGrpSel_None						0		// 不插入字符串组
#define MDFLAG_GS1DB_InsertGrpSel_Default					MDFLAG_GS1DB_InsertGrpSel_None

// “2704”，Dec，GS1 DataBar相关，码制转换功能；
#define MDFLAG_GS1DB_Conversion_None						0		// 不进行转换
#define MDFLAG_GS1DB_Conversion_UCCEAN128					1		// 根据“码制识别符传送功能”（8206）的设置进行传送，转化后 AIM 识别符定义为]Cm
#define MDFLAG_GS1DB_Conversion_UPCAEAN13					2		// 起始条码字符为“010”，然后接着是一个“0”的条码，将会转换成 EAN-13。起始条码字符为“0100”，然后接着是两个或多个“0”，但不能是 6 个“0”的条码，将会转换成UPC-A
#define MDFLAG_GS1DB_Conversion_Default						MDFLAG_GS1DB_Conversion_None

// “2705”，Dec，GS1 DataBar相关，输出结果是否带括号选项；
#define MDFLAG_GS1DB_OutputBrackets_Disable					0		// 扫描GS1条码后，输出结果不带括号
#define MDFLAG_GS1DB_OutputBrackets_Enable					1		// 扫描GS1条码后，输出结果带括号
#define MDFLAG_GS1DB_OutputBrackets_Default					MDFLAG_GS1DB_OutputBrackets_Disable

// “2706”，Hex，GS1 DataBar相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_GS1DB_AimID_0								0xFF		// 最多支持8个字符
#define MDFLAG_GS1DB_AimID_1								0xFF
#define MDFLAG_GS1DB_AimID_2								0xFF
#define MDFLAG_GS1DB_AimID_3								0xFF
#define MDFLAG_GS1DB_AimID_4								0xFF
#define MDFLAG_GS1DB_AimID_5								0xFF
#define MDFLAG_GS1DB_AimID_6								0xFF
#define MDFLAG_GS1DB_AimID_7								0xFF
#define MDFLAG_GS1DB_AimID_Default							(MDFLAG_GS1DB_AimID_0, MDFLAG_GS1DB_AimID_1, MDFLAG_GS1DB_AimID_2, MDFLAG_GS1DB_AimID_3, \
															 MDFLAG_GS1DB_AimID_4, MDFLAG_GS1DB_AimID_5, MDFLAG_GS1DB_AimID_6, MDFLAG_GS1DB_AimID_7)

// GS1 DataBar Limited
// “2801”，Dec，GS1 DataBar Limited码相关，识读功能；
#define MDFLAG_GS1LIMI_Read_Disable							0		// 失能识读GS1 DataBar Limited码
#define MDFLAG_GS1LIMI_Read_Enable							1		// 使能识读GS1 DataBar Limited码
#define MDFLAG_GS1LIMI_Read_Default							MDFLAG_CodeRead_Enabled

// “2802”，Hex，GS1 DataBar Limited码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“2802H52”字符R，H52
#define MDFLAG_GS1LIMI_CodeID_R								('R')
#define MDFLAG_GS1LIMI_CodeID_Default						MDFLAG_GS1LIMI_CodeID_R

// “2803”，Dec，GS1 DataBar Limited码相关，插入字符串组功能；
#define MDFLAG_GS1LIMI_InsertGrpSel_None					0		// 不插入字符串组
#define MDFLAG_GS1LIMI_InsertGrpSel_Default					MDFLAG_GS1LIMI_InsertGrpSel_None

// “2804”，Dec，GS1 DataBar Limited码相关，码制转换功能；
#define MDFLAG_GS1LIMI_Conversion_None						0		// 不进行转换
#define MDFLAG_GS1LIMI_Conversion_UCCEAN128					1		// 根据“码制识别符传送功能”（8206）的设置进行传送，转化后 AIM 识别符定义为]Cm
#define MDFLAG_GS1LIMI_Conversion_UPCAEAN13					2		// 起始条码字符为“010”，然后接着是一个“0”的条码，将会转换成 EAN-13。起始条码字符为“0100”，然后接着是两个或多个“0”，但不能是 6 个“0”的条码，将会转换成UPC-A
#define MDFLAG_GS1LIMI_Conversion_Default					MDFLAG_GS1LIMI_Conversion_None

// “2805”，Hex，GS1 DataBar Limited码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_GS1LIMI_AimID_0								0xFF		// 最多支持8个字符
#define MDFLAG_GS1LIMI_AimID_1								0xFF
#define MDFLAG_GS1LIMI_AimID_2								0xFF
#define MDFLAG_GS1LIMI_AimID_3								0xFF
#define MDFLAG_GS1LIMI_AimID_4								0xFF
#define MDFLAG_GS1LIMI_AimID_5								0xFF
#define MDFLAG_GS1LIMI_AimID_6								0xFF
#define MDFLAG_GS1LIMI_AimID_7								0xFF
#define MDFLAG_GS1LIMI_AimID_Default						(MDFLAG_GS1LIMI_AimID_0, MDFLAG_GS1LIMI_AimID_1, MDFLAG_GS1LIMI_AimID_2, MDFLAG_GS1LIMI_AimID_3, \
															 MDFLAG_GS1LIMI_AimID_4, MDFLAG_GS1LIMI_AimID_5, MDFLAG_GS1LIMI_AimID_6, MDFLAG_GS1LIMI_AimID_7)

// GS1 DataBar Expanded
// “2901”，Dec，GS1 DataBar Expanded码相关，识读功能；
#define MDFLAG_GS1EXPA_Read_Disable							0		// 失能识读GS1 DataBar Expanded码
#define MDFLAG_GS1EXPA_Read_Enable							1		// 使能识读GS1 DataBar Expanded码
#define MDFLAG_GS1EXPA_Read_Default							MDFLAG_CodeRead_Enabled

// “2902”，Dec，GS1 DataBar Expanded码相关，最大码字长度功能；
#define MDFLAG_GS1EXPA_MaxLen_99							99		// 最大码字长度99
#define MDFLAG_GS1EXPA_MaxLen_Default						MDFLAG_GS1EXPA_MaxLen_99

// “2903”，Dec，GS1 DataBar Expanded码相关，最小码字长度功能；
#define MDFLAG_GS1EXPA_MinLen_1								1		// 最小码字长度1
#define MDFLAG_GS1EXPA_MinLen_Default						MDFLAG_GS1EXPA_MinLen_1

// “2904”，Hex，GS1 DataBar Expanded码相关，自定义码字识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“2904H52”字符R，H52
#define MDFLAG_GS1EXPA_CodeID_R								('R')
#define MDFLAG_GS1EXPA_CodeID_Default						MDFLAG_GS1EXPA_CodeID_R

// “2905”，Dec，GS1 DataBar Expanded码相关，插入字符串组功能；
#define MDFLAG_GS1EXPA_InsertGrpSel_None					0		// 不插入字符串组
#define MDFLAG_GS1EXPA_InsertGrpSel_Default					MDFLAG_GS1EXPA_InsertGrpSel_None

// “2906”，Dec，GS1 DataBar Expanded码相关，码制转换功能；
#define MDFLAG_GS1EXPA_Conversion_None						0		// 不进行转换
#define MDFLAG_GS1EXPA_Conversion_UCCEAN128					1		// 根据“码制识别符传送功能”（8206）的设置进行传送，转化后 AIM 识别符定义为]Cm
#define MDFLAG_GS1EXPA_Conversion_Default					MDFLAG_GS1LIMI_Conversion_None

// “2907”，Hex，GS1 DataBar Expanded码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_GS1EXPA_AimID_0								0xFF		// 最多支持8个字符
#define MDFLAG_GS1EXPA_AimID_1								0xFF
#define MDFLAG_GS1EXPA_AimID_2								0xFF
#define MDFLAG_GS1EXPA_AimID_3								0xFF
#define MDFLAG_GS1EXPA_AimID_4								0xFF
#define MDFLAG_GS1EXPA_AimID_5								0xFF
#define MDFLAG_GS1EXPA_AimID_6								0xFF
#define MDFLAG_GS1EXPA_AimID_7								0xFF
#define MDFLAG_GS1EXPA_AimID_Default						(MDFLAG_GS1EXPA_AimID_0, MDFLAG_GS1EXPA_AimID_1, MDFLAG_GS1EXPA_AimID_2, MDFLAG_GS1EXPA_AimID_3, \
															 MDFLAG_GS1EXPA_AimID_4, MDFLAG_GS1EXPA_AimID_5, MDFLAG_GS1EXPA_AimID_6, MDFLAG_GS1EXPA_AimID_7)

// PDF417
// “3001”，Dec，PDF417码相关，识读功能；
#define MDFLAG_PDF417_Read_Disable							0		// 失能识读PDF417码
#define MDFLAG_PDF417_Read_Enable							1		// 使能识读PDF417码
#define MDFLAG_PDF417_Read_Default							MDFLAG_CodeRead_Disabled

// “3002”，Hex，PDF417码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“3002H70”字符p，H70
#define MDFLAG_PDF417_CodeID_p								('p')
#define MDFLAG_PDF417_CodeID_Default						MDFLAG_PDF417_CodeID_p

// “3003”，Dec，PDF417码相关，插入字符串组功能；
#define MDFLAG_PDF417_InsertGrpSel_None						0		// 不插入字符串组
#define MDFLAG_PDF417_InsertGrpSel_Default					MDFLAG_PDF417_InsertGrpSel_None

// “3004”，Dec，PDF417码相关，码制转换功能；
#define MDFLAG_PDF417_Conversion_Off						0		// 不进行转换
#define MDFLAG_PDF417_Conversion_UCCEAN128					1		// 转化后AIM识别符定义为]Cm
#define MDFLAG_PDF417_Conversion_UPCA_EAN13					2		// 起始条码字符为“010”，然后接着是一个“0”的条码，将会转换成EAN-13
																	// 起始条码字符为“0100”，然后接着是两个或多个“0”，但不能是6个“0”的条码，将会转换成UPC-A

// “3005”，Dec， PDF417码相关，最大码字长度；
#define MDFLAG_PDF417_MaxLen_9999							9999	// PDF417条码最大码字长度为9999
#define MDFLAG_PDF417_MaxLen_Default						MDFLAG_PDF417_MaxLen_9999

// “3006”，Dec， PDF417码相关，最小码字长度；
#define MDFLAG_PDF417_MinLen_1								1		// PDF417条码最小码字长度为1
#define MDFLAG_PDF417_MinLen_Default						MDFLAG_PDF417_MinLen_1

// “3007”，Hex，PDF417码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_PDF417_AimID_0								0xFF		// 最多支持8个字符
#define MDFLAG_PDF417_AimID_1								0xFF
#define MDFLAG_PDF417_AimID_2								0xFF
#define MDFLAG_PDF417_AimID_3								0xFF
#define MDFLAG_PDF417_AimID_4								0xFF
#define MDFLAG_PDF417_AimID_5								0xFF
#define MDFLAG_PDF417_AimID_6								0xFF
#define MDFLAG_PDF417_AimID_7								0xFF
#define MDFLAG_PDF417_AimID_Default							(MDFLAG_PDF417_AimID_0, MDFLAG_PDF417_AimID_1, MDFLAG_PDF417_AimID_2, MDFLAG_PDF417_AimID_3, \
															 MDFLAG_PDF417_AimID_4, MDFLAG_PDF417_AimID_5, MDFLAG_PDF417_AimID_6, MDFLAG_PDF417_AimID_7)


// MicroPDF417
// “3101”，Dec，MicroPDF417码相关，识读功能；
#define MDFLAG_MICPDF417_Read_Disable						0		// 失能识读MicroPDF417码
#define MDFLAG_MICPDF417_Read_Enable						1		// 使能识读MicroPDF417码
#define MDFLAG_MICPDF417_Read_Default						MDFLAG_CodeRead_Disabled

// “3102”，Hex，MicroPDF417码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“3102H70”字符p，H70
#define MDFLAG_MICPDF417_CodeID_p							('p')
#define MDFLAG_MICPDF417_CodeID_Default						MDFLAG_MICPDF417_CodeID_p

// “3103”，Dec，MicroPDF417码相关，插入字符串组功能；
#define MDFLAG_MICPDF417_InsertGrpSel_None					0		// 不插入字符串组
#define MDFLAG_MICPDF417_InsertGrpSel_Default				MDFLAG_MICPDF417_InsertGrpSel_None

// “3104”，Dec，MicroPDF417码相关，码制转换功能；
#define MDFLAG_MICPDF417_Conversion_Off						0		// 不进行转换
#define MDFLAG_MICPDF417_Conversion_UCCEAN128				1		// 转化后AIM识别符定义为]Cm
#define MDFLAG_MICPDF417_Conversion_UPCA_EAN13				2		// 起始条码字符为“010”，然后接着是一个“0”的条码，将会转换成EAN-13
																	// 起始条码字符为“0100”，然后接着是两个或多个“0”，但不能是6个“0”的条码，将会转换成UPC-A

// “3105”，Dec， MicroPDF417码相关，最大码字长度；
#define MDFLAG_MICPDF417_MaxLen_9999						9999	// MicroPDF417条码最大码字长度为9999
#define MDFLAG_MICPDF417_MaxLen_Default						MDFLAG_MICPDF417_MaxLen_9999

// “3106”，Dec， MicroPDF417码相关，最小码字长度；
#define MDFLAG_MICPDF417_MinLen_1							1		// MicroPDF417条码最小码字长度为1
#define MDFLAG_MICPDF417_MinLen_Default						MDFLAG_MICPDF417_MinLen_1

// “3107”，Hex，MicroPDF417码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_MICPDF417_AimID_0							0xFF		// 最多支持8个字符
#define MDFLAG_MICPDF417_AimID_1							0xFF
#define MDFLAG_MICPDF417_AimID_2							0xFF
#define MDFLAG_MICPDF417_AimID_3							0xFF
#define MDFLAG_MICPDF417_AimID_4							0xFF
#define MDFLAG_MICPDF417_AimID_5							0xFF
#define MDFLAG_MICPDF417_AimID_6							0xFF
#define MDFLAG_MICPDF417_AimID_7							0xFF
#define MDFLAG_MICPDF417_AimID_Default						(MDFLAG_MICPDF417_AimID_0, MDFLAG_MICPDF417_AimID_1, MDFLAG_MICPDF417_AimID_2, MDFLAG_MICPDF417_AimID_3, \
															 MDFLAG_MICPDF417_AimID_4, MDFLAG_MICPDF417_AimID_5, MDFLAG_MICPDF417_AimID_6, MDFLAG_MICPDF417_AimID_7)

// China Finance
// “3201”，Dec，中国财政码相关，识读功能；
#define MDFLAG_CHNFNC_Read_Disable							0		// 失能识读中国财政码
#define MDFLAG_CHNFNC_Read_Enable							1		// 使能识读中国财政码
#define MDFLAG_CHNFNC_Read_Default							MDFLAG_CodeRead_Disabled

// “3202”，Dec，中国财政码相关，最大码字长度功能；
#define MDFLAG_CHNFNC_MaxLen_10								10		// 最大码字长度10
#define MDFLAG_CHNFNC_MaxLen_Default						MDFLAG_CHNFNC_MaxLen_10

// “3203”，Dec，中国财政码相关，最小码字长度功能；
#define MDFLAG_CHNFNC_MinLen_10								10		// 最小码字长度10
#define MDFLAG_CHNFNC_MinLen_Default						MDFLAG_CHNFNC_MinLen_10

// “3204”，Dec，中国财政码相关，校验符确认功能；
#define MDFLAG_CHNFNC_ChkDigVer_Disable						0		// 失能校验符确认
#define MDFLAG_CHNFNC_ChkDigVer_Enable						1		// 使能校验符确认
#define MDFLAG_CHNFNC_ChkDigVer_Default						MDFLAG_CHNFNC_ChkDigVer_Disable

// “3205”，Dec，中国财政码相关，首字符56789转换成ABCDE功能；
#define MDFLAG_CHNFNC_FrtCharConv_Disable					0		// 失能首字符56789转换成ABCDE
#define MDFLAG_CHNFNC_FrtCharConv_Enable					1		// 使能首字符56789转换成ABCDE
#define MDFLAG_CHNFNC_FrtCharConv_5ConvA					2		// 仅5转换成A
#define MDFLAG_CHNFNC_FrtCharConv_6ConvB					3		// 仅6转换成B
#define MDFLAG_CHNFNC_FrtCharConv_7ConvC					4		// 仅7转换成C
#define MDFLAG_CHNFNC_FrtCharConv_8ConvD					5		// 仅8转换成D
#define MDFLAG_CHNFNC_FrtCharConv_9ConvE					6		// 仅9转换成E
#define MDFLAG_CHNFNC_FrtCharConv_Default					MDFLAG_CHNFNC_FrtCharConv_Enable

// “3206”，Dec，中国财政码相关，首字符指定功能；
#define MDFLAG_CHNFNC_FrtCharSpec_Disable					0		// 失能首字符指定
#define MDFLAG_CHNFNC_FrtCharSpec_0							1		// 首字符指定为0
#define MDFLAG_CHNFNC_FrtCharSpec_5OrA						2		// 首字符指定为5或者A（若没有开启首字符5转换成A，首字符指定为5；否则，首字符指定为A）
#define MDFLAG_CHNFNC_FrtCharSpec_6OrB						3		// 首字符指定为6或者B（若没有开启首字符6转换成B，首字符指定为6；否则，首字符指定为B）
#define MDFLAG_CHNFNC_FrtCharSpec_7OrC						4		// 首字符指定为7或者C（若没有开启首字符7转换成C，首字符指定为7；否则，首字符指定为C）
#define MDFLAG_CHNFNC_FrtCharSpec_8OrD						5		// 首字符指定为8或者D（若没有开启首字符8转换成D，首字符指定为8；否则，首字符指定为D）
#define MDFLAG_CHNFNC_FrtCharSpec_9OrE 						6		// 首字符指定为9或者E（若没有开启首字符9转换成E，首字符指定为9；否则，首字符指定为E）
#define MDFLAG_CHNFNC_FrtCharSpec_1 						7		// 首字符指定为1
#define MDFLAG_CHNFNC_FrtCharSpec_2 						8		// 首字符指定为2
#define MDFLAG_CHNFNC_FrtCharSpec_3 						9		// 首字符指定为3
#define MDFLAG_CHNFNC_FrtCharSpec_4 						10		// 首字符指定为4
#define MDFLAG_CHNFNC_FrtCharSpec_Default					MDFLAG_CHNFNC_FrtCharSpec_0

// “3207”，Hex，中国财政码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“3207H59”字符Y，H59
#define MDFLAG_CHNFNC_CodeID_Y								('Y')
#define MDFLAG_CHNFNC_CodeID_Default						MDFLAG_CHNFNC_CodeID_Y

// “3208”，Dec，中国财政码相关，插入字符串组功能；
#define MDFLAG_CHNFNC_InsertGrpSel_None						0		// 不插入字符串组
#define MDFLAG_CHNFNC_InsertGrpSel_Default					MDFLAG_CHNFNC_InsertGrpSel_None

// “3209”，Hex，中国财政码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_CHNFNC_AimID_0								0xFF		// 最多支持8个字符
#define MDFLAG_CHNFNC_AimID_1								0xFF
#define MDFLAG_CHNFNC_AimID_2								0xFF
#define MDFLAG_CHNFNC_AimID_3								0xFF
#define MDFLAG_CHNFNC_AimID_4								0xFF
#define MDFLAG_CHNFNC_AimID_5								0xFF
#define MDFLAG_CHNFNC_AimID_6								0xFF
#define MDFLAG_CHNFNC_AimID_7								0xFF
#define MDFLAG_CHNFNC_AimID_Default							(MDFLAG_CHNFNC_AimID_0, MDFLAG_CHNFNC_AimID_1, MDFLAG_CHNFNC_AimID_2, MDFLAG_CHNFNC_AimID_3, \
															 MDFLAG_CHNFNC_AimID_4, MDFLAG_CHNFNC_AimID_5, MDFLAG_CHNFNC_AimID_6, MDFLAG_CHNFNC_AimID_7)

// ISBT128
// “3301”，Dec，ISBT128码相关，识读功能；
#define MDFLAG_ISBT_Read_Disable							0		// 失能识读ISBT128码
#define MDFLAG_ISBT_Read_Enable								1		// 使能识读ISBT128码
#define MDFLAG_ISBT_Read_Default							MDFLAG_CodeRead_Enabled

// “3302”，Dec，ISBT128码相关，校验符确认功能；
#define MDFLAG_ISBT_CkDigiVer_Disable						0		// 失能校验符确认选项
#define MDFLAG_ISBT_CkDigiVer_Enable						1		// 使能校验符确认选项
#define MDFLAG_ISBT_CkDigiVer_Default						MDFLAG_ISBT_CkDigiVer_Enable

// “3303”，Dec，ISBT128码相关，校验符传送功能；
#define MDFLAG_ISBT_ChkDigTrans_Disable						0		// 失能传送校验符
#define MDFLAG_ISBT_ChkDigTrans_Enable						1		// 使能传送校验符
#define MDFLAG_ISBT_ChkDigTrans_Default						MDFLAG_ISBT_ChkDigTrans_Disable

// “3304”，Dec，ISBT128码相关，最大码字长度功能；
#define MDFLAG_ISBT_MaxLen_99								99		// 最大码字长度99
#define MDFLAG_ISBT_MaxLen_Default							MDFLAG_ISBT_MaxLen_99

// “3305”，Dec，ISBT128码相关，最小码字长度功能；
#define MDFLAG_ISBT_MinLen_1								1		// 最小码字长度1
#define MDFLAG_ISBT_MinLen_Default							MDFLAG_ISBT_MinLen_1

// “3306”，Hex，ISBT128码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“3306H4B”字符K，H4B
#define MDFLAG_ISBT_CodeID_K								('K')
#define MDFLAG_ISBT_CodeID_Default							MDFLAG_ISBT_CodeID_K

// “3307”，Dec，ISBT128码相关，插入字符串组功能；
#define MDFLAG_ISBT_InsertGrpSel_None						0		// 不插入字符串组
#define MDFLAG_ISBT_InsertGrpSel_Default					MDFLAG_ISBT_InsertGrpSel_None

// “3308”，Hex，ISBT128码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_ISBT_AimID_0									0xFF		// 最多支持8个字符
#define MDFLAG_ISBT_AimID_1									0xFF
#define MDFLAG_ISBT_AimID_2									0xFF
#define MDFLAG_ISBT_AimID_3									0xFF
#define MDFLAG_ISBT_AimID_4									0xFF
#define MDFLAG_ISBT_AimID_5									0xFF
#define MDFLAG_ISBT_AimID_6									0xFF
#define MDFLAG_ISBT_AimID_7									0xFF
#define MDFLAG_ISBT_AimID_Default							(MDFLAG_ISBT_AimID_0, MDFLAG_ISBT_AimID_1, MDFLAG_ISBT_AimID_2, MDFLAG_ISBT_AimID_3, \
															 MDFLAG_ISBT_AimID_4, MDFLAG_ISBT_AimID_5, MDFLAG_ISBT_AimID_6, MDFLAG_ISBT_AimID_7)

// UPCE1
// “3401”，Dec，UPC-E1码相关，UPC-E1识读功能；
#define MDFLAG_UPCE1_Read_Disable							0		// 失能识读UPC-E1码
#define MDFLAG_UPCE1_Read_Enable							1		// 使能识读UPC-E1码
#define MDFLAG_UPCE1_Read_Default							MDFLAG_CodeRead_Disabled

// “3402”，Dec，UPC-E1码相关，校验符确认功能；
#define MDFLAG_UPCE1_ChkDigVer_Disable						0		// 失能检测校验符
#define MDFLAG_UPCE1_ChkDigVer_Enable						1		// 使能检测校验符
#define MDFLAG_UPCE1_ChkDigVer_Default						MDFLAG_UPCE1_ChkDigVer_Enable

// “3403”，Dec，UPC-E1码相关，校验符传送功能；
#define MDFLAG_UPCE1_ChkDigTrans_Disable					0		// 失能传送校验符
#define MDFLAG_UPCE1_ChkDigTrans_Enable						1		// 使能传送校验符
#define MDFLAG_UPCE1_ChkDigTrans_Default					MDFLAG_UPCE1_ChkDigTrans_Enable

// “3404”，Dec，UPC-E1码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“3404H44”字符D，H44
#define MDFLAG_UPCE1_CodeID_D								('D')
#define MDFLAG_UPCE1_CodeID_Default							MDFLAG_UPCE1_CodeID_D

// “3405”，Dec，UPC-E1码相关，插入字符串组功能；
#define MDFLAG_UPCE1_InsertGrpSel_None						0		// 不插入字符串组
#define MDFLAG_UPCE1_InsertGrpSel_Default					MDFLAG_UPCE1_InsertGrpSel_None

// “3406”，Dec，UPC-E1码相关，附加码功能；
#define MDFLAG_UPCE1_SuplDig_None							0		// 无附加码功能
#define MDFLAG_UPCE1_SuplDig_2								1		// 可以识读带2位附加码的UPC-E1码
#define MDFLAG_UPCE1_SuplDig_5								2		// 可以识别带5位附加码的UPC-E1码
#define MDFLAG_UPCE1_SuplDig_25								3		// 可以识别带2位或5位附加码的UPC-E1码
#define MDFLAG_UPCE1_SuplDig_Only							4		// 只解带附加码的UPC-E1条码
#define MDFLAG_UPCE1_SuplDig_Default						MDFLAG_UPCE1_SuplDig_None

// “3407”，Dec，UPC-E1码相关，截去、扩展功能；
#define MDFLAG_UPCE1_TrunExp_None							0		// 不进行截去、扩展
#define MDFLAG_UPCE1_TrunExp_Trun							1		// 截去前导“0”，UPC-E1数据字符的前导一位或多位“0”将被截去
#define MDFLAG_UPCE1_TrunExp_ExpE13							2		// 输出数据扩展成 13位的 EAN-13 码
#define MDFLAG_UPCE1_TrunExp_ExpUA							3		// 输出数据扩展成 12位的 UPC-A 码
#define MDFLAG_UPCE1_TrunExp_Default						MDFLAG_UPCE1_TrunExp_None

// “3408”，Hex，UPC-E1码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_UPCE1_AimID_0								0xFF		// 最多支持8个字符
#define MDFLAG_UPCE1_AimID_1								0xFF
#define MDFLAG_UPCE1_AimID_2								0xFF
#define MDFLAG_UPCE1_AimID_3								0xFF
#define MDFLAG_UPCE1_AimID_4								0xFF
#define MDFLAG_UPCE1_AimID_5								0xFF
#define MDFLAG_UPCE1_AimID_6								0xFF
#define MDFLAG_UPCE1_AimID_7								0xFF
#define MDFLAG_UPCE1_AimID_Default							(MDFLAG_UPCE1_AimID_0, MDFLAG_UPCE1_AimID_1, MDFLAG_UPCE1_AimID_2, MDFLAG_UPCE1_AimID_3, \
															 MDFLAG_UPCE1_AimID_4, MDFLAG_UPCE1_AimID_5, MDFLAG_UPCE1_AimID_6, MDFLAG_UPCE1_AimID_7)

// “3501”，Dec，复合类型码，识读功能；
#define MDFLAG_Composite_Read_Disable						0		// 失能识读
#define MDFLAG_Composite_Read_DataBar_GS1_128				1		// 识读DataBar复合码，GS1-128复合码
#define MDFLAG_Composite_Read_DataBar_GS1_128_UPC_EAN		2		// 识读DataBar复合码，GS1-128复合码以及UPC/EAN复合码
#define MDFLAG_Composite_Read_Default						MDFLAG_Composite_Read_Disable

// “3601”，Dec，韩国邮政码相关，识读功能；
#define MDFLAG_KORPST_Read_Disable							0		// 失能识读韩国邮政码
#define MDFLAG_KORPST_Read_Enable							1		// 使能识读韩国邮政码
#define MDFLAG_KORPST_Read_Default							MDFLAG_KORPST_Read_Disable

// “3602”，Hex，韩国邮政码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“3602H54”字符T，H54
#define MDFLAG_KORPST_CodeID_T								('T')
#define MDFLAG_KORPST_CodeID_Default						MDFLAG_KORPST_CodeID_T

// “3603”，Dec，韩国邮政码相关，插入字符串组功能；
#define MDFLAG_KORPST_InsertGrpSel_None						0		// 不插入字符串组
#define MDFLAG_KORPST_InsertGrpSel_Default					MDFLAG_KORPST_InsertGrpSel_None

// “3604”，Hex，韩国邮政码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_KORPST_AimID_0								0xFF	// 最多支持8个字符
#define MDFLAG_KORPST_AimID_1								0xFF
#define MDFLAG_KORPST_AimID_2								0xFF
#define MDFLAG_KORPST_AimID_3								0xFF
#define MDFLAG_KORPST_AimID_4								0xFF
#define MDFLAG_KORPST_AimID_5								0xFF
#define MDFLAG_KORPST_AimID_6								0xFF
#define MDFLAG_KORPST_AimID_7								0xFF
#define MDFLAG_KORPST_AimID_Default							(MDFLAG_KORPST_AimID_0, MDFLAG_KORPST_AimID_1, MDFLAG_KORPST_AimID_2, MDFLAG_KORPST_AimID_3, \
															 MDFLAG_KORPST_AimID_4, MDFLAG_KORPST_AimID_5, MDFLAG_KORPST_AimID_6, MDFLAG_KORPST_AimID_7)

// QR Code
// “4001”，Dec，QR码相关，识读功能；
#define MDFLAG_QRCODE_Read_Disable							0		// 失能识读QR码
#define MDFLAG_QRCODE_Read_Enable							1		// 使能识读QR码
#define MDFLAG_QRCODE_Read_Default							MDFLAG_CodeRead_Enabled

// “4002”，Hex，QR码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“4002H71”字符q，H71
#define MDFLAG_QRCode_CodeID_q								('q')
#define MDFLAG_QRCode_CodeID_Default						MDFLAG_QRCode_CodeID_q

// “4003”，Dec，QR码相关，禁止访问网站功能；
// MDFLAG_QRCODE_WebCodeForbid_Disabled中forbid表示禁止，Disable表示失能，“否定”+“否定”为“肯定”，表示允许访问网站
// MDFLAG_QRCODE_WebCodeForbid_Disabled中forbid表示禁止，Enable表示使能，“否定”+“肯定”为“否定”，表示不允许访问网站
#define MDFLAG_QRCODE_WebCodeForbid_Disabled				0		// 允许访问网站
#define MDFLAG_QRCODE_WebCodeForbid_Enabled					1		// 不允许访问网站
#define MDFLAG_QRCODE_WebCodeForbid_DouYin_Disabled			2		// 只允许访问"抖音"的网站
#define MDFLAG_QRCODE_WebCodeForbid_Custom_Disabled			3		// 只允许访问"自定义"的网站
#define MDFLAG_QRCODE_WebCodeForbid_Default					MDFLAG_QRCODE_WebCodeForbid_Disabled

// “4004”，Dec， QR码相关，最大码字长度；
#define MDFLAG_QRCODE_MaxLen_9999							9999	// QR条码最大码字长度为9999
#define MDFLAG_QRCODE_MaxLen_Default						MDFLAG_QRCODE_MaxLen_9999

// “4005”，Dec， QR码相关，最小码字长度；
#define MDFLAG_QRCODE_MinLen_1								1		// QR条码最小码字长度为1
#define MDFLAG_QRCODE_MinLen_Default						MDFLAG_QRCODE_MinLen_1

// “4006”，Hex，QR码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_QRCODE_AimID_0								0xFF		// 最多支持8个字符
#define MDFLAG_QRCODE_AimID_1								0xFF
#define MDFLAG_QRCODE_AimID_2								0xFF
#define MDFLAG_QRCODE_AimID_3								0xFF
#define MDFLAG_QRCODE_AimID_4								0xFF
#define MDFLAG_QRCODE_AimID_5								0xFF
#define MDFLAG_QRCODE_AimID_6								0xFF
#define MDFLAG_QRCODE_AimID_7								0xFF
#define MDFLAG_QRCODE_AimID_Default							(MDFLAG_QRCODE_AimID_0, MDFLAG_QRCODE_AimID_1, MDFLAG_QRCODE_AimID_2, MDFLAG_QRCODE_AimID_3, \
															 MDFLAG_QRCODE_AimID_4, MDFLAG_QRCODE_AimID_5, MDFLAG_QRCODE_AimID_6, MDFLAG_QRCODE_AimID_7)

// DataMatrix
// “4101”，Dec，DataMatrix码相关，识读功能；
#define MDFLAG_DATAMATRIX_Read_Disable						0		// 失能识读DataMatrix码
#define MDFLAG_DATAMATRIX_Read_Enable						1		// 使能识读DataMatrix码
#define MDFLAG_DATAMATRIX_Read_Default						MDFLAG_CodeRead_Enabled

// “4102”，Hex，DataMatrix码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“4102H64”字符d，H71
#define MDFLAG_DataMtrix_CodeID_d							('d')
#define MDFLAG_DataMtrix_CodeID_Default						MDFLAG_DataMtrix_CodeID_d

// “4103”，Dec， DataMatrix码相关，最大码字长度；
#define MDFLAG_DATAMATRIX_MaxLen_9999						9999	// DataMatrix条码最大码字长度为9999
#define MDFLAG_DATAMATRIX_MaxLen_Default					MDFLAG_DATAMATRIX_MaxLen_9999

// “4104”，Dec， DataMatrix码相关，最小码字长度；
#define MDFLAG_DATAMATRIX_MinLen_1							1		// DataMatrix条码最小码字长度为1
#define MDFLAG_DATAMATRIX_MinLen_Default					MDFLAG_DATAMATRIX_MinLen_1

// 系统未定义“4103”~“4107”参数功能；

// “4108”，Dec，DataMatrix码相关，PPN（Pharmacy Product Number）码功能；
#define MDFLAG_DATAMATRIX_PPNCODE_Disable					0		// 失能输出PPN（Pharmacy Product Number）码
#define MDFLAG_DATAMATRIX_PPNCODE_Enable					1		// 使能输出PPN（Pharmacy Product Number）码
#define MDFLAG_DATAMATRIX_PPNCODE_Default					MDFLAG_DATAMATRIX_PPNCODE_Disable

// “4109”，Hex，DataMatrix码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_DATAMATRIX_AimID_0							0xFF		// 最多支持8个字符
#define MDFLAG_DATAMATRIX_AimID_1							0xFF
#define MDFLAG_DATAMATRIX_AimID_2							0xFF
#define MDFLAG_DATAMATRIX_AimID_3							0xFF
#define MDFLAG_DATAMATRIX_AimID_4							0xFF
#define MDFLAG_DATAMATRIX_AimID_5							0xFF
#define MDFLAG_DATAMATRIX_AimID_6							0xFF
#define MDFLAG_DATAMATRIX_AimID_7							0xFF
#define MDFLAG_DATAMATRIX_AimID_Default						(MDFLAG_DATAMATRIX_AimID_0, MDFLAG_DATAMATRIX_AimID_1, MDFLAG_DATAMATRIX_AimID_2, MDFLAG_DATAMATRIX_AimID_3, \
															 MDFLAG_DATAMATRIX_AimID_4, MDFLAG_DATAMATRIX_AimID_5, MDFLAG_DATAMATRIX_AimID_6, MDFLAG_DATAMATRIX_AimID_7)

// Chinese Sensible
// “4201”，Dec，汉信码相关，识读功能；
#define MDFLAG_CSCODE_Read_Disable							0		// 失能识读汉信码
#define MDFLAG_CSCODE_Read_Enable							1		// 使能识读汉信码
#define MDFLAG_CSCODE_Read_Default							MDFLAG_CodeRead_Disabled

// “4202”，Hex，汉信码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“4201H68”字符h，H68
#define MDFLAG_CSCode_CodeID_h								('h')
#define MDFLAG_CSCode_CodeID_Default						MDFLAG_CSCode_CodeID_h

// “4203”，Dec， 汉信码相关，最大码字长度；
#define MDFLAG_CSCODE_MaxLen_9999							9999	// 汉信码最大码字长度为9999
#define MDFLAG_CSCODE_MaxLen_Default						MDFLAG_CSCODE_MaxLen_9999

// “4204”，Dec， 汉信码相关，最小码字长度；
#define MDFLAG_CSCODE_MinLen_1								1		// 汉信码最小码字长度为1
#define MDFLAG_CSCODE_MinLen_Default						MDFLAG_CSCODE_MinLen_1

// “4205”，Hex，汉信码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_CSCODE_AimID_0								0xFF		// 最多支持8个字符
#define MDFLAG_CSCODE_AimID_1								0xFF
#define MDFLAG_CSCODE_AimID_2								0xFF
#define MDFLAG_CSCODE_AimID_3								0xFF
#define MDFLAG_CSCODE_AimID_4								0xFF
#define MDFLAG_CSCODE_AimID_5								0xFF
#define MDFLAG_CSCODE_AimID_6								0xFF
#define MDFLAG_CSCODE_AimID_7								0xFF
#define MDFLAG_CSCODE_AimID_Default							(MDFLAG_CSCODE_AimID_0, MDFLAG_CSCODE_AimID_1, MDFLAG_CSCODE_AimID_2, MDFLAG_CSCODE_AimID_3, \
															 MDFLAG_CSCODE_AimID_4, MDFLAG_CSCODE_AimID_5, MDFLAG_CSCODE_AimID_6, MDFLAG_CSCODE_AimID_7)

// Aztec
// “4301”，Dec，Aztec码相关，识读功能；
#define MDFLAG_AZTEC_Read_Disable							0		// 失能识读Aztec码
#define MDFLAG_AZTEC_Read_Enable							1		// 使能识读Aztec码
#define MDFLAG_AZTEC_Read_Default							MDFLAG_CodeRead_Disabled

// “4302”，Hex，Aztec码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“4302H61”字符a，H61
#define MDFLAG_Aztec_CodeID_a								('a')
#define MDFLAG_Aztec_CodeID_Default							MDFLAG_Aztec_CodeID_a

// “4303”，Dec， Aztec码相关，最大码字长度；
#define MDFLAG_AZTEC_MaxLen_9999							9999	// Aztec码最大码字长度为9999
#define MDFLAG_AZTEC_MaxLen_Default							MDFLAG_AZTEC_MaxLen_9999

// “4304”，Dec， Aztec码相关，最小码字长度；
#define MDFLAG_AZTEC_MinLen_1								1		// Aztec码最小码字长度为1
#define MDFLAG_AZTEC_MinLen_Default							MDFLAG_AZTEC_MinLen_1

// “4305”，Hex，Aztec码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_AZTEC_AimID_0								0xFF		// 最多支持8个字符
#define MDFLAG_AZTEC_AimID_1								0xFF
#define MDFLAG_AZTEC_AimID_2								0xFF
#define MDFLAG_AZTEC_AimID_3								0xFF
#define MDFLAG_AZTEC_AimID_4								0xFF
#define MDFLAG_AZTEC_AimID_5								0xFF
#define MDFLAG_AZTEC_AimID_6								0xFF
#define MDFLAG_AZTEC_AimID_7								0xFF
#define MDFLAG_AZTEC_AimID_Default							(MDFLAG_AZTEC_AimID_0, MDFLAG_AZTEC_AimID_1, MDFLAG_AZTEC_AimID_2, MDFLAG_AZTEC_AimID_3, \
															 MDFLAG_AZTEC_AimID_4, MDFLAG_AZTEC_AimID_5, MDFLAG_AZTEC_AimID_6, MDFLAG_AZTEC_AimID_7)

// Maxicode
// “4401”，Dec，Maxicode码相关，识读功能；
#define MDFLAG_MAXICODE_Read_Disable						0		// 失能识读Maxicode码
#define MDFLAG_MAXICODE_Read_Enable							1		// 使能识读Maxicode码
#define MDFLAG_MAXICODE_Read_Default						MDFLAG_CodeRead_Disabled

// “4402”，Dec， Maxicode码相关，最大码字长度；
#define MDFLAG_MAXICODE_MaxLen_9999							9999	// Maxicode码最大码字长度为9999
#define MDFLAG_MAXICODE_MaxLen_Default						MDFLAG_MAXICODE_MaxLen_9999

// “4403”，Dec， Maxicode码相关，最小码字长度；
#define MDFLAG_MAXICODE_MinLen_1							1		// Maxicode码最小码字长度为1
#define MDFLAG_MAXICODE_MinLen_Default						MDFLAG_MAXICODE_MinLen_1

// “4404”，Hex，Maxicode码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_MAXICODE_AimID_0								0xFF		// 最多支持8个字符
#define MDFLAG_MAXICODE_AimID_1								0xFF
#define MDFLAG_MAXICODE_AimID_2								0xFF
#define MDFLAG_MAXICODE_AimID_3								0xFF
#define MDFLAG_MAXICODE_AimID_4								0xFF
#define MDFLAG_MAXICODE_AimID_5								0xFF
#define MDFLAG_MAXICODE_AimID_6								0xFF
#define MDFLAG_MAXICODE_AimID_7								0xFF
#define MDFLAG_MAXICODE_AimID_Default						(MDFLAG_MAXICODE_AimID_0, MDFLAG_MAXICODE_AimID_1, MDFLAG_MAXICODE_AimID_2, MDFLAG_MAXICODE_AimID_3, \
															 MDFLAG_MAXICODE_AimID_4, MDFLAG_MAXICODE_AimID_5, MDFLAG_MAXICODE_AimID_6, MDFLAG_MAXICODE_AimID_7)

// MicroQR
// “4501”，Dec，MicroQR码相关，识读功能；
#define MDFLAG_MICROQR_Read_Disable							0		// 失能识读MicroQR码
#define MDFLAG_MICROQR_Read_Enable							1		// 使能识读MicroQR码
#define MDFLAG_MICROQR_Read_Default							MDFLAG_CodeRead_Disabled

// “4502”，Hex，MicroQR码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“4502H71”字符q，H71
#define MDFLAG_MicroQR_CodeID_q								('q')
#define MDFLAG_MicroQR_CodeID_Default						MDFLAG_MicroQR_CodeID_q

// “4503”，Dec， MicroQR码相关，最大码字长度；
#define MDFLAG_MICROQR_MaxLen_9999							9999	// MicroQR码最大码字长度为9999
#define MDFLAG_MICROQR_MaxLen_Default						MDFLAG_MICROQR_MaxLen_9999

// “4504”，Dec， MicroQR码相关，最小码字长度；
#define MDFLAG_MICROQR_MinLen_1								1		// MicroQR码最小码字长度为1
#define MDFLAG_MICROQR_MinLen_Default						MDFLAG_MICROQR_MinLen_1

// “4505”，Hex，MicroQR码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_MICROQR_AimID_0								0xFF		// 最多支持8个字符
#define MDFLAG_MICROQR_AimID_1								0xFF
#define MDFLAG_MICROQR_AimID_2								0xFF
#define MDFLAG_MICROQR_AimID_3								0xFF
#define MDFLAG_MICROQR_AimID_4								0xFF
#define MDFLAG_MICROQR_AimID_5								0xFF
#define MDFLAG_MICROQR_AimID_6								0xFF
#define MDFLAG_MICROQR_AimID_7								0xFF
#define MDFLAG_MICROQR_AimID_Default						(MDFLAG_MICROQR_AimID_0, MDFLAG_MICROQR_AimID_1, MDFLAG_MICROQR_AimID_2, MDFLAG_MICROQR_AimID_3, \
															 MDFLAG_MICROQR_AimID_4, MDFLAG_MICROQR_AimID_5, MDFLAG_MICROQR_AimID_6, MDFLAG_MICROQR_AimID_7)

// CodaBlockF
// “4601”，Dec，CodaBlockF码相关，识读功能；
#define MDFLAG_CODEBLOCKF_Read_Disable						0		// 失能识读CodaBlockF码
#define MDFLAG_CODEBLOCKF_Read_Enable						1		// 使能识读CodaBlockF码
#define MDFLAG_CODEBLOCKF_Read_Default						MDFLAG_CodeRead_Disabled

// “4602”，Hex，CodaBlockF码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“4602H63”字符c，H63
#define MDFLAG_CodaBlockF_CodeID_c							('c')
#define MDFLAG_CodaBlockF_CodeID_Default					MDFLAG_CodaBlockF_CodeID_c

// “4603”，Dec， CodaBlockF码相关，最大码字长度；
#define MDFLAG_CODABLOCKF_MaxLen_9999						9999	// CodaBlockF码最大码字长度为9999
#define MDFLAG_CODABLOCKF_MaxLen_Default					MDFLAG_CODABLOCKF_MaxLen_9999

// “4604”，Dec，  CodaBlockF码相关，最小码字长度；
#define MDFLAG_CODABLOCKF_MinLen_1							1		// CodaBlockF码最小码字长度为1
#define MDFLAG_CODABLOCKF_MinLen_Default					MDFLAG_CODABLOCKF_MinLen_1

// “4605”，Hex，CodaBlockF码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_CODABLOCKF_AimID_0							0xFF		// 最多支持8个字符
#define MDFLAG_CODABLOCKF_AimID_1							0xFF
#define MDFLAG_CODABLOCKF_AimID_2							0xFF
#define MDFLAG_CODABLOCKF_AimID_3							0xFF
#define MDFLAG_CODABLOCKF_AimID_4							0xFF
#define MDFLAG_CODABLOCKF_AimID_5							0xFF
#define MDFLAG_CODABLOCKF_AimID_6							0xFF
#define MDFLAG_CODABLOCKF_AimID_7							0xFF
#define MDFLAG_CODABLOCKF_AimID_Default						(MDFLAG_CODABLOCKF_AimID_0, MDFLAG_CODABLOCKF_AimID_1, MDFLAG_CODABLOCKF_AimID_2, MDFLAG_CODABLOCKF_AimID_3, \
															 MDFLAG_CODABLOCKF_AimID_4, MDFLAG_CODABLOCKF_AimID_5, MDFLAG_CODABLOCKF_AimID_6, MDFLAG_CODABLOCKF_AimID_7)

// GMCode
// “4701”，Dec，二维网格码相关，识读功能；
#define MDFLAG_GMCODE_Read_Disable							0		// 失能识读二维网格码
#define MDFLAG_GMCODE_Read_Enable							1		// 使能识读二维网格码
#define MDFLAG_GMCODE_Read_Default							MDFLAG_CodeRead_Disabled

// “4702”，Hex，二维网格码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“4702H67”字符g，H67
#define MDFLAG_GMCode_CodeID_g								('g')
#define MDFLAG_GMCode_CodeID_Default						MDFLAG_GMCode_CodeID_g

// “4703”，Dec， 二维网格码相关，最大码字长度；
#define MDFLAG_GMCODE_MaxLen_9999							9999	// 二维网格码最大码字长度为9999
#define MDFLAG_GMCODE_MaxLen_Default						MDFLAG_GMCODE_MaxLen_9999

// “4704”，Dec，  二维网格码相关，最小码字长度；
#define MDFLAG_GMCODE_MinLen_1								1		// 二维网格码最小码字长度为1
#define MDFLAG_GMCODE_MinLen_Default						MDFLAG_GMCODE_MinLen_1

// “4705”，Hex，二维网格码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_GMCODE_AimID_0								0xFF		// 最多支持8个字符
#define MDFLAG_GMCODE_AimID_1								0xFF
#define MDFLAG_GMCODE_AimID_2								0xFF
#define MDFLAG_GMCODE_AimID_3								0xFF
#define MDFLAG_GMCODE_AimID_4								0xFF
#define MDFLAG_GMCODE_AimID_5								0xFF
#define MDFLAG_GMCODE_AimID_6								0xFF
#define MDFLAG_GMCODE_AimID_7								0xFF
#define MDFLAG_GMCODE_AimID_Default							(MDFLAG_GMCODE_AimID_0, MDFLAG_GMCODE_AimID_1, MDFLAG_GMCODE_AimID_2, MDFLAG_GMCODE_AimID_3, \
															 MDFLAG_GMCODE_AimID_4, MDFLAG_GMCODE_AimID_5, MDFLAG_GMCODE_AimID_6, MDFLAG_GMCODE_AimID_7)

// BinaryCode
// “4801”，Dec，Binary码相关，识读功能；
#define MDFLAG_BINARYCODE_Read_Disable						0		// 失能识读Binary码
#define MDFLAG_BINARYCODE_Read_Enable						1		// 使能识读Binary码
#define MDFLAG_BINARYCODE_Read_Default						MDFLAG_CodeRead_Disabled

// “4802”，Hex，Binary码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“4802H62”字符b，H62
#define MDFLAG_BinaryCode_CodeID_b							('b')
#define MDFLAG_BinaryCode_CodeID_Default					MDFLAG_BinaryCode_CodeID_b

// “4803”，Dec， Binary码相关，最大码字长度；
#define MDFLAG_BINARYCODE_MaxLen_9999						9999	// Binary码最大码字长度为9999
#define MDFLAG_BINARYCODE_MaxLen_Default					MDFLAG_BINARYCODE_MaxLen_9999

// “4804”，Dec，  Binary码相关，最小码字长度；
#define MDFLAG_BINARYCODE_MinLen_1							1		// Binary码最小码字长度为1
#define MDFLAG_BINARYCODE_MinLen_Default					MDFLAG_BINARYCODE_MinLen_1

// “4805”，Hex，Binary码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_BINARYCODE_AimID_0								0xFF		// 最多支持8个字符
#define MDFLAG_BINARYCODE_AimID_1								0xFF
#define MDFLAG_BINARYCODE_AimID_2								0xFF
#define MDFLAG_BINARYCODE_AimID_3								0xFF
#define MDFLAG_BINARYCODE_AimID_4								0xFF
#define MDFLAG_BINARYCODE_AimID_5								0xFF
#define MDFLAG_BINARYCODE_AimID_6								0xFF
#define MDFLAG_BINARYCODE_AimID_7								0xFF
#define MDFLAG_BINARYCODE_AimID_Default							(MDFLAG_BINARYCODE_AimID_0, MDFLAG_BINARYCODE_AimID_1, MDFLAG_BINARYCODE_AimID_2, MDFLAG_BINARYCODE_AimID_3, \
															 MDFLAG_BINARYCODE_AimID_4, MDFLAG_BINARYCODE_AimID_5, MDFLAG_BINARYCODE_AimID_6, MDFLAG_BINARYCODE_AimID_7)

// DotCode
// “4901”，Dec，Dot码相关，识读功能；
#define MDFLAG_DotCode_Read_Disable							0		// 失能识读Dot码
#define MDFLAG_DotCode_Read_Enable							1		// 使能识读Dot码
#define MDFLAG_DotCode_Read_Default							MDFLAG_DotCode_Read_Disable

// “4902”，Hex，Dot码相关，自定义码制识别符功能；
// 自定义码制识别符，取值范围0x00~0xff，如默认值为“4902H5A”字符Z，H5A
#define MDFLAG_DotCode_CodeID_Z								('Z')
#define MDFLAG_DotCode_CodeID_Default						MDFLAG_DotCode_CodeID_Z

// “4903”，Dec，Dot码相关，最大码字长度；
#define MDFLAG_DotCode_MaxLen_9999							9999	// Dot码最大码字长度为9999
#define MDFLAG_DotCode_MaxLen_Default						MDFLAG_DotCode_MaxLen_9999

// “4904”，Dec， Dot码相关，最小码字长度；
#define MDFLAG_DotCode_MinLen_1								1		// Dot码最小码字长度为1
#define MDFLAG_DotCode_MinLen_Default						MDFLAG_DotCode_MinLen_1

// “4905”，Hex，Dot码相关，自定义AIM ID功能；
// 自定义AIM ID，取值范围0x00~0xff，共8个字节， 默认值: FFFFFFFFFFFFFFFF (使用标准AIM ID)
#define MDFLAG_DotCode_AimID_0								0xFF		// 最多支持8个字符
#define MDFLAG_DotCode_AimID_1								0xFF
#define MDFLAG_DotCode_AimID_2								0xFF
#define MDFLAG_DotCode_AimID_3								0xFF
#define MDFLAG_DotCode_AimID_4								0xFF
#define MDFLAG_DotCode_AimID_5								0xFF
#define MDFLAG_DotCode_AimID_6								0xFF
#define MDFLAG_DotCode_AimID_7								0xFF
#define MDFLAG_DotCode_AimID_Default						(MDFLAG_DotCode_AimID_0, MDFLAG_DotCode_AimID_1, MDFLAG_DotCode_AimID_2, MDFLAG_DotCode_AimID_3, \
															 MDFLAG_DotCode_AimID_4, MDFLAG_DotCode_AimID_5, MDFLAG_DotCode_AimID_6, MDFLAG_DotCode_AimID_7)

// Insert string settings
// “8001”，Hex，字符串设置，前缀字符串设置功能；
#define MDFLAG_STRSET_Prefix_None							0		// 没有前缀字符串设置
#define MDFLAG_STRSET_Prefix_0								'A'		// 最多支持22个字符
#define MDFLAG_STRSET_Prefix_1								'A'
#define MDFLAG_STRSET_Prefix_2								'A'
#define MDFLAG_STRSET_Prefix_3								'A'
#define MDFLAG_STRSET_Prefix_4								'A'
#define MDFLAG_STRSET_Prefix_5								'A'
#define MDFLAG_STRSET_Prefix_6								'A'
#define MDFLAG_STRSET_Prefix_7								'A'
#define MDFLAG_STRSET_Prefix_8								'A'
#define MDFLAG_STRSET_Prefix_9								'A'
#define MDFLAG_STRSET_Prefix_10								'A'
#define MDFLAG_STRSET_Prefix_11								'A'
#define MDFLAG_STRSET_Prefix_12								'A'
#define MDFLAG_STRSET_Prefix_13								'A'
#define MDFLAG_STRSET_Prefix_14								'A'
#define MDFLAG_STRSET_Prefix_15								'A'
#define MDFLAG_STRSET_Prefix_16								'A'
#define MDFLAG_STRSET_Prefix_17								'A'
#define MDFLAG_STRSET_Prefix_18								'A'
#define MDFLAG_STRSET_Prefix_19								'A'
#define MDFLAG_STRSET_Prefix_20								'A'
#define MDFLAG_STRSET_Prefix_21								'A'
#define MDFLAG_STRSET_Prefix_Default						MDFLAG_STRSET_Prefix_None

// “8002”，Hex，字符串设置，后缀字符串设置功能；
#define MDFLAG_STRSET_Suffix_0D								0x0D
#define MDFLAG_STRSET_Suffix_0A								0x0A
#define MDFLAG_STRSET_Suffix_0								0x0d	// 最多支持22个字符
#define MDFLAG_STRSET_Suffix_1								0x0a
#define MDFLAG_STRSET_Suffix_2								'A'
#define MDFLAG_STRSET_Suffix_3								'A'
#define MDFLAG_STRSET_Suffix_4								'A'
#define MDFLAG_STRSET_Suffix_5								'A'
#define MDFLAG_STRSET_Suffix_6								'A'
#define MDFLAG_STRSET_Suffix_7								'A'
#define MDFLAG_STRSET_Suffix_8								'A'
#define MDFLAG_STRSET_Suffix_9								'A'
#define MDFLAG_STRSET_Suffix_10								'A'
#define MDFLAG_STRSET_Suffix_11								'A'
#define MDFLAG_STRSET_Suffix_12								'A'
#define MDFLAG_STRSET_Suffix_13								'A'
#define MDFLAG_STRSET_Suffix_14								'A'
#define MDFLAG_STRSET_Suffix_15								'A'
#define MDFLAG_STRSET_Suffix_16								'A'
#define MDFLAG_STRSET_Suffix_17								'A'
#define MDFLAG_STRSET_Suffix_18								'A'
#define MDFLAG_STRSET_Suffix_19								'A'
#define MDFLAG_STRSET_Suffix_20								'A'
#define MDFLAG_STRSET_Suffix_21								'A'
#define MDFLAG_STRSET_Suffix_Default						(MDFLAG_STRSET_Suffix_0D, MDFLAG_STRSET_Suffix_0A)

// “8003”，Hex，字符串设置，前置字符串设置功能；
#define MDFLAG_STRSET_Preamb_None							0
#define MDFLAG_STRSET_Preamb_0								'A'
#define MDFLAG_STRSET_Preamb_1								'A'
#define MDFLAG_STRSET_Preamb_2								'A'
#define MDFLAG_STRSET_Preamb_3								'A'
#define MDFLAG_STRSET_Preamb_4								'A'
#define MDFLAG_STRSET_Preamb_5								'A'
#define MDFLAG_STRSET_Preamb_6								'A'
#define MDFLAG_STRSET_Preamb_7								'A'
#define MDFLAG_STRSET_Preamb_8								'A'
#define MDFLAG_STRSET_Preamb_9								'A'
#define MDFLAG_STRSET_Preamb_10								'A'
#define MDFLAG_STRSET_Preamb_11								'A'
#define MDFLAG_STRSET_Preamb_12								'A'
#define MDFLAG_STRSET_Preamb_13								'A'
#define MDFLAG_STRSET_Preamb_14								'A'
#define MDFLAG_STRSET_Preamb_15								'A'
#define MDFLAG_STRSET_Preamb_16								'A'
#define MDFLAG_STRSET_Preamb_17								'A'
#define MDFLAG_STRSET_Preamb_18								'A'
#define MDFLAG_STRSET_Preamb_19								'A'
#define MDFLAG_STRSET_Preamb_20								'A'
#define MDFLAG_STRSET_Preamb_21								'A'
#define MDFLAG_STRSET_Preamb_Default						MDFLAG_STRSET_Preamb_None

// “8004”，Hex，字符串设置，后置字符串设置功能；
#define MDFLAG_STRSET_Postamb_None							0
#define MDFLAG_STRSET_Postamb_0								'A'
#define MDFLAG_STRSET_Postamb_1								'A'
#define MDFLAG_STRSET_Postamb_2								'A'
#define MDFLAG_STRSET_Postamb_3								'A'
#define MDFLAG_STRSET_Postamb_4								'A'
#define MDFLAG_STRSET_Postamb_5								'A'
#define MDFLAG_STRSET_Postamb_6								'A'
#define MDFLAG_STRSET_Postamb_7								'A'
#define MDFLAG_STRSET_Postamb_8								'A'
#define MDFLAG_STRSET_Postamb_9								'A'
#define MDFLAG_STRSET_Postamb_10							'A'
#define MDFLAG_STRSET_Postamb_11							'A'
#define MDFLAG_STRSET_Postamb_12							'A'
#define MDFLAG_STRSET_Postamb_13							'A'
#define MDFLAG_STRSET_Postamb_14							'A'
#define MDFLAG_STRSET_Postamb_15							'A'
#define MDFLAG_STRSET_Postamb_16							'A'
#define MDFLAG_STRSET_Postamb_17							'A'
#define MDFLAG_STRSET_Postamb_18							'A'
#define MDFLAG_STRSET_Postamb_19							'A'
#define MDFLAG_STRSET_Postamb_20							'A'
#define MDFLAG_STRSET_Postamb_21							'A'
#define MDFLAG_STRSET_Postamb_Default						MDFLAG_STRSET_Postamb_None

// “8005”，Hex，字符串设置，字符串组G1设置功能；
#define MDFLAG_STRSET_G1_None								0
#define MDFLAG_STRSET_G1_0									'A'
#define MDFLAG_STRSET_G1_1									'A'
#define MDFLAG_STRSET_G1_2									'A'
#define MDFLAG_STRSET_G1_3									'A'
#define MDFLAG_STRSET_G1_4									'A'
#define MDFLAG_STRSET_G1_5									'A'
#define MDFLAG_STRSET_G1_6									'A'
#define MDFLAG_STRSET_G1_7									'A'
#define MDFLAG_STRSET_G1_8									'A'
#define MDFLAG_STRSET_G1_9									'A'
#define MDFLAG_STRSET_G1_10									'A'
#define MDFLAG_STRSET_G1_11									'A'
#define MDFLAG_STRSET_G1_12									'A'
#define MDFLAG_STRSET_G1_13									'A'
#define MDFLAG_STRSET_G1_14									'A'
#define MDFLAG_STRSET_G1_15									'A'
#define MDFLAG_STRSET_G1_16									'A'
#define MDFLAG_STRSET_G1_17									'A'
#define MDFLAG_STRSET_G1_18									'A'
#define MDFLAG_STRSET_G1_19									'A'
#define MDFLAG_STRSET_G1_20									'A'
#define MDFLAG_STRSET_G1_21									'A'
#define MDFLAG_STRSET_G1_Default							MDFLAG_STRSET_G1_None

// “8006”，Hex，字符串设置，字符串组G2设置功能；
#define MDFLAG_STRSET_G2_None								0
#define MDFLAG_STRSET_G2_0									'A'
#define MDFLAG_STRSET_G2_1									'A'
#define MDFLAG_STRSET_G2_2									'A'
#define MDFLAG_STRSET_G2_3									'A'
#define MDFLAG_STRSET_G2_4									'A'
#define MDFLAG_STRSET_G2_5									'A'
#define MDFLAG_STRSET_G2_6									'A'
#define MDFLAG_STRSET_G2_7									'A'
#define MDFLAG_STRSET_G2_8									'A'
#define MDFLAG_STRSET_G2_9									'A'
#define MDFLAG_STRSET_G2_10									'A'
#define MDFLAG_STRSET_G2_11									'A'
#define MDFLAG_STRSET_G2_12									'A'
#define MDFLAG_STRSET_G2_13									'A'
#define MDFLAG_STRSET_G2_14									'A'
#define MDFLAG_STRSET_G2_15									'A'
#define MDFLAG_STRSET_G2_16									'A'
#define MDFLAG_STRSET_G2_17									'A'
#define MDFLAG_STRSET_G2_18									'A'
#define MDFLAG_STRSET_G2_19									'A'
#define MDFLAG_STRSET_G2_20									'A'
#define MDFLAG_STRSET_G2_21									'A'
#define MDFLAG_STRSET_G2_Default							MDFLAG_STRSET_G2_None

// “8007”，Hex，字符串设置，字符串组G3设置功能；
#define MDFLAG_STRSET_G3_None								0
#define MDFLAG_STRSET_G3_0									'A'
#define MDFLAG_STRSET_G3_1									'A'
#define MDFLAG_STRSET_G3_2									'A'
#define MDFLAG_STRSET_G3_3									'A'
#define MDFLAG_STRSET_G3_4									'A'
#define MDFLAG_STRSET_G3_5									'A'
#define MDFLAG_STRSET_G3_6									'A'
#define MDFLAG_STRSET_G3_7									'A'
#define MDFLAG_STRSET_G3_8									'A'
#define MDFLAG_STRSET_G3_9									'A'
#define MDFLAG_STRSET_G3_10									'A'
#define MDFLAG_STRSET_G3_11									'A'
#define MDFLAG_STRSET_G3_12									'A'
#define MDFLAG_STRSET_G3_13									'A'
#define MDFLAG_STRSET_G3_14									'A'
#define MDFLAG_STRSET_G3_15									'A'
#define MDFLAG_STRSET_G3_16									'A'
#define MDFLAG_STRSET_G3_17									'A'
#define MDFLAG_STRSET_G3_18									'A'
#define MDFLAG_STRSET_G3_19									'A'
#define MDFLAG_STRSET_G3_20									'A'
#define MDFLAG_STRSET_G3_21									'A'
#define MDFLAG_STRSET_G3_Default							MDFLAG_STRSET_G3_None

// “8008”，Hex，字符串设置，字符串组G4设置功能；
#define MDFLAG_STRSET_G4_None								0
#define MDFLAG_STRSET_G4_0									'A'
#define MDFLAG_STRSET_G4_1									'A'
#define MDFLAG_STRSET_G4_2									'A'
#define MDFLAG_STRSET_G4_3									'A'
#define MDFLAG_STRSET_G4_4									'A'
#define MDFLAG_STRSET_G4_5									'A'
#define MDFLAG_STRSET_G4_6									'A'
#define MDFLAG_STRSET_G4_7									'A'
#define MDFLAG_STRSET_G4_8									'A'
#define MDFLAG_STRSET_G4_9									'A'
#define MDFLAG_STRSET_G4_10									'A'
#define MDFLAG_STRSET_G4_11									'A'
#define MDFLAG_STRSET_G4_12									'A'
#define MDFLAG_STRSET_G4_13									'A'
#define MDFLAG_STRSET_G4_14									'A'
#define MDFLAG_STRSET_G4_15									'A'
#define MDFLAG_STRSET_G4_16									'A'
#define MDFLAG_STRSET_G4_17									'A'
#define MDFLAG_STRSET_G4_18									'A'
#define MDFLAG_STRSET_G4_19									'A'
#define MDFLAG_STRSET_G4_20									'A'
#define MDFLAG_STRSET_G4_21									'A'
#define MDFLAG_STRSET_G4_Default							MDFLAG_STRSET_G4_None

// “8009”，Hex，字符串设置，FN1替换字符串设置功能；
#define MDFLAG_STRSET_FN1Substu_None						0x20	// 字符' '（空格）
#define MDFLAG_STRSET_FN1Substu_0							' '
#define MDFLAG_STRSET_FN1Substu_1							'A'
#define MDFLAG_STRSET_FN1Substu_2							'A'
#define MDFLAG_STRSET_FN1Substu_3							'A'
#define MDFLAG_STRSET_FN1Substu_Default						MDFLAG_STRSET_FN1Substu_None

// “8010”，Hex，字符串设置，截去前导字符串组G5选项功能；
#define MDFLAG_STRSET_TruecLeadG5_0							0x30	// 字符‘0’
#define MDFLAG_STRSET_TruecLeadG5_Default					MDFLAG_STRSET_TruecLeadG5_0

// “8011”，Hex，字符串设置，单字符G5重复选项功能；
#define MDFLAG_STRSET_RepeatG5_Time_1						1		// 单字符G5重复1次
#define MDFLAG_STRSET_RepeatG5_Time_Default					MDFLAG_STRSET_RepeatG5_Time_1

// “8012”，Hex，字符串设置，截去末尾字符串组G6功能；
#define MDFLAG_STRSET_TruncEndG6_0							0x30	// 字符‘0’
#define MDFLAG_STRSET_TruncEndG6_Default					MDFLAG_STRSET_TruncEndG6_0

// “8013”，Hex，字符串设置，单字符G6重复选项功能；
#define MDFLAG_STRSET_RepeatG6_Time_1						1		// 单字符G6重复1次
#define MDFLAG_STRSET_RepeatG6_Time_Default					MDFLAG_STRSET_RepeatG6_Time_1

// “8014”，Hex，字符串设置，单字符C1替换选项功能；
#define MDFLAG_STRSET_SingReplaC1_0							0
#define MDFLAG_STRSET_SingReplaC1_1							0
#define MDFLAG_STRSET_SingReplaC1_Default					(MDFLAG_STRSET_SingReplaC1_0, MDFLAG_STRSET_SingReplaC1_1)

// “8015”，Hex，字符串设置，单字符C2替换选项功能；
#define MDFLAG_STRSET_SingReplaC2_0							0
#define MDFLAG_STRSET_SingReplaC2_1							0
#define MDFLAG_STRSET_SingReplaC2_Default					(MDFLAG_STRSET_SingReplaC2_0, MDFLAG_STRSET_SingReplaC2_1)

// “8016”，Hex，字符串设置，单字符C3替换选项功能；
#define MDFLAG_STRSET_SingReplaC3_0							0
#define MDFLAG_STRSET_SingReplaC3_1							0
#define MDFLAG_STRSET_SingReplaC3_Default					(MDFLAG_STRSET_SingReplaC3_0, MDFLAG_STRSET_SingReplaC3_1)

// “8017”，Hex，字符串设置，“多字符”替换“单字符”功能选项功能；
#define MDFLAG_STRSET_ReplaChar2Str_None					0		// 功能不启用
#define MDFLAG_STRSET_ReplaChar2Str_0						'C'		// “单字符”目标
#define MDFLAG_STRSET_ReplaChar2Str_1						'S'		// “多字符”集合中第1个字符
#define MDFLAG_STRSET_ReplaChar2Str_2						'S'		// "多字符"集合中第2个字符
#define MDFLAG_STRSET_ReplaChar2Str_3						'S'
#define MDFLAG_STRSET_ReplaChar2Str_4						'S'
#define MDFLAG_STRSET_ReplaChar2Str_5						'S'
#define MDFLAG_STRSET_ReplaChar2Str_6						'S'
#define MDFLAG_STRSET_ReplaChar2Str_7						'S'
#define MDFLAG_STRSET_ReplaChar2Str_8						'S'
#define MDFLAG_STRSET_ReplaChar2Str_9						'S'
#define MDFLAG_STRSET_ReplaChar2Str_10						'S'
#define MDFLAG_STRSET_ReplaChar2Str_11						'S'
#define MDFLAG_STRSET_ReplaChar2Str_12						'S'
#define MDFLAG_STRSET_ReplaChar2Str_13						'S'
#define MDFLAG_STRSET_ReplaChar2Str_14						'S'
#define MDFLAG_STRSET_ReplaChar2Str_15						'S'
#define MDFLAG_STRSET_ReplaChar2Str_16						'S'
#define MDFLAG_STRSET_ReplaChar2Str_17						'S'
#define MDFLAG_STRSET_ReplaChar2Str_18						'S'
#define MDFLAG_STRSET_ReplaChar2Str_19						'S'
#define MDFLAG_STRSET_ReplaChar2Str_20						'S'
#define MDFLAG_STRSET_ReplaChar2Str_21						'S'		// 最多支持22个字符 (1个字符“单字符”目标，“多字符”集合)
#define MDFLAG_STRSET_ReplaChar2Str_Default					MDFLAG_STRSET_ReplaChar2Str_None

// Insert String Position
// “8101”，Dec，字符串设置，字符串组G1插入位置功能；
#define MDFLAG_STRPOS_G1_0									0
#define MDFLAG_STRPOS_G1_Default							MDFLAG_STRPOS_G1_0

// “8102”，Dec，字符串设置，字符串组G2插入位置功能；
#define MDFLAG_STRPOS_G2_0									0
#define MDFLAG_STRPOS_G2_Default							MDFLAG_STRPOS_G2_0

// “8103”，Dec，字符串设置，字符串组G3插入位置功能；
#define MDFLAG_STRPOS_G3_0									0
#define MDFLAG_STRPOS_G3_Default							MDFLAG_STRPOS_G3_0

// “8104”，Dec，字符串设置，字符串组G4插入位置功能；
#define MDFLAG_STRPOS_G4_0									0
#define MDFLAG_STRPOS_G4_Default							MDFLAG_STRPOS_G4_0

// “8105”，Dec，字符串设置，码制识别符位置功能；
#define MDFLAG_STRPOS_CodeID_Before							0		// 将码制识别符放在条码数据字符前
#define MDFLAG_STRPOS_CodeID_After							1		// 将码制识别符放在条码数据字符后
#define MDFLAG_STRPOS_CodeID_Default						MDFLAG_STRPOS_CodeID_Before

// String Transmission
// “8201”，Dec，字符串传送，前缀字符串传送功能；
#define MDFLAG_STRTRANS_Pre_Disable							0		// 失能传送前缀字符串
#define MDFLAG_STRTRANS_Pre_Enable							1		// 使能传送前缀字符串
#define MDFLAG_STRTRANS_Pre_Default							MDFLAG_STRTRANS_Pre_Disable

// “8202”，Dec，字符串传送，后缀字符串传送功能；
#define MDFLAG_STRTRANS_Suf_Disable							0		// 失能传送后缀字符串
#define MDFLAG_STRTRANS_Suf_Enable							1		// 使能传送后缀字符串
#define MDFLAG_STRTRANS_Suf_Default							MDFLAG_STRTRANS_Suf_Enable

// “8203”，Dec，字符串传送，条码类型名传送功能；
#define MDFLAG_STRTRANS_CodeName_Disable					0		// 失能传送条码类型名
#define MDFLAG_STRTRANS_CodeName_Enable						1		// 使能传送条码类型名
#define MDFLAG_STRTRANS_CodeName_Default					MDFLAG_STRTRANS_CodeName_Disable

// “8204”，Dec，字符串传送，前置字符串传送功能；
#define MDFLAG_STRTRANS_Preamb_Disable						0		// 失能传送前置字符串
#define MDFLAG_STRTRANS_Preamb_Enable						1		// 使能传送前置字符串
#define MDFLAG_STRTRANS_Preamb_Default						MDFLAG_STRTRANS_Preamb_Disable

// “8205”，Dec，字符串传送，后置字符串传送功能；
#define MDFLAG_STRTRANS_Postamb_Disable						0		// 失能传送后置字符串
#define MDFLAG_STRTRANS_Postamb_Enable						1		// 使能传送后置字符串
#define MDFLAG_STRTRANS_Postamb_Default						MDFLAG_STRTRANS_Postamb_Disable

// “8206”，Dec，字符串传送，码制识别符传送功能；
#define MDFLAG_STRTRANS_CodeID_Disable						0		// 失能传送码制识别符
#define MDFLAG_STRTRANS_CodeID_AIMID						2		// 仅传送AIM识别符
#if (MACR_CUSTOMER_Hyosung == 1)
	#define MDFLAG_STRTRANS_CodeID_Both						1		// 同时传送自定义识别符和AIM识别符
	#define MDFLAG_STRTRANS_CodeID_PropID					3		// 仅传送自定义识别符
#else
	#define MDFLAG_STRTRANS_CodeID_Both						3		// 同时传送自定义识别符和AIM识别符
	#define MDFLAG_STRTRANS_CodeID_PropID					1		// 仅传送自定义识别符
#endif
#define MDFLAG_STRTRANS_CodeID_Default						MDFLAG_STRTRANS_CodeID_Disable

// “8207”，Dec，字符串传送，数据字符长度传送功能；
#define MDFLAG_STRTRANS_CodeLen_Disable						0		// 失能传送数据字符长度
#define MDFLAG_STRTRANS_CodeLen_Enable						1		// 使能传送数据字符长度
#define MDFLAG_STRTRANS_CodeLen_Default						MDFLAG_STRTRANS_CodeLen_Disable

// “8208”，Dec，字符串传送，大小写转换功能；
#define MDFLAG_STRTRANS_CaseConv_Disable					0		// 不进行大小写转换
#define MDFLAG_STRTRANS_CaseConv_UpperDatOnly				1		// 将条码数据字符转换为大写
#define MDFLAG_STRTRANS_CaseConv_LowerDatOnly				2		// 将条码数据字符转换为小写
#define MDFLAG_STRTRANS_CaseConv_UpperWholeStr				3		// 将整个字符串转换为大写
#define MDFLAG_STRTRANS_CaseConv_LowWholeStr				4		// 将整个字符串转换为小写
#define MDFLAG_STRTRANS_CaseConv_Default					MDFLAG_STRTRANS_CaseConv_Disable

// “8209”，Dec，字符串传送，FN1替换字符串传送功能；
#define MDFLAG_STRTRANS_FN1Substu_Disable					0		// 禁止 FN1替换字符串传送功能
#define MDFLAG_STRTRANS_FN1Substu_Enable					1		// 使能 FN1替换字符串传送功能
#define MDFLAG_STRTRANS_FN1Substu_Default					MDFLAG_STRTRANS_FN1Substu_Disable

// “8210”，Dec，字符串传送，全非打印字符组成的字符串跟随字符设置功能；
#define MDFLAG_STRTRANS_NonePrintStr_Disable				0		// 失能全非打印字符组成的字符串跟随字符设置
#define MDFLAG_STRTRANS_NonePrintStr_Enable					1		// 使能全非打印字符组成的字符串跟随字符设置
#define MDFLAG_STRTRANS_NonePrintStr_Default				MDFLAG_STRTRANS_NonePrintStr_Disable

// “8211”，Dec，字符串传送，仅传送前N个字符功能；
#define MDFLAG_STRTRANS_KeepFirst_99						99		// 仅传送前99个字符
#define MDFLAG_STRTRANS_KeepFirst_Default					MDFLAG_STRTRANS_KeepFirst_99

// “8212”，Dec，字符串传送，仅传送后N个字符功能；
#define MDFLAG_STRTRANS_KeepLast_99							99		// 仅传送后99个字符
#define MDFLAG_STRTRANS_KeepLast_Default					MDFLAG_STRTRANS_KeepLast_99

// “8213”，Dec，字符串传送，屏蔽特殊键功能；
#define MDFLAG_STRTRANS_BanSpecialKeys_Disable				0		// 不进行屏蔽
#define MDFLAG_STRTRANS_BanSpecialKeys_Enable				1		// 屏蔽“TAB”、“Delete”、“Backspace”三个按键
#define MDFLAG_STRTRANS_BanSpecialKeys_Default				MDFLAG_STRTRANS_BanSpecialKeys_Enable

// “8214”，Dec，字符串传送，文本编辑功能；
#define MDFLAG_TEXTEDIT_Customize_Disable					0		// 不进行编辑
#define MDFLAG_TEXTEDIT_Customize_Invoice					1		// 税票打印相关
#define MDFLAG_TEXTEDIT_Customize_Evotrue					2		// 天演维真
#define MDFLAG_TEXTEDIT_Customize_Prowill					3		// 代理商
#define MDFLAG_TEXTEDIT_Customize_Inspur					4		// 浪潮集团
#define MDFLAG_TEXTEDIT_Customize_Elgin						5		// 巴西客户
#define MDFLAG_TEXTEDIT_Customize_Pulsa						6		// 德国客户
// 此参数值仅在哥伦布客户定制分支：feat_proj_sys_arm_v1.5.1272.1_customer_gelunbu_es4650wa_custom中使用
#define MDFLAG_TEXTEDIT_Customize_GeLunBu					7		// 哥伦布客户
#define MDFLAG_TEXTEDIT_Customize_ShengXiaoBang				8		// 盛销邦客户
#define MDFLAG_TEXTEDIT_Customize_XinGuoDu_GSGL				9		// 新国都_高速公路客户
#define MDFLAG_TEXTEDIT_Customize_InvoiceCust				10		// 税票自定义插入字符功能
#define MDFLAG_TEXTEDIT_Customize_Default					MDFLAG_TEXTEDIT_Customize_Disable

// “8215”，Dec，条码数据控制字符转字符串功能；
#define MDFLAG_Control_Characters_To_Strings_Disable		0
#define MDFLAG_Control_Characters_To_Strings_Enable			1
#define MDFLAG_Control_Characters_To_Strings_Default		MDFLAG_Control_Characters_To_Strings_Disable

// “8216”，Hex，字符串设置，TextEidt 自定义税票设置字符串 1 功能；
#define MDFLAG_TEXTEDIT_INVOICE_CUST_STR1_None				0		// 没有字符串 1 设置
#define MDFLAG_TEXTEDIT_INVOICE_CUST_STR1_0					'A'		// 最多支持 22 个字符
#define MDFLAG_TEXTEDIT_INVOICE_CUST_STR1_Default			MDFLAG_TEXTEDIT_INVOICE_CUST_STR1_None

// “8217”，Hex，字符串设置，TextEidt 自定义税票设置字符串 2 功能；
#define MDFLAG_TEXTEDIT_INVOICE_CUST_STR2_None				0		// 没有前缀字符串 2 设置
#define MDFLAG_TEXTEDIT_INVOICE_CUST_STR2_0					'A'		// 最多支持 22 个字符
#define MDFLAG_TEXTEDIT_INVOICE_CUST_STR2_Default			MDFLAG_TEXTEDIT_INVOICE_CUST_STR2_None

// “8218”，Hex，字符串设置，TextEidt 自定义税票设置字符串 3 功能；
#define MDFLAG_TEXTEDIT_INVOICE_CUST_STR3_None				0		// 没有字符串 3 设置
#define MDFLAG_TEXTEDIT_INVOICE_CUST_STR3_0					'A'		// 最多支持 22 个字符
#define MDFLAG_TEXTEDIT_INVOICE_CUST_STR3_Default			MDFLAG_TEXTEDIT_INVOICE_CUST_STR3_None

// “8219”，Hex，字符串设置，TextEidt 自定义税票设置字符串 4 功能；
#define MDFLAG_TEXTEDIT_INVOICE_CUST_STR4_None				0		// 没有前缀字符串 4 设置
#define MDFLAG_TEXTEDIT_INVOICE_CUST_STR4_0					'A'		// 最多支持 22 个字符
#define MDFLAG_TEXTEDIT_INVOICE_CUST_STR4_Default			MDFLAG_TEXTEDIT_INVOICE_CUST_STR4_None

// “8301”，Dec，数据有效性确认，首尾字符有效性匹配开关功能；
#define MDFLAG_DECO_RSLT_CHECK_Disable						0		// 功能禁止
#define MDFLAG_DECO_RSLT_CHECK_EnableAll					1		// 使能全部
#define MDFLAG_DECO_RSLT_CHECK_HeadCheckOnly				2		// 仅对头部字符检查
#define MDFLAG_DECO_RSLT_CHECK_TailCheckOnly				3		// 仅对尾部字符检查
#define MDFLAG_DECO_RSLT_CHECK_Default						MDFLAG_DECO_RSLT_CHECK_Disable

// “8302”，Hex，解码结果第 1 个有效头部字符匹配信息设置功能；
#define MDFLAG_DecoRsltCheck_1HeadStr_0						0x00
#define MDFLAG_DecoRsltCheck_1HeadStr_Default				MDFLAG_DecoRsltCheck_1HeadStr_0

// “8303”，Hex，解码结果第 1 个有效尾部字符匹配信息设置功能；
#define MDFLAG_DecoRsltCheck_1TailStr_0						0x00
#define MDFLAG_DecoRsltCheck_1TailStr_Default				MDFLAG_DecoRsltCheck_1TailStr_0

// “8304”，Dec，解码结果检测校验失败提示功能；
#define MDFLAG_DecoRsltCheck_CheckFailIndi_Disable			0		// 解码结果检测校验失败，不进行蜂鸣器提示
#define MDFLAG_DecoRsltCheck_CheckFailIndi_Enable			1		// 解码结果检测校验失败，进行蜂鸣器提示
#define MDFLAG_DecoRsltCheck_CheckFailIndi_Default			MDFLAG_DecoRsltCheck_CheckFailIndi_Enable

// “8305”，Hex，解码结果第 2 个有效头部字符匹配信息设置功能；
#define MDFLAG_DecoRsltCheck_2HeadStr_0						0x00
#define MDFLAG_DecoRsltCheck_2HeadStr_Default				MDFLAG_DecoRsltCheck_2HeadStr_0

// “8306”，Hex，解码结果第 2 个有效尾部字符匹配信息设置功能；
#define MDFLAG_DecoRsltCheck_2TailStr_0						0x00
#define MDFLAG_DecoRsltCheck_2TailStr_Default				MDFLAG_DecoRsltCheck_2TailStr_0

// “8307”，Hex，解码结果第 3 个有效头部字符匹配信息设置功能；
#define MDFLAG_DecoRsltCheck_3HeadStr_0						0x00
#define MDFLAG_DecoRsltCheck_3HeadStr_Default				MDFLAG_DecoRsltCheck_3HeadStr_0

// “8308”，Hex，解码结果第 3 个有效尾部字符匹配信息设置功能；
#define MDFLAG_DecoRsltCheck_3TailStr_0						0x00
#define MDFLAG_DecoRsltCheck_3TailStr_Default				MDFLAG_DecoRsltCheck_3TailStr_0

// “8309”，Hex，解码结果第 4 个有效头部字符匹配信息设置功能；
#define MDFLAG_DecoRsltCheck_4HeadStr_0						0x00
#define MDFLAG_DecoRsltCheck_4HeadStr_Default				MDFLAG_DecoRsltCheck_4HeadStr_0

// “8310”，Hex，解码结果第 4 个有效尾部字符匹配信息设置功能；
#define MDFLAG_DecoRsltCheck_4TailStr_0						0x00
#define MDFLAG_DecoRsltCheck_4TailStr_Default				MDFLAG_DecoRsltCheck_4TailStr_0

// “8311”，Hex，解码结果第 5 个有效头部字符匹配信息设置功能；
#define MDFLAG_DecoRsltCheck_5HeadStr_0						0x00
#define MDFLAG_DecoRsltCheck_5HeadStr_Default				MDFLAG_DecoRsltCheck_5HeadStr_0

// “8312”，Hex，解码结果第 5 个有效尾部字符匹配信息设置功能；
#define MDFLAG_DecoRsltCheck_5TailStr_0						0x00
#define MDFLAG_DecoRsltCheck_5TailStr_Default				MDFLAG_DecoRsltCheck_5TailStr_0

// “8313”，Hex，解码结果第 6 个有效头部字符匹配信息设置功能；
#define MDFLAG_DecoRsltCheck_6HeadStr_0						0x00
#define MDFLAG_DecoRsltCheck_6HeadStr_Default				MDFLAG_DecoRsltCheck_6HeadStr_0

// “8314”，Hex，解码结果第 6 个有效尾部字符匹配信息设置功能；
#define MDFLAG_DecoRsltCheck_6TailStr_0						0x00
#define MDFLAG_DecoRsltCheck_6TailStr_Default				MDFLAG_DecoRsltCheck_6TailStr_0

// “8315”，Hex，解码结果第 7 个有效头部字符匹配信息设置功能；
#define MDFLAG_DecoRsltCheck_7HeadStr_0						0x00
#define MDFLAG_DecoRsltCheck_7HeadStr_Default				MDFLAG_DecoRsltCheck_7HeadStr_0

// “8316”，Hex，解码结果第 7 个有效尾部字符匹配信息设置功能；
#define MDFLAG_DecoRsltCheck_7TailStr_0						0x00
#define MDFLAG_DecoRsltCheck_7TailStr_Default				MDFLAG_DecoRsltCheck_7TailStr_0

// “8401”，Dec，Optical Character Recognition（光学字符识别，简称OCR）功能选项；
#define MDFLAG_OCR_Recognize_AsPara							0		// 遵循参数设置识读 Optical Character Recognition（光学字符识别，简称OCR） 功能
#define MDFLAG_OCR_Recognize_DisableAll						1		// 失能全部 Optical Character Recognition（光学字符识别，简称OCR）功能
#define MDFLAG_OCR_Recognize_EnableAll						2		// 使能全部 Optical Character Recognition（光学字符识别，简称OCR）功能
#define MDFLAG_OCR_Recognize_Default						MDFLAG_OCR_Recognize_AsPara

// “8402”，Dec，护照机器识读区（Passport MRZ：Passport Machine Recognize Zone）识读选项；
#define MDFLAG_OCR_Recognize_PassportMRZ_Disable			0		// 失能识读护照机器识读区（Passport MRZ：Passport Machine Recognize Zone）
#define MDFLAG_OCR_Recognize_PassportMRZ_Enable				1		// 使能识读护照机器识读区（Passport MRZ：Passport Machine Recognize Zone）
#define MDFLAG_OCR_Recognize_PassportMRZ_Default			MDFLAG_OCR_Recognize_PassportMRZ_Disable

// “8403”，Dec，国际身份证（NationalCardID）识读选项；
#define MDFLAG_OCR_Recognize_NationalCardID_Disable			0		// 失能识读国际身份证（NationalCardID）
#define MDFLAG_OCR_Recognize_NationalCardID_Enable			1		// 使能识读国际身份证（NationalCardID）
#define MDFLAG_OCR_Recognize_NationalCardID_Default			MDFLAG_OCR_Recognize_NationalCardID_Disable

// “8404”，Dec，车牌（LicensePlate）识读选项；
#define MDFLAG_OCR_Recognize_LicensePlate_Disable			0		// 失能识读车牌（LicensePlate）
#define MDFLAG_OCR_Recognize_LicensePlate_Enable			1		// 使能识读车牌（LicensePlate）
#define MDFLAG_OCR_Recognize_LicensePlate_Default			MDFLAG_OCR_Recognize_LicensePlate_Disable

// “8450”，Dec，TurkishID Output Format 选项；
#define MDFLAG_OCR_TurkishID_OutputFormat_Original			0		// TurkishID 输出格式为原始格式
#define MDFLAG_OCR_TurkishID_OutputFormat_Customized		1		// TurkishID 输出格式为客户定制格式
#define MDFLAG_OCR_TurkishID_OutputFormat_Default			MDFLAG_OCR_TurkishID_OutputFormat_Original

// “8460”，Dec，双重确认选项；
#define MDFLAG_OCR_DoubleCheck_Disable						0		// 失能双重确认
#define MDFLAG_OCR_DoubleCheck_Enable						1		// 使能双重确认
#define MDFLAG_OCR_DoubleCheck_Default						MDFLAG_OCR_DoubleCheck_Disable


// “8501”，Hex，用户自定义第 1 个 ROI 坐标；
#define MDFLAG_Custom_Decode_ROI_1							0		// 第 1 个 ROI 坐标
#define MDFLAG_Custom_Decode_ROI_1_Default					MDFLAG_Custom_Decode_ROI_1

// “8502”，Hex，用户自定义第 2 个 ROI 坐标；
#define MDFLAG_Custom_Decode_ROI_2							0		// 第 2 个 ROI 坐标
#define MDFLAG_Custom_Decode_ROI_2_Default					MDFLAG_Custom_Decode_ROI_2

// “8503”，Hex，用户自定义第 3 个 ROI 坐标；
#define MDFLAG_Custom_Decode_ROI_3							0		// 第 3 个 ROI 坐标
#define MDFLAG_Custom_Decode_ROI_3_Default					MDFLAG_Custom_Decode_ROI_3

// “8504”，Hex，用户自定义第 4 个 ROI 坐标；
#define MDFLAG_Custom_Decode_ROI_4							0		// 第 4 个 ROI 坐标
#define MDFLAG_Custom_Decode_ROI_4_Default					MDFLAG_Custom_Decode_ROI_4

// “8505”，Hex，用户自定义第 5 个 ROI 坐标；
#define MDFLAG_Custom_Decode_ROI_5							0		// 第 5 个 ROI 坐标
#define MDFLAG_Custom_Decode_ROI_5_Default					MDFLAG_Custom_Decode_ROI_5

// “8506”，Hex，用户自定义第 6 个 ROI 坐标；
#define MDFLAG_Custom_Decode_ROI_6							0		// 第 6 个 ROI 坐标
#define MDFLAG_Custom_Decode_ROI_6_Default					MDFLAG_Custom_Decode_ROI_6

// “8507”，Hex，用户自定义第 7 个 ROI 坐标；
#define MDFLAG_Custom_Decode_ROI_7							0		// 第 7 个 ROI 坐标
#define MDFLAG_Custom_Decode_ROI_7_Default					MDFLAG_Custom_Decode_ROI_7

// “8508”，Hex，用户自定义第 8 个 ROI 坐标；
#define MDFLAG_Custom_Decode_ROI_8							0		// 第 8 个 ROI 坐标
#define MDFLAG_Custom_Decode_ROI_8_Default					MDFLAG_Custom_Decode_ROI_8

// “8509”，Hex，用户自定义第 9 个 ROI 坐标；
#define MDFLAG_Custom_Decode_ROI_9							0		// 第 9 个 ROI 坐标
#define MDFLAG_Custom_Decode_ROI_9_Default					MDFLAG_Custom_Decode_ROI_9

// “8510”，Hex，用户自定义第 10 个 ROI 坐标；
#define MDFLAG_Custom_Decode_ROI_10							0		// 第 10 个 ROI 坐标
#define MDFLAG_Custom_Decode_ROI_10_Default					MDFLAG_Custom_Decode_ROI_10

// “8511”，Hex，用户自定义第 11 个 ROI 坐标；
#define MDFLAG_Custom_Decode_ROI_11							0		// 第 11 个 ROI 坐标
#define MDFLAG_Custom_Decode_ROI_11_Default					MDFLAG_Custom_Decode_ROI_11

// “8512”，Hex，用户自定义第 12 个 ROI 坐标；
#define MDFLAG_Custom_Decode_ROI_12							0		// 第 12 个 ROI 坐标
#define MDFLAG_Custom_Decode_ROI_12_Default					MDFLAG_Custom_Decode_ROI_12

// “8601”，Dec，IVD 试管方向；
#define MADFLAG_IVD_TubeDirection_None						0		// 无效值
#define MADFLAG_IVD_TubeDirection_Left						1		// 试管位于设备左侧
#define MADFLAG_IVD_TubeDirection_Right						2		// 试管位于设备右侧
#define MADFLAG_IVD_TubeDirection_Default					MADFLAG_IVD_TubeDirection_None

// “8602”，Dec，IVD 试管管帽检测开关；
#define MADFLAG_IVD_DeteTubeCap_Disable						0		// 失能试管管帽识读
#define MADFLAG_IVD_DeteTubeCap_Enable						1		// 使能试管管帽识读
#define MADFLAG_IVD_DeteTubeCap_Default						MADFLAG_IVD_DeteTubeCap_Disable

// “8603”，Dec，IVD 试管管高检测开关；
#define MADFLAG_IVD_DeteTubeHeight_Disable					0		// 失能试管管高识读
#define MADFLAG_IVD_DeteTubeHeight_Enable					1		// 使能试管管高识读
#define MADFLAG_IVD_DeteTubeHeight_Default					MADFLAG_IVD_DeteTubeHeight_Disable

// “8604”，Dec，IVD 试管管高类型数量；
#define MADFLAG_IVD_DeteTubeHeightTypeNum_0					0		// 0 种类型管高
#define MADFLAG_IVD_DeteTubeHeightTypeNum_1					1		// 1 种类型管高
#define MADFLAG_IVD_DeteTubeHeightTypeNum_2					2		// 2 种类型管高
#define MADFLAG_IVD_DeteTubeHeightTypeNum_3					3		// 3 种类型管高
#define MADFLAG_IVD_DeteTubeHeightTypeNum_Default			MADFLAG_IVD_DeteTubeHeightTypeNum_0

// “9001”，Dec，二维码灯光类，解码照明模式选项功能；
#define MDFLAG_ILLUMINATION_AlwaysOff						0		// 强制关闭补光灯，并禁止外界进行控制，外界对补光灯开关的操作无效，但可调整灯光等级
#define MDFLAG_ILLUMINATION_AlwaysOn						1		// 强制开启补光灯，并禁止外界进行控制，外界对补光灯开关的操作无效，但可调整灯光等级
#define MDFLAG_ILLUMINATION_Flash							2		// 不对补光灯进行强制处理，由外界自由控制
#define MDFLAG_ILLUMINATION_OnWhenReading					3		// 不对补光灯进行强制处理，由外界自由控制
#define MDFLAG_ILLUMINATION_Default							MDFLAG_ILLUMINATION_OnWhenReading

// “9002”，Dec，二维码灯光类，解码瞄准模式选项功能；
#define MDFLAG_Aim_AlwaysOff								0		// 瞄准灯常关，关闭并且禁止外界对瞄准灯进行控制
#define MDFLAG_Aim_AlwaysOn									1		// 瞄准灯常开，成像过程中瞄准灯可自定义（如自动感应：闪烁，光标校正：常亮），非成像过程中常亮
#define MDFLAG_Aim_OnBeforeReading							2		// 解码成像前开启瞄准灯，解码成像过程中关闭，其它成像过程中自定义，非成像过程中常亮
#define MDFLAG_Aim_OnWhenReading							3		// 仅解码成像开启瞄准灯，解码成像过程中自定义，其它情况下关闭
#define MDFLAG_Aim_Default									MDFLAG_Aim_OnWhenReading

// “9003”，Dec，二维码灯光类，解码照明亮度等级选项功能；
#define MDFLAG_ILLUMINATION_LEVEL_Low						1		// 低亮度
#define MDFLAG_ILLUMINATION_LEVEL_Mid						2		// 中亮度
#define MDFLAG_ILLUMINATION_LEVEL_High						3		// 高亮度
#define MDFLAG_ILLUMINATION_LEVEL_Default					MDFLAG_ILLUMINATION_LEVEL_Mid

// “9004”，二维码灯光类，解码结束后，瞄准灯开启保持时长；
#define MDFLAG_2D_Light_AimingOnKeepTime_AfterDeco_Unit_1s	1000	// 保持时长单位1s
#define MDFLAG_2D_Light_AimingOnKeepTime_AfterDeco_0s		0		// 保持时长为0s
#define MDFLAG_2D_Light_AimingOnKeepTime_AfterDeco_1s		1		// 保持时长为1s
#define MDFLAG_2D_Light_AimingOnKeepTime_AfterDeco_5s		5		// 保持时长为5s
#define MDFLAG_2D_Light_AimingOnKeepTime_AfterDeco_Default	MDFLAG_2D_Light_AimingOnKeepTime_AfterDeco_0s

// “9005”，Dec，二维码灯光类，选择双色补光灯的光源；
#define MDFLAG_2D_Light_Group_Select_G1						0		// 选择组1作为补光灯灯源
#define MDFLAG_2D_Light_Group_Select_G2						1		// 选择组2作为补光灯灯源
#define MDFLAG_2D_Light_Group_Select_Default				MDFLAG_2D_Light_Group_Select_G1

// “9006”，Dec，二维码灯光类，记录补光灯最大驱动电流能力；(单位: 1mA)
#define MDFLAG_2D_Light_Max_CurrentDriveCapacity_Unit_1mA	1		// 最大驱动电流单位 1mA
#define MDFLAG_2D_Light_Max_CurrentDriveCapacity_700mA		700		// 当前补光灯最大驱动电流能力为 700mA
#define MDFLAG_2D_Light_Max_CurrentDriveCapacity_1400mA		1400	// 当前补光灯最大驱动电流能力为 1400mA
#define MDFLAG_2D_Light_Max_CurrentDriveCapacity_Default	MDFLAG_2D_Light_Max_CurrentDriveCapacity_700mA

// “9007”，Dec，二维码灯光类，记录补光灯同时点亮支持流过每颗LED的最大电流值；
#define MDFLAG_2D_Light_Max_SingleLED_Current_Unit_10mA		10		// 最大电流值单位 10mA
#define MDFLAG_2D_Light_Max_SingleLED_Current_350mA			35		// 补光灯同时点亮支持流过单颗LED的最大电流值 为 350 mA
#define MDFLAG_2D_Light_Max_SingleLED_Current_Default		MDFLAG_2D_Light_Max_SingleLED_Current_350mA

// “9008”，Dec，二维码灯光类，记录补光灯组1达到标准最大图像亮度时，所对应的电流值；
#define MDFLAG_2D_Light_Group1_Current_Unit_10mA			10		// 对应电流值单位 10mA
#define MDFLAG_2D_Light_Group1_Current_350mA				35		// 补光灯组1对应的电流值 为 350 mA
#define MDFLAG_2D_Light_Group1_Current_Default				MDFLAG_2D_Light_Group1_Current_350mA

// “9009”，Dec，二维码灯光类，记录补光灯组2达到标准最大图像亮度时，所对应的电流值；
#define MDFLAG_2D_Light_Group2_Current_Unit_10mA			10		// 对应电流值单位 10mA	
#define MDFLAG_2D_Light_Group2_Current_350mA				35		// 补光灯组2对应的电流值 为 350 mA
#define MDFLAG_2D_Light_Group2_Current_Default				MDFLAG_2D_Light_Group2_Current_350mA

// “9010”，二维码灯光类，开始解码前，瞄准灯开启保持时长；
// 取值范围为0~99，对应0~99s
#define MDFLAG_2D_Light_AimingOnKeepTime_BeforeDeco_Unit_1s	1		// 解码前瞄准灯开启持续单位1s
#define MDFLAG_2D_Light_AimingOnKeepTime_BeforeDeco_0s		0		// 解码前瞄准灯开启持续0秒 (禁用)
#define	MDFLAG_2D_Light_AimingOnKeepTime_BeforeDeco_1s		1		// 解码前瞄准灯开启持续1秒
#define	MDFLAG_2D_Light_AimingOnKeepTime_BeforeDeco_5s		5		// 解码前瞄准灯开启持续5秒
#define MDFLAG_2D_Light_AimingOnKeepTime_BeforeDeco_Default	MDFLAG_2D_Light_AimingOnKeepTime_BeforeDeco_5s

// “9011”，二维码灯光类，辅助补光灯控制选项；
#define MDFLAG_2D_Light_AuxiLumCtrl_Off						0		// 关闭辅助补光灯控制
#define MDFLAG_2D_Light_AuxiLumCtrl_On						1		// 开启辅助补光灯控制
#define MDFLAG_2D_Light_AuxiLumCtrl_Default					MDFLAG_2D_Light_AuxiLumCtrl_Off

// “9012”，二维码灯光类，辅助补光灯，灯光稳定补偿时间；
#define MDFLAG_2D_Light_AuxiLumCompTime_Unit_10us			10		// 补偿时间单位10s
#define MDFLAG_2D_Light_AuxiLumCompTime_0us					0		// 补偿时间为0us
#define MDFLAG_2D_Light_AuxiLumCompTime_Default				MDFLAG_2D_Light_AuxiLumCompTime_0us

// “9013”，二维码灯光类，补光灯单边控制选项；
#define MDFLAG_2D_Light_SingleSideCtrl_Off					0		// 默认选项，由设备自行控制补光灯亮灯侧
#define MDFLAG_2D_Light_SingleSideCtrl_Left					1		// 左灯亮
#define MDFLAG_2D_Light_SingleSideCtrl_Right				2		// 右灯亮
#define MDFLAG_2D_Light_SingleSideCtrl_Both					3		// 双灯亮
#define MDFLAG_2D_Light_SingleSideCtrl_Default				MDFLAG_2D_Light_SingleSideCtrl_Off

// 9100 ~ 9102为红会客户分支参数。(仅在feat_proj_sys_arm_v1.7.99.3_customer_honghui_contactform_202012310001分支上被使用)
// “9100”，红会客户模块中的版本选择
#define MDFLAG_VERSION_OPTION_Mindeo						0		// 使用民德标准版本
#define MDFLAG_VERSION_OPTION_HongHui						1		// 使用红会客户定制版本
#define MDFLAG_VERSION_OPTION_Default						MDFLAG_VERSION_OPTION_HongHui

// “9101”，条码信息加密控制开关
#define MDFLAG_HONGHUI_ENCRYPTION_Disable					0		// 失能条码信息加密功能
#define MDFLAG_HONGHUI_ENCRYPTION_Enable					1		// 使能条码信息加密功能
#define MDFLAG_HONGHUI_ENCRYPTION_Default					MDFLAG_HONGHUI_ENCRYPTION_Enable

// “9102”，“SN”号设置信息
#define MDFLAG_SERIAL_NUMBER_Null							0		// SN号信息为空
#define MDFLAG_SERIAL_NUMBER_Std							'1','2','3','4','5'		// 标准SN号信息
#define MDFLAG_SERIAL_NUMBER_Default						MDFLAG_SERIAL_NUMBER_Std

// 扬声器
// “9201”，Dec，扬声器相关，声音开关功能；
// 此处不太合理，稍后统一调整为MDFLAG开头
#define MACR_SPEAKER_VOICE_OFF								0		// 关闭扬声器
#define MACR_SPEAKER_VOICE_ON								1		// 打开扬声器
#define MACR_SPEAKER_VOICE_Default							MACR_SPEAKER_VOICE_OFF

// “9202”，Dec，扬声器相关，声音选择功能；
// 此处不太合理，稍后统一调整为MDFLAG开头
#define MACR_SPEAKER_SELECT_VOICE_A							0		// 选择声音A
#define MACR_SPEAKER_SELECT_VOICE_B							1		// 选择声音B
#define MACR_SPEAKER_SELECT_Default							MACR_SPEAKER_SELECT_VOICE_A

// “9203”，Dec，扬声器相关，声音间隔功能；（单位：1s）
#define MDFLAG_Speaker_VoiceInterval_Unit_1s				1000	// 声音间隔单位1s
#define MDFLAG_Speaker_VoiceInterval_10s					10		// 声音间隔10s
#define MDFLAG_Speaker_VoiceInterval_Default				MDFLAG_Speaker_VoiceInterval_10s

// “9204”，Dec，微信定制版本需要使用的相关定义；
#if (MACR_CUSTOMER_WeChatPay == 1)
	#define MDFLAG_ALIPAY_READ_SWITCH_OFF					0
	#define MDFLAG_ALIPAY_READ_SWITCH_ON					1

	#define MDFLAG_ALIPAY_READ_OFF							0
	#define MDFLAG_ALIPAY_VOICE_ON							1
	#define MDFLAG_ALIPAY_VOICE_OFF							2
#endif

// “9205”，Dec，扬声器相关，音量设置功能；
#define MDFLAG_Speaker_VoiceVolume_Level_0					0		// 0级声音（最小）
#define MDFLAG_Speaker_VoiceVolume_Level_1					1		// 1级声音
#define MDFLAG_Speaker_VoiceVolume_Level_2					2		// 2级声音
#define MDFLAG_Speaker_VoiceVolume_Level_3					3		// 3级声音
#define MDFLAG_Speaker_VoiceVolume_Level_4					4		// 4级声音
#define MDFLAG_Speaker_VoiceVolume_Level_5					5		// 5级声音
#define MDFLAG_Speaker_VoiceVolume_Level_6					6		// 6级声音
#define MDFLAG_Speaker_VoiceVolume_Level_7					7		// 7级声音（最大）
#define MDFLAG_Speaker_VoiceVolume_Level_Default			MDFLAG_Speaker_VoiceVolume_Level_7

// 9300 ~ 9312参数已在新大陆客户分支(feat_proj_sys_arm_v1.5.1364.3_mp150s_newland_customize)上被使用，
// 请其他同事避开这几个参数的设置。

// “9896”，Dec，保留参数，视觉类应用控制选项；
#define MDFLAG_RESV_VisionAppCtrl_None						0		// 不启用视觉类应用
#define MDFLAG_RESV_VisionAppCtrl_OCR						1		// 启用 OCR 应用
#define MDFLAG_RESV_VisionAppCtrl_Classification			2		// 启用分类应用
#define MDFLAG_RESV_VisionAppCtrl_DeteFeature				3		// 启用检测特征应用
#define MDFLAG_RESV_VisionAppCtrl_Default					MDFLAG_RESV_VisionAppCtrl_None

// “9898”，Hex，保留参数，用于保存自定义保持时长命令(与0402命令互斥)；
#define MDFLAG_RESV_CusScanModeHoldDuration_None			0		// 没有信息
#define MDFLAG_RESV_CusScanModeHoldDuration_Default			MDFLAG_RESV_CusScanModeHoldDuration_None

// “9899”，Hex，保留参数，自定义网址码信息；
#define MDFLAG_RESV_CusWebCodeInfo_None						0		// 没有信息
#define MDFLAG_RESV_CusWebCodeInfo_Default					MDFLAG_RESV_CusWebCodeInfo_None

// “9900”，Hex，保留参数，IVD 试管管高相关信息；
#define MDFLAG_RESV_IVD_TubeHightInfo_None					0		// 没有信息
#define MDFLAG_RESV_IVD_TubeHightInfo_Default				MDFLAG_RESV_IVD_TubeHightInfo_None

// “9901”，Dec，保留参数，条码结果输出与提示时延(0511 控制)作用对象；
#define MDFLAG_RESV_CodeRslt_TxIndi_DlyObj_None				0		// 无作用对象
#define MDFLAG_RESV_CodeRslt_TxIndi_DlyObj_OnlyTx			1		// 只作用在发送结果
#define MDFLAG_RESV_CodeRslt_TxIndi_DlyObj_TxAndIndi		2		// 提示与发送结果都作用
#define MDFLAG_RESV_CodeRslt_TxIndi_DlyObj_Defautl			MDFLAG_RESV_CodeRslt_TxIndi_DlyObj_None

// “9902”，Dec，保留参数，双窗平台设备间通信允许使用的最大波特率
#define MDFLAG_RESV_DEV_GROUP_BAUD_RATE_MAX_9600			0		// 双窗平台设备间通信波特率最高可达到9600bps
#define MDFLAG_RESV_DEV_GROUP_BAUD_RATE_MAX_115200			1		// 双窗平台设备间通信波特率最高可达到115200bps
#define MDFLAG_RESV_DEV_GROUP_BAUD_RATE_MAX_921600			2		// 双窗平台设备间通信波特率最高可达到921600bps
#define MDFLAG_RESV_DEV_GROUP_BAUD_RATE_MAX_Default			MDFLAG_RESV_DEV_GROUP_BAUD_RATE_MAX_921600

// “9903”，Dec，保留参数，正常帧率切换到低帧率拍摄时延；
#define MDFLAG_RESV_NOR2LOW_FRM_RATE_DURA_Forbid			0		// 禁止设备正常帧率切换到低帧率
#define MDFLAG_RESV_NOR2LOW_FRM_RATE_DURA_Unit_1s			1		// 正常帧率切换到低帧率时长单位为 1 秒
#define MDFLAG_RESV_NOR2LOW_FRM_RATE_DURA_1s				1		// 正常帧率切换到低帧率为 1 秒
#define MDFLAG_RESV_NOR2LOW_FRM_RATE_DURA_2s				2		// 正常帧率切换到低帧率为 2 秒
#define MDFLAG_RESV_NOR2LOW_FRM_RATE_DURA_3s				3		// 正常帧率切换到低帧率为 3 秒
#define MDFLAG_RESV_NOR2LOW_FRM_RATE_DURA_4s				4		// 正常帧率切换到低帧率为 4 秒
#define MDFLAG_RESV_NOR2LOW_FRM_RATE_DURA_5s				5		// 正常帧率切换到低帧率为 5 秒
// ...
#define MDFLAG_RESV_NOR2LOW_FRM_RATE_DURA_90s				90		// 正常帧率切换到低帧率为 90 秒
// ...
#define MDFLAG_RESV_NOR2LOW_FRM_RATE_DURA_9998s				9998	// 正常帧率切换到低帧率为 9998 秒
#define MDFLAG_RESV_NOR2LOW_FRM_RATE_DURA_9999s				9999	// 正常帧率切换到低帧率为 9999 秒
#define MDFLAG_RESV_NOR2LOW_FRM_RATE_DURA_Default			MDFLAG_RESV_NOR2LOW_FRM_RATE_DURA_Forbid

// “9904”，Dec，保留参数，图像伽马变换功能控制选项；
#define MDFLAG_RESV_Gamma_Ctrl_Disable						0		// 失能图像伽马变换功能
#define MDFLAG_RESV_Gamma_Ctrl_Enable						1		// 使能图像伽马变换功能
#define MDFLAG_RESV_Gamma_Ctrl_Default						MDFLAG_RESV_Gamma_Ctrl_Enable

// “9906”，Dec，保留参数，图像去噪功能控制选项；
#define MDFLAG_RESV_Denoise_Ctrl_Disable					0		// 失能图像去噪功能
#define MDFLAG_RESV_Denoise_Ctrl_Enable						1		// 使能图像去噪功能
#define MDFLAG_RESV_Denoise_Ctrl_Default					MDFLAG_RESV_Denoise_Ctrl_Disable

// “9907”，Dec，保留参数，命令触发解码支持“Deco Stay”（最短解码时长）选项；
#define MDFLAG_RESV_Cmd_Trig_Deco_Stay_Disable				0		// 失能命令触发解码支持“Deco Stay”（即命令触发解码不支持 Deco Stay）
#define MDFLAG_RESV_Cmd_Trig_Deco_Stay_Enable				1		// 使能命令触发解码支持“Deco Stay”（即命令触发解码支持 Deco Stay）
#define MDFLAG_RESV_Cmd_Trig_Deco_Stay_Default				MDFLAG_RESV_Cmd_Trig_Deco_Stay_Disable

#if (((MACR_PRODUCT_CS7259 == 1) && (MACR_PRODUCT_SUB_CS7259_BT == 1)) || 	\
	(MACR_PRODUCT_ES4690i == 1) || 	\
	(MACR_PRODUCT_CS7259 == 1) || 	\
	(MACR_PRODUCT_MD7959 == 1) || 	\
	((MACR_PRODUCT_CS7219 == 1) && (MACR_PRODUCT_SUB_CS7219_BT == 1)) || 	\
	(MACR_PRODUCT_MD7919 == 1) || 	\
	(MACR_PRODUCT_MD7919i == 1) || 	\
	(MACR_PRODUCT_ES4790i == 1) || 	\
	(MACR_PRODUCT_CS7219i == 1) || 	\
	(MACR_PRODUCT_AD_IR100 == 1) ||	\
	(MACR_PRODUCT_AD_IR101 == 1) || \
	(MACR_PRODUCT_ES4729i == 1))
	// “9908”，Dec，保留参数，为不同灯光类型获取自动成像参数控制选项
	// MD79X9和CS72X9等双色灯产品支持选择“标准红/白灯”与“偏振白灯/普通红灯”的两个成像参数组。
	#define MDFLAG_RESV_Get_AIM_Para_4_NormalLum_Red_White	0		// 使用普通红/白灯的自动成像参数
	#define MDFLAG_RESV_Get_AIM_Para_4_PolarLum_Red_White	1		// 使用偏振白灯/普通红灯的自动成像参数
	#define MDFLAG_RESV_Get_AIM_Para_4_LumType_Default		MDFLAG_RESV_Get_AIM_Para_4_NormalLum_Red_White
#elif (((MACR_PRODUCT_CS7290 == 1) && (MACR_PRODUCT_SUB_CS7290_BT == 1)) || 	\
		((MACR_PRODUCT_CS7290 == 1) && (MACR_PRODUCT_SUB_CS3290S_2D == 0)))
	// “9908”，Dec，保留参数，为不同灯光类型获取自动成像参数控制选项
	// CS7290产品支持选择“标准灯”、“高亮红灯”、“高亮白灯”三个成像参数组；
	// 注意：CS7290 的子产品 CS3290S_2D 仅支持设置为标准灯。
	#define MDFLAG_RESV_Get_AIM_Para_4_NormalLum			0		// 使用普通灯的自动成像参数
	#define MDFLAG_RESV_Get_AIM_Para_4_HighLum_White		1		// 使用高亮白灯的自动成像参数
	#define MDFLAG_RESV_Get_AIM_Para_4_HighLum_Red			2		// 使用高亮红灯的自动成像参数
	#define MDFLAG_RESV_Get_AIM_Para_4_LumType_Default		MDFLAG_RESV_Get_AIM_Para_4_NormalLum
#else
	// “9908”，Dec，保留参数，为不同灯光类型获取自动成像参数控制选项；
	// 其他产品仅支持选择标准灯的成像参数组。
	#define MDFLAG_RESV_Get_AIM_Para_4_NormalLum			0		// 使用普通灯的自动成像参数
	#define MDFLAG_RESV_Get_AIM_Para_4_LumType_Default		MDFLAG_RESV_Get_AIM_Para_4_NormalLum
#endif

// “9909”，Dec，保留参数，一次解码输出条码数量控制选项（有效范围1~30）；
#define MDFLAG_RESV_Decode_Result_Max_Num_1					1		// 一次最多能解 1 个条码
#define MDFLAG_RESV_Decode_Result_Max_Num_30				30		// 一次最多能解 30 个条码
#define MDFLAG_RESV_Decode_Result_Max_Num_Default			MDFLAG_RESV_Decode_Result_Max_Num_30

// “9910”，Dec，保留参数，输出数据打包控制选项；
#define MDFLAG_RESV_Packet_Output_Data_Disable				0		// 关闭打包输出数据功能
#define MDFLAG_RESV_Packet_Output_Data_Enable				1		// 开启打包输出数据功能
#define MDFLAG_RESV_Packet_Output_Data_Default				MDFLAG_RESV_Packet_Output_Data_Disable

// “9911”，Dec，保留参数，图片添加水印功能控制选项；
#define MDFLAG_RESV_Add_Watermark_Disable					0			// 关闭添加水印的功能
#define MDFLAG_RESV_Add_Watermark_Without_Background		1			// 添加无背景的水印
#define MDFLAG_RESV_Add_Watermark_With_Background			2			// 添加带有背景的水印
#define MDFLAG_RESV_Add_Watermark_Default					MDFLAG_RESV_Add_Watermark_Disable

// “9912”，Dec，保留参数，在架时蜂鸣器提示控制选项；
#define MDFLAG_RESV_Instand_Indicate_Disable				0
#define MDFLAG_RESV_Instand_Indicate_Enable					1
#define MDFLAG_RESV_Instand_Indicate_Default				MDFLAG_RESV_Instand_Indicate_Disable

// “9913”，Dec，保留参数，无线产品参数，433MHz手持枪的无线通信序号(由基座分配)；
// 手持枪的无线通信序号，默认取值范围 00~15
#define MDFLAG_RESV_Wireless_433_HHUIndex_0					0
#define MDFLAG_RESV_Wireless_433_HHUIndex_Default			MDFLAG_RESV_Wireless_433_HHUIndex_0

// “9914”，Dec，保留参数，在命令处理完成回复 ACK 后，发送命令执行结果（主要用于处理先回复 ACK 再执行动作的命令）；
#define MDFLAG_RESV_Send_Cmd_Exe_Rslt_Disable				0		// 失能在命令处理完成回复 ACK 后，发送命令执行结果（主要用于处理先回复 ACK 再执行动作的命令）
#define MDFLAG_RESV_Send_Cmd_Exe_Rslt_Enable				1		// 使能在命令处理完成回复 ACK 后，发送命令执行结果（主要用于处理先回复 ACK 再执行动作的命令）
#define MDFLAG_RESV_Send_Cmd_Exe_Rslt_Default				MDFLAG_RESV_Send_Cmd_Exe_Rslt_Disable

// “9915”，Dec，保留参数，USB HID键盘模式下，输出结果前后缀的处理方式；
#define MDFLAG_RESV_USB_KBD_PrefixSuffixHandle_Conventional	0		// 常规处理流程（功能键、键盘布局等参数共同决定处理方式）
#define MDFLAG_RESV_USB_KBD_PrefixSuffixHandle_PriorFuncKey	1		// 优先按功能键进行处理（若处理失败，按常规流程处理）
#define MDFLAG_RESV_USB_KBD_PrefixSuffixHandle_Default		MDFLAG_RESV_USB_KBD_PrefixSuffixHandle_Conventional

// “9916”，Dec，保留参数，允许配置USB OPOS选项；
#define MDFLAG_RESV_USB_OPOS_isPermitConfig_Disable			0		// 不允许配置USB OPOS，即不允许配置“0901D04”与“0901D11”
#define MDFLAG_RESV_USB_OPOS_isPermitConfig_Enable			1		// 允许配置USB OPOS，即不允许配置“0901D04”与“0901D11”
#define MDFLAG_RESV_USB_OPOS_isPermitConfig_Default			MDFLAG_RESV_USB_OPOS_isPermitConfig_Enable

// “9917”，Dec，保留参数，输出scan mode扫描阶段状态时间记录选项；
#define MDFLAG_RESV_SysMon_OutputScanModeTiming_Disable		0		// 失能输出scan mode扫描阶段状态时间记录
#define MDFLAG_RESV_SysMon_OutputScanModeTiming_Enable		1		// 使能输出scan mode扫描阶段状态时间记录
#define MDFLAG_RESV_SysMon_OutputThreadPer_Enable			2		// 使能输出线程CPU占用率（使能后通过DebugInfo输出）
#define MDFLAG_RESV_SysMon_OutputBoth_Enable				3		// 同时使能ScanModeTiming和ThreadPer
#define MDFLAG_RESV_SysMon_OutputScanModeTiming_Default		MDFLAG_RESV_SysMon_OutputScanModeTiming_Disable

// “9918”，Dec，保留参数，解码器调试信息输出选项；
#define MDFLAG_RESV_Decoder_DebugInfoOutput_None			0		// 不输出解码器调试信息
#define MDFLAG_RESV_Decoder_DebugInfoOutput_ThreadTime		1		// 解码器线程执行时间(包括图像信息)
#define MDFLAG_RESV_Decoder_DebugInfoOutput_Default			MDFLAG_RESV_Decoder_DebugInfoOutput_None

// “9919”，Dec，保留参数，Project Video的功能开关状态指示；
#define MDFLAG_RESV_ProjVideo_State_Off						0		// Project Video功能已关闭
#define MDFLAG_RESV_ProjVideo_State_On						1		// Project Video功能已开启
#define MDFLAG_RESV_ProjVideo_State_Default					MDFLAG_RESV_ProjVideo_State_Off

// “9920”，Dec，保留参数，休眠模式下图像传感器定时关闭时间；
#define MDFLAG_RESV_ImgSnr_LowPowerOff_Forbid				0		// 禁止关闭图像传感器
#define MDFLAG_RESV_ImgSnr_LowPowerOff_10Secs				1		// 10秒后关闭图像传感器
#define MDFLAG_RESV_ImgSnr_LowPowerOff_30Secs				2		// 30秒后关闭图像传感器
#define MDFLAG_RESV_ImgSnr_LowPowerOff_1Min					3		// 1分钟后关闭图像传感器
#define MDFLAG_RESV_ImgSnr_LowPowerOff_5Mins				4		// 5分钟后关闭图像传感器
#define MDFLAG_RESV_ImgSnr_LowPowerOff_10Mins				5		// 10分钟后关闭图像传感器
#define MDFLAG_RESV_ImgSnr_LowPowerOff_15Mins				6		// 15分钟后关闭图像传感器
#define MDFLAG_RESV_ImgSnr_LowPowerOff_30Mins				7		// 30分钟后关闭图像传感器
#define MDFLAG_RESV_ImgSnr_LowPowerOff_1Hour				8		// 1小时后关闭图像传感器
#define MDFLAG_RESV_ImgSnr_LowPowerOff_2Hours				9		// 2小时后关闭图像传感器
#define MDFLAG_RESV_ImgSnr_LowPowerOff_4Hours				10		// 4小时后关闭图像传感器
#define MDFLAG_RESV_ImgSnr_LowPowerOff_Default				MDFLAG_RESV_ImgSnr_LowPowerOff_Forbid

// “9921”，Dec，保留参数，数据接收模块的数据传输通道选择；
#define MDFLAG_RESV_TxRxDataChannel_None					0		// 无通道
#define MDFLAG_RESV_TxRxDataChannel_MainSerial				1		// 主串口
#define MDFLAG_RESV_TxRxDataChannel_AuxSerial				2		// 辅助串口
#define MDFLAG_RESV_TxRxDataChannel_DebugSerial				3		// 调试串口
#define MDFLAG_RESV_TxRxDataChannel_WirelessSerial			4		// 无线串口
#define MDFLAG_RESV_TxRxDataChannel_USBVCOM					5		// USB-虚拟串口
#define MDFLAG_RESV_TxRxDataChannel_USBCUSTOM				6		// USB高速
#define MDFLAG_RESV_TxRxDataChannel_Default					MDFLAG_RESV_TxRxDataChannel_None

// “9922”，Dec，保留参数，自动成像过程中的帧率控制；
#define MDFLAG_RESV_AIM_FrameRateCtrl_None					0		// 无效值
#define MDFLAG_RESV_AIM_FrameRateCtrl_SWCustom				1		// 软件自定义
#define MDFLAG_RESV_AIM_FrameRateCtrl_Reserve1				2		// 保留值 1
#define MDFLAG_RESV_AIM_FrameRateCtrl_Reserve2				3		// 保留值 2
#define MDFLAG_RESV_AIM_FrameRateCtrl_Reserve3				4		// 保留值 3
#define MDFLAG_RESV_AIM_FrameRateCtrl_Reserve4				5		// 保留值 4
#define MDFLAG_RESV_AIM_FrameRateCtrl_Reserve5				6		// 保留值 5
#define MDFLAG_RESV_AIM_FrameRateCtrl_Reserve6				7		// 保留值 6
#define MDFLAG_RESV_AIM_FrameRateCtrl_Reserve7				8		// 保留值 7
#define MDFLAG_RESV_AIM_FrameRateCtrl_Reserve8				9		// 保留值 8
#define MDFLAG_RESV_AIM_FrameRateCtrl_10fps					10		// 10fps
#define MDFLAG_RESV_AIM_FrameRateCtrl_11fps					11		// 11fps
// ...
#define MDFLAG_RESV_AIM_FrameRateCtrl_99fps					99		// 99fps

// “9924”，Dec，保留参数，QR码解码停止位位置；
#define MDFLAG_RESV_QRCode_DecoTerminPos_Reserve			0		// 保留值，暂用于占位(不允许被设置)
#define MDFLAG_RESV_QRCode_DecoTerminPos_1stTerminator		1		// 在第 1 个停止位时停止解码
#define MDFLAG_RESV_QRCode_DecoTerminPos_2ndTerminator		2		// 在第 2 个停止位时停止解码
#define MDFLAG_RESV_QRCode_DecoTerminPos_Default			MDFLAG_RESV_QRCode_DecoTerminPos_1stTerminator

// “9925”，Dec，保留参数，二维码纠错码输出控制选项；
#define MDFLAG_RESV_2DCode_ErrCorrCodeOutput_Disable		0		// 禁止 解码器输出 二维码纠错码
#define MDFLAG_RESV_2DCode_ErrCorrCodeOutput_Enable			1		// 使能 解码器输出 二维码纠错码
#define MDFLAG_RESV_2DCode_ErrCorrCodeOutput_Default		MDFLAG_RESV_2DCode_ErrCorrCodeOutput_Disable

// “9926”，Dec，保留参数，二维码纠错等级输出控制选项；
#define MDFLAG_RESV_2DCode_ErrCorrLevelOutput_Disable		0		// 禁止 解码器输出 二维码纠错等级
#define MDFLAG_RESV_2DCode_ErrCorrLevelOutput_Enable		1		// 使能 解码器输出 二维码纠错等级
#define MDFLAG_RESV_2DCode_ErrCorrLevelOutput_Default		MDFLAG_RESV_2DCode_ErrCorrLevelOutput_Disable

// “9927”，Dec，保留参数，二维码版本输出控制选项；
#define MDFLAG_RESV_2DCode_CodeVersionOutput_Disable		0		// 禁止 解码器输出 二维码版本信息
#define MDFLAG_RESV_2DCode_CodeVersionOutput_Enable			1		// 使能 解码器输出 二维码版本信息
#define MDFLAG_RESV_2DCode_CodeVersionOutput_Default		MDFLAG_RESV_2DCode_CodeVersionOutput_Disable

// “9928”，Dec，保留参数，无线连接异常断开下，自动重连功能使能；
#define MDFLAG_RESV_WIRELESS_AUTO_RECONNECT_Disable			0		// 禁止自动重连
#define MDFLAG_RESV_WIRELESS_AUTO_RECONNECT_Enable			1		// 允许自动重连
#define MDFLAG_RESV_WIRELESS_AUTO_RECONNECT_Default			MDFLAG_RESV_WIRELESS_AUTO_RECONNECT_Enable

// “9929”，Dec，保留参数，忽视瞄准灯校正信息状态；
// 0 表示  向外部程序提供瞄准灯校正信息
// 1 表示  不向外部程序提供瞄准灯校正信息
// 宏定义名称MDFLAG_RESV_NEGLECT_AIM_INFO_CTRL_Disable中，neglect + disable，否定加否定，表示肯定，故该宏定义表示允许向外界程序提供瞄准灯校正信息。
// 宏定义名称MDFLAG_RESV_NEGLECT_AIM_INFO_CTRL_Enable中，neglect + Enable，否定加肯定，表示否定，故该宏定义表示不允许向外界程序提供瞄准灯校正信息。
#define MDFLAG_RESV_NEGLECT_AIM_INFO_CTRL_Disable			0
#define MDFLAG_RESV_NEGLECT_AIM_INFO_CTRL_Enable			1
#define MDFLAG_RESV_NEGLECT_AIM_INFO_CTRL_Default			MDFLAG_RESV_NEGLECT_AIM_INFO_CTRL_Enable

// “9930”，Dec，保留参数，DataMatrix码相关，最小码字长度高位（千位与百位）；
// 注意：此参数为“临时”增加，稍后可能会移除
#define MDFLAG_RESV_DMCode_MinCodeLen_High_Amp				100		// DataMatrix码，最小码字长度高位放大系数为100
#define MDFLAG_RESV_DMCode_MinCodeLen_High_0				0		// DataMatrix码，最小码字长度高位为0，即千位与百位为0
#define MDFLAG_RESV_DMCode_MinCodeLen_High_Default			MDFLAG_RESV_DMCode_MinCodeLen_High_0

// “9931”，Dec，保留参数，DataMatrix码相关，最小码字长度低位（十位与个位）；
// 注意：此参数为“临时”增加，稍后可能会移除
#define MDFLAG_RESV_DMCode_MinCodeLen_Low_Amp				1		// DataMatrix码，最小码字长度低位放大系数为1
#define MDFLAG_RESV_DMCode_MinCodeLen_Low_1					1		// DataMatrix码，最小码字长度低位为1，即十位为0，个位为1
#define MDFLAG_RESV_DMCode_MinCodeLen_Low_Default			MDFLAG_RESV_DMCode_MinCodeLen_Low_1

// “9932”，Dec，保留参数，DataMatrix码相关，最大码字长度高位（千位与百位）；
// 注意：此参数为“临时”增加，稍后可能会移除
#define MDFLAG_RESV_DMCode_MaxCodeLen_High_Amp				100		// DataMatrix码，最大码字长度高位放大系数为100
#define MDFLAG_RESV_DMCode_MaxCodeLen_High_99				99		// DataMatrix码，最大码字长度高位为99，即千位与百位为9
#define MDFLAG_RESV_DMCode_MaxCodeLen_High_Default			MDFLAG_RESV_DMCode_MaxCodeLen_High_99

// “9933”，Dec，保留参数，DataMatrix码相关，最大码字长度低位（十位与个位）；
// 注意：此参数为“临时”增加，稍后可能会移除
#define MDFLAG_RESV_DMCode_MaxCodeLen_Low_Amp				1		// DataMatrix码，最大码字长度低位放大系数为1
#define MDFLAG_RESV_DMCode_MaxCodeLen_Low_99				99		// DataMatrix码，最大码字长度低位为99，即十位与个位为9
#define MDFLAG_RESV_DMCode_MaxCodeLen_Low_Default			MDFLAG_RESV_DMCode_MaxCodeLen_Low_99

// “9934”，Hex，保留参数，Data Format模块控制相关命令；
#define MDFLAG_RESV_DATA_FORMAT_CMD_Null					0		// 无命令
#define MDFLAG_RESV_DATA_FORMAT_CMD_Default					MDFLAG_RESV_DATA_FORMAT_CMD_Null

// “9935”，Dec，保留参数，持续扫描模式下自动感应状态补光灯亮度；
#define MDFLAG_RESV_SCAN_MODE_CONT_ADIMG_LUMIN_LEVEL_LOW	0
#define MDFLAG_RESV_SCAN_MODE_CONT_ADIMG_LUMIN_LEVEL_MID	1
#define MDFLAG_RESV_SCAN_MODE_CONT_ADIMG_LUMIN_LEVEL_HIGH	2
#define MDFLAG_RESV_SCAN_MODE_CONT_ADIMG_LUMIN_LEVEL_OFF	3

// “9936”，Dec，保留参数，持续扫描模式下无动作保持解码时长；
// 在参数值为1-90时分别代表设置持续、开关持续扫描模式下无动作保持解码时长为1-90s
#define MDFLAG_RESV_SCAN_TIMER_CONT_NO_ACTION_DURA_Forbid	0		// 禁止设备进入感应状态(设置持续、开关持续扫描模式下无动作保持解码时长为无限长)
#define MDFLAG_RESV_SCAN_TIMER_CONT_NO_ACTION_DURA_Unit_1s	1000	// 设置持续、开关持续扫描模式下无动作保持解码时长单位为1秒
#define MDFLAG_RESV_SCAN_TIMER_CONT_NO_ACTION_DURA_1s		1		// 设置持续、开关持续扫描模式下无动作保持解码时长为1秒
#define MDFLAG_RESV_SCAN_TIMER_CONT_NO_ACTION_DURA_2s		2		// 设置持续、开关持续扫描模式下无动作保持解码时长为2秒
#define MDFLAG_RESV_SCAN_TIMER_CONT_NO_ACTION_DURA_3s		3		// 设置持续、开关持续扫描模式下无动作保持解码时长为3秒
#define MDFLAG_RESV_SCAN_TIMER_CONT_NO_ACTION_DURA_4s		4		// 设置持续、开关持续扫描模式下无动作保持解码时长为4秒
#define MDFLAG_RESV_SCAN_TIMER_CONT_NO_ACTION_DURA_5s		5		// 设置持续、开关持续扫描模式下无动作保持解码时长为5秒
#define MDFLAG_RESV_SCAN_TIMER_CONT_NO_ACTION_DURA_90s		90		// 设置持续、开关持续扫描模式下无动作保持解码时长为90秒
#define MDFLAG_RESV_SCAN_TIMER_CONT_NO_ACTION_DURA_5min		91		// 设置持续、开关持续扫描模式下无动作保持解码时长为5分钟
#define MDFLAG_RESV_SCAN_TIMER_CONT_NO_ACTION_DURA_10min	92		// 设置持续、开关持续扫描模式下无动作保持解码时长为10分钟
#define MDFLAG_RESV_SCAN_TIMER_CONT_NO_ACTION_DURA_15min	93		// 设置持续、开关持续扫描模式下无动作保持解码时长为15分钟
#define MDFLAG_RESV_SCAN_TIMER_CONT_NO_ACTION_DURA_20min	94		// 设置持续、开关持续扫描模式下无动作保持解码时长为20分钟
#define MDFLAG_RESV_SCAN_TIMER_CONT_NO_ACTION_DURA_30min	95		// 设置持续、开关持续扫描模式下无动作保持解码时长为30分钟
#define MDFLAG_RESV_SCAN_TIMER_CONT_NO_ACTION_DURA_1h		96		// 设置持续、开关持续扫描模式下无动作保持解码时长为1小时
#define MDFLAG_RESV_SCAN_TIMER_CONT_NO_ACTION_DURA_2h		97		// 设置持续、开关持续扫描模式下无动作保持解码时长为2小时
#define MDFLAG_RESV_SCAN_TIMER_CONT_NO_ACTION_DURA_4h		98		// 设置持续、开关持续扫描模式下无动作保持解码时长为4小时
#define MDFLAG_RESV_SCAN_TIMER_CONT_NO_ACTION_DURA_8h		99		// 设置持续、开关持续扫描模式下无动作保持解码时长为8小时
#define MDFLAG_RESV_SCAN_TIMER_CONT_NO_ACTION_DURA_Default	MDFLAG_RESV_SCAN_TIMER_CONT_NO_ACTION_DURA_4s

// “9937”，Dec，保留参数，串口硬件流控配置控制；
#define MDFLAG_RESV_HWFlowCfgCtrl_Disable					0		// 失能串口硬件流控配置
#define MDFLAG_RESV_HWFlowCfgCtrl_Enable					1		// 使能串口硬件流控配置
#define MDFLAG_RESV_HWFlowCfgCtrl_Default					MDFLAG_RESV_HWFlowCfgCtrl_Enable

// “9938”，Dec，保留参数，有线类产品，定时重启控制；
#define MDFLAG_RESV_CordedTimeToRebootCtrl_Forbid			0		// 禁止定时重启
#define MDFLAG_RESV_CordedTimeToRebootCtrl_30Secs			1		// 定时30秒后重启设备
#define MDFLAG_RESV_CordedTimeToRebootCtrl_5Mins			2		// 定时5分钟后重启设备
#define MDFLAG_RESV_CordedTimeToRebootCtrl_15Mins			3		// 定时15分钟后重启设备
#define MDFLAG_RESV_CordedTimeToRebootCtrl_30Mins			4		// 定时30分钟后重启设备
#define MDFLAG_RESV_CordedTimeToRebootCtrl_1Hour			5		// 定时1小时后重启设备
#define MDFLAG_RESV_CordedTimeToRebootCtrl_2Hours			6		// 定时2小时后重启设备
#define MDFLAG_RESV_CordedTimeToRebootCtrl_3Hours			7		// 定时3小时后重启设备
#define MDFLAG_RESV_CordedTimeToRebootCtrl_4Hours			8		// 定时4小时后重启设备
#define MDFLAG_RESV_CordedTimeToRebootCtrl_8Hours			9		// 定时8小时后重启设备
#define MDFLAG_RESV_CordedTimeToRebootCtrl_12Hours			10		// 定时12小时后重启设备
#define MDFLAG_RESV_CordedTimeToRebootCtrl_24Hours			11		// 定时24小时后重启设备
#define MDFLAG_RESV_CordedTimeToRebootCtrl_48Hours			12		// 定时48小时后重启设备
#define MDFLAG_RESV_CordedTimeToRebootCtrl_Default			MDFLAG_RESV_CordedTimeToRebootCtrl_Forbid

// “9939”，Dec，保留参数，相对于原始位置，实际输出图像的翻转控制；
#define MDFLAG_RESV_ActualImgFlipCtrl_None					0		// 不进行翻转
#define MDFLAG_RESV_ActualImgFlipCtrl_Horizontal			1		// 进行水平翻转
#define MDFLAG_RESV_ActualImgFlipCtrl_Vertical				2		// 进行垂直翻转
#define MDFLAG_RESV_ActualImgFlipCtrl_Diagonal				3		// 进行对角线翻转
#define MDFLAG_RESV_ActualImgFlipCtrl_Default				MDFLAG_RESV_ActualImgFlipCtrl_None

// “9940”，Dec，保留参数，系统异常信息记录开关；
#define MDFLAG_RESV_SysExceptionRecord_Disable				0		// 系统异常时，不记录信息
#define MDFLAG_RESV_SysExceptionRecord_Enable				1		// 系统异常时，记录信息

// “9941”，Dec，保留参数，条码数据信息增加条码坐标选项；
#define MDFLAG_RESV_AttachCodePosOption_Disable				0		// 条码数据不增加条码坐标信息
#define MDFLAG_RESV_AttachCodePosOption_Enable				1		// 条码数据中增加条码坐标信息
#define MDFLAG_RESV_AttachCodePosOption_Default				MDFLAG_RESV_AttachCodePosOption_Disable

// “9942”，Dec，保留参数，EAS消磁信号保持时长；（单位：10ms）
#define MDFLAG_RESV_EAS_Degauss_HoldTime_Unit_10ms			10		// EAS消磁信号保持时长单位10ms
#define MDFLAG_RESV_EAS_Degauss_Forbid						0		// 禁止消磁
#define MDFLAG_RESV_EAS_Degauss_HoldTime_10ms				1		// EAS消磁信号保持时长10ms
#define MDFLAG_RESV_EAS_Degauss_HoldTime_Max				10		// EAS消磁信号保持时长最大值100ms
#define MDFLAG_RESV_EAS_Degauss_HoldTime_Default			MDFLAG_RESV_EAS_Degauss_HoldTime_10ms

// “9943”，Dec，保留参数，系统 状态信息输出控制；
#define MDFLAG_RESV_SysStateMsgOutput_Forbid				0		// 禁止输出
#define MDFLAG_RESV_SysStateMsgOutput_COM_Main				1		// 主串口
#define MDFLAG_RESV_SysStateMsgOutput_COM_Aux				2		// 辅助串口
#define MDFLAG_RESV_SysStateMsgOutput_COM_Test				3		// 调试串口
#define MDFLAG_RESV_SysStateMsgOutput_COM_Wireless			4		// 无线串口
#define MDFLAG_RESV_SysStateMsgOutput_USB_HID				5		// USB-HID
#define MDFLAG_RESV_SysStateMsgOutput_USB_VCOM				6		// USB-虚拟串口
#define MDFLAG_RESV_SysStateMsgOutput_USB_SimpleCOM			7		// USB-Simple COM
#define MDFLAG_RESV_SysStateMsgOutput_USB_OPOS				8		// USB-OPOS
#define MDFLAG_RESV_SysStateMsgOutput_USB_CustHSBulk		9		// USB-自定义高速批量
#define MDFLAG_RESV_SysStateMsgOutput_USB_CustFSBulk		10		// USB-自定义全速批量
#define MDFLAG_RESV_SysStateMsgOutput_USB_IBMHandHeld		11		// IBM Hand Held
#define MDFLAG_RESV_SysStateMsgOutput_Wireless				12		// 无线
#define MDFLAG_RESV_SysStateMsgOutput_PS2					13		// PS/2
#define MDFLAG_RESV_SysStateMsgOutput_USB_CustHSHIDInt		14		// USB-自定义高速HID中断
#define MDFLAG_RESV_SysStateMsgOutput_USB_VCOM_OPOS			15		// USB-虚拟串口 OPOS
#define MDFLAG_RESV_SysStateMsgOutput_Software				16		// SOFTWARE
#define MDFLAG_RESV_SysStateMsgOutput_TCP_Server			17		// TCP服务器
#define MDFLAG_RESV_SysStateMsgOutput_TCP_Client			18		// TCP客户端
#define MDFLAG_RESV_SysStateMsgOutput_Host_Ctrl_Ch			19		// 主机控制通道
#define MDFLAG_RESV_SysStateMsgOutput_Default				MDFLAG_RESV_SysStateMsgOutput_Forbid

// “9944”，Dec，保留参数，系统运行过程中的调试信息输出控制；
#define MDFLAG_RESV_SysDebugProcInfoCtrl_Forbid				0		// 禁止输出
#define MDFLAG_RESV_SysDebugProcInfoCtrl_Both				1		// 允许全部输出
#define MDFLAG_RESV_SysDebugProcInfoCtrl_Default			MDFLAG_RESV_SysDebugProcInfoCtrl_Forbid

// “9945”，Dec，保留参数，系统调试信息的输出通道选项；
#define MDFLAG_RESV_SysDebugInfoChannel_None				0		// 无通道
#define MDFLAG_RESV_SysDebugInfoChannel_COM_Main			1		// 主串口
#define MDFLAG_RESV_SysDebugInfoChannel_COM_Aux				2		// 辅助串口
#define MDFLAG_RESV_SysDebugInfoChannel_COM_Test			3		// 调试串口
#define MDFLAG_RESV_SysDebugInfoChannel_COM_Wireless		4		// 无线串口
#define MDFLAG_RESV_SysDebugInfoChannel_USB_HID				5		// USB-HID
#define MDFLAG_RESV_SysDebugInfoChannel_USB_VCOM			6		// USB-虚拟串口
#define MDFLAG_RESV_SysDebugInfoChannel_USB_SimpleCOM		7		// USB-Simple COM
#define MDFLAG_RESV_SysDebugInfoChannel_USB_OPOS			8		// USB-OPOS
#define MDFLAG_RESV_SysDebugInfoChannel_USB_CustHSBulk		9		// USB-自定义高速批量
#define MDFLAG_RESV_SysDebugInfoChannel_USB_CustFSBulk		10		// USB-自定义全速批量
#define MDFLAG_RESV_SysDebugInfoChannel_USB_IBMHandHeld		11		// IBM Hand Held
#define MDFLAG_RESV_SysDebugInfoChannel_Wireless			12		// 无线
#define MDFLAG_RESV_SysDebugInfoChannel_PS2					13		// PS/2
#define MDFLAG_RESV_SysDebugInfoChannel_USB_CustHSHIDInt	14		// USB-自定义高速HID中断
#define MDFLAG_RESV_SysDebugInfoChannel_USB_VCOM_OPOS		15		// USB-虚拟串口 OPOS
#define MDFLAG_RESV_SysDebugInfoChannel_Software			16		// SOFTWARE
#define MDFLAG_RESV_SysDebugInfoChannel_TCP_Server			17		// TCP服务器
#define MDFLAG_RESV_SysDebugInfoChannel_TCP_Client			18		// TCP客户端
#define MDFLAG_RESV_SysDebugInfoChannel_Host_Ctrl_Ch		19		// 主机控制通道
#define MDFLAG_RESV_SysDebugInfoChannel_Default				MDFLAG_RESV_SysDebugInfoChannel_None

// “9946”，Dec，保留参数，USB键值传输超时时间；（单位：100ms）
#define MDFLAG_RESV_SysOutput_KeyTxTimeout_Unit_100ms		100		// USB键值传输超时单位100ms
#define MDFLAG_RESV_SysOutput_KeyTxTimeout_100ms			1		// USB键值传输超时时间100ms
#define MDFLAG_RESV_SysOutput_KeyTxTimeout_Default			MDFLAG_RESV_SysOutput_KeyTxTimeout_100ms

// “9947”，Hex，保留参数，参数控制自定义结束解码命令；
#define MDFLAG_RESV_ParaCtrl_StopDecode_Null				0		// 参数控制自定义结束解码命令
#define MDFLAG_RESV_ParaCtrl_StopDecode_Default				MDFLAG_RESV_ParaCtrl_StopDecode_Null

// “9948”，Hex，保留参数，参数控制自定义开始解码命令；
#define MDFLAG_RESV_ParaCtrl_StartDecode_Null				0		// 参数控制自定义开始解码命令
#define MDFLAG_RESV_ParaCtrl_StartDecode_Default			MDFLAG_RESV_ParaCtrl_StartDecode_Null

// “9949”，Dec，保留参数，无线产品空闲状态时蓝灯开关状态；
#define MDFLAG_RESV_IdleStateBlueLEDSwitch_OFF				0		// 待状态蓝灯常灭
#define MDFLAG_RESV_IdleStateBlueLEDSwitch_ON				1		// 待状态蓝灯常亮
#define MDFLAG_RESV_IdleStateBlueLEDSwitch_DefaultMode		MDFLAG_RESV_IdleStateBlueLEDSwitch_OFF

// “9950”，Dec，保留参数，静态（长期）提示状态刷新周期（单位:10ms）；
#define MDFLAG_RESV_StaticIndicRefrePeriod_Unit_10ms		10		// 静态（长期）提示状态刷新周期单位10ms
#define MDFLAG_RESV_StaticIndicRefrePeriod_100ms			10		// 静态（长期）提示状态刷新周期100ms
#define MDFLAG_RESV_StaticIndicRefrePeriod_Default			MDFLAG_RESV_StaticIndicRefrePeriod_100ms

// “9951”，Dec，保留参数，静态（长期）提示生效延时（单位：50ms）；
#define MDFLAG_RESV_StaticIndicaDly_Unit_50ms				50		// 静态（长期）提示生效延时单位50ms
#define MDFLAG_RESV_StaticIndicaDly_400ms					8		// 静态（长期）提示生效延时400ms
#define MDFLAG_RESV_StaticIndicaDly_Default					MDFLAG_RESV_StaticIndicaDly_400ms

// “9952”，Dec，保留参数，系统提示模式选项
#define MDFLAG_RESV_SysIndica_DisplaceMode					0		// 置换模式
#define MDFLAG_RESV_SysIndica_WaitMode						1		// 等待模式
#define MDFLAG_RESV_SysIndica_SequenceMode					2		// 序列模式
#define MDFLAG_RESV_SysIndica_DefaultMode					MDFLAG_RESV_SysIndica_DisplaceMode

// “9953”，Dec，保留参数，无线产品电池电压检周期（单位：1s）；
#define MDFLAG_RESV_BatteryDetePeriod_Unit_1s				1000	// 无线产品电池电压检周期单位1s
#define MDFLAG_RESV_BatteryDetePeriod_10s					10		// 无线产品电池电压检周期10s
#define MDFLAG_RESV_BatteryDetePeriod_Default				MDFLAG_RESV_BatteryDetePeriod_10s

// “9954”，Dec，保留参数，在架检测的周期（单位：50ms）；
#define MDFLAG_RESV_InStandDetectPeriod_Unit_50ms			50		// 在架检测的周期单位50ms
#define MDFLAG_RESV_InStandDetectPeriod_200ms				4		// 在架检测的周期200ms
#define MDFLAG_RESV_InStandDetectPeriod_Default				MDFLAG_RESV_InStandDetectPeriod_200ms

// “9955”，Dec，保留参数，提示保护选项，持续提示最大时间（单位：100ms）；
#define MDFLAG_RESV_IndicProTimeDuraMax_Unit_100ms			100		// 提示保护选项，持续提示最大时间单位100ms
#define MDFLAG_RESV_IndicProTimeDuraMax_5s					50		// 提示保护选项，持续提示最大时间5s
#define MDFLAG_RESV_IndicProTimeDuraMax_Default				MDFLAG_RESV_IndicProTimeDuraMax_5s

// “9956”，Dec，保留参数，自动成像的参数类型选项
#define MDFLAG_RESV_AIM_ParaType_Reserve					0		// 保留
#define MDFLAG_RESV_AIM_ParaType_XD							1		// XD对应的成像参数
#define MDFLAG_RESV_AIM_ParaType_HD							2		// HD对应的成像参数
#define MDFLAG_RESV_AIM_ParaType_SR							3		// SR对应的成像参数
#define MDFLAG_RESV_AIM_ParaType_ER							4		// ER对应的成像参数
#define MDFLAG_RESV_AIM_ParaType_WA							5		// WA对应的成像参数
#define MDFLAG_RESV_AIM_ParaType_DPM						6		// DPM对应的成像参数
#define MDFLAG_RESV_AIM_ParaType_Default					MDFLAG_RESV_AIM_ParaType_SR

// “9957”，Dec，保留参数，Ref图像场景纹理判断功能选项
#define MDFLAG_RESV_AIM_RefScnTexture_OFF					0		// 关闭Ref图像场景纹理判断功能选项
#define MDFLAG_RESV_AIM_RefScnTexture_ON					1		// 开启Ref图像场景纹理判断功能选项
#define MDFLAG_RESV_AIM_RefScnTexture_Default				MDFLAG_RESV_AIM_RefScnTexture_ON

// “9958”，Dec，保留参数，根据前次解码成功成像参数设置新一轮初始成像参数的功能选项
#define MDFLAG_RESV_AIM_LastSuccImaging_OFF					0		// 关闭根据前次解码成功成像参数设置新一轮初始成像参数的功能
#define MDFLAG_RESV_AIM_LastSuccImaging_ON					1		// 开启根据前次解码成功成像参数设置新一轮初始成像参数的功能
#define MDFLAG_RESV_AIM_LastSuccImaging_Default				MDFLAG_RESV_AIM_LastSuccImaging_OFF

// “9959”，Dec，保留参数，码种信息的信息指针定义检查功能
#define MDFLAG_RESV_CodeTypeInfoPtDefCheck_OFF				0		// 关闭码种信息信息指针定义检查功能
#define MDFLAG_RESV_CodeTypeInfoPtDefCheck_ON				1		// 开启码种信息信息指针定义检查功能
#define MDFLAG_RESV_CodeTypeInfoPtDefCheck_Default			MDFLAG_RESV_CodeTypeInfoPtDefCheck_OFF

// “9960”，Dec，保留参数，看门狗保护机制的检测周期（单位：100ms）；
#define MDFLAG_RESV_WDogProtectCheckPeriod_Unit_100ms		100		// 看门狗保护机制的检测周期单位100ms
#define MDFLAG_RESV_WDogProtectCheckPeriod_1s				10		// 看门狗保护机制的检测周期1s
#define MDFLAG_RESV_WDogProtectCheckPeriod_Default			MDFLAG_RESV_WDogProtectCheckPeriod_1s

// “9961”，Dec，保留参数，检测到高密条码后提高补光亮度开关及持续时间（单位：1s）；
#define MDFLAG_RESV_AIM_HiLum4HDCode_Unit_1s				1000	// 检测到高密条码后提高补光亮度开关及持续时间单位1s
#define MDFLAG_RESV_AIM_HiLum4HDCode_OFF					0		// 关闭检测到高密条码后提高补光亮度行为
#define MDFLAG_RESV_AIM_HiLum4HDCode_MAX					99		// 开启检测到高密条码后提高补光亮度行为，持续时间最长
#define MDFLAG_RESV_AIM_HiLum4HDCode_Default				MDFLAG_RESV_AIM_HiLum4HDCode_OFF

// “9962”，Dec，保留参数，扫码触发按键软件去抖延时选项（单位：10ms）；
#define MDFLAG_RESV_Trigger1DebounceDelay_Unit_10ms			10		// 扫码触发按键软件去抖延时单位10ms
#define MDFLAG_RESV_Trigger1DebounceDelay_20ms				2		// 扫码触发按键软件去抖延时选项20ms
#define MDFLAG_RESV_Trigger1DebounceDelay_Default			MDFLAG_RESV_Trigger1DebounceDelay_20ms

// “9963”，Dec，保留参数，辅助调试功能选项；
#define MDFLAG_RESV_AuxDebugOpt_OFF							0		// 关闭辅助调试信息。
#define MDFLAG_RESV_AuxDebugOpt_Both						1		// 开启所有的辅助调试信息输出。
#define MDFLAG_RESV_AuxDebugOpt_TimingOutput				2		// 定时输出辅助调试信息。
#define MDFLAG_RESV_AuxDebugOpt_WDogProOutput				3		// 看门狗保护输出辅助调试信息。
#define MDFLAG_RESV_AuxDebugOpt_Default						MDFLAG_RESV_AuxDebugOpt_OFF

// “9964”，Dec，保留参数，扫描模式限制选项；
#define MDFLAG_RESV_ScanModeRestrict_NONE					0		// 无扫描模式设定限制。
#define MDFLAG_RESV_ScanModeRestrict_Mode_Cont				1		// 限制设置持续、开关持续模式。

// “9965”，Dec，保留参数，system recover 提示选项；
#define MDFLAG_RESV_SysRecoverIndication_OFF				0		// 关闭system recover提示音。
#define MDFLAG_RESV_SysRecoverIndication_ON					1		// 开启system recover提示音。
#define MDFLAG_RESV_SysRecoverIndication_Default			MDFLAG_RESV_SysRecoverIndication_OFF

// “9966”，Hex，保留参数，数据输出倍数选项；
#define MDFLAG_RESV_OutputMultiple_None						0x0		// 不进行翻倍
#define MDFLAG_RESV_OutputMultiple_Default					MDFLAG_RESV_OutputMultiple_None

// “9967”，Dec，保留参数，成像链保护机制触发异常后执行的操作选项；
#define MDFLAG_RESV_ImgChainProExptOp_None					0		// 成像链保护机制触发后，不进行处理。
#define MDFLAG_RESV_ImgChainProExptOp_Recover				1		// 成像链保护机制触发后，进行自恢复操作。
#define MDFLAG_RESV_ImgChainProExptOp_Reboot				2		// 成像链保护机制触发后，直接重启设备。
#define MDFLAG_RESV_ImgChainProExptOp_Default				MDFLAG_RESV_ImgChainProExptOp_Reboot

// “9968”，Dec，保留参数，成像链保护机制类型选项；
#define MDFLAG_RESV_ImgChainProType_None					0		// 关闭所有类型的成像链保护机制。
#define MDFLAG_RESV_ImgChainProType_Both					1		// 开启所有类型的成像链保护机制。
#define MDFLAG_RESV_ImgChainProType_Flow					2		// 仅开启成像链流程异常保护机制。
#define MDFLAG_RESV_ImgChainProType_Vsync					3		// 仅开启VSYNC信号异常保护机制。
#define MDFLAG_RESV_ImgChainProType_Default					MDFLAG_RESV_ImgChainProType_Both

// “9969”，Dec，保留参数，拍照按键功能模式选项，拍照按键功能模式类型；
																	// 功能说明: TrDeco: 触发解码; SwScan: 长按切换扫描模式; SwLumi: 长按切换补光灯等级; 
																	//          SwBeep: 长按切换蜂鸣器音调; StUcip: 长按设置自定义成像参数
#define MDFLAG_RESV_Trig1Func_En_Deco_ConvScanMode			0		// 模式0: TrDeco: ON  | SwScan: ON  | SwLumi: OFF | SwBeep: OFF | StUcip: OFF
#define MDFLAG_RESV_Trig1Func_En_Deco						1		// 模式1: TrDeco: ON  | SwScan: OFF | SwLumi: OFF | SwBeep: OFF | StUcip: OFF
#define MDFLAG_RESV_Trig1Func_En_ConvScanMode				2		// 模式2: TrDeco: OFF | SwScan: ON  | SwLumi: OFF | SwBeep: OFF | StUcip: OFF
#define MDFLAG_RESV_Trig1Func_Dis_All						3		// 模式3: TrDeco: OFF | SwScan: OFF | SwLumi: OFF | SwBeep: OFF | StUcip: OFF
#define MDFLAG_RESV_Trig1Func_En_Deco_ConvLumLevel			4		// 模式4: TrDeco: ON  | SwScan: OFF | SwLumi: ON  | SwBeep: OFF | StUcip: OFF
#define MDFLAG_RESV_Trig1Func_En_ConvLumLevel				5		// 模式5: TrDeco: OFF | SwScan: OFF | SwLumi: ON  | SwBeep: OFF | StUcip: OFF
#define MDFLAG_RESV_Trig1Func_En_Deco_ConvBeeperTone		6		// 模式6: TrDeco: ON  | SwScan: OFF | SwLumi: OFF | SwBeep: ON  | StUcip: OFF
#define MDFLAG_RESV_Trig1Func_En_ConvBeeperTone				7		// 模式7: TrDeco: OFF | SwScan: OFF | SwLumi: OFF | SwBeep: ON  | StUcip: OFF
#define MDFLAG_RESV_Trig1Func_En_Deco_SetUserCusImgPara		8		// 模式8: TrDeco: ON  | SwScan: OFF | SwLumi: OFF | SwBeep: OFF | StUcip: ON 
#define MDFLAG_RESV_Trig1Func_En_SetUserCusImgPara			9		// 模式9: TrDeco: OFF | SwScan: OFF | SwLumi: OFF | SwBeep: OFF | StUcip: ON 
#define MDFLAG_RESV_Trig1Func_Default						MDFLAG_RESV_Trig1Func_En_Deco_ConvScanMode

// “9970”，Dec，保留参数，声音切换按键去抖延时选项（单位：10ms）；
#define MDFLAG_Trigger2DebounceDelay_Unit_10ms				10		// 声音切换按键去抖延时单位10ms
#define MDFLAG_Trigger2DebounceDelay_20ms					2		// 声音切换按键去抖延时20ms
#define MDFLAG_Trigger2DebounceDelay_Default				MDFLAG_Trigger2DebounceDelay_20ms

// “9971”，Dec，保留参数，系统重启功能控制选项，系统重启功能控制开关；
#define MDFLAG_RESV_SystemResetSwitch_OFF					0		// 关闭系统重启功能
#define MDFLAG_RESV_SystemResetSwitch_ON					1		// 开启系统重启功能
#define MDFLAG_RESV_SystemResetSwitch_Default				MDFLAG_RESV_SystemResetSwitch_OFF

// “9972”，Dec，保留参数，log 输出模式选项；
#define MDFLAG_RESV_LOG_NONE								0		// 不进行“log”输出
#define MDFLAG_RESV_LOG_AUTO_DETECT							1		// 自动匹配“log”输出
#define MDFLAG_RESV_LOG_SERIAL_1							2		// 串口1“log”输出
#define MDFLAG_RESV_LOG_SERIAL_2							3		// 串口2“log”输出
#define MDFLAG_RESV_LOG_SERIAL_3							4		// 串口3“log”输出
#define MDFLAG_RESV_LOG_MEMORY_DEBUG						5		// “log”信息保存到memory debug区域
#define MDFLAG_RESV_LOG_MEMORY_SYS							6		// “log”信息保存到memory system区域
#define MDFLAG_RESV_LOG_Default								MDFLAG_RESV_LOG_AUTO_DETECT

// “9973”，Dec，保留参数，扫码设置参数结果反馈功能选项，扫码设置参数结果反馈功能开关；
#define MDFLAG_RESV_ScanSetParaFBSwitch_OFF					0		// 扫描设置码后不返回设置码内容
#define MDFLAG_RESV_ScanSetParaFBSwitch_ON					1		// 扫描设置码后输出设置码的内容
#define MDFLAG_RESV_ScanSetParaFBSwitch_Default				MDFLAG_RESV_ScanSetParaFBSwitch_OFF

// “9974”，Dec，保留参数，扫码查询参数功能选项，扫码查询参数功能开关；
#define MDFLAG_RESV_ScanQueryParaSwitch_OFF					0		// 关闭扫码查询参数功能
#define MDFLAG_RESV_ScanQueryParaSwitch_ON					1		// 开启扫码查询参数功能
#define MDFLAG_RESV_ScanQueryParaSwitch_Default				MDFLAG_RESV_ScanQueryParaSwitch_OFF

// “9975”，Dec，保留参数，误码检测功能控制选项，误码检测功能开关；
#define MDFLAG_RESV_ResultCompareSwitch_OFF					0		// 关闭误码检测
#define MDFLAG_RESV_ResultCompareSwitch_ON					1		// 开启误码检测
#define MDFLAG_RESV_ResultCompareSwitch_Default				MDFLAG_RESV_ResultCompareSwitch_OFF

// “9976”，Dec，保留参数，彩虹识读选项；
#define MDFLAG_RESV_RainbowRead_Disable						0		// 失能彩虹识读
#define MDFLAG_RESV_RainbowRead_Enable						1		// 使能彩虹识读
#define MDFLAG_RESV_RainbowRead_Default						MDFLAG_RESV_RainbowRead_Enable

// “9977”，Hex，保留参数，系统重启延时选项（单位：50ms）；
#define MDFLAG_RESV_SystemResetDelay_Unit_50ms				50		// 系统重启延时单位50ms
#define MDFLAG_RESV_SystemResetDelay_1s						0x14	// 系统重启延时1s
#define MDFLAG_RESV_SystemResetDelay_Default				MDFLAG_RESV_SystemResetDelay_1s

// “9978”，Dec，保留参数， 受限功能控制选项，受限功能开关；
#define MDFLAG_RESV_LimitFunctionSwitch_OFF					0		// 关闭受限功能
#define MDFLAG_RESV_LimitFunctionSwitch_ON					1		// 开启受限功能开关
#define MDFLAG_RESV_LimitFunctionSwitch_Default				MDFLAG_RESV_LimitFunctionSwitch_OFF

// “9979”，Dec，保留参数，强制对灯光进行处理；
#define MDFLAG_RESV_AIM_LuminForce_Forbid					0		// 禁止强制对灯光处理
#define MDFLAG_RESV_AIM_LuminForce_Both						1		// 注意：此处表示允许检测解码场景离开与过曝时的强制灯光处理
#define MDFLAG_RESV_AIM_LuminForce_CheckoutDecoOnly			2		// 只开启检测解码场景离开时，强制降低灯光
#define MDFLAG_RESV_AIM_LuminForce_OETurnOffOnly			3		// 只开启过曝强制关灯
#define MDFLAG_RESV_AIM_LuminForce_ForceTurnOff				4		// 直接强制关灯
#define MDFLAG_RESV_AIM_LuminForce_Default					MDFLAG_RESV_AIM_LuminForce_OETurnOffOnly

// “9980”，Dec，保留参数，客户自定义设置码控制选项，客户自定义设置码功能开关；
#define MDFLAG_RESV_CustomOpcodeCtrl_Forbid					0		// 关闭客户自定义设置码识别功能。
#define MDFLAG_RESV_CustomOpcodeCtrl_Elgin_Bank				1		// 开启巴西银行定制设置码识别。
#define MDFLAG_RESV_CustomOpcodeCtrl_Default				MDFLAG_RESV_CustomOpcodeCtrl_Forbid

// “9981”，Dec，保留参数，在数据传输过程中成像操作选项；
#define MDFLAG_RESV_TransImagingCtrl_Forbid					0		// 在传输数据的过程中，禁止成像。
#define MDFLAG_RESV_TransImagingCtrl_Allow					1		// 在传输数据的过程中，允许成像。
#define MDFLAG_RESV_TransImagingCtrl_DependsOnFW			2		// 在传输数据的过程中，根据程序配置决定是否禁止成像。
#define MDFLAG_RESV_TransImagingCtrl_Default				MDFLAG_RESV_TransImagingCtrl_DependsOnFW

// “9982”，Dec，保留参数，补光灯延迟关闭时间设置（单位：200ms）；
#define MDFLAG_RESV_LuminDlyOffTime_Unit_200ms				200		// 补光灯延迟关闭时间单位200ms
#define MDFLAG_RESV_LuminDlyOffTime_2s						10		// 补光灯延迟关闭时间2s
#define MDFLAG_RESV_LuminDlyOffTime_Default					MDFLAG_RESV_LuminDlyOffTime_2s

// “9983”，Dec，保留参数，是否在USB底层进行特殊处理 - 针对MP150高速传图失败问题；
#define MDFLAG_RESV_UsbCfgSpecial_OFF						0		// 关闭进行特殊处理
#define MDFLAG_RESV_UsbCfgSpecial_ON						1		// 开启进行特殊处理
#define MDFLAG_RESV_UsbCfgSpecial_Default 					MDFLAG_RESV_UsbCfgSpecial_OFF

// “9984”，Dec，保留参数，系统上电提示信息；
#define MDFLAG_RESV_SysStartReport_OFF						0		// 重启时不输出信息
#define MDFLAG_RESV_SysStartReport_ON						1		// 重启时输出“～SYSTEM STARTUP～”
#define MDFLAG_RESV_SysStartReport_Default					MDFLAG_RESV_SysStartReport_OFF

// “9985”，Dec，保留参数，是否在USB重新枚举失败后进行设备重启；
#define MDFLAG_RESV_UsbEnumFailReset_OFF					0		// 关闭在USB重新枚举失败后进行设备重启
#define MDFLAG_RESV_UsbEnumFailReset_ON						1		// 开启在USB重新枚举失败后进行设备重启
// 此处写法不太合理，稍后优化
#if (MACR_MCU_X1500 == 1)
	#define MDFLAG_RESV_UsbEnumFailReset_Default 			MDFLAG_RESV_UsbEnumFailReset_ON
#else
	#define MDFLAG_RESV_UsbEnumFailReset_Default 			MDFLAG_RESV_UsbEnumFailReset_OFF
#endif

// “9986”，Dec，保留参数，客户自定义指令匹配功能模式；
#define MDFLAG_RESV_CustomCmdMode_OFF						0		// 关闭客户自定义指令匹配
#define MDFLAG_RESV_CustomCmdMode_XinGuoDu					1		// 开启新国都客户自定义指令匹配
#define MDFLAG_RESV_CustomCmdMode_LangChao					2		// 开启浪潮客户自定义指令匹配
#define MDFLAG_RESV_CustomCmdMode_XinBeiYang				3		// 开启新北洋客户自定义指令匹配
#define MDFLAG_RESV_CustomCmdMode_Elgin						4		// 开启巴西客户自定义指令匹配
#define MDFLAG_RESV_CustomCmdMode_DaZu						5		// 开启大族客户自定义指令匹配
#define MDFLAG_RESV_CustomCmdMode_ParaCtrl					6		// 开启参数控制自定义指令匹配
// 此参数值仅在大真客户定制分支：feat_proj_sys_arm_v1.5.1275.1_customer_dazhen_es4610+_custom中使用
#define MDFLAG_RESV_CustomCmdMode_DaZhen					7		// 开启大真客户自定义指令匹配
#define MDFLAG_RESV_CustomCmdMode_TaiWanRuiZhan				8		// 开启台湾睿展客户自定义指令匹配
#define MDFLAG_RESV_CustomCmdMode_Perkon					9		// 开启土耳其客户自定义指令匹配
#define MDFLAG_RESV_CustomCmdMode_Mindeo_SoftDeco			10		// 开启 Mindeo 软解码项目客户自定义指令匹配（主要用于恢复解码命令 ACK/NAK）
#define MDFLAG_RESV_CustomCmdMode_Default					MDFLAG_RESV_CustomCmdMode_OFF

// “9987”，Dec，保留参数，重码时延工作机制（仅系统重码时延模块/系统+解码器重码时延模块）
#define MDFLAG_RESV_SameCodeMode_SysOnly					0		// 仅系统重码时延模块工作（原始流程）
#define MDFLAG_RESV_SameCodeMode_SysDeco					1		// 系统重码时延模块+解码器重码时延机制
#define MDFLAG_RESV_SameCodeMode_Default					MDFLAG_RESV_SameCodeMode_SysDeco

// “9988”，Dec，保留参数，异常重启后上电提示音；
#define MDFLAG_RESV_ExptRstPwrIndica_OFF					0		// 关闭异常重启后上电提示音
#define MDFLAG_RESV_ExptRstPwrIndica_ON						1		// 开启异常重启后上电提示音
#define MDFLAG_RESV_ExptRstPwrIndica_Default				MDFLAG_RESV_ExptRstPwrIndica_OFF

// “9989”，Dec，保留参数，内存信息输出开关；
#define MDFLAG_RESV_MemInfoSwitch_OFF						0		// 关闭内存信息输出开关
#define MDFLAG_RESV_MemInfoSwitch_ON						1		// 开启内存信息输出开关
#define MDFLAG_RESV_MemInfoSwitch_Default					MDFLAG_RESV_MemInfoSwitch_OFF

// “9990”，Dec，保留参数，参数列表打印开关；
#define MDFLAG_RESV_ParaDispSwitch_OFF						0		// 关闭参数列表打印
#define MDFLAG_RESV_ParaDispSwitch_ON						1		// 开启参数列表打印
#define MDFLAG_RESV_ParaDispSwitch_Default					MDFLAG_RESV_ParaDispSwitch_OFF

// “9991”，Dec，保留参数，重传最大次数；
#define MDFLAG_RESV_SysOutput_ReTxTime_0					0		// 重传最大次数0
#define MDFLAG_RESV_SysOutput_ReTxTime_Default				MDFLAG_RESV_SysOutput_ReTxTime_0

// “9992”，Dec，保留参数，重传最大延时（单位：1ms）；
#define MDFLAG_RESV_SysOutput_ReTxDelay_Unit_1ms			1		// 重传最大延时单位1ms
#define MDFLAG_RESV_SysOutput_ReTxDelay_10ms				10		// 重传最大延时10ms
#define MDFLAG_RESV_SysOutput_ReTxDelay_Default				MDFLAG_RESV_SysOutput_ReTxDelay_10ms

// “9993”，Dec，保留参数，USB重新枚举尝试次数；
#define MDFLAG_RESV_USB_ReEnumTime_3						3		// USB重新枚举尝试3次
#define MDFLAG_RESV_USB_ReEnumTime_Default					MDFLAG_RESV_USB_ReEnumTime_3

// “9994”，Dec，保留参数，USB重新枚举状态检查次数；
#define MDFLAG_RESV_USB_EnumChkTime_6						6		// USB重新枚举状态检查6次
#define MDFLAG_RESV_USB_EnumChkTime_Default					MDFLAG_RESV_USB_EnumChkTime_6

// “9995”，Dec，保留参数，备份解码图像；
#define MDFLAG_RESV_BackupDecoImg_OFF						0		// 关闭图像备份
#define MDFLAG_RESV_BackupDecoImg_Both						1		// 备份解码器级（解码器获取到数据）、系统级（系统完成数据的传输与提示）的解码成功图像，需要2个图像buff
#define MDFLAG_RESV_BackupDecoImg_SuccOnly					2		// 仅备份解码器级的解码成功图像，需要1个图像备份buff
#define MDFLAG_RESV_BackupDecoImg_Default					MDFLAG_RESV_BackupDecoImg_OFF

// “9996”，Dec，保留参数，自动成像算法成像模式切换；
#define MDFLAG_RESV_AIM_ModeSwitch_Forbid					0		// 禁止自动成像算法成像模式切换
#define MDFLAG_RESV_AIM_ModeSwitch_Allow					1		// 允许自动成像算法成像模式切换
#define MDFLAG_RESV_AIM_ModeSwitch_DependsOnFW				2		// 取决于软件
#define MDFLAG_RESV_AIM_ModeSwitch_Default					MDFLAG_RESV_AIM_ModeSwitch_DependsOnFW

// “9997”，Dec，保留参数，解码器输出顶点信息至Additional Info；
#define MDFLAG_RESV_DecodeVertex_Disable					0		// 关闭
#define MDFLAG_RESV_DecodeVertex_Enable						1		// 开启
#define MDFLAG_RESV_DecodeVertex_Default					MDFLAG_RESV_DecodeVertex_Disable

// “9998”，Dec，保留参数，生产过程老化测试选项；
#define MDFLAG_RESV_ManuAgingTest_OFF						0		// 关闭生产老化测试
#define MDFLAG_RESV_ManuAgingTest_MODE_1					1		// 生产老化测试模式1
#define MDFLAG_RESV_ManuAgingTest_MODE_2					2		// 生产老化测试模式2
#define MDFLAG_RESV_ManuAgingTest_MODE_3					3		// 生产老化测试模式3
#define MDFLAG_RESV_ManuAgingTest_MODE_4					4		// 生产老化测试模式4
#define MDFLAG_RESV_ManuAgingTest_Default					MDFLAG_RESV_ManuAgingTest_OFF

// “9999”，Hex，保留参数，自定义配置；
#define MDFLAG_RESV_CustomConfigs_None						0		// 关闭自定义配置
#define MDFLAG_RESV_CustomConfigs_Default					MDFLAG_RESV_CustomConfigs_None

#endif /* _PARA_CODE_OPTIONS_MACRO_H_ */
