//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_tmr.c
// Author		: XuCF
// Created On	: 2023/04/12
// Description	: hal_tmr.c
//
// History
// 1. V1.0, Created by XuCF. 2023/04/12
//=============================================================================

// Code impo >>
// Code impo <<

#include "object.h"
#include "hal/driver/hal_tmr.h"

// Code head Top
// Code head Bot

s32 HAL_MdTmrCall_OnTimeout(MdTmrCall *thiz)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdTmrCall *))(((VTable *)thiz)->__vtb__)(ApiOnTimeout))(thiz);
}

s32 HAL_MdTmr_Cycl(MdTmr *thiz, MdTmrCall *call, u32 us)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdTmr *, MdTmrCall *, u32))(((VTable *)thiz)->__vtb__)(ApiCycl))(thiz, call, us);
}

s32 HAL_MdTmr_Once(MdTmr *thiz, MdTmrCall *call, u32 us)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdTmr *, MdTmrCall *, u32))(((VTable *)thiz)->__vtb__)(ApiOnce))(thiz, call, us);
}

s32 HAL_MdTmr_Stop(MdTmr *thiz)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdTmr *))(((VTable *)thiz)->__vtb__)(ApiStop))(thiz);
}

s32 HAL_MdTmr_Read(MdTmr *thiz, u32 *us)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdTmr *, u32 *))(((VTable *)thiz)->__vtb__)(ApiRead))(thiz, us);
}

s32 HAL_MdTmr_Free(MdTmr *thiz)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdTmr *))(((VTable *)thiz)->__vtb__)(ApiFree))(thiz);
}

// Code static Top
// Code static Bot

s32 HAL_MdTmr_Init(MdTmr *thiz, u32 uuid, str name)
{
	// Code HAL_MdTmr_Init Top
	s32 ret = MACR_NONE;
	HAL_Device_Init((Device *)thiz, uuid, name);
	return ret;
	// Code HAL_MdTmr_Init Bot
}

// Code tail >>
// Code tail <<
