//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_uart.h
// Author		: XuCF
// Created On	: 2023/07/24
// Description	: hal_uart.h
//
// History
// 1. V1.0, Created by XuCF. 2023/07/24
//=============================================================================

#ifndef _HAL_UART_H
#define _HAL_UART_H

#include "object.h"
#include "hal/hal_device.h"

// Code head Top
#define MD_UART_BITS_8N1   (0x0)
#define MD_UART_BITS_8N2   (0x1)
#define MD_UART_BITS_7N1   (0x2)
#define MD_UART_BITS_7N2   (0x3)
#define MD_UART_PARI_NOR   (0x0)
#define MD_UART_PARI_ODD   (0x1)
#define MD_UART_PARI_EVE   (0x2)

#define MD_UART_STAT_OFF   (0x0)
#define MD_UART_STAT_IDLE  (0x1)
#define MD_UART_STAT_READY (0x2)
// Code head Bot

ClassDeclare(UartBack);
ClassDeclare(MdUart);

// Code structs >>
Class(UartCbkMeta)
{
	any data;
	u32 info;
};
Class(UartCfgMeta)
{
	UartBack *back;
	u32 baud;
	u8 bits;
	u8 pari;
	s8 compat;
};
// Code structs <<

// [UartBack] <= [] <- []
ClassRealize(UartBack)
{
	VTable super;
};
// [MdUart] <= [Device] <- []
ClassRealize(MdUart)
{
	Device super;
	// Code MdUart-class >>
	// Code MdUart-class <<
};

// UartBack funcs
s32 HAL_UartBack_OnRecv(UartBack *thiz, UartCbkMeta *meta);
// MdUart funcs
s32 HAL_MdUart_Init(MdUart *thiz, u32 uuid, str name);
s32 HAL_MdUart_Free(MdUart *thiz);
s32 HAL_MdUart_Load(MdUart *thiz, UartCfgMeta *meta);
s32 HAL_MdUart_Ctrl(MdUart *thiz, s32 stat);
s32 HAL_MdUart_Send(MdUart *thiz, any data, u32 size);
s32 HAL_MdUart_Recv(MdUart *thiz, any data, u32 size);
s32 HAL_MdUart_Wait(MdUart *thiz, u32 wait);
s32 HAL_MdUart_Inqu(MdUart *thiz);

// Code tail Top
// Code tail Bot

#endif // _HAL_UART_H
