//=============================================================================
// Copyright (C), 2004-2021, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_pwm.c
// Author		: XuCF
// Created On	: 2021/08/11
// Description	: hal_pwm.c
//
// History
// 1. V1.0, Created by XuCF. 2021/08/11
//=============================================================================

// Code impo >>
// Code impo <<

#include "object.h"
#include "hal/driver/hal_pwm.h"

// Code head >>
#include "hal/driver/hal_pin.h"
// Code head <<

s32 HAL_MdFakePwm_Load_Impl(MdFakePwm *thiz, u32 freq, u16 duty, u16 ndiv);
s32 HAL_MdFakePwm_Ctrl_Impl(MdFakePwm *thiz, s32 stat);

static void *__MdFakePwm_VTable__(int id)
{
	switch (id)
	{
		case ApiLoad: return HAL_MdFakePwm_Load_Impl;
		case ApiCtrl: return HAL_MdFakePwm_Ctrl_Impl;
		default: return __DefaultImpl__;
	}
}

s32 HAL_MdPwm_Load(MdPwm *thiz, u32 freq, u16 duty, u16 ndiv)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdPwm *, u32, u16, u16))(((VTable *)thiz)->__vtb__)(ApiLoad))(thiz, freq, duty, ndiv);
}

s32 HAL_MdPwm_Ctrl(MdPwm *thiz, s32 stat)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdPwm *, s32))(((VTable *)thiz)->__vtb__)(ApiCtrl))(thiz, stat);
}

// Code static >>
// Code static <<

s32 HAL_MdFakePwm_Init(MdFakePwm *thiz, u32 uuid, str name, u32 pin)
{
	// Code HAL_MdFakePwm_Init >>
	VTableBuild(MdFakePwm, thiz);
	HAL_Device_Init((Device *)thiz, uuid, name);
	thiz->pin = HAL_MdPinHelper_Find(pin);
	thiz->stat = 0;
	return 0;
	// Code HAL_MdFakePwm_Init <<
}

s32 HAL_MdFakePwm_Load_Impl(MdFakePwm *thiz, u32 freq, u16 duty, u16 ndiv)
{
	// Code HAL_MdFakePwm_Load_Impl >>
	return 0;
	// Code HAL_MdFakePwm_Load_Impl <<
}

s32 HAL_MdFakePwm_Ctrl_Impl(MdFakePwm *thiz, s32 stat)
{
	// Code HAL_MdFakePwm_Ctrl_Impl >>
	PinCfgMeta meta;
	if (!thiz->pin) return 0;
	switch (stat)
	{
		case MD_PWM_STAT_OUT: meta.attr = MD_PIO_MODE_PP | MACR_HIGH; break;
		case MD_PWM_STAT_RDY: meta.attr = MD_PIO_MODE_PP | MACR_LOW; break;
		case MD_PWM_STAT_OFF:
		default: meta.attr = MD_PIO_MODE_IN; break;
	}
	HAL_MdPin_Load(thiz->pin, &meta);
	thiz->stat = stat;
	return 0;
	// Code HAL_MdFakePwm_Ctrl_Impl <<
}

// Code tail >>
// Code tail <<
