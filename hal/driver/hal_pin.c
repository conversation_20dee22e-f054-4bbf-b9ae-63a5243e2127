//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_pin.c
// Author		: XuCF
// Created On	: 2023/06/16
// Description	: hal_pin.c
//
// History
// 1. V1.0, Created by XuCF. 2023/06/16
//=============================================================================

// Code impo >>
// Code impo <<

#include "object.h"
#include "hal/driver/hal_pin.h"

// Code head Top
// Code head Bot

s32 HAL_MdPinCall_OnPinExcited(MdPinCall *thiz, PinCbkMeta *meta)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdPinCall *, PinCbkMeta *))(((VTable *)thiz)->__vtb__)(ApiOnPinExcited))(thiz, meta);
}

s32 HAL_MdPin_Load(MdPin *thiz, PinCfgMeta *meta)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdPin *, PinCfgMeta *))(((VTable *)thiz)->__vtb__)(ApiLoad))(thiz, meta);
}

s32 HAL_MdPin_Irqc(MdPin *thiz, s32 ena)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdPin *, s32))(((VTable *)thiz)->__vtb__)(ApiIrqc))(thiz, ena);
}

s32 HAL_MdPin_Vset(MdPin *thiz, u32 val)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdPin *, u32))(((VTable *)thiz)->__vtb__)(ApiVset))(thiz, val);
}

s32 HAL_MdPin_Vget(MdPin *thiz)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdPin *))(((VTable *)thiz)->__vtb__)(ApiVget))(thiz);
}

s32 HAL_MdPin_Free(MdPin *thiz)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdPin *))(((VTable *)thiz)->__vtb__)(ApiFree))(thiz);
}

s32 HAL_MdPinGroup_List(MdPinGroup *thiz)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdPinGroup *))(((VTable *)thiz)->__vtb__)(ApiList))(thiz);
}

any HAL_MdPinGroup_Requ(MdPinGroup *thiz, u32 id)
{
	// Find virtual func and invoke it
	return ((any(*)(MdPinGroup *, u32))(((VTable *)thiz)->__vtb__)(ApiRequ))(thiz, id);
}

// Code static Top
// Code static Bot

any HAL_MdPin_Find(u32 id)
{
	// Code HAL_MdPin_Find Top
	MdPinGroup *group = HAL_Device_Find(MD_PIO_PORT(id));
	if (!group) return MACR_NULL;
	MdPin *pin = HAL_MdPinGroup_Requ(group, MD_PIO_NUMB(id));
	return pin;
	// Code HAL_MdPin_Find Bot
}

s32 HAL_MdPinGroup_Init(MdPinGroup *thiz, u32 uuid, str name)
{
	// Code HAL_MdPinGroup_Init >>
	s32 ret = MACR_NONE;
	HAL_Device_Init((Device *)thiz, uuid, name);
	return ret;
	// Code HAL_MdPinGroup_Init <<
}

// Code tail >>
// Code tail <<
