//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_usb.h
// Author		: XuCF
// Created On	: 2023/07/12
// Description	: hal_usb.h
//
// History
// 1. V1.0, Created by XuCF. 2023/07/12
//=============================================================================

#ifndef _HAL_USB_H
#define _HAL_USB_H

#include "object.h"
#include "hal/hal_device.h"

// Code head Top
#define MD_USB_FUNC_CHS		  0
#define MD_USB_FUNC_KBD		  1
#define MD_USB_FUNC_HID		  2
#define MD_USB_FUNC_ACM		  3
#define MD_USB_FUNC_CVC		  4
#define MD_USB_FUNC_MSD		  5
#define MD_USB_FUNC_LEG		  6
#define MD_USB_FUNC_NET		  7
#define MD_USB_FUNC_NUM		  8

#define MD_USB_INFO_FUN(info) (((info) >> 24u) & 0xffu)
#define MD_USB_INFO_LEN(info) ((info)&0x00ffffffu)
#define MD_USB_WAIT_TIM(wait) ((wait)&0x00ffffffu)

#define MD_USB_STAT_DIS		  0
#define MD_USB_STAT_CON		  1

// Code head Bot

ClassDeclare(UsbBack);
ClassDeclare(MdUsb);

// Code structs >>
Class(UsbCfgMeta)
{
	UsbBack *back;
	u32 funcs_sel;
	u16 vendor_id;
	u16 product_id;
	u16 device_rev;
	str vendor_str;
	str product_str;
	str serial_numb;
	u32 kbd_inv;
};
Class(UsbCbkMeta)
{
	any data;
	u32 info;
};
// Code structs <<

// [UsbBack] <= [] <- []
ClassRealize(UsbBack)
{
	VTable super;
};
// [MdUsb] <= [Device] <- []
ClassRealize(MdUsb)
{
	Device super;
	// Code MdUsb-class >>
	// Code MdUsb-class <<
};

// UsbBack funcs
s32 HAL_UsbBack_OnConfig(UsbBack *thiz, UsbCbkMeta *meta);
s32 HAL_UsbBack_OnSuspend(UsbBack *thiz, UsbCbkMeta *meta);
s32 HAL_UsbBack_OnResume(UsbBack *thiz, UsbCbkMeta *meta);
s32 HAL_UsbBack_OnSent(UsbBack *thiz, UsbCbkMeta *meta);
s32 HAL_UsbBack_OnReceived(UsbBack *thiz, UsbCbkMeta *meta);
// MdUsb funcs
s32 HAL_MdUsb_Init(MdUsb *thiz, u32 uuid, str name);
s32 HAL_MdUsb_Free(MdUsb *thiz);
s32 HAL_MdUsb_Load(MdUsb *thiz, UsbCfgMeta *meta);
s32 HAL_MdUsb_Send(MdUsb *thiz, any data, u32 info);
s32 HAL_MdUsb_Recv(MdUsb *thiz, any data, u32 info);
s32 HAL_MdUsb_Wait(MdUsb *thiz, u32 wait);
s32 HAL_MdUsb_Ctrl(MdUsb *thiz, s32 stat);
s32 HAL_MdUsb_Abort(MdUsb *thiz, u32 info);

// Code tail Top
// Code tail Bot

#endif // _HAL_USB_H
