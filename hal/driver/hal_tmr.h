//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_tmr.h
// Author		: XuCF
// Created On	: 2023/04/12
// Description	: hal_tmr.h
//
// History
// 1. V1.0, Created by XuCF. 2023/04/12
//=============================================================================

#ifndef _HAL_TMR_H
#define _HAL_TMR_H

#include "object.h"
#include "hal/hal_device.h"

// Code head Top
#define MD_TMR_MODE_CYCL	   0x00
#define MD_TMR_MODE_ONCE	   0x01
// Code head Bot

ClassDeclare(MdTmrCall);
ClassDeclare(MdTmr);

// Code structs >>
// Code structs <<

// [MdTmrCall] <= [] <- []
ClassRealize(MdTmrCall)
{
	VTable super;
};
// [MdTmr] <= [Device] <- []
ClassRealize(MdTmr)
{
	Device super;
	// Code MdTmr-class >>
	// Code MdTmr-class <<
};

// MdTmrCall funcs
s32 HAL_MdTmrCall_OnTimeout(MdTmrCall *thiz);
// MdTmr funcs
s32 HAL_MdTmr_Init(MdTmr *thiz, u32 uuid, str name);
s32 HAL_MdTmr_Cycl(MdTmr *thiz, MdTmrCall *call, u32 us);
s32 HAL_MdTmr_Once(MdTmr *thiz, MdTmrCall *call, u32 us);
s32 HAL_MdTmr_Stop(MdTmr *thiz);
s32 HAL_MdTmr_Read(MdTmr *thiz, u32 *us);
s32 HAL_MdTmr_Free(MdTmr *thiz);

// Code tail Top
// Code tail Bot

#endif // _HAL_TMR_H
