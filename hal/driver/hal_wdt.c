//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_wdt.c
// Author		: XuCF
// Created On	: 2023/06/28
// Description	: hal_wdt.c
//
// History
// 1. V1.0, Created by XuCF. 2023/06/28
//=============================================================================

// Code impo >>
// Code impo <<

#include "object.h"
#include "hal/driver/hal_wdt.h"

// Code head Top
// Code head Bot

s32 HAL_MdWdt_Load(MdWdt *thiz, WdtCfgMeta *meta)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdWdt *, WdtCfgMeta *))(((VTable *)thiz)->__vtb__)(ApiLoad))(thiz, meta);
}

s32 HAL_MdWdt_Dump(MdWdt *thiz, WdtCfgMeta *meta)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdWdt *, WdtCfgMeta *))(((VTable *)thiz)->__vtb__)(ApiDump))(thiz, meta);
}

s32 HAL_MdWdt_Keep(MdWdt *thiz)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdWdt *))(((VTable *)thiz)->__vtb__)(ApiKeep))(thiz);
}

// Code static Top
// Code static Bot

s32 HAL_MdWdt_Init(MdWdt *thiz, u32 uuid, str name)
{
	// Code HAL_MdWdt_Init Top
	s32 ret = MACR_NONE;
	ret = HAL_Device_Init((Device *)thiz, uuid, name);
	return ret;
	// Code HAL_MdWdt_Init Bot
}

// Code tail >>
// Code tail <<
