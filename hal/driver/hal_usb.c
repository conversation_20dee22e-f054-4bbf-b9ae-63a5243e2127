//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_usb.c
// Author		: XuCF
// Created On	: 2023/07/12
// Description	: hal_usb.c
//
// History
// 1. V1.0, Created by XuCF. 2023/07/12
//=============================================================================

// Code impo >>
// Code impo <<

#include "object.h"
#include "hal/driver/hal_usb.h"

// Code head Top
// Code head Bot

s32 HAL_UsbBack_OnConfig(UsbBack *thiz, UsbCbkMeta *meta)
{
	// Find virtual func and invoke it
	return ((s32(*)(UsbBack *, UsbCbkMeta *))(((VTable *)thiz)->__vtb__)(ApiOnConfig))(thiz, meta);
}

s32 HAL_UsbBack_OnSuspend(UsbBack *thiz, UsbCbkMeta *meta)
{
	// Find virtual func and invoke it
	return ((s32(*)(UsbBack *, UsbCbkMeta *))(((VTable *)thiz)->__vtb__)(ApiOnSuspend))(thiz, meta);
}

s32 HAL_UsbBack_OnResume(UsbBack *thiz, UsbCbkMeta *meta)
{
	// Find virtual func and invoke it
	return ((s32(*)(UsbBack *, UsbCbkMeta *))(((VTable *)thiz)->__vtb__)(ApiOnResume))(thiz, meta);
}

s32 HAL_UsbBack_OnSent(UsbBack *thiz, UsbCbkMeta *meta)
{
	// Find virtual func and invoke it
	return ((s32(*)(UsbBack *, UsbCbkMeta *))(((VTable *)thiz)->__vtb__)(ApiOnSent))(thiz, meta);
}

s32 HAL_UsbBack_OnReceived(UsbBack *thiz, UsbCbkMeta *meta)
{
	// Find virtual func and invoke it
	return ((s32(*)(UsbBack *, UsbCbkMeta *))(((VTable *)thiz)->__vtb__)(ApiOnReceived))(thiz, meta);
}

s32 HAL_MdUsb_Free(MdUsb *thiz)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdUsb *))(((VTable *)thiz)->__vtb__)(ApiFree))(thiz);
}

s32 HAL_MdUsb_Load(MdUsb *thiz, UsbCfgMeta *meta)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdUsb *, UsbCfgMeta *))(((VTable *)thiz)->__vtb__)(ApiLoad))(thiz, meta);
}

s32 HAL_MdUsb_Send(MdUsb *thiz, any data, u32 info)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdUsb *, any, u32))(((VTable *)thiz)->__vtb__)(ApiSend))(thiz, data, info);
}

s32 HAL_MdUsb_Recv(MdUsb *thiz, any data, u32 info)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdUsb *, any, u32))(((VTable *)thiz)->__vtb__)(ApiRecv))(thiz, data, info);
}

s32 HAL_MdUsb_Wait(MdUsb *thiz, u32 wait)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdUsb *, u32))(((VTable *)thiz)->__vtb__)(ApiWait))(thiz, wait);
}

s32 HAL_MdUsb_Ctrl(MdUsb *thiz, s32 stat)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdUsb *, s32))(((VTable *)thiz)->__vtb__)(ApiCtrl))(thiz, stat);
}

s32 HAL_MdUsb_Abort(MdUsb *thiz, u32 info)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdUsb *, u32))(((VTable *)thiz)->__vtb__)(ApiAbort))(thiz, info);
}

// Code static Top
// Code static Bot

s32 HAL_MdUsb_Init(MdUsb *thiz, u32 uuid, str name)
{
	// Code HAL_MdUsb_Init Top
	s32 ret = MACR_NONE;
	ret = HAL_Device_Init((Device *)thiz, uuid, name);
	return ret;
	// Code HAL_MdUsb_Init Bot
}

// Code tail >>
// Code tail <<
