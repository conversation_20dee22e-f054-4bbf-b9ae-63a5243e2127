//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_uart.c
// Author		: XuCF
// Created On	: 2023/07/24
// Description	: hal_uart.c
//
// History
// 1. V1.0, Created by XuCF. 2023/07/24
//=============================================================================

// Code impo >>
// Code impo <<

#include "object.h"
#include "hal/driver/hal_uart.h"

// Code head Top
// Code head Bot

s32 HAL_UartBack_OnRecv(UartBack *thiz, UartCbkMeta *meta)
{
	// Find virtual func and invoke it
	return ((s32(*)(UartBack *, UartCbkMeta *))(((VTable *)thiz)->__vtb__)(ApiOnRecv))(thiz, meta);
}

s32 HAL_MdUart_Free(MdUart *thiz)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdUart *))(((VTable *)thiz)->__vtb__)(ApiFree))(thiz);
}

s32 HAL_MdUart_Load(MdUart *thiz, UartCfgMeta *meta)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdUart *, UartCfgMeta *))(((VTable *)thiz)->__vtb__)(ApiLoad))(thiz, meta);
}

s32 HAL_MdUart_Ctrl(MdUart *thiz, s32 stat)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdUart *, s32))(((VTable *)thiz)->__vtb__)(ApiCtrl))(thiz, stat);
}

s32 HAL_MdUart_Send(MdUart *thiz, any data, u32 size)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdUart *, any, u32))(((VTable *)thiz)->__vtb__)(ApiSend))(thiz, data, size);
}

s32 HAL_MdUart_Recv(MdUart *thiz, any data, u32 size)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdUart *, any, u32))(((VTable *)thiz)->__vtb__)(ApiRecv))(thiz, data, size);
}

s32 HAL_MdUart_Wait(MdUart *thiz, u32 wait)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdUart *, u32))(((VTable *)thiz)->__vtb__)(ApiWait))(thiz, wait);
}

s32 HAL_MdUart_Inqu(MdUart *thiz)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdUart *))(((VTable *)thiz)->__vtb__)(ApiInqu))(thiz);
}

// Code static Top
// Code static Bot

s32 HAL_MdUart_Init(MdUart *thiz, u32 uuid, str name)
{
	// Code HAL_MdUart_Init Top
	s32 ret = MACR_NONE;
	ret = HAL_Device_Init((Device *)thiz, uuid, name);
	return ret;
	// Code HAL_MdUart_Init Bot
}

// Code tail >>
// Code tail <<
