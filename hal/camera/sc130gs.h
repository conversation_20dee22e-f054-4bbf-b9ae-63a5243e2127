//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: sc130gs.h
// Author		: CaoJL
// Created On	: 2018/08/20
// Description	: sc130gs.h
//
// History
// 1. V1.0, Created by CaoJL. 2018/08/20
// 1. V1.0, Created by XuCF. 2023/04/13
//=============================================================================

#ifndef SC130GS_H_
#define SC130GS_H_

#include "dev_common.h"

#include "common.h"
#include "board_info.h"
#include "dev_common.h"

#include "hal_device.h"
#include "./hal/kernel/hal_events.h" 

s32 SC130GS_Init(void);
void SC130GS_Test(void);
s32 SC130GS_WriteRegister(u16 reg_addr, u8 reg_val);
s32 SC130GS_ReadRegister(u16 reg_addr,u8 *reg_val);
void SC130GS_EnterStandby(void);
void SC130GS_ExitStandby(void);
void SC130GS_SetTrigger(u32 pin_state);
void SC130GS_EnterSlaveMode(void);
void SC130GS_SetAnalogGain(u16 ana_gain);
u32 SC130GS_GetAnalogGain(void);
void SC130GS_SetDigitalGain(u16 dig_gain);
u32 SC130GS_GetDigitalGain(void);
void SC130GS_SetExposure(u16 exposure);
u32 SC130GS_GetExposure(void);
void SC130GS_Reset(void);
void SC130GS_GetHistogram(u32* _hist, u32 bin_num, u8* pImgData);
u32 SC130GS_GetID(void);
u32 SC130GS_CalDlyBeforeExpo_us(void);
u32 SC130GS_GetDlyBeforeExpo_us(void);
void SC130GS_ImgQualityOptimize(void);
void SC130GS_EnableTestPattern(void);
void SC130GS_DisableTestPattern(void);
void SC130GS_SetFlipModeHori(void);
void SC130GS_SetFlipModeVert(void);
void SC130GS_SetFlipModeHoriVert(void);
void SC130GS_ResetFlipMode(void);
u32 SC130GS_GetStandby2CaptureDly_us(void);

#endif /* SC130GS_H_ */
