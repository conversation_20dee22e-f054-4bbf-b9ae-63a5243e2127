//=============================================================================
// Copyright (C), 2004-2021, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: v4l2.c
// Author		: XuCF
// Created On	: 2021/06/23
// Description	: v4l2.c
//
// History
// 1. V1.0, Created by XuCF. 2021/06/23
//=============================================================================

#include "v4l2.h"

s32 V4L2_Ctrl(Super *thiz, u32 what, void *param1, u32 param2)
{
	s32 result = MACR_NONE;
	return result;
}

s32 V4L2_Construct(V4L2 *thiz)
{
	s32 result = MACR_NONE;
	Overlay(V4L2_Ctrl);
	return result;
}
