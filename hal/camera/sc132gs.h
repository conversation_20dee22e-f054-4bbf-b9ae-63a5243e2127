//=============================================================================
// Copyright (C), 2004-2020, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: sc132gs.h
// Author		: CaoJL
// Created On	: 2020 April 17, Fri
// Description	: SC132GS Driver
//
// History
// 0. V1.0, Created by CaoJL, 2020 April 17, Fri
//
//=============================================================================

#ifndef SC132GS_H_
#define SC132GS_H_

#include "dev_common.h"

#include "common.h"
#include "board_info.h"
#include "dev_common.h"

#include "hal_device.h"
#include "./hal/kernel/hal_events.h" 

void SC132GS_Test(void);
s32 SC132GS_WriteRegister(u16 reg_addr, u8 reg_val);
s32 SC132GS_ReadRegister(u16 reg_addr,u8 *reg_val);
void SC132GS_EnterStandby(void);
void SC132GS_ExitStandby(void);
void SC132GS_SetTrigger(u32 pin_state);
void SC132GS_EnterSlaveMode(void);
void SC132GS_SetAnalogGain(u16 ana_gain);
u32 SC132GS_GetAnalogGain(void);
void SC132GS_SetDigitalGain(u16 dig_gain);
u32 SC132GS_GetDigitalGain(void);
void SC132GS_SetExposure(u16 exposure);
u32 SC132GS_GetExposure(void);
void SC132GS_Reset(void);
void SC132GS_GetHistogram(u32* _hist, u32 bin_num, u8* pImgData);
u32 SC132GS_GetID(void);
u32 SC132GS_CalDlyBeforeExpo_us(void);
u32 SC132GS_GetDlyBeforeExpo_us(void);
void SC132GS_ImgQualityOptimize(void);
void SC132GS_EnableTestPattern(void);
void SC132GS_DisableTestPattern(void);
void SC132GS_SetFlipModeHori(void);
void SC132GS_SetFlipModeVert(void);
void SC132GS_SetFlipModeHoriVert(void);
void SC132GS_ResetFlipMode(void);
s32 SC132GS_GetTemperatureWithReg(void);
void SC132GS_Sleep(void);
void SC132GS_Wake(void);
u32 SC132GS_GetStandby2CaptureDly_us(void);

#endif /* SC132GS_H_ */
