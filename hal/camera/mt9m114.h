/*
 *  mt9m114.h
 *
 *  Created on: 2017/05/26/
 *      Author: FANG
 */

#ifndef MT9M114_H_
#define MT9M114_H_


#define MT9M114_WRITE_ID                		(0x90)
#define MT9M114_READ_ID                 		(0x91)

#define MT9M114_IMG_WIDTH        				1280
#define MT9M114_IMG_HEIGHT       				800//960

#define	DIVIDER_M_N							    0x0120
#define	DIVIDER_P								0x0700

#define	SENSOR_CFG_PIXCLK						48000000
#define	FRAME_LENGTH_LINES						5000		//Read_Time:	25us*968 = 32.3ms		Active_Row:		20ms
#define	LINE_LENGTH_PCK							1400		//Line_Time:  	1604/96M*2 = 33.4us		Active_Pixel:	20.8us

#define	ROW_TIME								33			//us

#define	COARSE_INTEGRATION_TIME					120
#define	ANALOG_GAIN								0

/* sensor size */

#define	Y_ADDR_START							4
#define X_ADDR_START 							4
#define Y_<PERSON>DR_END 								967
#define X_ADDR_END 								1287

///////////////////////////////////////////
/* Registers */

#define REG_CHIP_ID                               0x0000
#define REG_MON_MAJOR_VERSION                     0x8000
#define REG_MON_MINOR_VERION                      0x8002
#define REG_MON_RELEASE_VERSION                   0x8004
#define REG_RESET_AND_MISC_CONTROL                0x001A
#define REG_PAD_SLEW_CONTROL                      0x001E
#define REG_COMMAND_REGISTER                      0x0080

#define HOST_COMMAND_APPLY_PATCH                  0x0001
#define HOST_COMMAND_SET_STATE                    0x0002
#define HOST_COMMAND_REFRESH                      0x0004
#define HOST_COMMAND_WAIT_FOR_EVENT               0x0008
#define HOST_COMMAND_OK                           0x8000

#define REG_ACCESS_CTL_STAT                       0x0982
#define REG_PHYSICAL_ADDRESS_ACCESS               0x098A
#define REG_LOGICAL_ADDRESS_ACCESS                0x098E
#define MCU_VARIABLE_DATA0                        0x0990
#define MCU_VARIABLE_DATA1                        0x0992
#define REG_RESET_REGISTER                        0x301A
#define REG_DAC_TXLO_ROW                          0x316A
#define REG_DAC_TXLO                              0x316C
#define REG_DAC_LD_4_5                            0x3ED0
#define REG_DAC_LD_6_7                            0x3ED2
#define REG_DAC_ECL                               0x316E
#define REG_DELTA_DK_CONTROL                      0x3180
#define REG_SAMP_COL_PUP2                         0x3E14
#define REG_COLUMN_CORRECTION                     0x30D4
#define REG_LL_ALGO                               0xBC04
#define LL_EXEC_DELTA_DK_CORRECTION               0x0200
#define REG_CAM_DGAIN_RED                         0xC840
#define REG_CAM_DGAIN_GREEN_1                     0xC842
#define REG_CAM_DGAIN_GREEN_2                     0xC844
#define REG_CAM_DGAIN_BLUE                        0xC846

#define REG_CAM_SYSCTL_PLL_ENABLE                 0xC97E
#define REG_CAM_SYSCTL_PLL_DIVIDER_M_N            0xC980
#define REG_CAM_SYSCTL_PLL_DIVIDER_P              0xC982
#define REG_CAM_SENSOR_CFG_Y_ADDR_START           0xC800
#define REG_CAM_SENSOR_CFG_X_ADDR_START           0xC802
#define REG_CAM_SENSOR_CFG_Y_ADDR_END             0xC804
#define REG_CAM_SENSOR_CFG_X_ADDR_END             0xC806
#define REG_CAM_SENSOR_CFG_PIXCLK                 0xC808
#define REG_CAM_SENSOR_CFG_PIXCLK_2               0xC80A
#define REG_CAM_SENSOR_CFG_ROW_SPEED              0xC80C
#define REG_CAM_SENSOR_CFG_FINE_INTEG_TIME_MIN    0xC80E
#define REG_CAM_SENSOR_CFG_FINE_INTEG_TIME_MAX    0xC810
#define REG_CAM_SENSOR_CFG_FRAME_LENGTH_LINES     0xC812
#define REG_CAM_SENSOR_CFG_LINE_LENGTH_PCK        0xC814
#define REG_CAM_SENSOR_CFG_FINE_CORRECTION        0xC816
#define REG_CAM_SENSOR_CFG_CPIPE_LAST_ROW         0xC818
#define REG_CAM_SENSOR_CFG_REG_0_DATA             0xC826
#define REG_CAM_SENSOR_CONTROL_READ_MODE          0xC834

#define CAM_SENSOR_CONTROL_VERT_FLIP_EN           0x0002
#define CAM_SENSOR_CONTROL_HORZ_FLIP_EN           0x0001
#define CAM_SENSOR_CONTROL_BINNING_EN             0x0330
#define CAM_SENSOR_CONTROL_SKIPPING_EN            0x0110
#define CAM_MON_HEARTBEAT                         0x8006 //the frame counter. updates on vertical blanking.

#define REG_CAM_CROP_WINDOW_XOFFSET               0xC854
#define REG_CAM_CROP_WINDOW_YOFFSET               0xC856
#define REG_CAM_CROP_WINDOW_WIDTH                 0xC858
#define REG_CAM_CROP_WINDOW_HEIGHT                0xC85A
#define REG_CAM_CROP_CROPMODE                     0xC85C
#define REG_CAM_OUTPUT_WIDTH                      0xC868
#define REG_CAM_OUTPUT_HEIGHT                     0xC86A
#define REG_CAM_OUTPUT_FORMAT                     0xC86C
#define REG_CAM_OUTPUT_OFFSET                     0xC870
#define REG_CAM_PORT_OUTPUT_CONTROL               0xC984
#define REG_CAM_OUPUT_FORMAT_YUV                  0xC86E
#define REG_CAM_STAT_AWB_CLIP_WINDOW_XSTART       0xC914
#define REG_CAM_STAT_AWB_CLIP_WINDOW_YSTART       0xC916
#define REG_CAM_STAT_AWB_CLIP_WINDOW_XEND         0xC918
#define REG_CAM_STAT_AWB_CLIP_WINDOW_YEND         0xC91A
#define REG_CAM_STAT_AE_INITIAL_WINDOW_XSTART     0xC91C
#define REG_CAM_STAT_AE_INITIAL_WINDOW_YSTART     0xC91E
#define REG_CAM_STAT_AE_INITIAL_WINDOW_XEND       0xC920
#define REG_CAM_STAT_AE_INITIAL_WINDOW_YEND       0xC922
#define REG_CAM_PGA_PGA_CONTROL                   0xC95E
#define REG_SYSMGR_NEXT_STATE                     0xDC00
#define REG_SYSMGR_CURRENT_STATE                  0xDC01
#define REG_PATCHLDR_LOADER_ADDRESS               0xE000
#define REG_PATCHLDR_PATCH_ID                     0xE002
#define REG_PATCHLDR_FIRMWARE_ID                  0xE004
#define REG_PATCHLDR_APPLY_STATUS                 0xE008
#define REG_AUTO_BINNING_MODE                     0xE801
#define REG_CAM_SENSOR_CFG_MAX_ANALOG_GAIN        0xC81C
#define REG_CROP_CROPMODE                         0xC85C
#define REG_CAM_AET_AEMODE                        0xC878
#define REG_CAM_AET_TARGET_AVG_LUMA               0xC87A
#define REG_CAM_AET_TARGET_AVERAGE_LUMA_DARK      0xC87B
#define REG_CAM_AET_BLACK_CLIPPING_TARGET         0xC87C
#define REG_CAM_AET_AE_MAX_VIRT_AGAIN             0xC886
#define REG_CAM_AET_MAX_FRAME_RATE                0xC88C
#define REG_CAM_AET_MIN_FRAME_RATE                0xC88E
#define REG_CAM_AET_TARGET_GAIN                   0xC890
#define REG_AE_ALGORITHM                          0xA404
#define REG_AE_TRACK_MODE                         0xA802
#define REG_AE_TRACK_AE_TRACKING_DAMPENING_SPEED  0xA80A

#define REG_CAM_LL_START_BRIGHTNESS               0xC926
#define REG_CAM_LL_STOP_BRIGHTNESS                0xC928
#define REG_CAM_LL_START_GAIN_METRIC              0xC946
#define REG_CAM_LL_STOP_GAIN_METRIC               0xC948
#define REG_CAM_LL_START_TARGET_LUMA_BM           0xC952
#define REG_CAM_LL_STOP_TARGET_LUMA_BM            0xC954
#define REG_CAM_LL_START_SATURATION               0xC92A
#define REG_CAM_LL_END_SATURATION                 0xC92B
#define REG_CAM_LL_START_DESATURATION             0xC92C
#define REG_CAM_LL_END_DESATURATION               0xC92D
#define REG_CAM_LL_START_DEMOSAIC                 0xC92E
#define REG_CAM_LL_START_AP_GAIN                  0xC92F
#define REG_CAM_LL_START_AP_THRESH                0xC930
#define REG_CAM_LL_STOP_DEMOSAIC                  0xC931
#define REG_CAM_LL_STOP_AP_GAIN                   0xC932
#define REG_CAM_LL_STOP_AP_THRESH                 0xC933
#define REG_CAM_LL_START_NR_RED                   0xC934
#define REG_CAM_LL_START_NR_GREEN                 0xC935
#define REG_CAM_LL_START_NR_BLUE                  0xC936
#define REG_CAM_LL_START_NR_THRESH                0xC937
#define REG_CAM_LL_STOP_NR_RED                    0xC938
#define REG_CAM_LL_STOP_NR_GREEN                  0xC939
#define REG_CAM_LL_STOP_NR_BLUE                   0xC93A
#define REG_CAM_LL_STOP_NR_THRESH                 0xC93B
#define REG_CAM_LL_START_CONTRAST_BM              0xC93C
#define REG_CAM_LL_STOP_CONTRAST_BM               0xC93E
#define REG_CAM_LL_GAMMA                          0xC940
#define REG_CAM_LL_START_CONTRAST_GRADIENT        0xC942
#define REG_CAM_LL_STOP_CONTRAST_GRADIENT         0xC943
#define REG_CAM_LL_START_CONTRAST_LUMA_PERCENTAGE 0xC944
#define REG_CAM_LL_STOP_CONTRAST_LUMA_PERCENTAGE  0xC945
#define REG_CAM_LL_START_FADE_TO_BLACK_LUMA       0xC94A
#define REG_CAM_LL_STOP_FADE_TO_BLACK_LUMA        0xC94C
#define REG_CAM_LL_CLUSTER_DC_TH_BM               0xC94E
#define REG_CAM_LL_CLUSTER_DC_GATE_PERCENTAGE     0xC950
#define REG_CAM_LL_SUMMING_SENSITIVITY_FACTOR     0xC951
#define REG_CAM_LL_MODE                           0xBC02 //might be BC07.
#define REG_CCM_DELTA_GAIN                        0xB42A


#define REG_CAM_HUE_ANGLE                         0xC873

// AWB
#define REG_AWB_AWB_MODE                          0xC909
#define REG_AWB_COL_TEMP                          0xC8F0//color temp, only writeable if awb mode is manual. in kelvin
#define REG_AWB_COL_TEMP_MAX                      0xC8EE//maximum color temp in kelvin
#define REG_AWB_COL_TEMP_MIN                      0xC8EC//minimum color temp in kelvin

// UVC
#define REG_UVC_AE_MODE                           0xCC00
#define REG_UVC_AUTO_WHITE_BALANCE_TEMPERATURE    0xCC01
#define REG_UVC_AE_PRIORITY                       0xCC02
#define REG_UVC_POWER_LINE_FREQUENCY              0xCC03
#define REG_UVC_EXPOSURE_TIME                     0xCC04
#define REG_UVC_BACKLIGHT_COMPENSATION            0xCC08
#define REG_UVC_BRIGHTNESS                        0xCC0A //set brightness in auto exposure mode.

#define REG_UVC_CONTRAST                          0xCC0C //not exactly what the name suggests. See chip documentation

#define REG_UVC_GAIN                              0xCC0E
#define REG_UVC_HUE                               0xCC10
#define REG_UVC_SATURATION                        0xCC12
#define REG_UVC_SHARPNESS                         0xCC14
#define REG_UVC_GAMMA                             0xCC16
#define REG_UVC_WHITE_BALANCE_TEMPERATURE         0xCC18
#define REG_UVC_FRAME_INTERVAL                    0xCC1C
#define REG_UVC_MANUAL_EXPOSURE                   0xCC20
#define REG_UVC_FLICKER_AVOIDANCE                 0xCC21
#define REG_UVC_ALGO                              0xCC22
#define REG_UVC_RESULT_STATUS                     0xCC24

/**This variable selects the system event that the host wishes to wait for.
 * 1: end of frame
 * 2: start of frame */
#define REG_CMD_HANDLER_WAIT_FOR_EVENT            0xFC00

/** This variable determines the number of system event occurrences for which the
 *  Command Handler component will wait */
#define REG_CMD_HANDLER_NUM_WAIT_EVENTS           0xFC02

/**Result status code for last refresh command. Updates after refresh command.
 * Possible values:
   0x00: ENOERR - refresh successful
   0x13: EINVCROPX - invalid horizontal crop configuration
   0x14: EINVCROPY - invalid vertical crop configuration
   0x15: EINVTC - invalid Tilt Correction percentage
*/

#define REG_SEQ_ERROR_CODE                        0x8406


/* SYS_STATE values (for SYSMGR_NEXT_STATE and SYSMGR_CURRENT_STATE) */
#define MT9M114_SYS_STATE_ENTER_CONFIG_CHANGE           0x28
#define MT9M114_SYS_STATE_STREAMING                     0x31
#define MT9M114_SYS_STATE_START_STREAMING               0x34
#define MT9M114_SYS_STATE_ENTER_SUSPEND                 0x40
#define MT9M114_SYS_STATE_SUSPENDED                     0x41
#define MT9M114_SYS_STATE_ENTER_STANDBY                 0x50
#define MT9M114_SYS_STATE_STANDBY                       0x52
#define MT9M114_SYS_STATE_LEAVE_STANDBY                 0x54

///////////////////////////////////////////////////////////////


void MT9M114_WriteRegister8(u16 reg_addr, u8 reg_val);
void MT9M114_WriteRegister16(u16 reg_addr, u16 reg_val);
void MT9M114_WriteRegister32(u16 reg_addr, u32 reg_val);
void MT9M114_ReadRegister8(u16 reg_addr, u8 *reg_val);
void MT9M114_ReadRegister16(u16 reg_addr, u16 *reg_val);
void MT9M114_ReadRegister32(u16 reg_addr, u32 *reg_val);

void MT9M114_Init(void);
void MT9M114_Reset(void);
void MT9M114_Test(void);
void MT9M114_ExitTest(void);
void MT9M114_AEC_Enable(void);
void MT9M114_AEC_Disable(void);
void MT9M114_AWB_Enable(void);
void MT9M114_AWB_Disable(void);
void MT9M114_ExitStandby(void);
void MT9M114_EnterStandby(void);
void MT9M114_SetTrigger(u32 pin_state);
void MT9M114_SetExposure(u16 exposure);
void MT9M114_SetAnalogGain(u16 gain);
void MT9M114_SetDummyLine(u16 lines);
void MT9M114_SetAECDesiredBin(u16 bin);
void MT9M114_SetAECMaxExposure(u16 exposure);
void MT9M114_SetROI(u16 y_start, u16 x_start, u16 y_end, u16 x_end);
void MT9M114_GetHistogram(u32* _hist, u32 bin_num, u8* pImgData);
u32  MT9M114_GetCurrentBin(u8* p_data);
u32  MT9M114_GetCurrentBin_80Per(u8* p_data);
u32  MT9M114_GetExposure(void);
u32  MT9M114_GetAnalogGain(void);
u8 MT9M114_ReadCurState(void);

//u32 MT9M114_GetAGCGain(void);
//u32 MT9M114_GetAECExposure(void);

#endif /* MT9M114_H_ */

