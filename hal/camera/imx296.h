//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: imx296.h
// Author		: DongDL
// Created On	: 2024/09/04
// Description	: IMX296 Driver
//
// History
// 0. V1.0, Created by DongDL, 2024/09/04
//
//=============================================================================

#ifndef IMX296_H_
#define IMX296_H_

#include "common.h"

void IMX296_Test(void);
s32 IMX296_WriteRegister(u16 reg_addr, u8 reg_val);
s32 IMX296_ReadRegister(u16 reg_addr,u8 *reg_val);
void IMX296_EnterStandby(void);
void IMX296_ExitStandby(void);
void IMX296_SetTrigger(u32 pin_state);
void IMX296_EnterSlaveMode(void);
void IMX296_SetAnalogGain(u16 ana_gain);
u32 IMX296_GetAnalogGain(void);
void IMX296_SetExposure(u16 exposure);
u32 IMX296_GetExposure(void);
void IMX296_Reset(void);
void IMX296_GetHistogram(u32* _hist, u32 bin_num, u8* pImgData);
u32 IMX296_CalDlyBeforeExpo_us(void);
u32 IMX296_GetDlyBeforeExpo_us(void);
void IMX296_ImgQualityOptimize(void);
void IMX296_EnableTestPattern(void);
void IMX296_DisableTestPattern(void);
void IMX296_SetFlipModeHori(void);
void IMX296_SetFlipModeVert(void);
void IMX296_SetFlipModeHoriVert(void);
void IMX296_ResetFlipMode(void);
s32 IMX296_GetTemperatureWithReg(void);
void IMX296_Sleep(void);
void IMX296_Wake(void);
u32 IMX296_GetStandby2CaptureDly_us(void);

#endif /* IMX296_H_ */
