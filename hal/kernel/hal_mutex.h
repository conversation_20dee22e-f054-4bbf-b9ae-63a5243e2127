//=============================================================================
// Copyright (C), 2004-2021, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_mutex.h
// Author		: XuCF
// Created On	: 2021/10/15
// Description	: hal_mutex.h
//
// History
// 1. V1.0, Created by XuCF. 2021/10/15
//=============================================================================

#ifndef _HAL_MUTEX_H
#define _HAL_MUTEX_H

#include "object.h"

Class(Mutex)
{
	void *core;
	u8 *name;
};
typedef u32 (*fun_mutex_callback)(void *);

s32 HAL_Mutex_Construct(Mutex *thiz, str name);
s32 HAL_Mutex_Destruct(Mutex *thiz);
s32 HAL_Mutex_Lock(Mutex *thiz);
s32 HAL_Mutex_Unlock(Mutex *thiz);
u8 *HAL_Mutex_Name(Mutex *thiz);
s32 HAL_Mutex_TryLock_Callback_Attr(fun_mutex_callback callback_fun);
s32 HAL_Mutex_Locked_Callback_Attr(fun_mutex_callback callback_fun);
s32 HAL_Mutex_Unlock_Callback_Attr(fun_mutex_callback callback_fun);

#endif //_HAL_MUTEX_H
