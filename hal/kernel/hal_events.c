//=============================================================================
// Copyright (C), 2004-2020, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_events.c
// Author		: XuCF
// Created On	: 2020/10/13
// Description	: hal_events.c
//
// History
// 1. V1.0, Created by XuCF. 2020/10/13
//=============================================================================

#include "hal_events.h"

s32 HAL_Events_Construct(Events *thiz, str name)
{
	s32 result = MACR_NONE;
	thiz->name = name;
	return result;
}

s32 HAL_Events_Destruct(Events *thiz)
{
	s32 result = MACR_NONE;
	return result;
}


WEAK u8 *HAL_Event_Name(Events *thiz)
{
    return (u8 *)"unknown event";
}

WEAK s32 HAL_Event_Construct_Callback_Attr(fun_event_callback callback_fun)
{
	return MACR_NONE;
}

WEAK s32 HAL_Event_Destruct_Callback_Attr(fun_event_callback callback_fun)
{
	return MACR_NONE;
}

WEAK s32 HAL_Event_Wait_And_Callback_Attr(fun_event_callback callback_fun)
{
	return MACR_NONE;
}

WEAK s32 HAL_Event_Wait_Or_Callback_Attr(fun_event_callback callback_fun)
{
	return MACR_NONE;
}

WEAK s32 HAL_Event_Receive_Callback_Attr(fun_event_callback callback_fun)
{
	return MACR_NONE;
}
