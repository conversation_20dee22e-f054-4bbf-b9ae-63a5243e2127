//=============================================================================
// Copyright (C), 2004-2020, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: sys_mqueue.h
// Author		: XuCF
// Created On	: 2020/10/16
// Description	: sys_mqueue.h
//
// History
// 1. V1.0, Created by XuCF. 2020/10/16
//=============================================================================

#ifndef _HAL_MQUEUE_H
#define _HAL_MQUEUE_H

#include "object.h"

// Mqueue Data
Class(Mail)
{
	u32 code;
	void *data;
};

// Mqueue Type
Class(Mqueue)
{
	str name;
	u32 capacity;
	void *mqueue_core;
};

// Mqueue Import Function Setup
s32 KNL_Mqueue_Setup(Mqueue *thiz);
s32 KNL_Mqueue_Reset(Mqueue *thiz);
// Mqueue Import Function Useage
s32 KNL_Mqueue_Send(Mqueue *thiz, Mail *mail, u32 timeout);
s32 KNL_Mqueue_Urgent(Mqueue *thiz, Mail *mail, u32 timeout);
s32 KNL_Mqueue_Receive(Mqueue *thiz, Mail *mail, u32 timeout);

// Mqueue Function Construct
s32 HAL_Mqueue_Construct(Mqueue *thiz);
s32 HAL_Mqueue_Destruct(Mqueue *thiz);
// Mqueue Function Setup
#define HAL_Mqueue_Setup   KNL_Mqueue_Setup
#define HAL_Mqueue_Reset   KNL_Mqueue_Reset
// Mqueue Function Useage
#define HAL_Mqueue_Send	   KNL_Mqueue_Send
#define HAL_Mqueue_Urgent  KNL_Mqueue_Urgent
#define HAL_Mqueue_Receive KNL_Mqueue_Receive

#endif //_HAL_MQUEUE_H
