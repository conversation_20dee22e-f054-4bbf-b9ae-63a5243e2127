//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_fskits.h
// Author		: XuCF
// Created On	: 2023/09/21
// Description	: hal_fskits.h
//
// History
// 1. V1.0, Created by XuCF. 2023/09/21
//=============================================================================

#ifndef _HAL_FSKITS_H
#define _HAL_FSKITS_H

#include "object.h"
#include "hal/hal_device.h"

// Code head Top
// Code head Bot

ClassDeclare(MdFile);
ClassDeclare(MdFileSys);

// Code structs >>
// Code structs <<

// [MdFile] <= [] <- []
ClassRealize(MdFile)
{
	VTable super;
	// Code MdFile-class >>
	// Code MdFile-class <<
};
// [MdFileSys] <= [Device] <- []
ClassRealize(MdFileSys)
{
	Device super;
	// Code MdFileSys-class >>
	// Code MdFileSys-class <<
};

// MdFile funcs
s32 HAL_MdFile_Size(MdFile *thiz);
s32 HAL_MdFile_Write(MdFile *thiz, any data, u32 size);
s32 HAL_MdFile_Read(MdFile *thiz, any data, u32 size);
s32 HAL_MdFile_Flush(MdFile *thiz);
s32 HAL_MdFile_Close(MdFile *thiz);
// MdFileSys funcs
MdFile *HAL_MdFileSys_Open(MdFileSys *thiz, str path, str mode);
s32 HAL_MdFileSys_Delete(MdFileSys *thiz, str path);

// Code tail Top
// Code tail Bot

#endif // _HAL_FSKITS_H
