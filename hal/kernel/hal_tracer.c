//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_tracer.c
// Author		: XuCF
// Created On	: 2023/11/22
// Description	: hal_tracer.c
//
// History
// 1. V1.0, Created by XuCF. 2023/11/22
//=============================================================================

#include "kernel/hal_tracer.h"
#include "libcustom.h"

void HAL_Tracer_Init(unsigned int vol)
{
	unsigned char tracer_mask_bytes[TRACER_MASK_BYTES];
	clike_memset(tracer_mask_bytes, 0xff, sizeof(tracer_mask_bytes));
	tracer_init(vol);
	tracer_set_mask(tracer_mask_bytes);
}

void HAL_Tracer_Output(tracer_outputer_t outputer)
{
	int size = 0;
	unsigned char mask[TRACER_MASK_BYTES] = {0}, backup[TRACER_MASK_BYTES];
	char buffer[TRACER_LOG_MAX_LEN];
	// backup and clean mask
	tracer_get_mask(backup);
	tracer_set_mask(mask);
	// do output
	while (1)
	{
		size = tracer_read(buffer, TRACER_LOG_MAX_LEN);
		if (size <= 0) break;
		outputer(buffer, size);
	}
	// restore mask
	tracer_set_mask(backup);
}
