//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_fskits.c
// Author		: XuCF
// Created On	: 2023/09/21
// Description	: hal_fskits.c
//
// History
// 1. V1.0, Created by XuCF. 2023/09/21
//=============================================================================

// Code impo >>
// Code impo <<

#include "object.h"
#include "hal/kernel/hal_fskits.h"

// Code head Top
// Code head Bot

s32 HAL_MdFile_Size(MdFile *thiz)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdFile *))(((VTable *)thiz)->__vtb__)(ApiSize))(thiz);
}

s32 HAL_MdFile_Write(MdFile *thiz, any data, u32 size)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdFile *, any, u32))(((VTable *)thiz)->__vtb__)(ApiWrite))(thiz, data, size);
}

s32 HAL_MdFile_Read(MdFile *thiz, any data, u32 size)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdFile *, any, u32))(((VTable *)thiz)->__vtb__)(ApiRead))(thiz, data, size);
}

s32 HAL_MdFile_Flush(MdFile *thiz)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdFile *))(((VTable *)thiz)->__vtb__)(ApiFlush))(thiz);
}

s32 HAL_MdFile_Close(MdFile *thiz)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdFile *))(((VTable *)thiz)->__vtb__)(ApiClose))(thiz);
}

MdFile *HAL_MdFileSys_Open(MdFileSys *thiz, str path, str mode)
{
	// Find virtual func and invoke it
	return ((MdFile *(*)(MdFileSys *, str, str))(((VTable *)thiz)->__vtb__)(ApiOpen))(thiz, path, mode);
}

s32 HAL_MdFileSys_Delete(MdFileSys *thiz, str path)
{
	// Find virtual func and invoke it
	return ((s32(*)(MdFileSys *, str))(((VTable *)thiz)->__vtb__)(ApiDelete))(thiz, path);
}

// Code static Top
// Code static Bot

// Code tail >>
// Code tail <<
