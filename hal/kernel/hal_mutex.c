//=============================================================================
// Copyright (C), 2004-2021, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_mutex.c
// Author		: XuCF
// Created On	: 2021/10/15
// Description	: hal_mutex.c
//
// History
// 1. V1.0, Created by XuCF. 2021/10/15
//=============================================================================

#include "hal_mutex.h"

Weak s32 HAL_Mutex_Construct(Mutex *thiz, str name)
{
    return MACR_NONE;
}
Weak s32 HAL_Mutex_Destruct(Mutex *thiz)
{
    return MACR_NONE;
}
Weak s32 HAL_Mutex_Lock(Mutex *thiz)
{
    return MACR_NONE;
}
Weak s32 HAL_Mutex_Unlock(Mutex *thiz)
{
    return MACR_NONE;
}
Weak u8 *HAL_Mutex_Name(Mutex *thiz)
{
    return (u8 *)"unknown mutex";
}
Weak s32 HAL_Mutex_TryLock_Callback_Attr(fun_mutex_callback callback_fun)
{
    return MACR_NONE;
}
Weak s32 HAL_Mutex_Locked_Callback_Attr(fun_mutex_callback callback_fun)
{
    return MACR_NONE;
}
Weak s32 HAL_Mutex_Unlock_Callback_Attr(fun_mutex_callback callback_fun)
{
    return MACR_NONE;
}

