//=============================================================================
// Copyright (C), 2004-2020, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: sys_mqueue.c
// Author		: XuCF
// Created On	: 2020/10/16
// Description	: sys_mqueue.c
//
// History
// 1. V1.0, Created by XuCF. 2020/10/16
//=============================================================================

#include "hal_mqueue.h"

// Mqueue Construct
s32 HAL_Mqueue_Construct(Mqueue *thiz)
{
	s32 result = MACR_NONE;
	thiz->name = "Unnamed";
	thiz->capacity = 0;
	thiz->mqueue_core = MACR_NULL;
	return result;
}
s32 HAL_Mqueue_Destruct(Mqueue *thiz)
{
	s32 result = MACR_NONE;
	thiz->name = "Unnamed";
	thiz->capacity = 0;
	thiz->mqueue_core = MACR_NULL;
	return result;
}
