//=============================================================================
// Copyright (C), 2004-2020, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_debug.c
// Author		: XuCF
// Created On	: 2020/10/27
// Description	: hal_debug.c
//
// History
// 1. V1.0, Created by XuCF. 2020/10/27
//=============================================================================

#include "hal_debug.h"

#define LOGD(...)

void Debug_SendHex(u8 *data, s32 len)
{
	for (s32 i = 0; i < len; i++)
	{
		LOGD("%02X ", data[i]);
	}
	LOGD("\r\n");
}
