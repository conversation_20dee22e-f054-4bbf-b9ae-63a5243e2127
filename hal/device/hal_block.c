//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_block.c
// Author		: XuCF
// Created On	: 2023/07/31
// Description	: hal_block.c
//
// History
// 1. V1.0, Created by XuCF. 2023/07/31
//=============================================================================

// Code impo >>
// Code impo <<

#include "object.h"
#include "hal/device/hal_block.h"

// Code head >>
// Code head <<

s32 HAL_Block_Seek(Block *thiz, u32 addr)
{
	// Find virtual func and invoke it
	return ((s32(*)(Block *, u32))(((VTable *)thiz)->__vtb__)(ApiSeek))(thiz, addr);
}

s32 HAL_Block_Write(Block *thiz, any data, u32 size)
{
	// Find virtual func and invoke it
	return ((s32(*)(Block *, any, u32))(((VTable *)thiz)->__vtb__)(ApiWrite))(thiz, data, size);
}

s32 HAL_Block_Read(Block *thiz, any data, u32 size)
{
	// Find virtual func and invoke it
	return ((s32(*)(Block *, any, u32))(((VTable *)thiz)->__vtb__)(ApiRead))(thiz, data, size);
}

s32 HAL_Block_Wipe(Block *thiz, u32 addr, u32 size)
{
	// Find virtual func and invoke it
	return ((s32(*)(Block *, u32, u32))(((VTable *)thiz)->__vtb__)(ApiWipe))(thiz, addr, size);
}

s32 HAL_Block_Dump(Block *thiz, BlockInfo *info)
{
	// Find virtual func and invoke it
	return ((s32(*)(Block *, BlockInfo *))(((VTable *)thiz)->__vtb__)(ApiDump))(thiz, info);
}

s32 HAL_Block_Free(Block *thiz)
{
	// Find virtual func and invoke it
	return ((s32(*)(Block *))(((VTable *)thiz)->__vtb__)(ApiFree))(thiz);
}

// Code static >>
// Code static <<

// Code tail >>
// Code tail <<
