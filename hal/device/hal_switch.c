//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_switch.c
// Author		: XuCF
// Created On	: 2024/07/15
// Description	: hal_switch.c
//
// History
// 1. V1.0, Created by XuCF. 2024/07/15
//=============================================================================

// Code impo >>
// Code impo <<

#include "object.h"
#include "hal/device/hal_switch.h"

// Code head >>
#include "common.h"
// Code head <<

s32 HAL_PinSwitch_On_Impl(PinSwitch *thiz);
s32 HAL_PinSwitch_Off_Impl(PinSwitch *thiz);

static void *__PinSwitch_VTable__(int id)
{
	switch (id)
	{
		case ApiOn: return HAL_PinSwitch_On_Impl;
		case ApiOff: return HAL_PinSwitch_Off_Impl;
		default: return __DefaultImpl__;
	}
}

s32 HAL_Switch_On(Switch *thiz)
{
	// Find virtual func and invoke it
	return ((s32(*)(Switch *))(((VTable *)thiz)->__vtb__)(ApiOn))(thiz);
}

s32 HAL_Switch_Off(Switch *thiz)
{
	// Find virtual func and invoke it
	return ((s32(*)(Switch *))(((VTable *)thiz)->__vtb__)(ApiOff))(thiz);
}

// Code static >>
// Code static <<

s32 HAL_PinSwitch_Init(PinSwitch *thiz, u32 uuid, str name, u32 pin, s32 pola)
{
	// Code HAL_PinSwitch_Init >>
	s32 ret = MACR_NONE;

	Assert(!(thiz), return MACR_ERROR);
	HAL_Device_Init((Device *)thiz, uuid, name);
	VTableBuild(PinSwitch, thiz);
	thiz->pin = HAL_MdPinHelper_Find(pin);
	Assert(!(thiz->pin), return MACR_ERROR)
	thiz->pola = pola;
	thiz->avai = MACR_FALSE;

	return ret;
	// Code HAL_PinSwitch_Init <<
}

s32 HAL_PinSwitch_On_Impl(PinSwitch *thiz)
{
	// Code HAL_PinSwitch_On_Impl >>
	s32 ret = MACR_NONE;

	Assert(!(thiz), return MACR_ERROR);
	Assert(!(thiz->pin), return MACR_ERROR);
	if (!(thiz->avai))
	{
		thiz->avai = MACR_TRUE;
		PinCfgMeta meta = {.attr = MD_PIO_MODE_PP | thiz->pola};
		ret = HAL_MdPin_Load(thiz->pin, &meta);
	}
	else
	{
		ret = HAL_MdPin_Vset(thiz->pin, thiz->pola);
	}

	return ret;
	// Code HAL_PinSwitch_On_Impl <<
}

s32 HAL_PinSwitch_Off_Impl(PinSwitch *thiz)
{
	// Code HAL_PinSwitch_Off_Impl >>
	s32 ret = MACR_NONE;

	Assert(!(thiz), return MACR_ERROR);
	Assert(!(thiz->pin), return MACR_ERROR);
	if (!(thiz->avai))
	{
		thiz->avai = MACR_TRUE;
		PinCfgMeta meta = {.attr = MD_PIO_MODE_PP | !(thiz->pola)};
		ret = HAL_MdPin_Load(thiz->pin, &meta);
	}
	else
	{
		ret = HAL_MdPin_Vset(thiz->pin, !(thiz->pola));
	}

	return ret;
	// Code HAL_PinSwitch_Off_Impl <<
}

// Code tail >>
// Code tail <<
