//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_switch.h
// Author		: XuCF
// Created On	: 2024/07/15
// Description	: hal_switch.h
//
// History
// 1. V1.0, Created by XuCF. 2024/07/15
//=============================================================================

#ifndef _HAL_SWITCH_H
#define _HAL_SWITCH_H

#include "object.h"
#include "hal/hal_device.h"

// Code head >>
#include "hal_pin.h"
// Code head <<

ClassDeclare(Switch);
ClassDeclare(PinSwitch);

// Code structs >>
// Code structs <<

// [Switch] <= [Device] <- []
ClassRealize(Switch)
{
	Device super;
	// Code Switch-class >>
	// Code Switch-class <<
};
// [PinSwitch] <= [Switch] <- []
ClassRealize(PinSwitch)
{
	Switch super;
	// Code PinSwitch-class >>
	MdPin *pin;
	s32 pola;
	s32 avai;
	// Code PinSwitch-class <<
};

// Switch funcs
s32 HAL_Switch_On(Switch *thiz);
s32 HAL_Switch_Off(Switch *thiz);
// PinSwitch funcs
s32 HAL_PinSwitch_Init(PinSwitch *thiz, u32 uuid, str name, u32 pin, s32 pola);

// Code tail >>
// Code tail <<

#endif // _HAL_SWITCH_H
