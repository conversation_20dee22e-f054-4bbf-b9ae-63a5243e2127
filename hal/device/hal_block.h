//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_block.h
// Author		: XuCF
// Created On	: 2023/07/31
// Description	: hal_block.h
//
// History
// 1. V1.0, Created by XuCF. 2023/07/31
//=============================================================================

#ifndef _HAL_BLOCK_H
#define _HAL_BLOCK_H

#include "object.h"
#include "hal/hal_device.h"

// Code head Top
// Code head Bot

ClassDeclare(Block);

// Code structs >>
Class(BlockInfo)
{
	u32 page_size;
	u32 page_numb;
};
// Code structs <<

// [Block] <= [Device] <- []
ClassRealize(Block)
{
	Device super;
	// Code Block-class >>
	// Code Block-class <<
};

// Block funcs
s32 HAL_Block_Seek(Block *thiz, u32 addr);
s32 HAL_Block_Write(Block *thiz, any data, u32 size);
s32 HAL_Block_Read(Block *thiz, any data, u32 size);
s32 HAL_Block_Wipe(Block *thiz, u32 addr, u32 size);
s32 HAL_Block_Dump(Block *thiz, BlockInfo *info);
s32 HAL_Block_Free(Block *thiz);

// Code tail Top
// Code tail Bot

#endif // _HAL_BLOCK_H
