//=============================================================================
// Copyright (C), 2004-2020, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: dev_beeper.h
// Author		: XuCF
// Created On	: 2020/10/19
// Description	: dev_beeper.h
//
// History
// 1. V1.0, Created by XuCF. 2020/10/19
//=============================================================================

#ifndef _HAL_BEEPER_H
#define _HAL_BEEPER_H

#include "driver/hal_pwm.h"
#include "object.h"

Class(Beeper)
{
	// Structure
	MdPwm *pwm;
	// Attributes
	u32 pwm_mode;
	// Status
	u32 tone;
	u32 volume;
};

s32 HAL_Beeper_Construct(Beeper *thiz, u32 pwm_id, u32 pwm_mode);
s32 HAL_Beeper_Init(Beeper *thiz);
s32 HAL_Beeper_Set(Beeper *thiz, u32 tone, u32 volume);
s32 HAL_Beeper_ON(Beeper *thiz);
s32 HAL_Beeper_OFF(Beeper *thiz);

#endif //_HAL_BEEPER_H
