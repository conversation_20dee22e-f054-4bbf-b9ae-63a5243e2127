//=============================================================================
// Copyright (C), 2004-2021, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_video.c
// Author		: XuCF
// Created On	: 2021/09/10
// Description	: hal_video.c
//
// History
// 1. V1.0, Created by XuCF. 2021/09/10
//=============================================================================

#include "hal_video.h"

Weak s32 HAL_Video_Construct(Video *thiz, VideoIniList *ini)
{
    thiz->call = ini->call;
    thiz->stage = VideoSta_PowerOff;
    return MACR_NONE;
}

Weak s32 HAL_Video_Destruct(Video *thiz)
{
	s32 result = MACR_NONE;
	return result;
}

Weak u32 HAL_Video_Probe(Video *thiz)
{
    return MACR_VIDEO_CH1;
}

Weak s32 HAL_Video_GetFormat(Video *thiz)
{
    return MACR_NONE;
}

Weak s32 HAL_Video_GetVersion(Video *thiz, str *ver_lib, str *ver_svr, u32 *ver_drv)
{
    return MACR_NONE;
}

