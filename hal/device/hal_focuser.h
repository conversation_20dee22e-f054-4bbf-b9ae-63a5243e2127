//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_focuser.h
// Author		: XuCF
// Created On	: 2025/03/13
// Description	: hal_focuser.h
//
// History
// 1. V1.0, Created by XuCF. 2025/03/13
//=============================================================================

#ifndef _HAL_FOCUSER_H
#define _HAL_FOCUSER_H

#include "libcustom.h"

ClassDeclare(MyFocuser);
int MyFocuser_Ctrl(MyFocuser *thiz, int on);
int MyFocuser_Vset(MyFocuser *thiz, int voltage_mv);
int MyFocuser_Vget(MyFocuser *thiz);
int MyFocuser_Vchk(MyFocuser *thiz);
int MyFocuser_Rset(MyFocuser *thiz, int dac_value);
int MyFocuser_Rget(MyFocuser *thiz);
int MyFocuser_Tget(MyFocuser *thiz);
int MyFocuser_Chip(MyFocuser *thiz);

#endif //_HAL_FOCUSER_H
