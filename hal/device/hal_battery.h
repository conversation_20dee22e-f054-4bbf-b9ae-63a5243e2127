//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_battery.h
// Author		: XuCF
// Created On	: 2024/03/13
// Description	: hal_battery.h
//
// History
// 1. V1.0, Created by XuCF. 2024/03/13
//=============================================================================

#ifndef _HAL_BATTERY_H
#define _HAL_BATTERY_H

#include "object.h"
#include "hal/hal_device.h"

// Code head >>
// Code head <<

ClassDeclare(Battery);

// Code structs >>
// Code structs <<

// [Battery] <= [Device] <- []
ClassRealize(Battery)
{
	Device super;
	// Code Battery-class >>
	// Code Battery-class <<
};

// Battery funcs
s32 HAL_Battery_Read(Battery *thiz);

// Code tail >>
// Code tail <<

#endif // _HAL_BATTERY_H
