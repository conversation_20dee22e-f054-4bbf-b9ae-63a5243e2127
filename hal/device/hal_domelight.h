//=============================================================================
// Copyright (C), 2004-2025, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_domelight.h
// Author		: DongDL
// Created On	: 2025/01/22
// Description	: hal_domelight.h
//
// History
// 1. V1.0, Created by DongDL. 2025/01/22
//=============================================================================

#ifndef _HAL_DOMELIGHT_H
#define _HAL_DOMELIGHT_H

#include "libcustom.h"
#include "hal_pin.h"

ClassDeclare(DmLt);
ClassDeclare(DmLtPin);

ClassRealize(DmLtPin)
{
	union
	{
		u32 id;
		MdPin *pin;
	};
	byt pola;
};

ClassRealize(DmLt)
{
	Device super;
	s32 iic[2];
	u32 color; // byt[24:16] red brightness; byt[15:8] blue brightness; byt[8:0] green brightness;
	DmLtPin *strobe;
	u32 strobe_cnt;
	DmLtPin *tx;
	u32 tx_cnt;
};

void HAL_Domelight_Init(DmLtPin *pins, u32 cnt);
s32 HAL_Domelight_Start(DmLt *thiz);
s32 HAL_Domelight_Load(DmLt *thiz, u32 color);

#endif //_HAL_DOMELIGHT_H
