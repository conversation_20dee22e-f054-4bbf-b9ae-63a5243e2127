//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_speaker.h
// Author		: XuCF
// Created On	: 2024/12/12
// Description	: hal_speaker.h
//
// History
// 1. V1.0, Created by XuCF. 2024/12/12
//=============================================================================

#ifndef _HAL_SPEAKER_H
#define _HAL_SPEAKER_H

#include "libcustom.h"

ClassDeclare(Speaker);

int HAL_Speaker_Load(Speaker *thiz, str path);
int HAL_Speaker_Play(Speaker *thiz, u32 *voice, u32 num);
int HAL_Speaker_Conf(Speaker *thiz, u32 volume);
int HAL_Speaker_Left(Speaker *thiz);
int HAL_Speaker_Free(Speaker *thiz);

#endif //_HAL_SPEAKER_H
