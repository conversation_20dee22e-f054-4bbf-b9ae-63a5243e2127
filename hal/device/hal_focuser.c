//=============================================================================
// Copyright (C), 2004-2024, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_focuser.c
// Author		: XuCF
// Created On	: 2025/03/13
// Description	: hal_focuser.c
//
// History
// 1. V1.0, Created by XuCF. 2025/03/13
//=============================================================================

#include "libcustom.h"
#include "hal_focuser.h"
#include "hal_camera.h"

// I2C address
#define _IIC_ADDR_		  0x0C

// Register addresses
#define _REG_MODE_		  0x00
#define _REG_SPEED_		  0x01
#define _REG_DAC_MSB_	  0x02
#define _REG_DAC_LSB_	  0x03
#define _REG_STATUS_	  0x04
#define _REG_TIMER_MSB_	  0x05
#define _REG_TIMER_LSB_	  0x06
#define _REG_CHIP_ID_	  0x07

// Voltage range
#define _MIN_VOLTAGE_MV_  -10000 // -10V in millivolts
#define _MAX_VOLTAGE_MV_  50000	 // 50V in millivolts

// Return codes
#define _SUCCESS_		  0	 // Return code for success
#define _ERR_UNSTABLE_	  -1 // Error: Cannot write DAC while unstable
#define _ERR_VOLTAGE_OOR_ -2 // Error: Voltage out of range
#define _ERR_TIMEOUT_	  -3 // Error: Operation timed out

ClassRealize(MyFocuser)
{
	CamPort *camera;
	int status;
};

/**
 * Read a byte from the specified register
 * @param thiz The MyFocuser instance
 * @param addr Register address to read from
 * @return Value read from the register
 */
static byt _read_byte_(MyFocuser *thiz, byt addr)
{
	byt buffer[2] = {addr, 0};
	// Write 1 byte register address and read 1 byte register value
	// info[31:25]: Slave address
	// info[24]: I2C read flag
	// info[15:8]: Address length (1 byte)
	// info[7:0]: Data length (1 byte)
	HAL_CamPort_Xfer(thiz->camera, buffer, 0x01 | (0x01 << 8) | (1 << 24) | (_IIC_ADDR_ << 25));
	return buffer[1];
}

/**
 * Write a byte to the specified register
 * @param thiz The MyFocuser instance
 * @param addr Register address to write to
 * @param value Value to write to the register
 */
static void _write_byte_(MyFocuser *thiz, byt addr, byt value)
{
	byt buffer[2] = {addr, value};
	// Write 2 bytes: 1 byte register address + 1 byte register value
	// info[31:25]: Slave address
	// info[24]: I2C write flag (0)
	// info[15:8]: Address length (1 byte)
	// info[7:0]: Data length (1 byte)
	HAL_CamPort_Xfer(thiz->camera, buffer, 0x01 | (0x01 << 8) | (0 << 24) | (_IIC_ADDR_ << 25));
}

/**
 * Read a 16-bit value from consecutive registers
 * @param thiz The MyFocuser instance
 * @param addr Address of the MSB register
 * @return 16-bit value
 */
static unsigned short _read_short_(MyFocuser *thiz, byt addr)
{
	byt buffer[3] = {addr, 0, 0};
	// Write 1 byte register address and read 2 bytes
	// info[31:25]: Slave address
	// info[24]: I2C read flag
	// info[15:8]: Address length (1 byte)
	// info[7:0]: Data length (2 bytes)
	HAL_CamPort_Xfer(thiz->camera, buffer, 0x01 | (0x02 << 8) | (1 << 24) | (_IIC_ADDR_ << 25));

	// Combine into 16-bit value
	return (buffer[1] << 8) | buffer[2];
}

/**
 * Write a 16-bit value to consecutive registers
 * @param thiz The MyFocuser instance
 * @param addr Address of the MSB register
 * @param value 16-bit value to write
 */
static void _write_short_(MyFocuser *thiz, byt addr, unsigned short value)
{
	byt buffer[3] = {addr, (value >> 8) & 0xFF, value & 0xFF};
	// Write 3 bytes: 1 byte register address + 2 byte value
	// info[31:25]: Slave address
	// info[24]: I2C write flag (0)
	// info[15:8]: Address length (1 byte)
	// info[7:0]: Data length (2 bytes)
	HAL_CamPort_Xfer(thiz->camera, buffer, 0x01 | (0x02 << 8) | (0 << 24) | (_IIC_ADDR_ << 25));
}

/**
 * Read the current timer value
 * @param thiz The MyFocuser instance
 * @return 16-bit timer value
 * 
 * Note: Reading while timer is counting may give invalid results.
 * The timer counts when VOUT_LOCK is LOW (during voltage transitions).
 */
static unsigned int _read_timer_(MyFocuser *thiz)
{
	// Use _read_short_ to read the 16-bit timer value
	return _read_short_(thiz, _REG_TIMER_MSB_);
}

/**
 * Reset the timer to 0
 * @param thiz The MyFocuser instance
 * 
 * Note: Timer needs to be reset before a new DAC command
 * to measure the transition time correctly.
 */
static void _reset_timer_(MyFocuser *thiz)
{
	// Use _write_short_ to reset the 16-bit timer value to 0
	_write_short_(thiz, _REG_TIMER_MSB_, 0x0000);
}

/**
 * Check if output voltage is locked
 * @param thiz The MyFocuser instance
 * @return 1 if locked, 0 if in transition
 */
static int _is_stable_(MyFocuser *thiz)
{
	return (_read_byte_(thiz, _REG_STATUS_) & 0x01) ? 1 : 0;
}

/**
 * Read the current DAC value
 * @param thiz The MyFocuser instance
 * @return The current DAC value (signed integer)
 * 
 * Note: Returns the current DAC setting with proper sign
 * based on the polarity bit.
 */
static int _read_dac_(MyFocuser *thiz)
{
	// Read the 16-bit DAC value
	unsigned short dac_value = _read_short_(thiz, _REG_DAC_MSB_);
	// Check polarity bit (MSB)
	if (dac_value & 0x8000)
	{
		// Positive polarity (OutP >= OutN)
		return dac_value & 0x03FF; // Return 10-bit value
	}
	else
	{
		// Negative polarity (OutP <= OutN)
		return -((int)(dac_value & 0x03FF)); // Return negative 10-bit value
	}
}

/**
 * Write a value to the DAC
 * @param thiz The MyFocuser instance
 * @param value The DAC value to write (can be negative)
 * @return 1 if successful, 0 if output is not locked
 */
static int _write_dac_(MyFocuser *thiz, int value)
{
	// Check if output is locked (stable)
	if (!_is_stable_(thiz))
	{
		LOGE("Can't write DAC while output is not locked (unstable)");
		return _ERR_UNSTABLE_;
	}
	// Reset timer before new DAC command
	_reset_timer_(thiz);
	// Get absolute value and set polarity
	unsigned short dac_value;
	if (value < 0)
	{
		// Negative polarity (OutP <= OutN)
		dac_value = (-value) & 0x03FF; // 10-bit value
	}
	else
	{
		// Positive polarity (OutP >= OutN)
		dac_value = (value & 0x03FF) | 0x8000; // Set MSB for positive polarity
	}
	// Write to DAC registers
	_write_short_(thiz, _REG_DAC_MSB_, dac_value);
	return _SUCCESS_;
}

/**
 * Get the current lens position voltage
 * @param thiz The MyFocuser instance
 * @return Current voltage in millivolts
 * 
 * Note: Converts the current DAC value to voltage in millivolts.
 * The conversion uses the same scale factor as _set_voltage_.
 */
static int _get_voltage_(MyFocuser *thiz)
{
	// DAC LSB step is 55mV (middle value of 53-57mV range)
	const int VLSB = 55;
	// Get the current DAC value with proper sign
	int dac_value = _read_dac_(thiz);
	// Convert DAC value to voltage in millivolts
	int voltage_mv = dac_value * VLSB;
	return voltage_mv;
}

/**
 * Set lens position by voltage
 * @param thiz The MyFocuser instance
 * @param voltage_mv Voltage to set in millivolts (can be negative)
 * @return 0 if successful, negative value if failed
 */
static int _set_voltage_(MyFocuser *thiz, int voltage_mv)
{
	// DAC LSB step is 55mV (middle value of 53-57mV range)
	const int VLSB = 55;
	// Check voltage range
	if (voltage_mv > _MAX_VOLTAGE_MV_)
	{
		LOGE("Voltage %d mV too high, maximum is %d mV", voltage_mv, _MAX_VOLTAGE_MV_);
		voltage_mv = _MAX_VOLTAGE_MV_;
		return _ERR_VOLTAGE_OOR_;
	}
	else if (voltage_mv < _MIN_VOLTAGE_MV_)
	{
		LOGE("Voltage %d mV too low, minimum is %d mV", voltage_mv, _MIN_VOLTAGE_MV_);
		voltage_mv = _MIN_VOLTAGE_MV_;
		return _ERR_VOLTAGE_OOR_;
	}
	// Convert voltage to DAC value
	int dac_value = voltage_mv / VLSB;
	// Use DAC interface to set value
	return _write_dac_(thiz, dac_value);
}

/**
 * Clean error flags
 * @param thiz The MyFocuser instance
 */
static void _clean_err_(MyFocuser *thiz)
{
	// Write 0 to STATUS register to reset DAC_ERRLTCH latch
	_write_byte_(thiz, _REG_STATUS_, 0x00);
}

/**
 * Set the speed of the lens movement
 * @param thiz The MyFocuser instance
 * @param sink_strength Current sink strength (0-3, 3=fastest)
 * @param settling_speed Charge pump and control speed (0-15, 15=fastest)
 */
static void _set_speed_(MyFocuser *thiz, int sink_strength, int settling_speed)
{
	// Ensure values are in valid range
	sink_strength = sink_strength & 0x03;	// 2 bits
	settling_speed = settling_speed & 0x0F; // 4 bits
	// Combine into SPEED register value
	byt speed = (sink_strength << 4) | settling_speed;
	_write_byte_(thiz, _REG_SPEED_, speed);
}

/**
 * Enable the lens driver with timer configuration
 * @param thiz The MyFocuser instance
 * @param free_run 1 to enable free-running mode, 0 for transition timer mode
 * @param div16 1 to count every 16 OSC cycles, 0 to count every OSC cycle
 * @return 0 if successful
 */
static int _enable_(MyFocuser *thiz, int free_run, int div16)
{
	// Reset DAC to default
	_write_short_(thiz, _REG_DAC_MSB_, 0x0000);
	// Using default speed: medium sink strength (2) and settling speed (8)
	_set_speed_(thiz, 2, 8);
	// Clean any errors
	_clean_err_(thiz);
	// Prepare mode with timer configuration and ENABLE=1
	byt mode = 0x01; // ENABLE=1
	// Update TN_ENA bit (bit 3)
	if (free_run) mode |= 0x08; // Set TN_ENA bit for free-run mode
	// Update TM_DIV16 bit (bit 2)
	if (div16) mode |= 0x04; // Set TM_DIV16 bit to count every 16 OSC cycles
	// Write mode with timer config and enable bit
	_write_byte_(thiz, _REG_MODE_, mode);
	// Wait for voltage to stabilize
	for (int i = 0; i < 100; i++)
	{
		if (_is_stable_(thiz)) break;
		LOGI("Waiting for voltage to stabilize");
		clike_delay_ms(1);
	}
	return _SUCCESS_;
}

/**
 * Disable the lens driver
 * @param thiz The MyFocuser instance
 * @return 0 if successful
 */
static int _disable_(MyFocuser *thiz)
{
	// Simply set ENABLE bit to 0 (standby mode)
	_write_byte_(thiz, _REG_MODE_, 0);
	return _SUCCESS_;
}

any MyFocuser_New(any camera)
{
	MyFocuser *thiz = clike_new(MyFocuser);
	thiz->camera = camera;
	thiz->status = 0;
	return thiz;
}

non MyFocuser_Del(any thiz)
{
	clike_free(thiz);
}

int MyFocuser_Ctrl(MyFocuser *thiz, int on)
{
	if (on) // enable
	{
		if (thiz->status != 0) return -1;
		thiz->status = 1;
		LOGI("Chip id = %x", _read_byte_(thiz, _REG_CHIP_ID_));
		return _enable_(thiz, 0, 0);
	}
	else // disable
	{
		if (thiz->status != 1) return -1;
		thiz->status = 0;
		return _disable_(thiz);
	}
}

int MyFocuser_Vset(MyFocuser *thiz, int voltage_mv)
{
	return _set_voltage_(thiz, voltage_mv);
}

int MyFocuser_Vget(MyFocuser *thiz)
{
	return _get_voltage_(thiz);
}

int MyFocuser_Vchk(MyFocuser *thiz)
{
	return _is_stable_(thiz);
}

int MyFocuser_Rset(MyFocuser *thiz, int dac_value)
{
	return _write_dac_(thiz, dac_value);
}

int MyFocuser_Rget(MyFocuser *thiz)
{
	return _read_dac_(thiz);
}

int MyFocuser_Tget(MyFocuser *thiz)
{
	return _read_timer_(thiz);
}

int MyFocuser_Chip(MyFocuser *thiz)
{
	return _read_byte_(thiz, _REG_CHIP_ID_);
}
