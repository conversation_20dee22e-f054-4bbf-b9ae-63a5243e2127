//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_camera_helper.c
// Author		: XuCF
// Created On	: 2023/05/19
// Description	: hal_camera_helper.c
//
// History
// 1. V1.0, Created by XuCF. 2023/05/19
//=============================================================================

#include "device/hal_camera_helper.h"

s32 HAL_CamHelper_Flash(Camera *thiz, CamHelperFlashMeta *meta)
{
	CamTimMeta tmeta;
	tmeta.seq_mode = meta->seq_mode;
	tmeta.aim_mode = meta->aim_mode;
	tmeta.lum_mode = meta->lumin_mode;
	tmeta.irf_mode = meta->lumir_mode;
	tmeta.dlt_mode = meta->lumdl_mode;
	tmeta.cycl_us = meta->expo_us + meta->wait_us + meta->dark_us + meta->wait_off_us;
	tmeta.glow_us = meta->expo_us + meta->wait_us;
	tmeta.ldly_us = meta->wait_us;
	tmeta.ddly_us = meta->wait_off_us;
	tmeta.cap_cycl = meta->trig_lumin_cnt;
	tmeta.aim_line = meta->aim_line = 0;
	tmeta.lum1_level = meta->lum1_level;
	tmeta.lum2_level = meta->lum2_level;
	HAL_Camera_Load(thiz, (CamMeta *)&tmeta, MD_CAMERA_META_LED);
	return 0;
}

u32 HAL_CamHelper_SetExpo(Camera *thiz, u32 expo_us)
{
	CamImgMeta meta;
	meta.cfgs = MD_CAMERA_CFG_EXPO;
	meta.expo = expo_us;
	HAL_Camera_Load(thiz, (CamMeta *)&meta, MD_CAMERA_META_IMG);
	return (u32)meta.expo;
}

u32 HAL_CamHelper_GetExpo(Camera *thiz)
{
	CamImgMeta meta;
	meta.cfgs = MD_CAMERA_CFG_EXPO;
	meta.expo = 0;
	HAL_Camera_Dump(thiz, (CamMeta *)&meta, MD_CAMERA_META_IMG);
	return (u32)meta.expo;
}

u32 HAL_CamHelper_SetGain(Camera *thiz, u32 gain)
{
	CamImgMeta meta;
	meta.cfgs = MD_CAMERA_CFG_GAIN;
	meta.gain = gain;
	HAL_Camera_Load(thiz, (CamMeta *)&meta, MD_CAMERA_META_IMG);
	return (u32)meta.gain;
}

u32 HAL_CamHelper_GetGain(Camera *thiz)
{
	CamImgMeta meta;
	meta.cfgs = MD_CAMERA_CFG_GAIN;
	meta.gain = 0;
	HAL_Camera_Dump(thiz, (CamMeta *)&meta, MD_CAMERA_META_IMG);
	return (u32)meta.gain;
}
