//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_battery.c
// Author		: XuCF
// Created On	: 2024/03/13
// Description	: hal_battery.c
//
// History
// 1. V1.0, Created by XuCF. 2024/03/13
//=============================================================================

// Code impo >>
// Code impo <<

#include "object.h"
#include "hal/device/hal_battery.h"

// Code head >>
// Code head <<

s32 HAL_Battery_Read(Battery *thiz)
{
	// Find virtual func and invoke it
	return ((s32(*)(Battery *))(((VTable *)thiz)->__vtb__)(ApiRead))(thiz);
}

// Code static >>
// Code static <<

// Code tail >>
// Code tail <<
