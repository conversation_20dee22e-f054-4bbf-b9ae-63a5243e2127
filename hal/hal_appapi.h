//=============================================================================
// Copyright (C), 2004-2021, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_appapi.h
// Author		: XuCF
// Created On	: 2021/06/16
// Description	: hal_appapi.h
//
// History
// 1. V1.0, Created by XuCF. 2021/06/16
//=============================================================================

#ifndef _HAL_APPAPI_H
#define _HAL_APPAPI_H

#ifdef __cplusplus
extern "C"
{
#endif

#include "common.h"
#include "hal_basic.h"

// Driver
#include "driver/hal_pwm.h"
#include "driver/hal_uart.h"
#include "driver/hal_usb.h"
#include "driver/hal_wdt.h"

// Device
#include "device/hal_beeper.h"
#include "device/hal_camera.h"
#include "device/hal_lumin.h"
#include "device/hal_power.h"
#include "device/hal_video.h"

// Kernel
#include "kernel/hal_events.h"
#include "kernel/hal_mqueue.h"
#include "kernel/hal_mutex.h"
#include "kernel/hal_thread.h"
#include "kernel/hal_ticker.h"

#ifdef __cplusplus
}
#endif

#endif //_HAL_APPAPI_H
