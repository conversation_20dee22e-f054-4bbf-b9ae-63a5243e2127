//================================================================
//Copyright(C),2020,Minde Electronics Technology Ltd
//File name   : hal_test_io.c
//Author      : ChenJR
//Date        : 2020-06-28
//Description : test IO1 operation at HAL level
//History     : 1. V1.0, Created by ChenJR 2020-06-28
//				2. V1.1, Updated by ChenJR 2020-07-22
//=================================================================

/* Headers */
#include "hal_test_io.h"

/* Functions */
/*************************************************************
 * Function		:	hal_test_io1_init
 * Description	:	Set Test IO1 as Output for indication
 * Input		:	None
 * Output		:	None
 * Return		:	None
 * History		:	1. V1.0 Created by unknown 2020-01-16
 * 					2. V1.1 Updated by <PERSON><PERSON>R  2020-06-28
 * 					3. V1.2 Updated by ChenJR  2020-07-22
**************************************************************/
void hal_test_io1_init(void)
{
#if (MACR_HW_TEST_IO1 == 1)
	drv_gpio_cfg(&dev_gpio_tck_test_io1_indicate);
#else
#endif
}

/*************************************************************
 * Function		:	hal_test_io1_h
 * Description	:	Set Test IO1 as HIGH
 * Input		:	None
 * Output		:	None
 * Return		:	None
 * History		:	1. V1.0 Created by unknown 2020-01-16
 * 					2. V1.1 Updated by ChenJR  2020-06-28
 * 					3. V1.2 Updated by ChenJR  2020-07-22
**************************************************************/
void hal_test_io1_h(void)
{
#if (MACR_HW_TEST_IO1 == 1)
	drv_gpio_set_level(&dev_gpio_tck_test_io1_indicate, MACR_PIN_LEVEL_HIGH);
#else
#endif
}

/*************************************************************
 * Function		:	hal_test_io1_l
 * Description	:	Set Test IO1 as LOW
 * Input		:	None
 * Output		:	None
 * Return		:	None
 * History		:	1. V1.0 Created by unknown 2020-01-16
 * 					2. V1.1 Updated by ChenJR  2020-06-28
 * 					3. V1.2 Updated by ChenJR  2020-07-22
**************************************************************/
void hal_test_io1_l(void)
{
#if (MACR_HW_TEST_IO1 == 1)
	drv_gpio_set_level(&dev_gpio_tck_test_io1_indicate, MACR_PIN_LEVEL_LOW);
#else
#endif
}

