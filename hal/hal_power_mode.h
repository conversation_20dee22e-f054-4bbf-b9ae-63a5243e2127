//=============================================================================
// Copyright (C), 2004-2020, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_power_mode.h
// Author		: XuCF
// Created On	: 2020/9/17
// Description	: hal_power_mode
//
// History
// 1. V1.0, Created by XuCF. 2020/9/17
//=============================================================================

#ifndef _HAL_POWER_MODE_H
#define _HAL_POWER_MODE_H

#include "common.h"
#include "product_info.h"

void hal_power_mode_low(void);
void hal_power_mode_normal(void);
u32 hal_power_mode_init_current_limiting(void);
u32 hal_power_mode_enable_current_limiting(void);
u32 hal_power_mode_disable_current_limiting(void);

#endif //_HAL_POWER_MODE_H
