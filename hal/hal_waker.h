//=============================================================================
// Copyright (C), 2004-2020, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_waker.h
// Author		: XuCF
// Created On	: 2021/1/22
// Description	: hal_waker.h
//
// History
// 1. V1.0, Created by XuCF. 2021/1/22
//=============================================================================

#ifndef _HAL_WAKER_H
#define _HAL_WAKER_H

#include "driver/hal_pin.h"

Class(Waker)
{
	MdPin *pin;
	u32 pin_id;
};

s32 Waker_Construct(Waker *thiz, u32 pin_id);
s32 Waker_Setup(Waker *thiz);
s32 Waker_Reset(Waker *thiz);
s32 Waker_Check(Waker *thiz);

#endif //_HAL_WAKER_H