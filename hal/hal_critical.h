//=============================================================================
// Copyright (C), 2004-2020, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_critical.h
// Author		: LiG
// Created On	: 2019/05/22
// Description	: hal_critical.h
//
// History
// 1. V1.0, Created by LiG. 2019/05/22
// 1. V2.0, Created by XuCF. 2020/12/7
//=============================================================================

#ifndef _HAL_CRITICAL_H
#define _HAL_CRITICAL_H

#include "object.h"

void HAL_Critical_Construct(void);
void HAL_Critical_DisableSchedule(void);
void HAL_Critical_EnableSchedule(void);
void HAL_Critical_DisableInterrupt(void);
void HAL_Critical_EnableInterrupt(void);
s32 HAL_Critical_IsCritical(void);
void HAL_Critical_Enter(void);
void HAL_Critical_Exit(void);

#endif //_HAL_CRITICAL_H
