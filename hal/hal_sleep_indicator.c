//================================================================
//Copyright(C),2020,Minde Electronics Technology Ltd
//File name   : hal_sleep_indicator.c
//Author      : ChenJR
//Date        : 2020-06-23
//Description : todo
//History     : 1. V1.0, Created by ChenJR 2020-06-23
//			  : 2. V1.1, Updated by ChenJR 2020-07-22
//=================================================================

/* Headers */
#include "hal_sleep_indicator.h"

/* Functions */
/*************************************************************
 * Function		:	hal_sleep_indicator_on
 * Description	:	Set PWRDWN pin high
 * Input		:	None
 * Output		:	None
 * Return		:	None
 * History		:	1. V1.0, Created by ChenJR 2020-06-23
 * 					2. V1.1, Updated by ChenJR 2020-07-22
**************************************************************/
void hal_sleep_indicator_on(void)
{
#if (MACR_FUNCTION_SLEEP_INDICATION == 1)
	drv_gpio_set_level(&dev_gpio_power_down, MACR_PIN_LEVEL_HIGH);
#endif
}

/*************************************************************
 * Function		:	hal_sleep_indicator_off
 * Description	:	Set PWRDWN pin high
 * Input		:	None
 * Output		:	None
 * Return		:	None
 * History		:	1. V1.0, Created by ChenJR 2020-06-23
 *  				2. V1.1, Updated by ChenJR 2020-07-22
**************************************************************/
void hal_sleep_indicator_off(void)
{
#if (MACR_FUNCTION_SLEEP_INDICATION == 1)
	drv_gpio_set_level(&dev_gpio_power_down, MACR_PIN_LEVEL_LOW);
#endif
}
