//================================================================
//Copyright(C),2020,Minde Electronics Technology Ltd
//File name   : hal_wakeup_pin.h
//Author      : ChenJR
//Date        : 2020-06-23
//Description : wakeup pin operation at HAL level
//History     : 1. V1.0, Created by ChenJR 2020-06-23
//				2. V1.1, Updated by ChenJR 2020-07-18
//=================================================================

#ifndef HAL_WAKEUP_PIN_H_
#define HAL_WAKEUP_PIN_H_

/* Headers */
#include "common.h"
#include "product_info.h"
#include "dev_common.h"

/* Functions */
void hal_wakeup_pin_init(gpio_handler_t* handler);

#endif /* HAL_WAKEUP_PIN_H_ */
