//=============================================================================
// Copyright (C), 2004-2020, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_critical.c
// Author		: LiG
// Created On	: 2019/05/22
// Description	: hal_critical.c
//
// History
// 1. V1.0, Created by LiG. 2019/05/22
// 1. V2.0, Created by XuCF. 2020/12/7
//=============================================================================

#include "hal_critical.h"
#include "hal_basic.h"

Object(Critical)
{
	u32 interrupt_cpsr;
	u32 interrupt_nest;
};

void HAL_Critical_Construct(void)
{
	Critical.interrupt_nest = 0;
	Critical.interrupt_cpsr = 0;
}

void HAL_Critical_DisableSchedule(void)
{
	HAL_Scheduler_Disable();
}

void HAL_Critical_EnableSchedule(void)
{
	HAL_Scheduler_Enable();
}

void HAL_Critical_DisableInterrupt(void)
{
	if (Critical.interrupt_nest == 0)
	{
		HAL_Intterrupt_Disable(&(Critical.interrupt_cpsr));
	}
	Critical.interrupt_nest++;
}

void HAL_Critical_EnableInterrupt(void)
{
	Critical.interrupt_nest--;
	if (Critical.interrupt_nest == 0)
	{
		HAL_Intterrupt_Enable(&(Critical.interrupt_cpsr));
	}
}

s32 HAL_Critical_IsCritical(void)
{
	s32 result = MACR_FALSE;
	
	if (Critical.interrupt_nest > 0)
	{
		result = MACR_TRUE;
	}
	
	return result;
}

void HAL_Critical_Enter(void)
{
	HAL_Critical_DisableInterrupt();
	HAL_Critical_DisableSchedule();
}

void HAL_Critical_Exit(void)
{
	HAL_Critical_EnableSchedule();
	HAL_Critical_EnableInterrupt();
}
