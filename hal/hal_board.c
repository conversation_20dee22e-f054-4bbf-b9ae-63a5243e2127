//=============================================================================
// Copyright (C), 2004-2022, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: hal_board.c
// Author		: XuCF
// Created On	: 2023/08/01
// Description	: hal_board.c
//
// History
// 1. V1.0, Created by XuCF. 2023/08/01
//=============================================================================

#include "hal_board.h"

Weak s32 HAL_Board_Init(void)
{
	s32 err = 0;
	return err;
}

Weak s32 HAL_Board_Info(void *data, u32 type)
{
	s32 err = 0;
	return err;
}

Weak s32 HAL_Board_Mark(void *data, u32 type)
{
	s32 err = 0;
	return err;
}