/*================================================================
//Copyright(C),2018,Minde Electronics Technology Ltd
//File name   : hal_simulation.c
//Author      : MiaoC
//Date        : 2018-07-20
//Description : HAL layer for software simulation interface.
//History     : 1. V1.0, Created by MiaoC 2018-07-20
//=================================================================*/

/*
 *  Head file
 */
#include "hal_simulation.h"


/*
 *  Variable
 */

/*
 * Exported Interface
 */
/*********************************************************************************
*Function:          hal_simulation_output_result
*Description:       Output module software output interface.
*Input:          	None
*Output:         	None
*Return:         	None
*Create Date:	 	2018-07-20
**********************************************************************************/
s32 hal_simulation_output_result(u8* pt_info_addr, u16 info_len, u16 md_barcode_type)
{
	s32 ret_val = MACR_FALSE;
#if (MACR_PLATFORM_ANDROID == 1)
    ret_val = MACR_TRUE;
#endif
    return ret_val;
}

