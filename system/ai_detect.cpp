//==============================================================================
// Copyright (C), 2004-2023, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: ai_detect.cpp
// Author		: TanJW
// Created On	: 2023 April 10, Mon
// Description	: AI Detect Result
//
// History
// 0. V1.0, Created by TanJW, 2023 April 10, Mon
//
//==============================================================================

/*
 * Headers
 */
#include "ai_engine/Common/include/AIEngineCommon.h"
#include "common.h"
#include <cstddef>
#include <stdio.h>
#include <string.h>
#include <vector>
#include "./system/ai_engine/Common/include/AIEngineCore.hpp"
#include "ai_detect_lpr_Validator.hpp"
#include "ai_detect_lpr_Geometry.hpp"

extern "C"{
#include "product_info.h"
#include "ai_detect.h"
#include "system_debug.h"
#include "./algorithm/crc.h"
#include "./system/system_imaging/imaging_chain.h"
#include "./system/system_timer/system_timer.h"
#include "./system/system_config/system_cfg.h"
#include "./common/buff_tool.h"
#include "./common/para_code_macro.h"
#include "./common/para_code_options_macro.h"
#include "./system/system_msg_manager.h"
#include "./system/memory/memory.h"
#include "./file_system/file_system.h"
}

#if (MACR_FUNCTION_AI_DETECT == 1)

using namespace std;

#define MACR_AI_DETE_RESULT_BUFF_SIZE						(MACR_IMAGE_AREA_NUM + 5)		// 多预留 5 个空间
#define MACR_AI_DETE_RESULT_BUFF_INDEX_MAX					(MACR_IMAGE_AREA_NUM - 1)		// 缓冲区空间最大索引值

#define MACR_AI_DETE_RSLT_ROI_NUM_RECORD_BUFF_SIZE_MAX		20								// 暂定 20 个记录
#define MACR_AI_DETE_RSLT_VALID_ROI_SCORE_THRESHEOLD		0.4								// 有效 ROI 置信度阈值

#define MACR_AI_DETE_RSLT_RECORD_BUFF_SIZE					100								// 最多记录100次最近结果

#define MACR_AI_DETE_RSLT_VALID_ROI_SCORE_THRE_MAX			0.40							// 解码有效 ROI 置信度上阈值
#define MACR_AI_DETE_RSLT_VALID_ROI_SCORE_THRE_MIN			0.40							// 解码有效 ROI 置信度下阈值

#define MACR_AI_DETE_NUM_SESSIONS							1				// AI 推理会话数量

#define MACR_AI_DETE_MODEL_STATE_NONE						0				// 未初始化
#define MACR_AI_DETE_MODEL_STATE_INITED						1				// 已完成初始化 (即初始化成功)
#define MACR_AI_DETE_MODEL_STATE_RUNING						2				// 正在运行中

// 分类映射表相关常量定义
#define MAX_MODEL_NAME_LEN									32				// 模型名称最大长度
#define MAX_CLASS_COUNT										256				// 最大类别数量
#define MAX_CLASS_NAME_LEN									64				// 类别名称最大长度
#define MACR_CLS_MAPPING_VERSION_SIZE_MAX					16				// 映射表版本信息大小
#define MACR_CLS_MAPPING_DATA_INFO_SIZE_MAX					8192			// 映射表数据缓冲区大小
#define MACR_CLS_MAPPING_CRC_VAL_INIT						0				// CRC初始值

// 映射表数据结构偏移定义
#define MACR_CLS_MAPPING_DATA_INFO_OFFSET_PacketLen			0				// 数据包长度偏移
#define MACR_CLS_MAPPING_DATA_INFO_OFFSET_Version			2				// 版本信息偏移
#define MACR_CLS_MAPPING_DATA_INFO_OFFSET_ModelName			18				// 模型名称偏移
#define MACR_CLS_MAPPING_DATA_INFO_OFFSET_ClassCount		50				// 类别数量偏移
#define MACR_CLS_MAPPING_DATA_INFO_OFFSET_ClassNames		51				// 类别名称数组偏移

// AI 模型标签, 方便后续定位, 进行统一地管理数据
typedef enum 
{
	AI_MODEL_TAGID_NONE				= 0,						// 无模型
	AI_MODEL_TAGID_ROI_DETECT		= 1,						// ROI检测模型
	AI_MODEL_TAGID_ROI_DETECT_ALT,								// ROI检测模型 (方案二)
	AI_MODEL_TAGID_CONTOUR_DETECT,								// 轮廓检测模型
	AI_MODEL_TAGID_FINDER_320,									// QR码定位标识模型（320x320）
	AI_MODEL_TAGID_FINDER_640,									// QR码定位标识模型（640x640）
	AI_MODEL_TAGID_DOT_320,										// 打点模型（320x320）
	AI_MODEL_TAGID_CAP_DET,										// 管帽识读模型
	AI_MODEL_TAGID_OCR_DET,										// OCR字符检测模型
	AI_MODEL_TAGID_OCR_REC,										// OCR字符识别模型
	AI_MODEL_TAGID_OCR_DET_GEN,									// 通用 OCR 字符检测模型
	AI_MODEL_TAGID_OCR_REC_GEN,									// 通用 OCR 字符识别模型
	AI_MODEL_TAGID_OCR_DET_CHNLP,								// 中国车牌检测模型 (位置检测)
	AI_MODEL_TAGID_OCR_REC_CHNLP,								// 中国车牌识别模型 (字符识别)
	AI_MODEL_TAGID_OCR_DET_RUSLP,								// 俄罗斯车牌检测模型 (位置检测)
	AI_MODEL_TAGID_OCR_REC_RUSLP,								// 俄罗斯车牌识别模型 (字符识别)
	AI_MODEL_TAGID_RESTORER,									// 复原模型
	AI_MODEL_TAGID_CLASSIFICATION,								// 分类模型
	AI_MODEL_TAGID_MAX,											// 模型标签最大值
}e_ai_model_tag_id;

struct ai_dete_result
{
	s32 is_used;							// 空间是否已经使用
	g_ImageAreaStruct *img_area_pt;			// 图像
	vector<DetectionBBoxInfo>result_global;	// 全局 AI 检测结果
	vector<DetectionBBoxInfo>result_roi;	// 中心区域 AI 检测结果
	s32 ai_roi_global_dete_img_bit;			// AI ROI 全图缩略检测实际生效图层
	s32 ai_roi_center_dete_img_bit;			// AI ROI 中心挖图检测实际生效图层
};

struct ai_dete_cap_result
{
	vector<DetectionBBoxInfo>result_cap;	// 管帽检测结果
	s32 ai_dete_img_bit;					// AI 检测管帽实际生效图层
};

// 分类模型映射表结构
typedef struct _classification_mapping_st {
	char model_name[MAX_MODEL_NAME_LEN];					// 模型名称
	u8 class_count;											// 类别总数
	char class_names[MAX_CLASS_COUNT][MAX_CLASS_NAME_LEN];	// 类别名称数组
} classification_mapping_st;

struct ai_dete_result_roi_num_record
{
	volatile u32 img_index;					// 图像索引号
	volatile s32 valid_roi_num;				// 有效 ROI 数量
};

class ai_dete_model_info
{
	// 注意, 设计时需要考虑到双摄的情况，这个类有可能被多个线程同时访问，需要考虑线程安全问题

public:
	// 基本信息
	const e_ai_model_tag_id model_tag_id;
	const char *model_tag;

	// 运行信息
	volatile s32 state;		// 当前模块状态
	s32 need_init;			// 是否需要初始化 (暂时仅在上电后第一次生效)
	s32 init_ret;			// 初始化返回信息

private:
	// 记录信息
	u32 record_index;											// 记录索引
	u64 record_ts[MACR_AI_DETE_RSLT_RECORD_BUFF_SIZE];			// 记录时间戳(us)
	s32 record_rslt[MACR_AI_DETE_RSLT_RECORD_BUFF_SIZE];		// 记录结果(带原始AI返回结果数量, 但一般而言, 只会提示0(无效)和非0(有效)两种情况)
	u64 record_cost[MACR_AI_DETE_RSLT_RECORD_BUFF_SIZE];		// 记录耗时(us)
	s32 record_errval[MACR_AI_DETE_RSLT_RECORD_BUFF_SIZE];		// 记录错误返回值(一般而言 0 表示成功,其他值表示错误代码)

public:
	// 构造函数
	ai_dete_model_info(e_ai_model_tag_id model_tag_id, const char *model_tag, s32 need_init) : model_tag_id(model_tag_id), model_tag(model_tag), need_init(need_init)
	{
		// 运行信息
		state = MACR_AI_DETE_MODEL_STATE_NONE;
		init_ret = 0;

		// 记录信息
		record_index = 0;
		memset(record_ts, 0, sizeof(record_ts));
		memset(record_rslt, 0, sizeof(record_rslt));
		memset(record_cost, 0, sizeof(record_cost));
		memset(record_errval, 0, sizeof(record_errval));
	}

	void calcRecord(s32 rslt, u64 cost_us, s32 error_val);			// 计算记录函数
	void dumpRecord(printf_method method, const char *prefix);	// 输出记录函数
};

void ai_dete_model_info::calcRecord(s32 rslt, u64 cost_us, s32 error_val)	// 计算记录函数
{	
	record_ts[record_index] = read_system_time_us();
	record_rslt[record_index] = rslt;
	record_cost[record_index] = cost_us;
	record_errval[record_index] = error_val;
	record_index++;
	record_index = (record_index >= MACR_AI_DETE_RSLT_RECORD_BUFF_SIZE) ? 0 : record_index;
}

void ai_dete_model_info::dumpRecord(printf_method method, const char *prefix)
{
	u32 tmp_index = 0;
	u32 i = 0;

	if (prefix == NULL)
	{
		prefix = "";
	}

	if ((record_index == 0) && (record_ts[0] == 0))
	{
		method((s8*)"%sHad Not Record\r\n", prefix);
	}
	else
	{
		tmp_index = record_index;	
		for (i = 0; i < MACR_AI_DETE_RSLT_RECORD_BUFF_SIZE; i++)
		{
			if (record_ts[tmp_index] == 0)
			{
				// 跳过无效记录
				tmp_index++;
				tmp_index = (tmp_index >= MACR_AI_DETE_RSLT_RECORD_BUFF_SIZE) ? 0 : tmp_index;
				continue;
			}

			method((s8*)"%sTimeStamp(ms): %-10.2f, Result: %-8s, Cost(ms): %-10.2f, Error_Val: %-5d\r\n", 
										prefix, 
										((float)record_ts[tmp_index]/1000), 
										(record_rslt[tmp_index] > 0) ? "Valid" : "Invalid", 
										((float)record_cost[tmp_index]/1000),
										record_errval[tmp_index]);
			tmp_index++;
			tmp_index = (tmp_index >= MACR_AI_DETE_RSLT_RECORD_BUFF_SIZE) ? 0 : tmp_index;
		}
	}
}

// 所有支持的模型的列表
static ai_dete_model_info s_ai_dete_model_info_buff[] =
{
	// 条码检测模型
	#if (MACR_FUNCTION_AI_DETECT_ROI == 1)
		#if (MACR_AI_DETE_ROI_USING_320x320 == 1)
			{ AI_MODEL_TAGID_ROI_DETECT,			AI_MODEL_ROI_DETECT_320x320,		MACR_TRUE },			// 四分类模型（1D、QR、DM、Others）（320x320）
		#elif (MACR_AI_DETE_ROI_USING_160x320 == 1)
			{ AI_MODEL_TAGID_ROI_DETECT,			AI_MODEL_ROI_DETECT_160x320,		MACR_TRUE },			// 四分类模型（1D、QR、DM、Others）（160x320）
		#elif (MACR_AI_DETE_ROI_USING_320x160 == 1)
			{ AI_MODEL_TAGID_ROI_DETECT,			AI_MODEL_ROI_DETECT_320x160,		MACR_TRUE },			// 单分类模型（1D）（320x160）
		#elif (MACR_AI_DETE_ROI_USING_LEGACY_320x320 == 1)
			{ AI_MODEL_TAGID_ROI_DETECT,			AI_MODEL_ROI_DETECT_LEGACY_320x320,	MACR_TRUE },			// 四分类模型（1D、QR、DM、Others）（320x320）
		#endif

		// 重复性检验 (必须且仅能选择一个)
		#if ((MACR_AI_DETE_ROI_USING_320x320 + \
			MACR_AI_DETE_ROI_USING_160x320 + \
			MACR_AI_DETE_ROI_USING_320x160 + \
			MACR_AI_DETE_ROI_USING_LEGACY_320x320) != 1)
			#error "AI_DETECT_ROI_MODEL_SELECT_ERROR"
		#endif
	#endif

	// 条码检测模型 (备选)
	#if (MACR_FUNCTION_AI_DETECT_ROI_ALT == 1)
		#if (MACR_AI_DETE_ROI_ALT_USING_512x640 == 1)
			{ AI_MODEL_TAGID_ROI_DETECT_ALT,		AI_MODEL_ROI_DETECT_512x640,		MACR_FALSE },			// 四分类模型（1D、QR、DM、Others）（512x640）
		#endif

		#if (MACR_AI_DETE_ROI_ALT_USING_640x512 == 1)
			{ AI_MODEL_TAGID_ROI_DETECT_ALT,		AI_MODEL_ROI_DETECT_640x512,		MACR_FALSE },			// 四分类模型（1D、QR、DM、Others）（640x512）
		#endif

		// 重复性检验 (必须且仅能选择一个)
		#if ((MACR_AI_DETE_ROI_ALT_USING_512x640 + \
			MACR_AI_DETE_ROI_ALT_USING_640x512) != 1)
			#error "AI_DETECT_ROI_ALT_MODEL_SELECT_ERROR"
		#endif
	#endif

	// 轮廓检测模型
	#if (MACR_FUNCTION_AI_DETECT_CONTOUR == 1)
		{ AI_MODEL_TAGID_CONTOUR_DETECT,		AI_MODEL_CONTOUR_DETECT,			MACR_TRUE },			// 轮廓检测模型
	#endif

	// QR码定位标识模型
	#if (MACR_FUNCTION_AI_DETECT_FINDER == 1)
		{ AI_MODEL_TAGID_FINDER_320,			AI_MODEL_FINDER_320,				MACR_TRUE },			// QR码定位标识模型（320x320）
		{ AI_MODEL_TAGID_FINDER_640,			AI_MODEL_FINDER_640,				MACR_TRUE },			// QR码定位标识模型（640x640）
	#endif

	// 打点模型
	#if (MACR_FUNCTION_AI_DETECT_DOT == 1)
		{ AI_MODEL_TAGID_DOT_320,				AI_MODEL_DOT_320x320,				MACR_TRUE },			// 打点模型（320x320）
	#endif

	// 试管管帽检测模型
	#if (MACR_FUNCTION_AI_DETECT_CAP == 1)
		{ AI_MODEL_TAGID_CAP_DET,				AI_MODEL_CAP_DET,					MACR_TRUE },			// 试管管帽检测模型
	#endif

	// OCR字符检测模模型
	#if (MACR_FUNCTION_AI_DETECT_OCR == 1)
		{ AI_MODEL_TAGID_OCR_DET,				AI_MODEL_OCR_DET_320x320,			MACR_TRUE },			// OCR字符位置标定模型（320x320）
		{ AI_MODEL_TAGID_OCR_REC,				AI_MODEL_OCR_REC_960x48,			MACR_TRUE },			// 获取 OCR 转换字符串
	#endif

	// 通用 OCR 字符检测模型
	#if (MACR_FUNCTION_AI_DETECT_OCR_GEN == 1)
		#if (MACR_AI_DETE_OCR_DET_GEN_SUING_512x640 == 1)
			{ AI_MODEL_TAGID_OCR_DET_GEN,			AI_MODEL_OCR_DET_General_512x640,	MACR_TRUE },			// 通用 OCR 字符检测模型（512x640）
		#endif
		#if (MACR_AI_DETE_OCR_DET_GEN_SUING_768x960 == 1)
			{ AI_MODEL_TAGID_OCR_DET_GEN,			AI_MODEL_OCR_DET_General_768x960,	MACR_TRUE },			// 通用 OCR 字符检测模型（768x960）
		#endif

		#if ((MACR_AI_DETE_OCR_DET_GEN_SUING_512x640 + \
			MACR_AI_DETE_OCR_DET_GEN_SUING_768x960) != 1)
			#error "AI_DETECT_OCR_DET_GEN_MODEL_SELECT_ERROR"
		#endif

		{ AI_MODEL_TAGID_OCR_REC_GEN,			AI_MODEL_OCR_REC_General_320x48,	MACR_TRUE },			// 通用 OCR 字符识别模型（320x48）
	#endif

	// 车牌识别模型
	#if (MACR_FUNCTION_AI_DETECT_LPR == 1)
		{ AI_MODEL_TAGID_OCR_DET_CHNLP,		AI_MODEL_OCR_DET_CHNLP,				MACR_TRUE },			// 车牌检测模型 (位置检测)
		{ AI_MODEL_TAGID_OCR_REC_CHNLP,		AI_MODEL_OCR_REC_CHNLP,				MACR_TRUE },			// 车牌识别模型 (字符识别)
	#endif

	// AI 复原模型
	#if (MACR_FUNCTION_AI_DETECT_RESTORER == 1)
		{ AI_MODEL_TAGID_RESTORER,			AI_MODEL_RESTORER,					MACR_TRUE },			// 复原模型
	#endif

	// 分类模型
	#if (MACR_FUNCTION_AI_DETECT_CLASSIFICATION == 1)
		{ AI_MODEL_TAGID_CLASSIFICATION,	AI_MODEL_CLASSIFICATION,			MACR_TRUE },			// 分类模型
	#endif
};
static s32 s_ai_dete_model_info_buff_size = sizeof(s_ai_dete_model_info_buff) / sizeof(s_ai_dete_model_info_buff[0]);

static s32 s_ai_dete_model_init_rslt = MACR_NONE;

static struct ai_dete_result s_ai_dete_result_buff[MACR_AI_DETE_RESULT_BUFF_SIZE] = { 0 };		// AI 检测结果缓冲区
static u8 s_ai_dete_raw8_img_data_buff[MACR_IMAGE_SIZE] = { 0 };
// AI 检测结果临时缓存区(用于临时保存APP调用AI检测模块时得到的检测结果)
static vector<DetectionBBoxInfo>s_ai_dete_roi_result_temp;
static std::vector<std::vector<Point>> s_ai_dete_contour_result_temp;
static vector<DetectionBBoxInfo>s_ai_dete_finder_result_temp;
static vector<DetectionBBoxInfo>s_ai_dete_dot_result_temp;
static vector<QuadPoints>s_ai_dete_ocr_result_temp;
static vector<QuadPoints>s_ai_dete_lpr_result_temp;
static vector<OCRCharacter>s_ai_dete_lpr_char_temp;
static s32 s_ai_dete_lpr_char_temp_len = 0;
static vector<OCRCharacter>s_ai_dete_get_ocr_string_temp;
static AIEngine::Tensor<uint8_t>s_ai_dete_restorer_result_temp;
static u8* s_ai_dete_restorer_result_buff = NULL;
static s32 s_ai_dete_restorer_result_buff_len = 0;
static ai_dete_cap_result s_ai_dete_cap_result_temp;
static vector<ClassificationResult>s_ai_dete_classification_result_temp;
static ai_dete_result_roi_num_record s_ai_dete_rslt_roi_num_record_buff[MACR_AI_DETE_RSLT_ROI_NUM_RECORD_BUFF_SIZE_MAX] = { 0 };
// 保存 AI 检测结果 ROI 数量记录的次数，当前变量大于 MACR_AI_DETE_RSLT_ROI_NUM_RECORD_BUFF_SIZE_MAX 时，则发生覆盖。
// 覆盖原则：最新的覆盖最旧的
static volatile u32 s_ai_dete_rslt_roi_num_record_save_num = 0;
// 缓冲区中的偏移，保存下一个记录的偏移
static volatile u32 s_ai_dete_rslt_roi_num_record_next_offset = 0;
// 软件 ROI 中心挖图检测需求控制
static volatile s32 s_ai_dete_software_roi_center_dete_ctrl = MACR_FALSE;

static volatile f32 s_ai_dete_valid_roi_score_thre_max = 0.0f;
static volatile f32 s_ai_dete_valid_roi_score_thre_min = 0.0f;

static SMsgHandler s_ai_dete_msg_handler;

// 分类映射表相关静态变量
static classification_mapping_st g_classification_mapping_info;
static u8 g_classification_mapping_data_info[MACR_CLS_MAPPING_DATA_INFO_SIZE_MAX] = { 0 };
static u8 g_classification_mapping_version[MACR_CLS_MAPPING_VERSION_SIZE_MAX] = "CLS_MAP_V1.0";
static char g_classification_mapping_filename[128] = { 0 };  // 动态映射表文件名
static char g_current_loaded_model[MAX_MODEL_NAME_LEN] = { 0 };  // 当前已加载的模型名称
static volatile u8 g_classification_mapping_loaded = MACR_FALSE;
static char g_classification_fallback_name[32] = { 0 };  // 用于存储格式化的类别索引字符串

static ai_dete_model_info *AIDete_GetModelInfo(e_ai_model_tag_id model_tag_id);
static u8 *AIDete_ConvImg2Raw8(IN g_ImageAreaStruct *img_area_pt);
static u8 *AIDete_ConvRaw10ToRaw8(IN u8 *raw10_img_data_pt, IN s32 raw8_img_bit);
static u8 *AIDete_ConvRaw12ToRaw8(IN u8 *raw12_img_data_pt, IN s32 raw8_img_bit);
static s32 AIDete_InitAIRsltROINumRecord(void);
static s32 AIDete_isPermitConfigHandler(INOUT syscfg_para_setting_str *setting_info_pt, IN u32 arg1, INOUT void *arg2_pt);
static void AIDete_HandleMessage(IN SMsg msg);

// 分类映射表相关函数声明
static s32 AIDete_Classification_InitMapping(void);
static s32 AIDete_Classification_LoadMapping(const char* model_name);
static s32 AIDete_Classification_SaveMapping(const char* model_name);
static s32 AIDete_Classification_SetDefaultMapping(void);
static s32 AIDete_Classification_EnsureMappingLoaded(const char* model_name);
static void AIDete_Classification_GenerateFilename(const char* model_name, char* filename, size_t filename_size);
static const char* AIDete_Classification_GetClassName(const char* model_name, int class_id);

/*******************************************************************************
Function      : AIDete_GetModelInfo()
Description   : 获取模型信息
Input         : model_tag_id : 模型标签ID
				is_need_runing : 是否需要运行中
Output        : None
Return        : 模型信息指针
                NULL : 模型不存在
Author        : PengHZ
Create Date   : 2025/07/09
Last Update   : None
Note          : None
*******************************************************************************/
static ai_dete_model_info *AIDete_GetModelInfo(e_ai_model_tag_id model_tag_id, s32 is_need_running)
{
	static s32 is_inited = MACR_FALSE;
	static s32 model_id_offset_map[AI_MODEL_TAGID_MAX + 1] = { -1 };
	ai_dete_model_info * ret_val = NULL;

	// 初始化模型ID偏移映射
	if (is_inited == MACR_FALSE)
	{
		is_inited = MACR_TRUE;

		memset(model_id_offset_map, -1, sizeof(model_id_offset_map));

		// 初始化模型ID偏移映射
		for (u32 i = 0; i < s_ai_dete_model_info_buff_size; i++)
		{
			model_id_offset_map[s_ai_dete_model_info_buff[i].model_tag_id] = i;
		}
	}

	// 获取模型偏移
	if (model_id_offset_map[model_tag_id] < 0)
	{
		// 模型不存在
		return NULL;
	}

	// 获取模型对象
	ret_val = &s_ai_dete_model_info_buff[model_id_offset_map[model_tag_id]];

	if (is_need_running == MACR_TRUE)
	{
		if (ret_val->state != MACR_AI_DETE_MODEL_STATE_RUNING)
		{
			// 模型未运行中
			return NULL;
		}
	}

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_Init()
Description   : 初始化
Input         : None
Output        : None
Return        : MACR_NONE : 初始化成功
                MACR_ERROR : 初始化失败
Author        : TanJW
Create Date   : 2023/04/10
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" s32 AIDete_Init(void)
{
	u32 i = 0;
	static s32 is_first_init = MACR_TRUE;

	u8 roi_model_sel = MDFLAG_AI_ROI_Model_Default;
	ai_dete_model_info *model_info_pt = NULL;

	for (i = 0; i < MACR_AI_DETE_RESULT_BUFF_SIZE; i++)
	{
		s_ai_dete_result_buff[i].is_used = MACR_FALSE;
		s_ai_dete_result_buff[i].img_area_pt = NULL;
	}

	if (is_first_init == MACR_TRUE)
	{
		is_first_init = MACR_FALSE;

		// 初始化消息处理器
		s_ai_dete_msg_handler.HandleMessage = AIDete_HandleMessage;

		// 注册消息映射
		MsgManager->AttachMessageMap(&s_ai_dete_msg_handler, Msg_Exe_Param_Cfg_Content);

		// 注册参数设置控制信息回调函数
		SysCfg_AddParaSettingCtrlInfo(AIDete_isPermitConfigHandler, (u8*)"AI_HDL");

		SysCfg_GetParaVal_1B_WithCode(MACR_PARACODE_1011, MACR_CURRENT_PARA, &roi_model_sel);

		if (roi_model_sel == MDFLAG_AI_ROI_Model_320x320)
		{
			// 当前指定 AI_MODEL_TAGID_ROI_DETECT 为生效模型
			model_info_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_ROI_DETECT, MACR_FALSE);
			if (model_info_pt != NULL)	{	model_info_pt->need_init = MACR_TRUE;	}

			model_info_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_ROI_DETECT_ALT, MACR_FALSE);
			if (model_info_pt != NULL)	{	model_info_pt->need_init = MACR_FALSE;	}
		}
		else if (roi_model_sel == MDFLAG_AI_ROI_Model_512x640)
		{
			// 当前指定 AI_MODEL_TAGID_ROI_DETECT_ALT 为生效模型
			model_info_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_ROI_DETECT_ALT, MACR_FALSE);
			if (model_info_pt != NULL)	{	model_info_pt->need_init = MACR_TRUE;	}

			model_info_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_ROI_DETECT, MACR_FALSE);
			if (model_info_pt != NULL)	{	model_info_pt->need_init = MACR_FALSE;	}
		}

		//NPU模块初始化
		// 逐个初始化模型
		for (auto &model_info : s_ai_dete_model_info_buff)
		{
			// 跳过不需要初始化的模型
			if (model_info.need_init == MACR_FALSE)
			{
				continue;
			}

			// 初始化模型
			model_info.init_ret = ai_engine_model_init(model_info.model_tag, MACR_AI_DETE_NUM_SESSIONS);

			// 根据结果更新状态
			if (model_info.init_ret == MACR_NONE)
			{
				// 此处初始化完成即进入运行状态
				model_info.state = MACR_AI_DETE_MODEL_STATE_RUNING;
			}
			else
			{
				// 初始化失败，将模型状态设置为关闭
				model_info.state = MACR_AI_DETE_MODEL_STATE_NONE;
				// 存在初始化失败项, 则同步记录到全局变量中
				s_ai_dete_model_init_rslt = MACR_ERROR;
			}
		}
	}

	// 初始化 AI 结果 ROI 信息记录
	AIDete_InitAIRsltROINumRecord();

	// 初始化分类映射表
	AIDete_Classification_InitMapping();

	return MACR_NONE;
}

/*******************************************************************************
Function      : AIDete_GetInitResult()
Description   : 获取AI初始化结果
Input         : None
Output        : None
Return        : MACR_NONE : 初始化成功
                MACR_ERROR : 初始化失败
Author        : PengHZ
Create Date   : 2025/07/02
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" s32 AIDete_GetInitResult(void)
{
	return s_ai_dete_model_init_rslt;
}

/*******************************************************************************
Function      : AIDete_CalcResult()
Description   : AI 检测计算结果
Input         : img_area_pt : 图像空间指针
				dete_mode : AI 检测模式
Output        : None
Return        : MACR_NONE : 计算成功
                MACR_ERROR : 计算失败
Author        : TanJW
Create Date   : 2023/04/10
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" s32 AIDete_CalcResult(IN g_ImageAreaStruct *img_area_pt, u32 dete_mode, IN CoordinateVOC *roi_coord_pt, IN CoordinateYOLO *center_coord_pt)
{
	u32 i = 0;
	s32 ret_val = MACR_ERROR;
	ImageInfo image_info = { 0 };
	s32 is_dla_config_succ = MACR_TRUE;
	s32 is_buff_used = MACR_FALSE;
	void *result_global_pt = NULL;
	void *result_roi_pt = NULL;
	s32 ai_roi_global_dete_img_bit = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE;			// AI ROI 全图缩略检测实际生效图层
	s32 ai_roi_center_dete_img_bit = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE;			// AI ROI 中心挖图检测实际生效图层
	vector<DetectionBBoxInfo> *result_pt = NULL;
	ai_dete_model_info *model_info_pt = NULL;
	u64 start_us = 0;
	s32 err_val = MACR_NONE;

	// 查找 ROI 检测模型对象
	model_info_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_ROI_DETECT, MACR_TRUE);
	if (model_info_pt == NULL)
	{
		// 没有找到/未运行中, 则查找替代模型
		model_info_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_ROI_DETECT_ALT, MACR_TRUE);
	}

	if (model_info_pt == NULL)
	{
		return MACR_ERROR;
	}

	// 查找一个可用空间来缓存 AI 检测结果
	for (i = 0; i < MACR_AI_DETE_RESULT_BUFF_SIZE; i++)
	{
		if (s_ai_dete_result_buff[i].is_used == MACR_FALSE)
		{
			// 查找到未被使用的buffer，开始执行AI检测
			// 清除 buffer 中的信息
			s_ai_dete_result_buff[i].result_global.clear();
			s_ai_dete_result_buff[i].result_roi.clear();
			s_ai_dete_result_buff[i].ai_roi_global_dete_img_bit = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE;
			s_ai_dete_result_buff[i].ai_roi_center_dete_img_bit = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE;

			// 暂存相关信息
			image_info.img_data_pt = img_area_pt->pImgArea;
			image_info.img_height = img_area_pt->height;
			image_info.img_width = img_area_pt->width;
			image_info.color_format = COLOR_FORMAT_GRAY;
			// RAW12格式图像
			if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW12)
			{
				image_info.bit_depth = BIT_DEPTH_RAW12;
				switch (img_area_pt->ai_dete_raw8_img_bit)
				{
					case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_ADAPTIVE: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE; break;
					case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_0_7: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_00_07; break;
					case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_1_8: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_01_08; break;
					case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_2_9: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_02_09; break;
					case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_3_10: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_03_10; break;
					case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_4_11: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_04_11; break;
					default: is_dla_config_succ = MACR_FALSE; break;
				}
			}
			// RAW10格式图像
			else if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW10)
			{
				image_info.bit_depth = BIT_DEPTH_RAW10;
				switch (img_area_pt->ai_dete_raw8_img_bit)
				{
					case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_ADAPTIVE: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE; break;
					case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_0_7: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_00_07; break;
					case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_1_8: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_01_08; break;
					case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_2_9: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_02_09; break;
					default: is_dla_config_succ = MACR_FALSE; break;
				}
			}
			// 其他格式图像
			else
			{
				// AI 模块暂时不支持其他格式图像的检测，标记dla配置失败
				is_dla_config_succ = MACR_FALSE;
			}

			// 如果dla配置失败，直接退出AI检测并返回检测失败
			if (is_dla_config_succ == MACR_FALSE)
			{
				ret_val = MACR_ERROR;
				break;
			}

			if ((dete_mode&MACR_AI_DETE_MODE_COORD) == MACR_AI_DETE_MODE_COORD)
			{
				if (roi_coord_pt == NULL)
				{
					image_info.prep_type = PREP_TYPE_FULL_IMG_RESIZE;
				}
				else
				{
					image_info.prep_type = PREP_TYPE_ROI_RESIZE_ADAPTIVE_DEP_ON_COORD;
					image_info.roi_coord = *roi_coord_pt;
				}

				result_pt = (&(s_ai_dete_result_buff[i].result_global));

				// 进行计算
				start_us = read_system_time_us();
				err_val = ai_engine_task_detection(model_info_pt->model_tag, &image_info, result_pt);
				model_info_pt->calcRecord(
					(s_ai_dete_result_buff[i].result_global.size()), 
					(read_system_time_us() - start_us),
					 err_val);
				if (err_val != MACR_NONE)
				{
					break;
				}

				// 临时缓冲全局检测结果
				result_global_pt = (void *)(&(s_ai_dete_result_buff[i].result_global));
				// 记录 AI 推理实际生效图层
				ai_roi_global_dete_img_bit = image_info.sub_bit_range;
				is_buff_used = MACR_TRUE;

				// 如果当前成像应用不是 scanOS.video 再选择性跳过 ROI 检测步骤，scanOS.video 如果启用了 ROI 检测，则一定需要进行中心挖图检测
				if (ImagingChain_isFrameTypeProjectVideo() == MACR_TRUE)
				{
					// Nothing
				}
				// 当前成像应用不是 scanOS.video 选择性跳过 ROI 检测步骤
				else
				{
					// 选择性进行中心挖图检测
					if ((dete_mode & MACR_AI_DETE_MODE_CENTER) == MACR_AI_DETE_MODE_CENTER)
					{
						// 判断邻近识读是否开启，如果功能开启，则进行中心挖图检测
						if (s_ai_dete_software_roi_center_dete_ctrl == MACR_TRUE)
						{
							// Nothing
						}
						// 功能没有开启
						else
						{
							// 如果全图 AI 检测得到了有效检测结果，则跳过 ROI 检测的步骤
							if (AIDete_isDeteSucc((void *)(&(s_ai_dete_result_buff[i].result_global)), MACR_AI_DETE_RSLT_VALID_ROI_SCORE_THRESHEOLD) == MACR_TRUE)
							{
								// 不执行ROI检测
								dete_mode &= ~MACR_AI_DETE_MODE_CENTER;
							}
						}
					}
				}
			}

			if ((dete_mode&MACR_AI_DETE_MODE_CENTER) == MACR_AI_DETE_MODE_CENTER)
			{
				// 平台类产品，直接检测中心区域
				if ((ProductInfo_GetForm_Product() == PRODUCT_FORM_PLATFORM) || 	\
					(center_coord_pt == NULL))
				{
					image_info.prep_type = PREP_TYPE_ROI_CETNER;
				}
				// 其他产品，根据成像应用设置的参数执行检测
				else
				{
					image_info.prep_type = PREP_TYPE_ROI_RESIZE_2X_DEP_ON_CENTER_COORD;
					image_info.center_coord = *center_coord_pt;
				}

				result_pt = (&(s_ai_dete_result_buff[i].result_roi));

				// 进行计算
				start_us = read_system_time_us();
				err_val = ai_engine_task_detection(model_info_pt->model_tag, &image_info, result_pt);
				model_info_pt->calcRecord(
					(s_ai_dete_result_buff[i].result_roi.size()), 
					(read_system_time_us() - start_us),
					 err_val);
				if (err_val != MACR_NONE)
				{
					break;
				}

				// 临时缓冲 ROI 检测结果
				result_roi_pt = (void *)(&(s_ai_dete_result_buff[i].result_roi));
				// 记录 AI 推理实际生效图层
				ai_roi_center_dete_img_bit = image_info.sub_bit_range;
				is_buff_used = MACR_TRUE;
			}

			if (is_buff_used == MACR_TRUE)
			{
				// 计算成功，记录结果
				s_ai_dete_result_buff[i].img_area_pt = img_area_pt;
				s_ai_dete_result_buff[i].img_area_pt->ai_dete_rslt_index = (s32)i;
				s_ai_dete_result_buff[i].img_area_pt->ai_dete_rslt_global_pt = result_global_pt;
				s_ai_dete_result_buff[i].img_area_pt->ai_dete_rslt_roi_pt = result_roi_pt;
				s_ai_dete_result_buff[i].ai_roi_global_dete_img_bit = ai_roi_global_dete_img_bit;
				s_ai_dete_result_buff[i].ai_roi_center_dete_img_bit = ai_roi_center_dete_img_bit;
				s_ai_dete_result_buff[i].is_used = is_buff_used;
				ret_val = MACR_NONE;
			}

			break;
		}
	}

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_ClrResult()
Description   : 清除 AI 检测计算结果
Input         : img_area_pt : 清除结果前的图像指针
Output        : img_area_pt : 清除结果后的图像指针
Return        : MACR_NONE : 清除成功
                MACR_ERROR : 清除失败
Author        : TanJW
Create Date   : 2023/04/10
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" s32 AIDete_ClrResult(INOUT g_ImageAreaStruct *img_area_pt)
{
	s32 result_index = MACR_AI_DETE_RESULT_BUFF_INDEX_INVALID;
	s32 ret_val = MACR_NONE;

	// 缓冲 AI 检测结果索引值
	result_index = img_area_pt->ai_dete_rslt_index;

	// 检测索引值是否有效
	if ((result_index >= 0) && (result_index <= MACR_AI_DETE_RESULT_BUFF_INDEX_MAX))
	{
		// 清空缓存区相关信息
		s_ai_dete_result_buff[result_index].is_used = MACR_FALSE;
		s_ai_dete_result_buff[result_index].img_area_pt = NULL;

		// 清空图像相关信息
		img_area_pt->ai_dete_rslt_index = MACR_AI_DETE_RESULT_BUFF_INDEX_INVALID;
		img_area_pt->ai_dete_rslt_global_pt = NULL;
		img_area_pt->ai_dete_rslt_roi_pt = NULL;
	}
	else
	{
		ret_val = MACR_ERROR;
	}

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_ConvImg2Raw8()
Description   : 将图像转换成 RAW8
Input         : img_area_pt : 图像存储空间
Output        : None
Return        : NULL : 转换成功
                Others : 转换失败
Author        : TanJW
Create Date   : 2023/04/19
Last Update   : None
Note          : None
*******************************************************************************/
static s32 AI_Dete_TranslatePureRaw10ToRaw8(u8 *src_pt, u8 *dst_pt)
{
	const u32 SRC_IMG_DATA_COL = 1280;	// 1024 / 4 * 5 = 1280，PureRaw10 类型图像，5 个字节存储 4 个像素
	const u32 SRC_IMG_DATA_ROW = 1280;	// 一张图像数据可以看成一共有 1280 行

	u32 i = 0;
	u32 cnt = 0;

	// 判断指针有效性
	if ((src_pt == NULL) || (dst_pt == NULL))
	{
		return MACR_ERROR;
	}

	for (i = 0; i < (SRC_IMG_DATA_COL*SRC_IMG_DATA_ROW); i += 10)
	{
		dst_pt[cnt++] = ((src_pt[i + 0] >> 2) | (src_pt[i + 1] << 6));
		dst_pt[cnt++] = ((src_pt[i + 1] >> 4) | (src_pt[i + 2] << 4));
		dst_pt[cnt++] = ((src_pt[i + 2] >> 6) | (src_pt[i + 3] << 2));
		dst_pt[cnt++] = ((src_pt[i + 3] >> 8) | (src_pt[i + 4] << 0));

		dst_pt[cnt++] = ((src_pt[i + 5] >> 2) | (src_pt[i + 6] << 6));
		dst_pt[cnt++] = ((src_pt[i + 6] >> 4) | (src_pt[i + 7] << 4));
		dst_pt[cnt++] = ((src_pt[i + 7] >> 6) | (src_pt[i + 8] << 2));
		dst_pt[cnt++] = ((src_pt[i + 8] >> 8) | (src_pt[i + 9] << 0));
	}

	return MACR_NONE;
}

static u8 *AIDete_ConvImg2Raw8(IN g_ImageAreaStruct *img_area_pt)
{
	u8 *raw8_img_data_pt = NULL;
	u8 img_depth = MACR_IMAGE_IMG_DEPTH_NONE;

	// 判断指针有效性
	if (img_area_pt == NULL)
	{
		return NULL;
	}

	// 临时缓冲图像深度
	img_depth =img_area_pt->depth;

	// 根据需求将当前图像转换为 RAW8 图像
	if (img_depth == MACR_IMAGE_IMG_DEPTH_RAW10)
	{
		// RAW10 根据需求转换成 RAW8
		raw8_img_data_pt = AIDete_ConvRaw10ToRaw8(img_area_pt->pImgArea, img_area_pt->ai_dete_raw8_img_bit);
		// raw8_img_data_pt = s_ai_dete_raw8_img_data_buff;
		// AI_Dete_TranslatePureRaw10ToRaw8(img_area_pt->pImgArea, raw8_img_data_pt);
	}
	else if (img_depth == MACR_IMAGE_IMG_DEPTH_RAW12)
	{
		// RAW12 根据需求转换成 RAW8
		raw8_img_data_pt = AIDete_ConvRaw12ToRaw8(img_area_pt->pImgArea, img_area_pt->ai_dete_raw8_img_bit);
	}
	else if (img_depth == MACR_IMAGE_IMG_DEPTH_RAW8)
	{
		raw8_img_data_pt = img_area_pt->pImgArea;
	}
	else
	{
		// 其他
		raw8_img_data_pt = NULL;
	}

	return raw8_img_data_pt;
}

/*******************************************************************************
Function      : AIDete_ConvRaw10ToRaw8()
Description   : 将 RAW10 图像转换成 RAW8 图像
Input         : raw10_img_data_pt : RAW10 图像数据
                raw8_img_bit : 转换成 RAW8 的位数
Output        : None
Return        : NULL : 转换成功
                Others : 转换失败
Author        : TanJW
Create Date   : 2023/04/19
Last Update   : None
Note          : None
*******************************************************************************/
static u8 *AIDete_ConvRaw10ToRaw8(IN u8 *raw10_img_data_pt, IN s32 raw8_img_bit)
{
	u8 *raw8_img_data_pt = NULL;
	s32 is_need_conv = MACR_TRUE;
	u32 i = 0;
	u8 *src_pt = NULL;
	u8 *dst_pt = NULL;
	u32 cnt = 0;
	u32 src_img_data_size = MACR_IMAGE_SIZE * 2;	// * 2 表示每个像素占据 2 个字节
	u8 shifting = 0;
	u8 others = 0;

	// 判断指针有效性
	if (raw10_img_data_pt == NULL)
	{
		return NULL;
	}

	switch (raw8_img_bit)
	{
		// 需要将 0-7 Bit 转换成 RAW8 图像
		case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_0_7:
			shifting = 0;
			break;
		// 需要将 1-8 Bit 转换成 RAW8 图像
		case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_1_8:
			shifting = 1;
			break;
		// 需要将 2-9 Bit 转换成 RAW8 图像
		case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_2_9:
			shifting = 2;
			break;
		default:
			is_need_conv = MACR_FALSE;
			break;
	}

	if (is_need_conv == MACR_TRUE)
	{
		src_pt = raw10_img_data_pt;
		dst_pt = s_ai_dete_raw8_img_data_buff;
		others = 8 - shifting;		// shifting | others = 8 Bit：一个像素

		if (shifting == 0)
		{
			// 当需求是将低 8 位 转换成 RAW8 图像时，每 2 个字节取一个低字节
			for (i = 0; i < src_img_data_size; i += 16)
			{
				dst_pt[cnt++] = src_pt[i + 0];
				dst_pt[cnt++] = src_pt[i + 2];
				dst_pt[cnt++] = src_pt[i + 4];
				dst_pt[cnt++] = src_pt[i + 6];

				dst_pt[cnt++] = src_pt[i + 8];
				dst_pt[cnt++] = src_pt[i + 10];
				dst_pt[cnt++] = src_pt[i + 12];
				dst_pt[cnt++] = src_pt[i + 14];
			}
		}
		else
		{
			// 每 16 个字节处理一次，提高效率
			for (i = 0; i < src_img_data_size; i += 16)
			{
				dst_pt[cnt++] = ((src_pt[i + 0] >> shifting) | (src_pt[i + 1] << others));
				dst_pt[cnt++] = ((src_pt[i + 2] >> shifting) | (src_pt[i + 3] << others));
				dst_pt[cnt++] = ((src_pt[i + 4] >> shifting) | (src_pt[i + 5] << others));
				dst_pt[cnt++] = ((src_pt[i + 6] >> shifting) | (src_pt[i + 7] << others));

				dst_pt[cnt++] = ((src_pt[i + 8] >> shifting) | (src_pt[i + 9] << others));
				dst_pt[cnt++] = ((src_pt[i + 10] >> shifting) | (src_pt[i + 11] << others));
				dst_pt[cnt++] = ((src_pt[i + 12] >> shifting) | (src_pt[i + 13] << others));
				dst_pt[cnt++] = ((src_pt[i + 14] >> shifting) | (src_pt[i + 15] << others));
			}
		}

		raw8_img_data_pt = dst_pt;
	}

	return raw8_img_data_pt;
}

/*******************************************************************************
Function      : AIDete_ConvRaw12ToRaw8()
Description   : 将 RAW12 图像转换成 RAW8 图像
Input         : raw12_img_data_pt : RAW12 图像数据
                raw8_img_bit : 转换成 RAW8 的位数
Output        : None
Return        : NULL : 转换成功
                Others : 转换失败
Author        : TanJW
Create Date   : 2023/04/19
Last Update   : None
Note          : None
*******************************************************************************/
static u8 *AIDete_ConvRaw12ToRaw8(IN u8 *raw12_img_data_pt, IN s32 raw8_img_bit)
{
	u8 *raw8_img_data_pt = NULL;
	s32 is_need_conv = MACR_TRUE;
	u32 i = 0;
	u8 *src_pt = NULL;
	u8 *dst_pt = NULL;
	u32 cnt = 0;
	u32 src_img_data_size = MACR_IMAGE_SIZE * 2;	// * 2 表示每个像素占据 2 个字节
	u8 shifting = 0;
	u8 others = 0;

	// 判断指针有效性
	if (raw12_img_data_pt == NULL)
	{
		return NULL;
	}

	switch (raw8_img_bit)
	{
		// 需要将 0-7 Bit 转换成 RAW8 图像
		case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_0_7:
			shifting = 0;
			break;
		// 需要将 1-8 Bit 转换成 RAW8 图像
		case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_1_8:
			shifting = 1;
			break;
		// 需要将 2-9 Bit 转换成 RAW8 图像
		case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_2_9:
			shifting = 2;
			break;
		// 需要将 3-10 Bit 转换成 RAW8 图像
		case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_3_10:
			shifting = 3;
			break;
		// 需要将 4-11 Bit 转换成 RAW8 图像
		case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_4_11:
			shifting = 4;
			break;
		default:
			is_need_conv = MACR_FALSE;
			break;
	}

	if (is_need_conv == MACR_TRUE)
	{
		src_pt = raw12_img_data_pt;
		dst_pt = s_ai_dete_raw8_img_data_buff;
		others = 8 - shifting;		// shifting | others = 8 Bit：一个像素

		if (shifting == 0)
		{
			// 当需求是将低 8 位 转换成 RAW8 图像时，每 2 个字节取一个低字节
			for (i = 0; i < src_img_data_size; i += 16)
			{
				dst_pt[cnt++] = src_pt[i + 0];
				dst_pt[cnt++] = src_pt[i + 2];
				dst_pt[cnt++] = src_pt[i + 4];
				dst_pt[cnt++] = src_pt[i + 6];

				dst_pt[cnt++] = src_pt[i + 8];
				dst_pt[cnt++] = src_pt[i + 10];
				dst_pt[cnt++] = src_pt[i + 12];
				dst_pt[cnt++] = src_pt[i + 14];
			}
		}
		else
		{
			// 每 16 个字节处理一次，提高效率
			for (i = 0; i < src_img_data_size; i += 16)
			{
				dst_pt[cnt++] = ((src_pt[i + 0] >> shifting) | (src_pt[i + 1] << others));
				dst_pt[cnt++] = ((src_pt[i + 2] >> shifting) | (src_pt[i + 3] << others));
				dst_pt[cnt++] = ((src_pt[i + 4] >> shifting) | (src_pt[i + 5] << others));
				dst_pt[cnt++] = ((src_pt[i + 6] >> shifting) | (src_pt[i + 7] << others));

				dst_pt[cnt++] = ((src_pt[i + 8] >> shifting) | (src_pt[i + 9] << others));
				dst_pt[cnt++] = ((src_pt[i + 10] >> shifting) | (src_pt[i + 11] << others));
				dst_pt[cnt++] = ((src_pt[i + 12] >> shifting) | (src_pt[i + 13] << others));
				dst_pt[cnt++] = ((src_pt[i + 14] >> shifting) | (src_pt[i + 15] << others));
			}
		}

		raw8_img_data_pt = dst_pt;
	}

	return raw8_img_data_pt;
}


//宏
#define MACR_RAWDATA_INTERCEPT_HIGH_ORDER_POSITION_FIRST			1		//截取raw8数据的起始位为首位（高位截取）
#define MACR_RAWDATA_INTERCEPT_HIGH_ORDER_POSITION_SECOND			2		//截取raw8数据的起始位为第二位
#define MACR_RAWDATA_INTERCEPT_HIGH_ORDER_POSITION_THIRD			3		//截取raw8数据的起始位为第三位
#define MACR_RAWDATA_INTERCEPT_HIGH_ORDER_POSITION_FOURTH			4		//截取raw8数据的起始位为第四位
#define MACR_RAWDATA_INTERCEPT_HIGH_ORDER_POSITION_FIFTH			5		//截取raw8数据的起始位为第五位

u8 g_img_data_tmp_buff1[1280*1024] = { 0 };
u8 g_img_data_tmp_buff2[1280 * 1024] = { 0 };
//-----------截取raw12/raw10数据的指定raw8数据--------------
// 函数名：	RawData_GetSelPos_Raw8Data()
// 输入：	s32 rawdata_figure	rawdata位数(raw12/raw10,即输入为12或10,未来可拓展)
//			u8 *raw_data_in		raw原图数据指针
//			s32 w, h			raw原图宽高
//			s32 set_position	raw数据截取起始位
// 输出：	u8 *raw8_data_out	输出raw8数据
// 返回：	1~succ  0~fail
// 说明：	~
//----------------------------------------------------------
s32 RawData_GetSelPos_Raw8Data(s32 rawdata_figure, u8* raw_data_in, s32 w, s32 h, s32 set_position, u8* raw8_data_out)
{
	s32 i;
	s32 ret = 1;
	u16 data_tmp = 0;

	switch (rawdata_figure)
	{
		case 12:	//raw12数据
		{
			switch (set_position)
			{
				case MACR_RAWDATA_INTERCEPT_HIGH_ORDER_POSITION_FIRST:
				{
					for (i = 0; i < w * h; i++)
					{
						data_tmp = ((u16*)raw_data_in)[i];
						raw8_data_out[i] = (u8)(data_tmp >> 4);
					}
					break;
				}
				case MACR_RAWDATA_INTERCEPT_HIGH_ORDER_POSITION_SECOND:
				{
					for (i = 0; i < w * h; i++)
					{
						data_tmp = ((u16*)raw_data_in)[i];
						raw8_data_out[i] = (data_tmp >> 11) ? 255 : (u8)(data_tmp >> 3);
					}
					break;
				}
				case MACR_RAWDATA_INTERCEPT_HIGH_ORDER_POSITION_THIRD:
				{
					for (i = 0; i < w * h; i++)
					{
						data_tmp = ((u16*)raw_data_in)[i];
						raw8_data_out[i] = (data_tmp >> 10) ? 255 : (u8)(data_tmp >> 2);
					}
					break;
				}
				case MACR_RAWDATA_INTERCEPT_HIGH_ORDER_POSITION_FOURTH:
				{
					for (i = 0; i < w * h; i++)
					{
						data_tmp = ((u16*)raw_data_in)[i];
						raw8_data_out[i] = (data_tmp >> 9) ? 255 : (u8)(data_tmp >> 1);
					}
					break;
				}
				case MACR_RAWDATA_INTERCEPT_HIGH_ORDER_POSITION_FIFTH:
				{
					for (i = 0; i < w * h; i++)
					{
						data_tmp = ((u16*)raw_data_in)[i];
						raw8_data_out[i] = (data_tmp >> 8) ? 255 : (u8)(data_tmp);
					}
					break;
				}
				default:
				{
					ret = 0;
					break;
				}
			}
			break;
		}
		case 10:	//raw10数据
		{
			switch (set_position)
			{
				case MACR_RAWDATA_INTERCEPT_HIGH_ORDER_POSITION_FIRST:
				{
					for (i = 0; i < w * h; i++)
					{
						data_tmp = ((u16*)raw_data_in)[i];
						raw8_data_out[i] = (u8)(data_tmp >> 2);
					}
					break;
				}
				case MACR_RAWDATA_INTERCEPT_HIGH_ORDER_POSITION_SECOND:
				{
					for (i = 0; i < w * h; i++)
					{
						data_tmp = ((u16*)raw_data_in)[i];
						raw8_data_out[i] = (data_tmp >> 9) ? 255 : (u8)(data_tmp >> 1);
					}
					break;
				}
				case MACR_RAWDATA_INTERCEPT_HIGH_ORDER_POSITION_THIRD:
				{
					for (i = 0; i < w * h; i++)
					{
						data_tmp = ((u16*)raw_data_in)[i];
						raw8_data_out[i] = (data_tmp >> 8) ? 255 : (u8)(data_tmp);
					}
					break;
				}
				default:
				{
					ret = 0;
					break;
				}
			}
			break;
		}
		default:
		{
			ret = 0;
			break;
		}
	}

	return ret;
}

//-----------raw12/raw10数据的1/4下采样函数-----------------
// 函数名：	ImgDownSampleByRatio4()
// 输入：	u16 *rawdata_in			raw原图数据指针
//			s32 w, h				raw原图宽高
// 输出：	u16 *rawdata_out		输出raw图像数据
//			s32 *w_out, *h_out		输出raw图像宽高
// 返回：	~
// 说明：	1/4下采样函数
//----------------------------------------------------------
void RawImgDownSampleByRatio4(u16* rawdata_in, s32 w_in, s32 h_in, u16* rawdata_out, s32 *w_out, s32 *h_out)
{
	s32 width_in = w_in;			//input width
	s32 height_in = h_in;			//input height
	s32 width_out;					//output_width
	s32 height_out;					//output_height
	u16* data_in = rawdata_in;		//input image data
	u16* data_out = rawdata_out;	//output image data
	s32 adr_in = 0;					//input image address
	s32 adr_out = 0;				//output image address
	f32 step;						//circulate step
	s32 i, j;						//circulate parameter
	f32 x0, y0;						//float circulate parameter
	s32 x1, y1;						//int	circulate parameter

	width_out = (s32)(width_in >> 1);
	height_out = (s32)(height_in >> 1);
	step = 2;

	y0 = 0;
	adr_out = 0;
	for (i = 0; i < height_out; i++)
	{
		y1 = (s32)(y0 + 0.5);
		x0 = 0;
		for (j = 0; j < width_out; j++)
		{
			x1 = (s32)(x0 + 0.5);
			adr_in = x1 + y1 * width_in;
			data_out[adr_out] = data_in[adr_in];
			adr_out++;
			x0 += step;
		}
		y0 += step;
	}
	*w_out = width_out;
	*h_out = height_out;
}

//-----------raw12/raw10数据的下采样函数--------------------
// 函数名：	RawData_DownSampleByRatio()
// 输入：	u8 *raw_data_in			raw原图数据指针
//			s32 w, h				raw原图宽高
//			s32 downsample_ratio	下采样率	1/4/16/64
// 输出：	u8 *raw_data_out		输出raw图像数据
//			s32 *w_out, *h_out		输出raw图像宽高
// 返回：	1~succ  0~fail
// 说明：	~
//----------------------------------------------------------
s32 RawData_DownSampleByRatio(u8* raw_data_in, s32 w, s32 h, s32 downsample_ratio, u8* raw_data_out, s32* w_out, s32* h_out)
{
	s32 i, j;
	s32 ret = 1;
	s32 w_tmp1 = 0, w_tmp2 = 0;
	s32 h_tmp1 = 0, h_tmp2 = 0;

	switch (downsample_ratio)
	{
		case 4:
		{
			RawImgDownSampleByRatio4((u16*)raw_data_in, w, h, (u16*)raw_data_out, w_out, h_out);
			break;
		}
		case 16:
		{
			RawImgDownSampleByRatio4((u16*)raw_data_in, w, h, (u16*)g_img_data_tmp_buff1, &w_tmp1, &h_tmp1);
			RawImgDownSampleByRatio4((u16*)g_img_data_tmp_buff1, w_tmp1, h_tmp1, (u16*)raw_data_out, w_out, h_out);
			break;
		}
		case 64:
		{
			RawImgDownSampleByRatio4((u16*)raw_data_in, w, h, (u16*)g_img_data_tmp_buff1, &w_tmp1, &h_tmp1);
			RawImgDownSampleByRatio4((u16*)g_img_data_tmp_buff1, w_tmp1, h_tmp1, (u16*)g_img_data_tmp_buff2, &w_tmp2, &h_tmp2);
			RawImgDownSampleByRatio4((u16*)g_img_data_tmp_buff2, w_tmp2, h_tmp2, (u16*)raw_data_out, w_out, h_out);
			break;
		}
		default:
		{
			ret = 0;
			break;
		}
	}

	return ret;
}

//-----------raw12/raw10数据的下采样函数--------------------
// 函数名：	RawData_DownSampleByWH()
// 输入：	u8 *raw_data_in			raw原图数据指针
//			s32 w, h				raw原图宽高
//			s32 downsample_w/h		下采样图像大小
// 输出：	u8 *raw_data_out		输出raw图像数据
// 返回：	1~succ  0~fail
// 说明：	~
//----------------------------------------------------------
s32 RawData_DownSampleByWH(u8* raw_data_in, s32 w, s32 h, s32 downsample_w, s32 downsample_h, u8* raw_data_out)
{
	s32 i, j;								//circulate parameter
	s32 width_in = w;						//input width
	s32 height_in = h;						//input height
	s32 width_out = downsample_w;			//output_width
	s32 height_out = downsample_h;			//output_height
	u16* data_in = (u16*)raw_data_in;		//input image data
	u16* data_out = (u16*)raw_data_out;		//output image data
	s32 adr_in = 0;							//input image address
	s32 adr_out = 0;						//output image address
	f32 step_w, step_h;						//circulate step
	f32 x0, y0;								//float circulate parameter
	s32 x1, y1;								//int	circulate parameter

	if ( (downsample_h >= h) || (downsample_w >= w) )
	{
		return 0;
	}

	step_w = (f32)(w) / (f32)(downsample_w);
	step_h = (f32)(h) / (f32)downsample_h;

	y0 = 0;
	adr_out = 0;
	for (i = 0; i < height_out; i++)
	{
		y1 = (s32)(y0 + 0.5);
		x0 = 0;
		for (j = 0; j < width_out; j++)
		{
			x1 = (s32)(x0 + 0.5);
			adr_in = x1 + y1 * width_in;
			data_out[adr_out] = data_in[adr_in];
			adr_out++;
			x0 += step_w;
		}
		y0 += step_h;
	}

	return 1;
}

//------raw12/raw10数据的指定比例下采样，并截取raw8数据-----
// 函数名：	RawData_DownSampleByRatioAndGetSelPosRaw8Data()
// 输入：	s32 rawdata_figure		rawdata位数(raw12/raw10,即输入为12或10,未来可拓展)
//			u8 *raw_data_in			raw原图数据指针
//			s32 w, h				raw原图宽高
//			s32 downsample_ratio	下采样率	1/4/16/64
//			s32 set_position		raw数据截取起始位	1/2/3/4/5
// 输出：	u8 *raw8_data_out		输出raw8数据
//			s32 *w_out, *h_out		输出raw图像宽高
// 返回：	1~succ  0~fail
// 说明：	~
//----------------------------------------------------------
s32 RawData_DownSampleByRatioAndGetSelPosRaw8Data(s32 rawdata_figure, u8* raw_data_in, s32 w, s32 h, s32 downsample_ratio, s32 set_position,
	u8* raw8_data_out, s32* w_out, s32* h_out)
{
	s32 ret = 0;
	s32 w_out_tmp = 0;
	s32 h_out_tmp = 0;

	//执行下采样
	ret = RawData_DownSampleByRatio(raw_data_in, w, h, downsample_ratio, g_img_data_tmp_buff1, &w_out_tmp, &h_out_tmp);

	//执行截取raw8数据
	if (ret)
	{
		ret = RawData_GetSelPos_Raw8Data(rawdata_figure, g_img_data_tmp_buff1, w_out_tmp, h_out_tmp, set_position, raw8_data_out);
	}

	*w_out = w_out_tmp;
	*h_out = h_out_tmp;

	return ret;
}

//------raw12/raw10数据的指定大小下采样，并截取raw8数据-----
// 函数名：	RawData_DownSampleByWHAndGetSelPosRaw8Data()
// 输入：	s32 rawdata_figure		rawdata位数(raw12/raw10,即输入为12或10,未来可拓展)
//			u8 *raw_data_in			raw原图数据指针
//			s32 w, h				raw原图宽高
//			s32 downsample_w/h		下采样图像大小
//			s32 set_position		raw数据截取起始位	1/2/3/4/5
// 输出：	u8 *raw8_data_out		输出raw8数据
// 返回：	1~succ  0~fail
// 说明：	~
//----------------------------------------------------------
s32 RawData_DownSampleByWHAndGetSelPosRaw8Data(s32 rawdata_figure, u8* raw_data_in, s32 w, s32 h, s32 downsample_w, s32 downsample_h, s32 set_position,
	u8* raw8_data_out)
{
	s32 ret = 0;

	//执行下采样
	ret = RawData_DownSampleByWH(raw_data_in, w, h, downsample_w, downsample_h, g_img_data_tmp_buff1);

	//执行截取raw8数据
	if (ret)
	{
		ret = RawData_GetSelPos_Raw8Data(rawdata_figure, g_img_data_tmp_buff1, downsample_w, downsample_h, set_position, raw8_data_out);
	}

	return ret;
}


//-----------raw8_Gray数据转rgb8_RGB数据---------------------
// 函数名：	RawData_Raw8GrayToRGB()
// 输入：	u8 *raw_data_in		raw8灰度数据指针
//			s32 w, h			raw8灰度图宽高
// 输出：	u8 *rgb8_data_out	输出rgb8_rgb数据
// 返回：	~
// 说明：	输出数据空间大小，是输入数据空间的三倍
//----------------------------------------------------------
void RawData_Raw8GrayToRGB(u8* raw8_data_in, s32 w, s32 h, u8* rgb8_data_out)
{
	s32 i, j;

	for (i = 0; i < h; i ++)
	{
		for (j = 0; j < w * 3; j ++)
		{
			rgb8_data_out[i * (w * 3) + j] = raw8_data_in[i * w + (j / 3)];
		}
	}

	return;
}

/*******************************************************************************
Function      : AIDete_WriteResult2Buffer()
Description   : 获取 AI 检测计算结果
Input         : img_area_pt : 图像存储空间
              : max_len : buffer 空间最大长度
Output        : buffer : 检测结果输出空间
Return        : MACR_ERROR : 获取失败
                Others : 获取成功（返回值为buffer中数据有效长度）
Author        : DongDL
Create Date   : 2023/05/10
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" s32 AIDete_WriteResult2Buffer(void *ai_dete_rslt_pt, u8 *buffer, u32 max_len)
{
	u32 cnt = 0;
	u16 info_num = 0;
	vector<DetectionBBoxInfo> *result = NULL;
	u32 xmin = 0;		// x_left
	u32 ymin = 0;		// y_top
	u32 xmax = 0;		// x_right
	u32 ymax = 0;		// y_bottom
	u32 score = 0;		// 置信度
	u32 classID = 0;	// 类别ID
	u32 i = 0;
	u8 ai_roi_dete_img_bit = 0;

	if ((ai_dete_rslt_pt == NULL) || 	\
		(buffer == NULL) || 	\
		(max_len == 0))
	{
		return MACR_ERROR;
	}

	// 获取 AI 检测结果
	result = (vector<DetectionBBoxInfo> *)ai_dete_rslt_pt;

	// 缓存 AI 检测实际图层
	for (i = 0; i < MACR_AI_DETE_RESULT_BUFF_SIZE; i++)
	{
		if (result == &(s_ai_dete_result_buff[i].result_global))
		{
			ai_roi_dete_img_bit = (u8)s_ai_dete_result_buff[i].ai_roi_global_dete_img_bit;
			break;
		}

		if (result == &(s_ai_dete_result_buff[i].result_roi))
		{
			ai_roi_dete_img_bit = (u8)s_ai_dete_result_buff[i].ai_roi_center_dete_img_bit;
			break;
		}
	}

	info_num = result->size();
	info_num = MIN(info_num, ((max_len - sizeof(info_num)) / sizeof(DetectionBBoxInfo)));

	// 保存 AI 检测结果的数量
	buffer[cnt++] = ((info_num) & 0xff);
	buffer[cnt++] = ((info_num>>8) & 0xff);
	buffer[cnt++] = ((ai_roi_dete_img_bit) & 0x0f);		// 用低 4Bit 表示 AI 检测实际生效图层，高 4Bit 保留
	// 保留1个字节用于对齐
	cnt += 1;

	// 将AI检测结果保存至buffer
	auto it = result->begin();
	auto end = result->end();
	for (int i = 0; i < info_num && it != end; i++, it++)
	{
		// 取出AI检测结果（这里以u32的格式读取内存，方便后续的移位操作）
		xmin = *(u32 *)(&(it->xmin));
		ymin = *(u32 *)(&(it->ymin));
		xmax = *(u32 *)(&(it->xmax));
		ymax = *(u32 *)(&(it->ymax));
		score = *(u32 *)(&(it->score));
		classID = *(u32 *)(&(it->classID));

		// 将 xmin 写入buffer
		buffer[cnt++] = ((xmin) & 0xff);
		buffer[cnt++] = ((xmin>>8) & 0xff);
		buffer[cnt++] = ((xmin>>16) & 0xff);
		buffer[cnt++] = ((xmin>>24) & 0xff);

		// 将 ymin 写入buffer
		buffer[cnt++] = ((ymin) & 0xff);
		buffer[cnt++] = ((ymin>>8) & 0xff);
		buffer[cnt++] = ((ymin>>16) & 0xff);
		buffer[cnt++] = ((ymin>>24) & 0xff);

		// 将 xmax 写入buffer
		buffer[cnt++] = ((xmax) & 0xff);
		buffer[cnt++] = ((xmax>>8) & 0xff);
		buffer[cnt++] = ((xmax>>16) & 0xff);
		buffer[cnt++] = ((xmax>>24) & 0xff);

		// 将 ymax 写入buffer
		buffer[cnt++] = ((ymax) & 0xff);
		buffer[cnt++] = ((ymax>>8) & 0xff);
		buffer[cnt++] = ((ymax>>16) & 0xff);
		buffer[cnt++] = ((ymax>>24) & 0xff);

		// 将 score 写入buffer
		buffer[cnt++] = ((score) & 0xff);
		buffer[cnt++] = ((score>>8) & 0xff);
		buffer[cnt++] = ((score>>16) & 0xff);
		buffer[cnt++] = ((score>>24) & 0xff);

		// 将 classID 写入buffer
		buffer[cnt++] = ((classID) & 0xff);
		buffer[cnt++] = ((classID>>8) & 0xff);
		buffer[cnt++] = ((classID>>16) & 0xff);
		buffer[cnt++] = ((classID>>24) & 0xff);
	}

	return cnt;
}

/*******************************************************************************
Function      : AIDete_WriteCapDetResult2Buffer()
Description   : 获取 AI 管帽检测计算结果
Input         : img_area_pt : 图像存储空间
              : max_len : buffer 空间最大长度
Output        : buffer : 检测结果输出空间
Return        : MACR_ERROR : 获取失败
                Others : 获取成功（返回值为buffer中数据有效长度）
Author        : TanJW
Create Date   : 2024/08/23
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" s32 AIDete_WriteCapDetResult2Buffer(void *ai_dete_rslt_pt, u8 *buffer, u32 max_len)
{
	u32 cnt = 0;
	u16 info_num = 0;
	vector<DetectionBBoxInfo> *result = NULL;
	u32 xmin = 0;		// x_left
	u32 ymin = 0;		// y_top
	u32 xmax = 0;		// x_right
	u32 ymax = 0;		// y_bottom
	u32 score = 0;		// 置信度
	u32 classID = 0;	// 类别ID
	u32 i = 0;
	u8 ai_roi_dete_img_bit = 0;

	if ((ai_dete_rslt_pt == NULL) || 	\
		(buffer == NULL) || 	\
		(max_len == 0))
	{
		return MACR_ERROR;
	}

	// 记录 AI 检测实际生效图层
	ai_roi_dete_img_bit = s_ai_dete_cap_result_temp.ai_dete_img_bit;

	// 获取 AI 检测结果
	result = (vector<DetectionBBoxInfo> *)ai_dete_rslt_pt;

	info_num = result->size();
	info_num = MIN(info_num, ((max_len - sizeof(info_num)) / sizeof(DetectionBBoxInfo)));

	// 保存 AI 检测结果的数量
	buffer[cnt++] = ((info_num) & 0xff);
	buffer[cnt++] = ((info_num>>8) & 0xff);
	buffer[cnt++] = ((ai_roi_dete_img_bit) & 0x0f);		// 用低 4Bit 表示 AI 检测实际生效图层，高 4Bit 保留
	// 保留1个字节用于对齐
	cnt += 1;

	// 将AI检测结果保存至buffer
	auto it = result->begin();
	auto end = result->end();
	for (int i = 0; i < info_num && it != end; i++, it++)
	{
		// 取出AI检测结果（这里以u32的格式读取内存，方便后续的移位操作）
		xmin = *(u32 *)(&(it->xmin));
		ymin = *(u32 *)(&(it->ymin));
		xmax = *(u32 *)(&(it->xmax));
		ymax = *(u32 *)(&(it->ymax));
		score = *(u32 *)(&(it->score));
		classID = *(u32 *)(&(it->classID));

		// 将 xmin 写入buffer
		buffer[cnt++] = ((xmin) & 0xff);
		buffer[cnt++] = ((xmin>>8) & 0xff);
		buffer[cnt++] = ((xmin>>16) & 0xff);
		buffer[cnt++] = ((xmin>>24) & 0xff);

		// 将 ymin 写入buffer
		buffer[cnt++] = ((ymin) & 0xff);
		buffer[cnt++] = ((ymin>>8) & 0xff);
		buffer[cnt++] = ((ymin>>16) & 0xff);
		buffer[cnt++] = ((ymin>>24) & 0xff);

		// 将 xmax 写入buffer
		buffer[cnt++] = ((xmax) & 0xff);
		buffer[cnt++] = ((xmax>>8) & 0xff);
		buffer[cnt++] = ((xmax>>16) & 0xff);
		buffer[cnt++] = ((xmax>>24) & 0xff);

		// 将 ymax 写入buffer
		buffer[cnt++] = ((ymax) & 0xff);
		buffer[cnt++] = ((ymax>>8) & 0xff);
		buffer[cnt++] = ((ymax>>16) & 0xff);
		buffer[cnt++] = ((ymax>>24) & 0xff);

		// 将 score 写入buffer
		buffer[cnt++] = ((score) & 0xff);
		buffer[cnt++] = ((score>>8) & 0xff);
		buffer[cnt++] = ((score>>16) & 0xff);
		buffer[cnt++] = ((score>>24) & 0xff);

		// 将 classID 写入buffer
		buffer[cnt++] = ((classID) & 0xff);
		buffer[cnt++] = ((classID>>8) & 0xff);
		buffer[cnt++] = ((classID>>16) & 0xff);
		buffer[cnt++] = ((classID>>24) & 0xff);
	}

	return cnt;
}

/*******************************************************************************
Function      : AIDete_ContourDet_WriteResult2Buffer()
Description   : 获取 AI Contour Det检测计算结果
Input         : img_area_pt : 图像存储空间
              : max_len : buffer 空间最大长度
Output        : buffer : 检测结果输出空间
Return        : MACR_ERROR : 获取失败
                Others : 获取成功（返回值为buffer中数据有效长度）
Author        : PengHZ
Create Date   : 2024/05/21
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" s32 AIDete_ContourDet_WriteResult2Buffer(void *ai_dete_rslt_pt, u8 *buffer, u32 max_len)
{
	u32 cnt = 0;
	s32 mem_full_falg = MACR_FALSE;
	u16 info_num = 0;
	u16 points_num = 0;
	u32 x = 0;
	u32 y = 0;
	std::vector<std::vector<Point>> *result = NULL;

	// 写内存方式
	//    结果/多边形数量 n (4Byte, 只使用了前2Byte, 低位在前)
	//        多边形点数量 i1 (4Byte, 只使用了前2Byte, 低位在前)
	//        i1 个 点 (x, y)
	//        下一个多边形点数量 i2
	//        i2 个 点 (x, y)
	//        ....
	//    内存不足时, 最后输出的一个结果可能会截去一部分点

	if ((ai_dete_rslt_pt == NULL) || 	\
		(buffer == NULL) || 	\
		(max_len == 0))
	{
		return MACR_ERROR;
	}

	// 获取 AI 检测结果
	result = (std::vector<std::vector<Point>> *)ai_dete_rslt_pt;

	// 预留四Btye空位进行保存检测结果的数量
	cnt += 4;

	for (auto &polygon : *result)
	{
		points_num = polygon.size();

		// 检查
		if ((points_num * sizeof(Point) + 4) > (max_len - cnt))
		{
			// 内存不足，进行数据截断
			points_num = (max_len - cnt - 4) / sizeof(Point);
			mem_full_falg = MACR_TRUE;
		}

		// 保存 AI 检测结果的数量
		buffer[cnt++] = ((points_num) & 0xff);
		buffer[cnt++] = ((points_num>>8) & 0xff);
		// 保留两个字节用于对齐
		cnt += 2;

		auto it = polygon.begin();
		auto end = polygon.end();
		for (int i = 0; i < points_num && it != end; i++, it ++)
		{
			// 取出AI检测结果（这里以u32的格式读取内存，方便后续的移位操作）
			x = *(u32 *)(&(it->x));
			y = *(u32 *)(&(it->y));
			// 将 x 写入buffer
			buffer[cnt++] = ((x) & 0xff);
			buffer[cnt++] = ((x>>8) & 0xff);
			buffer[cnt++] = ((x>>16) & 0xff);
			buffer[cnt++] = ((x>>24) & 0xff);
			// 将 y 写入buffer
			buffer[cnt++] = ((y) & 0xff);
			buffer[cnt++] = ((y>>8) & 0xff);
			buffer[cnt++] = ((y>>16) & 0xff);
			buffer[cnt++] = ((y>>24) & 0xff);
		}

		info_num ++;

		// 内存已满, 
		if (mem_full_falg == MACR_TRUE)
		{
			break;
		}
	}

	// 补充结果数量
	buffer[0] = ((info_num) & 0xff);
	buffer[1] = ((info_num>>8) & 0xff);

	return cnt;
}

/*******************************************************************************
Function      : AIDete_OCRDet_WriteResult2Buffer()
Description   : 获取 AI OCR_Det检测计算结果
Input         : img_area_pt : 图像存储空间
              : max_len : buffer 空间最大长度
Output        : buffer : 检测结果输出空间
Return        : MACR_ERROR : 获取失败
                Others : 获取成功（返回值为buffer中数据有效长度）
Author        : PengHZ
Create Date   : 2024/05/21
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" s32 AIDete_OCRDet_WriteResult2Buffer(void *ai_dete_rslt_pt, u8 *buffer, u32 max_len)
{
	u32 cnt = 0;
	u16 info_num = 0;
	vector<QuadPoints> *result = NULL;
	u32 x0, y0;
	u32 x1, y1;
	u32 x2, y2;
	u32 x3, y3;

	if ((ai_dete_rslt_pt == NULL) || 	\
		(buffer == NULL) || 	\
		(max_len == 0))
	{
		return MACR_ERROR;
	}

	// 获取 AI 检测结果
	result = (vector<QuadPoints> *)ai_dete_rslt_pt;
	// 获取 AI 检测结果的数量
	info_num = result->size();
	info_num = MIN(info_num, ((max_len - 4) / sizeof(QuadPoints)));

	// 保存 AI 检测结果的数量
	buffer[cnt++] = ((info_num) & 0xff);
	buffer[cnt++] = ((info_num>>8) & 0xff);
	// 保留两个字节用于对齐
	cnt += 2;

	// 将AI检测结果保存至buffer
	auto it = result->begin();
	auto end = result->end();
	for (int i = 0; i < info_num && it != end; i++, it++)
	{
		// 取出AI检测结果（这里以u32的格式读取内存，方便后续的移位操作）
		x0 = *(u32 *)(&(it->x0));
		y0 = *(u32 *)(&(it->y0));
		x1 = *(u32 *)(&(it->x1));
		y1 = *(u32 *)(&(it->y1));
		x2 = *(u32 *)(&(it->x2));
		y2 = *(u32 *)(&(it->y2));
		x3 = *(u32 *)(&(it->x3));
		y3 = *(u32 *)(&(it->y3));

		// 将 x0 写入buffer
		buffer[cnt++] = ((x0) & 0xff);
		buffer[cnt++] = ((x0>>8) & 0xff);
		buffer[cnt++] = ((x0>>16) & 0xff);
		buffer[cnt++] = ((x0>>24) & 0xff);

		// 将 y0 写入buffer
		buffer[cnt++] = ((y0) & 0xff);
		buffer[cnt++] = ((y0>>8) & 0xff);
		buffer[cnt++] = ((y0>>16) & 0xff);
		buffer[cnt++] = ((y0>>24) & 0xff);

		// 将 x1 写入buffer
		buffer[cnt++] = ((x1) & 0xff);
		buffer[cnt++] = ((x1>>8) & 0xff);
		buffer[cnt++] = ((x1>>16) & 0xff);
		buffer[cnt++] = ((x1>>24) & 0xff);

		// 将 y1 写入buffer
		buffer[cnt++] = ((y1) & 0xff);
		buffer[cnt++] = ((y1>>8) & 0xff);
		buffer[cnt++] = ((y1>>16) & 0xff);
		buffer[cnt++] = ((y1>>24) & 0xff);

		// 将 x2 写入buffer
		buffer[cnt++] = ((x2) & 0xff);
		buffer[cnt++] = ((x2>>8) & 0xff);
		buffer[cnt++] = ((x2>>16) & 0xff);
		buffer[cnt++] = ((x2>>24) & 0xff);

		// 将 y2 写入buffer
		buffer[cnt++] = ((y2) & 0xff);
		buffer[cnt++] = ((y2>>8) & 0xff);
		buffer[cnt++] = ((y2>>16) & 0xff);
		buffer[cnt++] = ((y2>>24) & 0xff);

		// 将 x3 写入buffer
		buffer[cnt++] = ((x3) & 0xff);
		buffer[cnt++] = ((x3>>8) & 0xff);
		buffer[cnt++] = ((x3>>16) & 0xff);
		buffer[cnt++] = ((x3>>24) & 0xff);

		// 将 y3 写入buffer
		buffer[cnt++] = ((y3) & 0xff);
		buffer[cnt++] = ((y3>>8) & 0xff);
		buffer[cnt++] = ((y3>>16) & 0xff);
		buffer[cnt++] = ((y3>>24) & 0xff);
	}

	return cnt;
}

/*******************************************************************************
Function      : AIDete_OCRGetString_WriteResult2Buffer()
Description   : 获取 AI OCR_GetString 检测计算结果
Input         : img_area_pt : 图像存储空间
              : max_len : buffer 空间最大长度
Output        : buffer : 检测结果输出空间
Return        : MACR_ERROR : 获取失败
                Others : 获取成功（返回值为buffer中数据有效长度）
Author        : TanJW
Create Date   : 2024/05/24
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" s32 AIDete_OCRGetString_WriteResult2Buffer(IN void *ai_dete_rslt_pt, OUT u8 *buffer, IN u32 max_len)
{
	u32 cnt = 0;
	vector<OCRCharacter> *result = NULL;
	u32 size = 0;
	u16 info_num = 0;
	u32 score = 0;
	u8 character = 0;

	if ((ai_dete_rslt_pt == NULL) || 	\
		(buffer == NULL) || 	\
		(max_len == 0))
	{
		return MACR_ERROR;
	}

	// 获取 AI 检测结果
	result = (vector<OCRCharacter> *)ai_dete_rslt_pt;

	cnt = 4;		// 预留 4 个字节用来存储字符个数

	// 以下算式中，"5" 实际上是 sizeof(OCRCharacter)，但因编译器的自动对齐机制，导致其向上对齐
	// 现为尽可能不浪费空间，则手动给值"5"
	info_num = MIN(result->size(), (max_len - 4) / 5);

	// 将AI检测结果保存至buffer
	auto it = result->begin();
	auto end = result->end();
	for (int i = 0; i < info_num && it != end; i++, it++)
	{
		// 取出AI检测结果（这里以u32的格式读取内存，方便后续的移位操作）
		character = *(u8 *)(&(it->character));
		score = *(u32 *)(&(it->score));

		// 将 character 写入 buffer
		buffer[cnt++] = character;

		// 将 score 写入 buffer
		buffer[cnt++] = ((score) & 0xff);
		buffer[cnt++] = ((score>>8) & 0xff);
		buffer[cnt++] = ((score>>16) & 0xff);
		buffer[cnt++] = ((score>>24) & 0xff);
	}

	// 更新数据量大小
	size = info_num;

	buffer[0] = (u8)((size) & 0xff);
	buffer[1] = (u8)((size >> 8) & 0xff);
	buffer[2] = (u8)((size >> 16) & 0xff);
	buffer[3] = (u8)((size >> 24) & 0xff);

	return cnt;
}

/*******************************************************************************
Function      : AIDete_Restorer_WriteResult2Buffer()
Description   : 将 AI 检测结果以数组形式写入 buffer
Input         : ai_dete_rslt_pt : AI 检测结果
              : buffer : 数组形式的 AI 检测结果
              : max_len : buffer 可写入区域的长度
Output        : buffer : 数组形式的 AI 检测结果
Return        : MACR_ERROR : 写入失败
                Others : 写入成功（返回值为buffer中数据有效长度）
Author        : PengHZ
Create Date   : 2025/04/16
Last Update   : 2025/04/16
Note          : None
*******************************************************************************/
extern "C" s32 AIDete_Restorer_WriteResult2Buffer(void *ai_dete_rslt_pt, u8 *buffer, u32 max_len)
{
	AIEngine::Tensor<uint8_t> *result = NULL;
	u32 pack_size = 0;
	u32 img_size = 0;
	u32 img_row = 0;
	u32 img_col = 0;
	u32 cnt = 0;

	const u32 RESERVE_SIZE = 128;

	result = (AIEngine::Tensor<uint8_t> *)ai_dete_rslt_pt;

	if ((ai_dete_rslt_pt == NULL) || 	\
		(buffer == NULL) || 	\
		(max_len == 0))
	{
		return MACR_ERROR;
	}

	img_row = result->Shape()[0];
	img_col = result->Shape()[1];
	img_size = img_row * img_col * 1;
	pack_size = RESERVE_SIZE + img_size;

	// 空间判断
	if (pack_size > max_len)
	{
		return MACR_ERROR;
	}

	cnt = 0;
	// 写入总大小
	buffer[cnt++] = (u8)((pack_size>>0 ) & 0xFF);
	buffer[cnt++] = (u8)((pack_size>>8 ) & 0xFF);
	buffer[cnt++] = (u8)((pack_size>>16) & 0xFF);
	buffer[cnt++] = (u8)((pack_size>>24) & 0xFF);
	// 写入图像尺寸
	buffer[cnt++] = (u8)((img_row>>0) & 0xFF);
	buffer[cnt++] = (u8)((img_row>>8) & 0xFF);
	buffer[cnt++] = (u8)((img_col>>0) & 0xFF);
	buffer[cnt++] = (u8)((img_col>>8) & 0xFF);

	// 写入图像数据
	memcpy(&buffer[RESERVE_SIZE], result->Host(), img_size);

	return pack_size;
}

/*******************************************************************************
Function      : AIDete_OCRGetStringPosition()
Description   : 将 OCR 字符坐标以数组形式写入 buffer
Input         : img_area_pt : 图像存储空间
              : max_len : buffer 空间最大长度
Output        : buffer : 检测结果输出空间
Return        : MACR_ERROR : 获取失败
                Others : 获取成功（返回值为buffer中数据有效长度）
Author        : TanJW
Create Date   : 2024/05/24
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" s32 AIDete_OCRGetStringPosition(IN void *ai_dete_rslt_pt, OUT u8 *buffer, IN u32 max_len)
{
	u32 cnt = 0;
	vector<QuadPoints> *result = NULL;
	u32 size = 0;

	if ((ai_dete_rslt_pt == NULL) || 	\
		(buffer == NULL) || 	\
		(max_len == 0))
	{
		return MACR_ERROR;
	}

	// Reserve

	return size;
}

/*******************************************************************************
Function      : AIDete_Classification_WriteResult2Buffer()
Description   : 获取 AI 分类检测计算结果
Input         : ai_dete_rslt_pt : AI 检测结果
              : max_len : buffer 空间最大长度
Output        : buffer : 检测结果输出空间
Return        : MACR_ERROR : 获取失败
                Others : 获取成功（返回值为buffer中数据有效长度）
Author        : TangHL
Create Date   : 2025/07/24
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" s32 AIDete_Classification_WriteResult2Buffer(void *ai_dete_rslt_pt, u8 *buffer, u32 max_len)
{
	u32 cnt = 0;
	vector<ClassificationResult> *result = NULL;
	u32 size = 0;
	u16 info_num = 0;
	u32 score = 0;
	u32 classID = 0;

	if ((ai_dete_rslt_pt == NULL) || 	\
		(buffer == NULL) || 	\
		(max_len == 0))
	{
		return MACR_ERROR;
	}

	// 获取 AI 分类检测结果
	result = (vector<ClassificationResult> *)ai_dete_rslt_pt;

	cnt = 4;		// 预留 4 个字节用来存储分类结果个数

	// 计算可以存储的分类结果数量
	// 每个分类结果需要 8 个字节：4字节classID + 4字节score
	info_num = MIN(result->size(), (max_len - 4) / 8);

	// 将AI分类检测结果保存至buffer
	auto it = result->begin();
	auto end = result->end();
	for (int i = 0; i < info_num && it != end; i++, it++)
	{
		// 取出AI分类检测结果
		classID = (u32)(it->classID);
		score = *(u32 *)(&(it->score));

		// 将 classID 写入 buffer (4字节)
		buffer[cnt++] = ((classID) & 0xff);
		buffer[cnt++] = ((classID>>8) & 0xff);
		buffer[cnt++] = ((classID>>16) & 0xff);
		buffer[cnt++] = ((classID>>24) & 0xff);

		// 将 score 写入 buffer (4字节)
		buffer[cnt++] = ((score) & 0xff);
		buffer[cnt++] = ((score>>8) & 0xff);
		buffer[cnt++] = ((score>>16) & 0xff);
		buffer[cnt++] = ((score>>24) & 0xff);
	}

	// 更新数据量大小
	size = info_num;

	// 将分类结果数量写入buffer前4个字节
	buffer[0] = (u8)((size) & 0xff);
	buffer[1] = (u8)((size >> 8) & 0xff);
	buffer[2] = (u8)((size >> 16) & 0xff);
	buffer[3] = (u8)((size >> 24) & 0xff);

	return cnt;
}

/*******************************************************************************
Function      : AIDete_ROI_Dete()
Description   : AI ROI 检测函数
Input         : img_area_pt : 图像存储空间
              : roi_ena : 是否开启ROI检测
              : roi_coord : ROI检测区域
Output        : None
Return        : NULL : 检测失败
                Others : AI 检测结果
Author        : DongDL
Create Date   : 2023/07/07
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" void *AIDete_ROI_Dete(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN CoordinateVOC *roi_coord)
{
	void *ret_val = NULL;
	s32 is_dla_config_succ = MACR_TRUE;
	ImageInfo image_info = { 0 };
	vector<DetectionBBoxInfo> *result_pt = NULL;
	ai_dete_model_info *model_info_pt = NULL;
	u64 start_us = 0;
	s32 err_val = MACR_NONE;

	// 查找 ROI 检测模型对象
	model_info_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_ROI_DETECT, MACR_TRUE);
	if (model_info_pt == NULL)
	{
		// 没有找到/未运行中, 则查找替代模型
		model_info_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_ROI_DETECT_ALT, MACR_TRUE);
	}

	if (model_info_pt == NULL)
	{
		return NULL;
	}

	// 判断传入参数地有效性
	if ((img_area_pt != MACR_NONE) && 	\
		(img_area_pt->pImgArea != NULL))
	{
		// 清空旧的信息
		s_ai_dete_roi_result_temp.clear();
		// 暂存相关信息
		result_pt = (&s_ai_dete_roi_result_temp);
		image_info.img_data_pt = img_area_pt->pImgArea;
		image_info.img_height = img_area_pt->height;
		image_info.img_width = img_area_pt->width;
		image_info.color_format = COLOR_FORMAT_GRAY;

		// RAW12格式图像
		if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW12)
		{
			image_info.bit_depth = BIT_DEPTH_RAW12;
			switch (img_area_pt->ai_dete_raw8_img_bit)
			{
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_ADAPTIVE: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_0_7: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_00_07; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_1_8: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_01_08; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_2_9: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_02_09; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_3_10: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_03_10; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_4_11: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_04_11; break;
				default: is_dla_config_succ = MACR_FALSE; break;
			}
		}
		// RAW10格式图像
		else if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW10)
		{
			image_info.bit_depth = BIT_DEPTH_RAW10;
			switch (img_area_pt->ai_dete_raw8_img_bit)
			{
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_ADAPTIVE: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_0_7: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_00_07; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_1_8: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_01_08; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_2_9: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_02_09; break;
				default: is_dla_config_succ = MACR_FALSE; break;
			}
		}
		// 其他格式图像
		else
		{
			// AI 模块暂时不支持其他格式图像的检测，此处直接退出并返回AI检测失败
			is_dla_config_succ = MACR_FALSE;
		}

		// 更具当前信息选择检测前处理类型
		if (roi_ena == MACR_TRUE)
		{
			if (roi_coord == NULL)
			{
				image_info.prep_type = PREP_TYPE_ROI_CETNER;
			}
			else
			{
				image_info.prep_type = PREP_TYPE_ROI_DEP_ON_COORD;
				image_info.roi_coord = *roi_coord;
			}
		}
		else
		{
			image_info.prep_type = PREP_TYPE_FULL_IMG_RESIZE;
		}

		// 只有dla配置成功时才允许进行计算
		if (is_dla_config_succ == MACR_TRUE)
		{
			// 进行计算
			start_us = read_system_time_us();
			err_val = ai_engine_task_detection(model_info_pt->model_tag, &image_info, result_pt);
			model_info_pt->calcRecord(
					(s_ai_dete_roi_result_temp.size()), 
					(read_system_time_us() - start_us),
					 err_val);
			if (err_val == MACR_NONE)
			{
				// 计算成功，记录结果
				ret_val = (void *)(&s_ai_dete_roi_result_temp);
			}
		}
	}

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_Contour_Det()
Description   : AI Contour_Det 检测函数
Input         : img_area_pt : 图像存储空间
              : roi_ena : 是否开启ROI检测
              : roi_coord : ROI检测区域
Output        : None
Return        : NULL : 检测失败
                Others : AI 检测结果
Author        : PengHZ
Create Date   : 2024/11/01
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" void *AIDete_Contour_Det(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN CoordinateVOC *roi_coord)
{
	void *ret_val = NULL;
	u32 roi_width = 0;
	u32 roi_height = 0;
	s32 is_dla_config_succ = MACR_TRUE;
	std::vector<std::vector<Point>> *result_pt = NULL;
	std::vector<int> classes;
	ai_dete_model_info *model_info_pt = NULL;
	u64 start_us = 0;
	s32 err_val = MACR_NONE;

	// 查找AI模型对象
	model_info_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_CONTOUR_DETECT, MACR_TRUE);
	if (model_info_pt == NULL)
	{
		return NULL;
	}

	ImageInfo image_info = { 0 };

	// 判断传入参数地有效性
	if ((img_area_pt != MACR_NONE) && 	\
		(img_area_pt->pImgArea != NULL))
	{
		// 清空旧的信息
		for (auto &innverVec : s_ai_dete_contour_result_temp)
		{
			innverVec.clear();
		}
		s_ai_dete_contour_result_temp.clear();

		// 暂存相关信息
		result_pt = (&s_ai_dete_contour_result_temp);
		image_info.img_data_pt = img_area_pt->pImgArea;
		image_info.img_height = img_area_pt->height;
		image_info.img_width = img_area_pt->width;
		image_info.color_format = COLOR_FORMAT_GRAY;

		// RAW12格式图像
		if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW12)
		{
			image_info.bit_depth = BIT_DEPTH_RAW12;
			switch (img_area_pt->ai_dete_raw8_img_bit)
			{
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_ADAPTIVE: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_0_7: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_00_07; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_1_8: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_01_08; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_2_9: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_02_09; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_3_10: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_03_10; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_4_11: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_04_11; break;
				default: is_dla_config_succ = MACR_FALSE; break;
			}
		}
		// RAW10格式图像
		else if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW10)
		{
			image_info.bit_depth = BIT_DEPTH_RAW10;
			switch (img_area_pt->ai_dete_raw8_img_bit)
			{
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_ADAPTIVE: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_0_7: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_00_07; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_1_8: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_01_08; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_2_9: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_02_09; break;
				default: is_dla_config_succ = MACR_FALSE; break;
			}
		}
		// 其他格式图像
		else
		{
			// AI 模块暂时不支持其他格式图像的检测，此处直接退出并返回AI检测失败
			is_dla_config_succ = MACR_FALSE;
		}

		// 只有dla配置成功时才允许进行计算
		if (is_dla_config_succ == MACR_TRUE)
		{
			// 根据ROI的尺寸选择用于 AI 检测的模型
			if ((roi_ena == MACR_FALSE) || (roi_coord == NULL))
			{
				// 进行计算,未指定ROI尺寸或关闭ROI检测时，进行全图检测
				image_info.prep_type = PREP_TYPE_FULL_IMG_RESIZE;
				start_us = read_system_time_us();
				err_val = ai_engine_task_semantic_segmentation(model_info_pt->model_tag, &image_info, MACR_NULL, result_pt, &classes);
				model_info_pt->calcRecord(
					(s_ai_dete_contour_result_temp.size()), 
					(read_system_time_us() - start_us),
					 err_val);
				if (err_val == MACR_NONE)
				{
					// 计算成功，记录结果
					ret_val = (void *)(&s_ai_dete_contour_result_temp);
				}
			}
			else
			{
				if (roi_coord != NULL)
				{
					image_info.roi_coord = *roi_coord;

					// 计算ROI的尺寸，此处必须与底层的计算逻辑保持一致，否则可能导致检测失败
					roi_width = (u32)(roi_coord->xmax - roi_coord->xmin + 1);
					roi_height = (u32)(roi_coord->ymax - roi_coord->ymin + 1);
				}

				image_info.prep_type = PREP_TYPE_ROI_RESIZE_ADAPTIVE_DEP_ON_COORD;

				// 进行计算
				start_us = read_system_time_us();
				err_val = ai_engine_task_semantic_segmentation(model_info_pt->model_tag, &image_info, MACR_NULL, result_pt, &classes);
				model_info_pt->calcRecord(
					(s_ai_dete_contour_result_temp.size()), 
					(read_system_time_us() - start_us),
					 err_val);
				if (err_val == MACR_NONE)
				{
					// 计算成功，记录结果
					ret_val = (void *)(&s_ai_dete_contour_result_temp);
				}
			}
		}
	}

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_Finder_Dete()
Description   : AI 标识符 检测函数
Input         : img_area_pt : 图像存储空间
              : roi_ena : 是否开启ROI检测
              : roi_coord : ROI检测区域
Output        : None
Return        : NULL : 检测失败
                Others : AI 检测结果
Author        : DongDL
Create Date   : 2023/07/07
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" void *AIDete_Finder_Dete(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN CoordinateVOC *roi_coord)
{
	void *ret_val = NULL;
	u32 roi_width = 0;
	u32 roi_height = 0;
	s32 is_dla_config_succ = MACR_TRUE;
	vector<DetectionBBoxInfo> *result_pt = NULL;
	ImageInfo image_info = { 0 };
	ai_dete_model_info *model_finder_320_pt = NULL;
	ai_dete_model_info *model_finder_640_pt = NULL;
	u64 start_us = 0;
	s32 err_val = MACR_NONE;

	// 查找AI模型对象 此处需要考虑两个模型
	model_finder_320_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_FINDER_320, MACR_TRUE);
	model_finder_640_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_FINDER_640, MACR_TRUE);

	// 此处如果能查找到其中一个模型, 则按后续流程, 还是有可能检测成功的
	if ((model_finder_320_pt == NULL) && (model_finder_640_pt == NULL))
	{
		return NULL;
	}

	// 判断传入参数地有效性
	if ((img_area_pt != MACR_NONE) && 	\
		(img_area_pt->pImgArea != NULL))
	{
		// 清空旧的信息
		s_ai_dete_finder_result_temp.clear();
		// 暂存相关信息
		result_pt = (&s_ai_dete_finder_result_temp);
		image_info.img_data_pt = img_area_pt->pImgArea;
		image_info.img_height = img_area_pt->height;
		image_info.img_width = img_area_pt->width;
		image_info.color_format = COLOR_FORMAT_GRAY;

		// RAW12格式图像
		if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW12)
		{
			image_info.bit_depth = BIT_DEPTH_RAW12;
			switch (img_area_pt->ai_dete_raw8_img_bit)
			{
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_ADAPTIVE: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_0_7: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_00_07; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_1_8: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_01_08; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_2_9: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_02_09; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_3_10: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_03_10; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_4_11: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_04_11; break;
				default: is_dla_config_succ = MACR_FALSE; break;
			}
		}
		// RAW10格式图像
		else if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW10)
		{
			image_info.bit_depth = BIT_DEPTH_RAW10;
			switch (img_area_pt->ai_dete_raw8_img_bit)
			{
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_ADAPTIVE: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_0_7: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_00_07; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_1_8: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_01_08; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_2_9: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_02_09; break;
				default: is_dla_config_succ = MACR_FALSE; break;
			}
		}
		// 其他格式图像
		else
		{
			// AI 模块暂时不支持其他格式图像的检测，此处直接退出并返回AI检测失败
			is_dla_config_succ = MACR_FALSE;
		}

		// 只有dla配置成功时才允许进行计算
		if (is_dla_config_succ == MACR_TRUE)
		{
			// 根据ROI的尺寸选择用于 AI 检测的模型
			if ((roi_ena == MACR_FALSE) || (roi_coord == NULL))
			{
				image_info.prep_type = PREP_TYPE_ROI_CETNER;
				// 进行计算,未指定ROI尺寸或关闭ROI检测时，使用尺寸为640的模型进行检测
				if (model_finder_640_pt != NULL)
				{
					start_us = read_system_time_us();
					err_val = ai_engine_task_detection(model_finder_640_pt->model_tag, &image_info, result_pt);
					model_finder_640_pt->calcRecord(
						(s_ai_dete_finder_result_temp.size()), 
						(read_system_time_us() - start_us),
						err_val);
					if (err_val == MACR_NONE)
					{
						// 计算成功，记录结果
						ret_val = (void *)(&s_ai_dete_finder_result_temp);
					}
				}
			}
			else
			{
				if (roi_coord != NULL)
				{
					image_info.roi_coord = *roi_coord;

					// 计算ROI的尺寸，此处必须与底层的计算逻辑保持一致，否则可能导致检测失败
					roi_width = (u32)(roi_coord->xmax - roi_coord->xmin + 1);
					roi_height = (u32)(roi_coord->ymax - roi_coord->ymin + 1);
				}

				// 根据ROI区域的大小选择可以用于检测该区域标识符，尺寸最小的模型
				// 判断320*320尺寸的模型能够满足检测需求
				if ((roi_width <= 320) && (roi_height <= 320))
				{
					image_info.prep_type = PREP_TYPE_ROI_DEP_ON_COORD;

					// 进行计算
					if (model_finder_320_pt != NULL)
					{
						start_us = read_system_time_us();
						err_val = ai_engine_task_detection(model_finder_320_pt->model_tag, &image_info, result_pt);
						model_finder_320_pt->calcRecord(
							(s_ai_dete_finder_result_temp.size()), 
							(read_system_time_us() - start_us),
							 err_val);
						if (err_val == MACR_NONE)
						{
							// 计算成功，记录结果
							ret_val = (void *)(&s_ai_dete_finder_result_temp);
						}
					}
				}
				// 如果 320*320 尺寸的模型不能够满足检测需求，判断 640*640 尺寸的模型是否能够满足检测需求
				else if ((roi_width <= 640) && (roi_height <= 640))
				{
					image_info.prep_type = PREP_TYPE_ROI_DEP_ON_COORD;

					// 进行计算
					if (model_finder_640_pt != NULL)
					{
						start_us = read_system_time_us();
						err_val = ai_engine_task_detection(model_finder_640_pt->model_tag, &image_info, result_pt);
						model_finder_640_pt->calcRecord(
							(s_ai_dete_finder_result_temp.size()), 
							(read_system_time_us() - start_us),
							 err_val);
						if (err_val == MACR_NONE)
						{
							// 计算成功，记录结果
							ret_val = (void *)(&s_ai_dete_finder_result_temp);
						}
					}
				}
				else
				{
					image_info.prep_type = PREP_TYPE_ROI_RESIZE_ADAPTIVE_DEP_ON_COORD;

					// 进行计算
					if (model_finder_320_pt != NULL)
					{
						start_us = read_system_time_us();
						err_val = ai_engine_task_detection(model_finder_320_pt->model_tag, &image_info, result_pt);
						model_finder_320_pt->calcRecord(
							(s_ai_dete_finder_result_temp.size()), 
							(read_system_time_us() - start_us),
							 err_val);
						if (err_val == MACR_NONE)
						{
							// 计算成功，记录结果
							ret_val = (void *)(&s_ai_dete_finder_result_temp);
						}
					}
				}
			}
		}
	}

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_Dot_Dete()
Description   : AI Dot 检测函数
Input         : img_area_pt : 图像存储空间
              : roi_ena : 是否开启ROI检测
              : roi_coord : ROI检测区域
Output        : None
Return        : NULL : 检测失败
                Others : AI 检测结果
Author        : DongDL
Create Date   : 2023/12/19
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" void *AIDete_Dot_Dete(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN CoordinateVOC *roi_coord)
{
	void *ret_val = NULL;
	u32 roi_width = 0;
	u32 roi_height = 0;
	s32 is_dla_config_succ = MACR_TRUE;
	vector<DetectionBBoxInfo> *result_pt = NULL;
	ImageInfo image_info = { 0 };
	ai_dete_model_info *model_info_pt = NULL;
	u64 start_us = 0;
	s32 err_val = MACR_NONE;

	// 查找AI模型对象
	model_info_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_DOT_320, MACR_TRUE);
	if (model_info_pt == NULL)
	{
		return NULL;
	}

	// 判断传入参数地有效性
	if ((img_area_pt != MACR_NONE) && 	\
		(img_area_pt->pImgArea != NULL))
	{
		// 清空旧的信息
		s_ai_dete_dot_result_temp.clear();
		// 暂存相关信息
		result_pt = (&s_ai_dete_dot_result_temp);
		image_info.img_data_pt = img_area_pt->pImgArea;
		image_info.img_height = img_area_pt->height;
		image_info.img_width = img_area_pt->width;
		image_info.color_format = COLOR_FORMAT_GRAY;

		// RAW12格式图像
		if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW12)
		{
			image_info.bit_depth = BIT_DEPTH_RAW12;
			switch (img_area_pt->ai_dete_raw8_img_bit)
			{
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_ADAPTIVE: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_0_7: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_00_07; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_1_8: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_01_08; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_2_9: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_02_09; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_3_10: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_03_10; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_4_11: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_04_11; break;
				default: is_dla_config_succ = MACR_FALSE; break;
			}
		}
		// RAW10格式图像
		else if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW10)
		{
			image_info.bit_depth = BIT_DEPTH_RAW10;
			switch (img_area_pt->ai_dete_raw8_img_bit)
			{
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_ADAPTIVE: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_0_7: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_00_07; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_1_8: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_01_08; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_2_9: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_02_09; break;
				default: is_dla_config_succ = MACR_FALSE; break;
			}
		}
		// 其他格式图像
		else
		{
			// AI 模块暂时不支持其他格式图像的检测，此处直接退出并返回AI检测失败
			is_dla_config_succ = MACR_FALSE;
		}

		// 只有dla配置成功时才允许进行计算
		if (is_dla_config_succ == MACR_TRUE)
		{
			// 根据ROI的尺寸选择用于 AI 检测的模型
			if ((roi_ena == MACR_FALSE) || (roi_coord == NULL))
			{
				image_info.prep_type = PREP_TYPE_ROI_CETNER;
				// 进行计算,未指定ROI尺寸或关闭ROI检测时，使用尺寸为640的模型进行检测
				start_us = read_system_time_us();
				err_val = ai_engine_task_detection(model_info_pt->model_tag, &image_info, result_pt);
				model_info_pt->calcRecord(
					(s_ai_dete_dot_result_temp.size()), 
					(read_system_time_us() - start_us),
					 err_val);
				if (err_val == MACR_NONE)
				{
					// 计算成功，记录结果
					ret_val = (void *)(&s_ai_dete_dot_result_temp);
				}
			}
			else
			{
				if (roi_coord != NULL)
				{
					image_info.roi_coord = *roi_coord;

					// 计算ROI的尺寸，此处必须与底层的计算逻辑保持一致，否则可能导致检测失败
					roi_width = (u32)(roi_coord->xmax - roi_coord->xmin + 1);
					roi_height = (u32)(roi_coord->ymax - roi_coord->ymin + 1);
				}

				// 根据ROI区域的大小选择可以用于检测该区域标识符，尺寸最小的模型
				// 判断320*320尺寸的模型能够满足检测需求
				if ((roi_width <= 320) && (roi_height <= 320))
				{
					image_info.prep_type = PREP_TYPE_ROI_DEP_ON_COORD;
					// 进行计算,未指定ROI尺寸或关闭ROI检测时，使用尺寸为640的模型进行检测
					start_us = read_system_time_us();
					err_val = ai_engine_task_detection(model_info_pt->model_tag, &image_info, result_pt);
					model_info_pt->calcRecord(
						(s_ai_dete_dot_result_temp.size()), 
						(read_system_time_us() - start_us),
						 err_val);
					if (err_val == MACR_NONE)
					{
						// 计算成功，记录结果
						ret_val = (void *)(&s_ai_dete_dot_result_temp);
					}
				}
				// 如果模型不能够满足检测区域大小的需求，则对检测区域进行下采样后检测
				else
				{
					image_info.prep_type = PREP_TYPE_ROI_RESIZE_ADAPTIVE_DEP_ON_COORD;
					// 进行计算,未指定ROI尺寸或关闭ROI检测时，使用尺寸为640的模型进行检测
					start_us = read_system_time_us();
					err_val = ai_engine_task_detection(model_info_pt->model_tag, &image_info, result_pt);
					model_info_pt->calcRecord(
						(s_ai_dete_dot_result_temp.size()), 
						(read_system_time_us() - start_us),
						 err_val);
					if (err_val == MACR_NONE)
					{
						// 计算成功，记录结果
						ret_val = (void *)(&s_ai_dete_dot_result_temp);
					}
				}
			}
		}
	}

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_Restorer()
Description   : AI 复原检测函数
Input         : img_area_pt : 图像存储空间
              : roi_ena : 是否开启ROI检测
              : roi_coord : ROI检测区域
Output        : None
Return        : NULL : 检测失败
                Others : AI 检测结果
Author        : PengHZ
Create Date   : 2025/04/11
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" void *AIDete_Restorer(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN CoordinateVOC *roi_coord)
{
	void *ret_val = NULL;
	s32 is_dla_config_succ = MACR_TRUE;
	AIEngine::Tensor<uint8_t> *result_pt = NULL;
	ImageInfo image_info = { 0 };
	ai_dete_model_info *model_info_pt = NULL;
	u64 start_us = 0;
	s32 err_val = MACR_NONE;

	// 查找 ROI 检测模型对象
	model_info_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_RESTORER, MACR_TRUE);
	if (model_info_pt == NULL)
	{
		return NULL;
	}

	// 判断传入参数地有效性
	if ((img_area_pt != MACR_NONE) && 	\
		(img_area_pt->pImgArea != NULL))
	{
		// 清空旧的信息
		// s_ai_dete_restorer_result_temp.clear(); 内部使用C++智能指针，在计算时，会自动清空等操作
		// 暂存相关信息
		result_pt = (&s_ai_dete_restorer_result_temp);
		image_info.img_data_pt = img_area_pt->pImgArea;
		image_info.img_height = img_area_pt->height;
		image_info.img_width = img_area_pt->width;
		image_info.color_format = COLOR_FORMAT_GRAY;

		// RAW12格式图像
		if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW12)
		{
			image_info.bit_depth = BIT_DEPTH_RAW12;
			switch (img_area_pt->ai_dete_raw8_img_bit)
			{
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_ADAPTIVE: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_0_7: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_00_07; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_1_8: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_01_08; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_2_9: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_02_09; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_3_10: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_03_10; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_4_11: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_04_11; break;
				default: is_dla_config_succ = MACR_FALSE; break;
			}
		}
		// RAW10格式图像
		else if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW10)
		{
			image_info.bit_depth = BIT_DEPTH_RAW10;
			switch (img_area_pt->ai_dete_raw8_img_bit)
			{
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_ADAPTIVE: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_0_7: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_00_07; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_1_8: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_01_08; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_2_9: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_02_09; break;
				default: is_dla_config_succ = MACR_FALSE; break;
			}
		}
		// 其他格式图像
		else
		{
			// AI 模块暂时不支持其他格式图像的检测，此处直接退出并返回AI检测失败
			is_dla_config_succ = MACR_FALSE;
		}

		// 只有dla配置成功时才允许进行计算
		if (is_dla_config_succ == MACR_TRUE)
		{
			image_info.prep_type = PREP_TYPE_ROI_FILL_INPUT_DEP_ON_COORD;
			if ((roi_ena == MACR_TRUE) && (roi_coord != NULL))
			{
				image_info.roi_coord = *roi_coord;
			}
			else
			{
				image_info.roi_coord.xmin = 0;
				image_info.roi_coord.ymin = 0;
				image_info.roi_coord.xmax = image_info.img_width - 1;
				image_info.roi_coord.ymax = image_info.img_height - 1;
			}

			// 进行计算
			start_us = read_system_time_us();
			err_val = ai_engine_task_image_filter(model_info_pt->model_tag, &image_info, result_pt);
			model_info_pt->calcRecord(
					(1), 									// 由于返回值是一张图片, 故没有办法进行检测内容是否有效, 直接以结果数为1来表示计算成功
					(read_system_time_us() - start_us),
					err_val);
			if (err_val == MACR_NONE)
			{
				// 计算成功，记录结果
				ret_val = (void *)(&s_ai_dete_restorer_result_temp);
			}
		}
	}

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_Restorer_WriteResult2StaticBuffer()
Description   : 将AI 复原检测结果写入到静态缓冲区
Input         : result_pt : AI 检测结果
Output        : None
Return        : NULL : 检测失败
                Others : AI 检测结果
Author        : PengHZ
Create Date   : 2025/04/11
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" void *AIDete_Restorer_WriteResult2StaticBuffer(IN void *result_pt)
{
	void *ret_val = NULL;

	u32 ret_img_row = 0;
	u32 ret_img_col = 0;
	u32 ret_img_size = 0;
	u32 ret_pack_size = 0;
	AIEngine::Tensor<uint8_t> *result = (AIEngine::Tensor<uint8_t> *)result_pt;
	u32 cnt = 0;
	const u32 RESERVE_SIZE = 128;

	if (result_pt == NULL)
	{
		return NULL;
	}

	ret_img_row = result->Shape()[0];
	ret_img_col = result->Shape()[1];
	ret_img_size = ret_img_row * ret_img_col * 1;
	ret_pack_size = RESERVE_SIZE + ret_img_size;

	// 与之前一次的结果的buff长度不一致时，则需要重新申请buff
	if (s_ai_dete_restorer_result_buff_len != ret_pack_size)
	{
		Mem_Free(s_ai_dete_restorer_result_buff);
		s_ai_dete_restorer_result_buff = NULL;
		s_ai_dete_restorer_result_buff_len = 0;
	}

	// 申请空间
	if (s_ai_dete_restorer_result_buff == NULL)
	{
		s_ai_dete_restorer_result_buff = (u8 *)Mem_Alloc(ret_pack_size);
	}

	// 将结果拷贝到buff中
	if (s_ai_dete_restorer_result_buff != NULL)
	{
		cnt = 0;
		// 写入总大小
		s_ai_dete_restorer_result_buff[cnt++] = (u8)((ret_pack_size>>0 ) & 0xFF);
		s_ai_dete_restorer_result_buff[cnt++] = (u8)((ret_pack_size>>8 ) & 0xFF);
		s_ai_dete_restorer_result_buff[cnt++] = (u8)((ret_pack_size>>16) & 0xFF);
		s_ai_dete_restorer_result_buff[cnt++] = (u8)((ret_pack_size>>24) & 0xFF);
		// 写入图像尺寸
		s_ai_dete_restorer_result_buff[cnt++] = (u8)((ret_img_row>>0) & 0xFF);
		s_ai_dete_restorer_result_buff[cnt++] = (u8)((ret_img_row>>8) & 0xFF);
		s_ai_dete_restorer_result_buff[cnt++] = (u8)((ret_img_col>>0) & 0xFF);
		s_ai_dete_restorer_result_buff[cnt++] = (u8)((ret_img_col>>8) & 0xFF);
		// 写入图像结果
		memcpy(&s_ai_dete_restorer_result_buff[RESERVE_SIZE], result->Host(), ret_img_size);

		s_ai_dete_restorer_result_buff_len = ret_pack_size;

		ret_val = s_ai_dete_restorer_result_buff;
	}
	else
	{
		ret_val = NULL;
	}

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_OCRDet()
Description   : AI OCR_Det 检测函数
Input         : img_area_pt : 图像存储空间
              : roi_ena : 是否开启ROI检测 (暂未使用)
              : roi_coord : ROI检测区域 (暂未使用)
Output        : None
Return        : NULL : 检测失败
                Others : AI 检测结果
Author        : PengHZ
Create Date   : 2024/05/21
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" void *AIDete_OCRDet(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN CoordinateVOC *roi_coord)
{
	void *ret_val = NULL;
	u32 roi_width = 0;
	u32 roi_height = 0;
	s32 is_dla_config_succ = MACR_TRUE;
	vector<QuadPoints> *result_pt = NULL;
	ImageInfo image_info = { 0 };
	ai_dete_model_info *model_info_pt = NULL;
	u64 start_us = 0;
	s32 err_val = MACR_NONE;

	// 查找AI模型对象
	model_info_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_OCR_DET, MACR_TRUE);
	if (model_info_pt == NULL)
	{
		return NULL;
	}

	// 判断传入参数地有效性
	if ((img_area_pt != MACR_NONE) && 	\
		(img_area_pt->pImgArea != NULL))
	{
		// 清空旧的信息
		s_ai_dete_ocr_result_temp.clear();
		// 暂存相关信息
		result_pt = (&s_ai_dete_ocr_result_temp);
		image_info.img_data_pt = img_area_pt->pImgArea;
		image_info.img_height = img_area_pt->height;
		image_info.img_width = img_area_pt->width;
		image_info.color_format = COLOR_FORMAT_GRAY;

		// RAW12格式图像
		if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW12)
		{
			image_info.bit_depth = BIT_DEPTH_RAW12;
			switch (img_area_pt->ai_dete_raw8_img_bit)
			{
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_ADAPTIVE: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_0_7: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_00_07; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_1_8: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_01_08; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_2_9: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_02_09; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_3_10: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_03_10; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_4_11: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_04_11; break;
				default: is_dla_config_succ = MACR_FALSE; break;
			}
		}
		// RAW10格式图像
		else if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW10)
		{
			image_info.bit_depth = BIT_DEPTH_RAW10;
			switch (img_area_pt->ai_dete_raw8_img_bit)
			{
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_ADAPTIVE: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_0_7: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_00_07; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_1_8: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_01_08; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_2_9: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_02_09; break;
				default: is_dla_config_succ = MACR_FALSE; break;
			}
		}
		// 其他格式图像
		else
		{
			// AI 模块暂时不支持其他格式图像的检测，此处直接退出并返回AI检测失败
			is_dla_config_succ = MACR_FALSE;
		}

		if (roi_coord != NULL)
		{
			image_info.roi_coord = *roi_coord;
		}

		// 只有dla配置成功时才允许进行计算
		if (is_dla_config_succ == MACR_TRUE)
		{
			image_info.prep_type = PREP_TYPE_FULL_IMG_RESIZE;
			start_us = read_system_time_us();
			err_val = ai_engine_task_ocr_detection(model_info_pt->model_tag, &image_info, result_pt);
			model_info_pt->calcRecord(
					(s_ai_dete_ocr_result_temp.size()), 
					(read_system_time_us() - start_us),
					 err_val);
			if (err_val == MACR_NONE)
			{
				// 计算成功，记录结果
				ret_val = (void *)(&s_ai_dete_ocr_result_temp);
			}
		}
	}

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_GetOCRString()
Description   : 获取 OCR 转换字符串
Input         : img_area_pt : 图像存储空间
              : roi_ena : 是否开启ROI检测 (暂未使用)
              : roi_coord : ROI检测区域 (暂未使用)
Output        : None
Return        : NULL : 获取失败
                Others : 保存有字符串的 vector 指针
Author        : TanJW
Create Date   : 2024/05/23
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" void *AIDete_GetOCRString(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN QuadPoints *roi_coord)
{
	void *ret_val = NULL;
	s32 is_dla_config_succ = MACR_TRUE;
	vector<OCRCharacter> *result_pt = NULL;
	ImageInfo image_info = { 0 };
	ai_dete_model_info *model_info_pt = NULL;
	u64 start_us = 0;
	s32 err_val = MACR_NONE;

	// 查找AI模型对象
	model_info_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_OCR_REC, MACR_TRUE);
	if (model_info_pt == NULL)
	{
		return NULL;
	}

	// 判断传入参数地有效性
	if ((img_area_pt != MACR_NONE) && 	\
		(img_area_pt->pImgArea != NULL))
	{
		// 清空旧的信息
		s_ai_dete_get_ocr_string_temp.clear();
		// 暂存相关信息
		result_pt = (&s_ai_dete_get_ocr_string_temp);
		image_info.img_data_pt = img_area_pt->pImgArea;
		image_info.img_height = img_area_pt->height;
		image_info.img_width = img_area_pt->width;
		image_info.color_format = COLOR_FORMAT_GRAY;

		// RAW12格式图像
		if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW12)
		{
			image_info.bit_depth = BIT_DEPTH_RAW12;
			switch (img_area_pt->ai_dete_raw8_img_bit)
			{
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_ADAPTIVE: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_0_7: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_00_07; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_1_8: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_01_08; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_2_9: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_02_09; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_3_10: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_03_10; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_4_11: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_04_11; break;
				default: is_dla_config_succ = MACR_FALSE; break;
			}
		}
		// RAW10格式图像
		else if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW10)
		{
			image_info.bit_depth = BIT_DEPTH_RAW10;
			switch (img_area_pt->ai_dete_raw8_img_bit)
			{
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_ADAPTIVE: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_0_7: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_00_07; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_1_8: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_01_08; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_2_9: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_02_09; break;
				default: is_dla_config_succ = MACR_FALSE; break;
			}
		}
		// 其他格式图像
		else
		{
			// AI 模块暂时不支持其他格式图像的检测，此处直接退出并返回AI检测失败
			is_dla_config_succ = MACR_FALSE;
		}

		if (roi_coord != NULL)
		{
			image_info.quad_points = *roi_coord;
		}

		// 只有dla配置成功时才允许进行计算
		if (is_dla_config_succ == MACR_TRUE)
		{
			// 根据ROI的尺寸选择用于 AI 检测的模型
			if (roi_coord == NULL)
			{
				// Reserve
			}
			// 坐标有效
			else
			{
				// 对于OCR 而言，需要将图像旋转到正向(^)。
				#if (MACR_IMG_ORIENTATION == 0)				// 原始图像朝向: 0 表示正向(^)
					image_info.prep_type = PREP_TYPE_ROI_DEP_ON_QUAD_POINTS;
				#elif (MACR_IMG_ORIENTATION == 1)			// 原始图像朝向: 1 表示图像向右倒(>)
					image_info.prep_type = PREP_TYPE_ROI_DEP_ON_QUAD_POINTS_90_DEGREES_CLOCKWISE;
				#elif (MACR_IMG_ORIENTATION == 2)			// 原始图像朝向: 2 表示图像旋转180度(v)
					// 暂时无处理, 直接按原图处理
					image_info.prep_type = PREP_TYPE_ROI_DEP_ON_QUAD_POINTS;
				#elif (MACR_IMG_ORIENTATION == 3)			// 原始图像朝向: 3 表示图像向左倒(<)
					image_info.prep_type = PREP_TYPE_ROI_DEP_ON_QUAD_POINTS_90_DEGREES_ANTICLOCKWISE;
				#else
					image_info.prep_type = PREP_TYPE_ROI_DEP_ON_QUAD_POINTS;
				#endif

				// 进行计算
				start_us = read_system_time_us();
				err_val = ai_engine_task_ocr_recognize(model_info_pt->model_tag, &image_info, result_pt);
				model_info_pt->calcRecord(
					(s_ai_dete_get_ocr_string_temp.size()), 
					(read_system_time_us() - start_us),
					 err_val);
				if (err_val == MACR_NONE)
				{
					// 计算成功，记录结果
					ret_val = (void *)(&s_ai_dete_get_ocr_string_temp);
				}
			}
		}
	}

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_OCRDet_General()
Description   : AI 通用 OCR 字符位置标定模型 检测函数
Input         : img_area_pt : 图像存储空间
              : roi_ena : 是否开启ROI检测 (暂未使用)
              : roi_coord : ROI检测区域 (暂未使用)
Output        : None
Return        : NULL : 检测失败
                Others : AI 检测结果
Author        : PengHZ
Create Date   : 2025/07/22
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" void *AIDete_OCRDet_General(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN CoordinateVOC *roi_coord)
{
	void *ret_val = NULL;
	u32 roi_width = 0;
	u32 roi_height = 0;
	s32 is_dla_config_succ = MACR_TRUE;
	vector<QuadPoints> *result_pt = NULL;
	ImageInfo image_info = { 0 };
	ai_dete_model_info *model_info_pt = NULL;
	u64 start_us = 0;
	s32 err_val = MACR_NONE;

	// 查找AI模型对象
	model_info_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_OCR_DET_GEN, MACR_TRUE);
	if (model_info_pt == NULL)
	{
		return NULL;
	}

	// 判断传入参数地有效性
	if ((img_area_pt != MACR_NONE) && 	\
		(img_area_pt->pImgArea != NULL))
	{
		// 清空旧的信息
		s_ai_dete_ocr_result_temp.clear();
		// 暂存相关信息
		result_pt = (&s_ai_dete_ocr_result_temp);
		image_info.img_data_pt = img_area_pt->pImgArea;
		image_info.img_height = img_area_pt->height;
		image_info.img_width = img_area_pt->width;
		image_info.color_format = COLOR_FORMAT_GRAY;

		// RAW12格式图像
		if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW12)
		{
			image_info.bit_depth = BIT_DEPTH_RAW12;
			switch (img_area_pt->ai_dete_raw8_img_bit)
			{
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_ADAPTIVE: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_0_7: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_00_07; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_1_8: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_01_08; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_2_9: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_02_09; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_3_10: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_03_10; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_4_11: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_04_11; break;
				default: is_dla_config_succ = MACR_FALSE; break;
			}
		}
		// RAW10格式图像
		else if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW10)
		{
			image_info.bit_depth = BIT_DEPTH_RAW10;
			switch (img_area_pt->ai_dete_raw8_img_bit)
			{
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_ADAPTIVE: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_0_7: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_00_07; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_1_8: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_01_08; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_2_9: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_02_09; break;
				default: is_dla_config_succ = MACR_FALSE; break;
			}
		}
		// 其他格式图像
		else
		{
			// AI 模块暂时不支持其他格式图像的检测，此处直接退出并返回AI检测失败
			is_dla_config_succ = MACR_FALSE;
		}

		if (roi_coord != NULL)
		{
			image_info.roi_coord = *roi_coord;
		}

		// 只有dla配置成功时才允许进行计算
		if (is_dla_config_succ == MACR_TRUE)
		{
			image_info.prep_type = PREP_TYPE_FULL_IMG_RESIZE;
			// 进行计算
			start_us = read_system_time_us();
			err_val = ai_engine_task_ocr_detection(model_info_pt->model_tag, &image_info, result_pt);
			model_info_pt->calcRecord(
				(s_ai_dete_ocr_result_temp.size()), 
				(read_system_time_us() - start_us),
					err_val);
			if (err_val == MACR_NONE)
			{
				// 计算成功，记录结果
				ret_val = (void *)(&s_ai_dete_ocr_result_temp);
			}
		}
	}

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_OCRRec_General()
Description   : 获取通用 OCR 转换字符串
Input         : img_area_pt : 图像存储空间
              : roi_ena : 是否开启ROI检测 (暂未使用)
              : roi_coord : ROI检测区域 (暂未使用)
Output        : None
Return        : NULL : 获取失败
                Others : 保存有字符串的 vector 指针
Author        : PengHZ
Create Date   : 2025/07/22
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" void *AIDete_OCRRec_General(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN QuadPoints *roi_coord)
{
	void *ret_val = NULL;
	s32 is_dla_config_succ = MACR_TRUE;
	vector<OCRCharacter> *result_pt = NULL;
	ImageInfo image_info = { 0 };
	ai_dete_model_info *model_info_pt = NULL;
	u64 start_us = 0;
	s32 err_val = MACR_NONE;

	// 查找AI模型对象
	model_info_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_OCR_REC_GEN, MACR_TRUE);
	if (model_info_pt == NULL)
	{
		return NULL;
	}

	// 判断传入参数地有效性
	if ((img_area_pt != MACR_NONE) && 	\
		(img_area_pt->pImgArea != NULL))
	{
		// 清空旧的信息
		s_ai_dete_get_ocr_string_temp.clear();
		// 暂存相关信息
		result_pt = (&s_ai_dete_get_ocr_string_temp);
		image_info.img_data_pt = img_area_pt->pImgArea;
		image_info.img_height = img_area_pt->height;
		image_info.img_width = img_area_pt->width;
		image_info.color_format = COLOR_FORMAT_GRAY;

		// RAW12格式图像
		if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW12)
		{
			image_info.bit_depth = BIT_DEPTH_RAW12;
			switch (img_area_pt->ai_dete_raw8_img_bit)
			{
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_ADAPTIVE: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_0_7: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_00_07; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_1_8: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_01_08; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_2_9: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_02_09; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_3_10: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_03_10; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_4_11: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_04_11; break;
				default: is_dla_config_succ = MACR_FALSE; break;
			}
		}
		// RAW10格式图像
		else if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW10)
		{
			image_info.bit_depth = BIT_DEPTH_RAW10;
			switch (img_area_pt->ai_dete_raw8_img_bit)
			{
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_ADAPTIVE: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_0_7: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_00_07; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_1_8: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_01_08; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_2_9: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_02_09; break;
				default: is_dla_config_succ = MACR_FALSE; break;
			}
		}
		// 其他格式图像
		else
		{
			// AI 模块暂时不支持其他格式图像的检测，此处直接退出并返回AI检测失败
			is_dla_config_succ = MACR_FALSE;
		}

		if (roi_coord != NULL)
		{
			image_info.quad_points = *roi_coord;
		}

		// 只有dla配置成功时才允许进行计算
		if (is_dla_config_succ == MACR_TRUE)
		{
			// 根据ROI的尺寸选择用于 AI 检测的模型
			if (roi_coord == NULL)
			{
				// Reserve
			}
			// 坐标有效
			else
			{
				// 对于OCR 而言，需要将图像旋转到正向(^)。
				#if (MACR_IMG_ORIENTATION == 0)				// 原始图像朝向: 0 表示正向(^)
					image_info.prep_type = PREP_TYPE_ROI_DEP_ON_QUAD_POINTS;
				#elif (MACR_IMG_ORIENTATION == 1)			// 原始图像朝向: 1 表示图像向右倒(>)
					image_info.prep_type = PREP_TYPE_ROI_DEP_ON_QUAD_POINTS_90_DEGREES_CLOCKWISE;
				#elif (MACR_IMG_ORIENTATION == 2)			// 原始图像朝向: 2 表示图像旋转180度(v)
					// 暂时无处理, 直接按原图处理
					image_info.prep_type = PREP_TYPE_ROI_DEP_ON_QUAD_POINTS;
				#elif (MACR_IMG_ORIENTATION == 3)			// 原始图像朝向: 3 表示图像向左倒(<)
					image_info.prep_type = PREP_TYPE_ROI_DEP_ON_QUAD_POINTS_90_DEGREES_ANTICLOCKWISE;
				#else
					image_info.prep_type = PREP_TYPE_ROI_DEP_ON_QUAD_POINTS;
				#endif

				// 进行计算
				start_us = read_system_time_us();
				err_val = ai_engine_task_ocr_recognize(model_info_pt->model_tag, &image_info, result_pt);
				model_info_pt->calcRecord(
					(s_ai_dete_get_ocr_string_temp.size()), 
					(read_system_time_us() - start_us),
						err_val);
				if (err_val == MACR_NONE)
				{
					// 计算成功，记录结果
					ret_val = (void *)(&s_ai_dete_get_ocr_string_temp);
				}
			}
		}
	}

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_OCRGeneral_DeteString()
Description   : AI 通用 OCR通用字符模型 检测并获取字符串函数
Input         : img_area_pt : 图像存储空间
              : buff : 输出缓冲区
              : buff_len : 输出缓冲区长度
              : buff_size : 输出缓冲区大小
Output        : None
Return        : MACR_NONE : 检测成功
                MACR_ERROR : 检测失败
Author        : PengHZ
Create Date   : 2025/07/24
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" s32 AIDete_OCRGeneral_DeteString(IN g_ImageAreaStruct *img_area_pt, IN u8* buff, IN u32* buff_len, IN const u32 buff_size)
{
	s32 ret_val = MACR_NONE;
	vector<QuadPoints> * dete_result_pt = NULL;
	vector<OCRCharacter> * rec_result_pt = NULL;
	u32 w_index = 0;


	if ((img_area_pt == NULL) || (img_area_pt->pImgArea == NULL))
	{
		return MACR_ERROR;
	}

	if ((buff == NULL) || (buff_len == NULL) || (buff_size == 0))
	{
		return MACR_ERROR;
	}

	// 获取字符位置标定结果
	dete_result_pt = (vector<QuadPoints> *)AIDete_OCRDet_General(img_area_pt, MACR_FALSE, MACR_NULL);
	if (dete_result_pt == NULL)
	{
		return MACR_ERROR;
	}

	// 提前清空原始buff, 防止后续检测无结果, 导致外部误使用旧数据
	buff[0] = '\0';
	w_index = 0;

	// 根据字符位置标定结果，依次进检测获取字符串
	for (auto quad: *dete_result_pt)
	{
		rec_result_pt = (vector<OCRCharacter> *)AIDete_OCRRec_General(img_area_pt, MACR_FALSE, &quad);
		if (rec_result_pt == NULL)
		{
			// 部分检测框可能无法识别, 此处直接跳过
			// UCIP_LOG("AIDete_OCRGeneral_DeteString: OCRRec_General failed, quad = (%d, %d), (%d, %d), (%d, %d), (%d, %d)\r\n", quad.x0, quad.y0, quad.x1, quad.y1, quad.x2, quad.y2, quad.x3, quad.y3);
			continue;
		}

		for (auto character: *rec_result_pt)
		{
			// 同时, 此接口确保了输出长度不足时, 会自动截断, 不会出现溢出, 但utf8格式下, 可能会只输出一个unicode 的一半编码
			// 此处为兼容utf8格式输出，可能存在一个字符占用多个字节的情况，此处使用%.4s格式化输出，确保不会出现乱码
			w_index += buff_appendf(buff, buff_size, w_index, (char *)"%.4s", &character.character);
		}
	}

	*buff_len = w_index;
	ret_val = MACR_NONE;

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_LPR_Dete()
Description   : AI LPR 检测函数
Input         : img_area_pt : 图像存储空间
Output        : out_len: 车牌字符长度(长度小于0表示失败)
              : rect: 车牌位置(4个点, 共计8个int)
Return        : NULL : 检测失败
                Others : AI 检测结果
Author        : PengHZ
Create Date   : 2025/07/01
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" s32 AIDete_LPR_Dete(IN g_ImageAreaStruct *img_area_pt, OUT u8* out, OUT s32 *rect)
{
	s32 ret_val = NULL;
	u32 roi_width = 0;
	u32 roi_height = 0;
	s32 is_dla_config_succ = MACR_TRUE;
	s32 len = 0;
	vector<QuadPoints> *det_result_pt = NULL;
	vector<OCRCharacter> *rec_result_pt = NULL;
	ImageInfo image_info = { 0 };
	ai_dete_model_info *model_det_pt = NULL;
	ai_dete_model_info *model_rec_pt = NULL;
	u64 start_us = 0;
	s32 err_val = MACR_NONE;

	// 查找AI模型对象 此处需要考虑两个模型
	model_det_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_OCR_DET_CHNLP, MACR_TRUE);
	model_rec_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_OCR_REC_CHNLP, MACR_TRUE);
	// 车牌识别模型, 必须同时使用两个模型才能成功
	if ((model_det_pt == NULL) || (model_rec_pt == NULL))
	{
		return NULL;
	}

	// 判断传入参数地有效性
	if ((img_area_pt != MACR_NONE) && 	\
		(img_area_pt->pImgArea != NULL))
	{
		// 清空旧的信息
		s_ai_dete_lpr_result_temp.clear();
		// 暂存相关信息
		det_result_pt = (&s_ai_dete_lpr_result_temp);
		image_info.img_data_pt = img_area_pt->pImgArea;
		image_info.img_height = img_area_pt->height;
		image_info.img_width = img_area_pt->width;
		image_info.color_format = COLOR_FORMAT_GRAY;

		// RAW12格式图像
		if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW12)
		{
			image_info.bit_depth = BIT_DEPTH_RAW12;
			switch (img_area_pt->ai_dete_raw8_img_bit)
			{
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_ADAPTIVE: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_0_7: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_00_07; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_1_8: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_01_08; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_2_9: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_02_09; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_3_10: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_03_10; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_4_11: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_04_11; break;
				default: is_dla_config_succ = MACR_FALSE; break;
			}
		}
		// RAW10格式图像
		else if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW10)
		{
			image_info.bit_depth = BIT_DEPTH_RAW10;
			switch (img_area_pt->ai_dete_raw8_img_bit)
			{
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_ADAPTIVE: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_0_7: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_00_07; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_1_8: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_01_08; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_2_9: image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_02_09; break;
				default: is_dla_config_succ = MACR_FALSE; break;
			}
		}
		// 其他格式图像
		else
		{
			// AI 模块暂时不支持其他格式图像的检测，此处直接退出并返回AI检测失败
			is_dla_config_succ = MACR_FALSE;
		}

		if (rect != NULL)
		{
			image_info.roi_coord = *(CoordinateVOC*)rect;
		}

		// 只有dla配置成功时才允许进行计算
		if (is_dla_config_succ == MACR_TRUE)
		{
			image_info.prep_type = PREP_TYPE_FULL_IMG_RESIZE;
			start_us = read_system_time_us();
			err_val = ai_engine_task_ocr_detection(model_det_pt->model_tag, &image_info, det_result_pt);
			model_det_pt->calcRecord(
				(s_ai_dete_lpr_result_temp.size()), 
				(read_system_time_us() - start_us),
				 err_val);
			if (err_val == MACR_NONE)
			{
				// Det 检测成功，LPR 模型需要在同一个接口内直接获取到最终String数据, 故需要手动进一步处理
				typedef Geometry2D<int> Geom;

				for (auto quad: *det_result_pt)
				{
					int length = std::max(Geom::Calculate_Distance(quad.x0, quad.y0, quad.x1, quad.y1),
										Geom::Calculate_Distance(quad.x2, quad.y2, quad.x1, quad.y1));
					if (length < 100) continue;
					
					// 清空旧的信息
					s_ai_dete_lpr_char_temp.clear();
					// 暂存相关信息
					rec_result_pt = (&s_ai_dete_lpr_char_temp);
					image_info.quad_points = quad;

					// 对于OCR_Rec 而言，需要将图像旋转到正向(^)。
					#if (MACR_IMG_ORIENTATION == 0)				// 原始图像朝向: 0 表示正向(^)
						image_info.prep_type = PREP_TYPE_ROI_DEP_ON_QUAD_POINTS;
					#elif (MACR_IMG_ORIENTATION == 1)			// 原始图像朝向: 1 表示图像向右倒(>)
						image_info.prep_type = PREP_TYPE_ROI_DEP_ON_QUAD_POINTS_90_DEGREES_CLOCKWISE;
					#elif (MACR_IMG_ORIENTATION == 2)			// 原始图像朝向: 2 表示图像旋转180度(v)
						// 暂时无处理, 直接按原图处理
						image_info.prep_type = PREP_TYPE_ROI_DEP_ON_QUAD_POINTS;
					#elif (MACR_IMG_ORIENTATION == 3)			// 原始图像朝向: 3 表示图像向左倒(<)
						image_info.prep_type = PREP_TYPE_ROI_DEP_ON_QUAD_POINTS_90_DEGREES_ANTICLOCKWISE;
					#else
						image_info.prep_type = PREP_TYPE_ROI_DEP_ON_QUAD_POINTS;
					#endif

					start_us = read_system_time_us();
					err_val = ai_engine_task_ocr_recognize(model_rec_pt->model_tag, &image_info, rec_result_pt);
					model_rec_pt->calcRecord(
						(s_ai_dete_get_ocr_string_temp.size()), 
						(read_system_time_us() - start_us),
						 err_val);
					if (err_val == MACR_NONE)
					{
						// 进行字符置信度阈值校验: 置信度低于一定值的字符直接过滤掉(删除)
						auto newEnd = std::remove_if(rec_result_pt->begin(), rec_result_pt->end(), [](const OCRCharacter& character) {
							return (character.score < 0.5);
						});
						rec_result_pt->erase(newEnd, rec_result_pt->end());

						static ChineseLicensePlateValidator validator;
						if (validator.Validate_Plate(*rec_result_pt))
						{
							len = 0;
							for (auto character: *rec_result_pt)
							{
								int cnt = strlen((char*)&character.character);
								memcpy(out + len, &character.character, cnt);
								len += cnt;
							}
							rect[0] = quad.x0, rect[1] = quad.y0;
							rect[2] = quad.x1, rect[3] = quad.y1;
							rect[4] = quad.x2, rect[5] = quad.y2;
							rect[6] = quad.x3, rect[7] = quad.y3;
							ret_val = len;
							break;
						}
					}
				}
			}
		}
	}

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_CAP_Dete()
Description   : AI 试管检测模型 检测函数
Input         : img_area_pt : 图像存储空间
              : roi_ena : 是否开启ROI检测
              : roi_coord : ROI检测区域
Output        : None
Return        : NULL : 检测失败
                Others : AI 检测结果
Author        : PengHZ
Create Date   : 2024/07/01
Last Update   : None
Note          : None
*******************************************************************************/
void *AIDete_CAP_Dete(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN CoordinateVOC *roi_coord)
{
	void *ret_val = NULL;
	ImageInfo image_info = { 0 };
	u32 roi_width = 0;
	u32 roi_height = 0;
	s32 is_dla_config_succ = MACR_TRUE;
	s32 ai_global_rslt_dete_bit = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE;
	s32 ai_cap_rslt_dete_bit = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE;
	vector<DetectionBBoxInfo> *global_result_pt = NULL;
	vector<DetectionBBoxInfo> *result_pt = NULL;
	u32 i = 0;
	ai_dete_model_info *model_info_pt = NULL;
	u64 start_us = 0;
	s32 err_val = MACR_NONE;

	// 查找AI模型对象
	model_info_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_CAP_DET, MACR_TRUE);
	if (model_info_pt == NULL)
	{
		return NULL;
	}

	// 判断传入参数地有效性
	if ((img_area_pt != MACR_NONE) && 	\
		(img_area_pt->pImgArea != NULL))
	{
		// 清空旧的信息
		s_ai_dete_cap_result_temp.result_cap.clear();
		s_ai_dete_cap_result_temp.ai_dete_img_bit = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE;

		global_result_pt = (vector<DetectionBBoxInfo> *)(img_area_pt->ai_dete_rslt_global_pt);

		// 缓存 AI 检测实际图层
		for (i = 0; i < MACR_AI_DETE_RESULT_BUFF_SIZE; i++)
		{
			if (global_result_pt == &(s_ai_dete_result_buff[i].result_global))
			{
				// 全局 AI 检测图层信息
				ai_global_rslt_dete_bit = s_ai_dete_result_buff[i].ai_roi_global_dete_img_bit;

				// 管帽检测图像遵循“全局 AI 检测图层向下取 2 个图层规则”
				ai_cap_rslt_dete_bit = ai_global_rslt_dete_bit - 2;

				// 如果需要检测的图层已经小于最低的图层，就使用最低的图层
				if (ai_cap_rslt_dete_bit < MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE)
				{
					ai_cap_rslt_dete_bit = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE;
				}
				break;
			}
		}

		// 暂存相关信息
		result_pt = (&s_ai_dete_cap_result_temp.result_cap);
		image_info.img_data_pt = img_area_pt->pImgArea;
		image_info.img_width = img_area_pt->width;
		image_info.img_height = img_area_pt->height;
		// RAW12格式图像
		if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW12)
		{
			image_info.bit_depth = BIT_DEPTH_RAW12;
			switch (ai_cap_rslt_dete_bit)
			{
				case MACR_SUB_BIT_EXTRACTED_RANGE_00_07:
				case MACR_SUB_BIT_EXTRACTED_RANGE_01_08:
				case MACR_SUB_BIT_EXTRACTED_RANGE_02_09:
				case MACR_SUB_BIT_EXTRACTED_RANGE_03_10:
				case MACR_SUB_BIT_EXTRACTED_RANGE_04_11:
					image_info.sub_bit_range = ai_cap_rslt_dete_bit;
					break;
				// 理论上永远不会运行到此处
				case MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE:
				default: is_dla_config_succ = MACR_FALSE; break;
			}
		}
		// RAW10格式图像
		else if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW10)
		{
			image_info.bit_depth = BIT_DEPTH_RAW10;
			switch (ai_cap_rslt_dete_bit)
			{
				case MACR_SUB_BIT_EXTRACTED_RANGE_00_07:
				case MACR_SUB_BIT_EXTRACTED_RANGE_01_08:
				case MACR_SUB_BIT_EXTRACTED_RANGE_02_09:
					image_info.sub_bit_range = ai_cap_rslt_dete_bit;
					break;

				// 理论上永远不会运行到此处
				case MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE:
				default: is_dla_config_succ = MACR_FALSE; break;
			}
		}
		// 其他格式图像
		else
		{
			// AI 模块暂时不支持其他格式图像的检测，此处直接退出并返回AI检测失败
			is_dla_config_succ = MACR_FALSE;
		}

		image_info.roi_coord = *roi_coord;

		// 只有dla配置成功时才允许进行计算
		if (is_dla_config_succ == MACR_TRUE)
		{
			image_info.prep_type = PREP_TYPE_ROI_RESIZE_ADAPTIVE_DEP_ON_COORD;
			start_us = read_system_time_us();
			err_val = ai_engine_task_detection(model_info_pt->model_tag, &image_info, result_pt);
			model_info_pt->calcRecord(
				(s_ai_dete_cap_result_temp.result_cap.size()), 
				(read_system_time_us() - start_us),
				 err_val);
			if (err_val == MACR_NONE)
			{
				// 计算成功，记录结果
				ret_val = (void *)(&s_ai_dete_cap_result_temp.result_cap);
				s_ai_dete_cap_result_temp.ai_dete_img_bit = image_info.sub_bit_range;
			}
		}
	}

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_Classification_Dete()
Description   : AI 分类检测函数
Input         : img_area_pt : 图像存储空间
Output        : None
Return        : NULL : 检测失败
                Others : AI 分类检测结果
Author        : TangHL
Create Date   : 2025/07/24
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" void *AIDete_Classification_Dete(IN g_ImageAreaStruct *img_area_pt)
{
	void *ret_val = NULL;
	s32 is_dla_config_succ = MACR_TRUE;
	vector<ClassificationResult> *result_pt = NULL;
	ImageInfo image_info = { 0 };
	ai_dete_model_info *model_info_pt = NULL;
	u64 start_us = 0;
	s32 err_val = MACR_NONE;

	// 查找分类模型对象
	model_info_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_CLASSIFICATION, MACR_TRUE);
	if (model_info_pt == NULL)
	{
		return NULL;
	}

	// 判断传入参数的有效性
	if ((img_area_pt != MACR_NONE) && 	\
		(img_area_pt->pImgArea != NULL))
	{
		// 清空旧的信息
		s_ai_dete_classification_result_temp.clear();
		// 暂存相关信息
		result_pt = (&s_ai_dete_classification_result_temp);
		image_info.img_data_pt = img_area_pt->pImgArea;
		image_info.img_height = img_area_pt->height;
		image_info.img_width = img_area_pt->width;
		image_info.color_format = COLOR_FORMAT_GRAY;

		// 处理图像格式和位深度
		if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW12)
		{
			image_info.bit_depth = BIT_DEPTH_RAW12;
			switch (img_area_pt->ai_dete_raw8_img_bit)
			{
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_ADAPTIVE:
					image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_ADAPTIVE; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_0_7:
					image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_00_07; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_1_8:
					image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_01_08; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_2_9:
					image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_02_09; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_3_10:
					image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_03_10; break;
				case MACR_IMAGE_AI_DETE_RAW8_IMG_BIT_4_11:
					image_info.sub_bit_range = MACR_SUB_BIT_EXTRACTED_RANGE_04_11; break;
				default: is_dla_config_succ = MACR_FALSE; break;
			}
		}
		else if (img_area_pt->depth == MACR_IMAGE_IMG_DEPTH_RAW8)
		{
			image_info.bit_depth = BIT_DEPTH_RAW8;
		}
		else
		{
			is_dla_config_succ = MACR_FALSE;
		}

		// 只有配置成功时才允许进行计算
		if (is_dla_config_succ == MACR_TRUE)
		{
			// 分类任务只需要全图resize，不需要ROI处理
			image_info.prep_type = PREP_TYPE_FULL_IMG_RESIZE;
			start_us = read_system_time_us();

			// 调用AI引擎分类任务接口
			err_val = ai_engine_task_classification(model_info_pt->model_tag, &image_info, result_pt);

			// 记录性能统计
			model_info_pt->calcRecord(
					(s_ai_dete_classification_result_temp.size()),
					(read_system_time_us() - start_us),
					 err_val);

			// 返回结果
			if (err_val == MACR_NONE)
			{
				ret_val = (void *)(&s_ai_dete_classification_result_temp);
			}
		}
	}

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_Classification_Dete_WithClassName()
Description   : AI 分类检测函数（带类别名称）
Input         : img_area_pt : 图像存储空间
Output        : None
Return        : NULL : 检测失败
                Others : AI 分类检测结果（包含类别名称）
Author        : TangHL
Create Date   : 2025/07/28
Last Update   : 2025/07/29
Note          : 支持多模型动态切换，自动识别当前使用的分类模型
*******************************************************************************/
extern "C" void *AIDete_Classification_Dete_WithClassName(IN g_ImageAreaStruct *img_area_pt)
{
	void *ret_val = NULL;
	vector<ClassificationResult> *result_pt = NULL;
	ai_dete_model_info *model_info_pt = NULL;
	const char *model_tag = NULL;

	// 获取分类模型信息以确定实际使用的模型标识
	model_info_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_CLASSIFICATION, MACR_TRUE);
	if (model_info_pt == NULL)
	{
		return NULL;
	}
	model_tag = model_info_pt->model_tag;

	// 调用原有的分类检测函数
	ret_val = AIDete_Classification_Dete(img_area_pt);
	if (ret_val == NULL)
	{
		return NULL;
	}

	// 获取检测结果
	result_pt = (vector<ClassificationResult> *)ret_val;

	// 安全检查：确保result_pt不为空
	if (result_pt == NULL)
	{
		return NULL;
	}

	// 为每个结果填充类别名称
	for (auto& result : *result_pt)
	{
		// 使用实际的模型标识获取类别名称
		const char* class_name = AIDete_Classification_GetClassName(model_tag, result.classID);

		// 直接使用返回的字符串指针
		// 如果映射表正常加载，返回真实的类别名称
		// 如果映射表加载失败，返回格式化的索引字符串（如"class_0"）
		result.className = class_name;
	}

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_Classification_Dete_WithClassName_ByModel()
Description   : AI 分类检测函数（带类别名称，指定模型）
Input         : img_area_pt : 图像存储空间
                model_name : 指定的模型名称
Output        : None
Return        : NULL : 检测失败
                Others : AI 分类检测结果（包含类别名称）
Author        : TangHL
Create Date   : 2025/07/29
Last Update   : None
Note          : 支持指定模型名称进行分类检测，用于多模型场景
*******************************************************************************/
extern "C" void *AIDete_Classification_Dete_WithClassName_ByModel(IN g_ImageAreaStruct *img_area_pt, IN const char* model_name)
{
	void *ret_val = NULL;
	vector<ClassificationResult> *result_pt = NULL;

	// 参数检查
	if (model_name == NULL)
	{
		return NULL;
	}

	// 调用原有的分类检测函数
	ret_val = AIDete_Classification_Dete(img_area_pt);
	if (ret_val == NULL)
	{
		return NULL;
	}

	// 获取检测结果
	result_pt = (vector<ClassificationResult> *)ret_val;

	// 安全检查：确保result_pt不为空
	if (result_pt == NULL)
	{
		return NULL;
	}

	// 为每个结果填充类别名称
	for (auto& result : *result_pt)
	{
		// 使用指定的模型名称获取类别名称
		const char* class_name = AIDete_Classification_GetClassName(model_name, result.classID);

		// 直接使用返回的字符串指针
		result.className = class_name;
	}

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_isDeteSucc()
Description   : 判断图像中是否存在置信度超过阈值的条码
Input         : ai_dete_rslt_pt : AI 检测结果
                score_thre : 置信度阈值
Output        : None
Return        : MACR_TRUE : 条码存在
                MACR_FALSE : 条码不存在
                MACR_ERROE : 查询失败
Author        : DongDL
Create Date   : 2023/07/25
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" s32 AIDete_isDeteSucc(void *ai_dete_rslt_pt, f32 score_thre)
{
	s32 ret_val = MACR_FALSE;
	vector<DetectionBBoxInfo> *result = (vector<DetectionBBoxInfo> *)ai_dete_rslt_pt;

	// 检查输入参数是否合法
	if (result == NULL)
	{
		return MACR_ERROR;
	}

	// 遍历所有 AI 结果，查看是否有置信度超过阈值的条码
	auto it = result->begin();
	auto end = result->end();
	for (;it != end; it++)
	{
		if (it->score > score_thre)
		{
			return MACR_TRUE;
		}
	}

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_GetValidROINum()
Description   : 获取AI检测结果中有效ROI的数量
Input         : ai_dete_rslt_pt : AI 检测结果
                score_thre : 置信度阈值
Output        : None
Return        : MACR_ERROR : 获取失败
                Others : AI 检测结果中有效 ROI 的数量
Author        : DongDL
Create Date   : 2023/08/17
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" s32 AIDete_GetValidROINum(void *ai_dete_rslt_pt, f32 score_thre)
{
	s32 valid_roi_num = 0;
	vector<DetectionBBoxInfo> *result = (vector<DetectionBBoxInfo> *)ai_dete_rslt_pt;

	// 检查输入参数是否合法
	if (result == NULL)
	{
		return MACR_ERROR;
	}

	// 遍历所有 AI 结果，查看是否有置信度超过阈值的条码
	auto it = result->begin();
	auto end = result->end();
	for (;it != end; it++)
	{
		if (it->score > score_thre)
		{
			valid_roi_num++;
		}
	}

	return valid_roi_num;
}

/*******************************************************************************
Function      : AIDete_GetROIWithHighestScore()
Description   : 获取AI检测结果中置信度最高的ROI
Input         : ai_dete_rslt_pt : AI 检测结果
Output        : score_pt：置信度
                x0_pt：ROI左上角坐标
                y0_pt：ROI左上角坐标
                x1_pt：ROI右下角坐标
                y1_pt：ROI右下角坐标
                class_id_pt：AI 分类类型
Return        : MACR_ERROR : 获取失败（AI检测结果中无ROI）
                MACR_TRUE  : 获取成功
Author        : DongDL
Create Date   : 2023/08/24
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" s32 AIDete_GetROIWithHighestScore(void *ai_dete_rslt_pt, f32 *score_pt, f32 *x0_pt, f32 *y0_pt, f32 *x1_pt, f32 *y1_pt, s32 *class_id_pt)
{
	s32 ret_val = MACR_ERROR;
	vector<DetectionBBoxInfo> *result = (vector<DetectionBBoxInfo> *)ai_dete_rslt_pt;
	DetectionBBoxInfo info_temp = {0};

	// 检查输入参数是否合法
	if ((result == NULL) ||		\
		(score_pt == NULL) ||	\
		(x0_pt == NULL) ||			\
		(y0_pt == NULL) ||			\
		(x1_pt == NULL) ||			\
		(y1_pt == NULL) ||			
		(class_id_pt == NULL))
	{
		return MACR_ERROR;
	}

	// 遍历所有 AI 结果，获取置信度最高的ROI
	auto it = result->begin();
	auto end = result->end();
	if (result->size() != 0)
	{
		// 将第一个结果保存在 info_temp 中，作为当前得到的最该置信度的结果
		info_temp.score = it->score;
		info_temp.classID = it->classID;
		info_temp.xmin = it->xmin;
		info_temp.xmax = it->xmax;
		info_temp.ymin = it->ymin;
		info_temp.ymax = it->ymax;

		// 遍历所有结果，查看是否有置信度更高的结果
		for (;it != end; it++)
		{
			if (it->score > info_temp.score)
			{
				info_temp.score = it->score;
				info_temp.classID = it->classID;
				info_temp.xmin = it->xmin;
				info_temp.xmax = it->xmax;
				info_temp.ymin = it->ymin;
				info_temp.ymax = it->ymax;
			}
		}

		// 输出ROI的坐标及置信度信息
		*score_pt = info_temp.score;
		*x0_pt = info_temp.xmin;
		*y0_pt = info_temp.ymin;
		*x1_pt = info_temp.xmax;
		*y1_pt = info_temp.ymax;
		*class_id_pt = info_temp.classID;
		ret_val = MACR_TRUE;
	}

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_InitAIRsltROINumRecord()
Description   : 初始化 ROI 数量记录
Input         : None
Output        : None
Return        : MACR_NONE : 成功
                MACR_ERROR : 失败
Author        : TanJW
Create Date   : 2023/08/16
Last Update   : None
Note          : None
*******************************************************************************/
static s32 AIDete_InitAIRsltROINumRecord(void)
{
	s32 ret_val = MACR_NONE;

	// 清空缓冲区
	memset(s_ai_dete_rslt_roi_num_record_buff, 0, sizeof(s_ai_dete_rslt_roi_num_record_buff));

	// 清空记录下一次偏移
	s_ai_dete_rslt_roi_num_record_next_offset = 0;

	// 清空保存记录的总数
	s_ai_dete_rslt_roi_num_record_save_num = 0;

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_SetAIRsltROINumRecord()
Description   : 设置 ROI 数量记录，优先全局结果，如果全局结果 ROI 数量不符要求再保存局部结果 ROI 数量
Input         : img_area_pt : 图像结构体指针，里面包含所有 AI 检测结果
Output        : None
Return        : MACR_NONE : 成功
                MACR_ERROR : 失败
Author        : TanJW
Create Date   : 2023/08/16
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" s32 AIDete_SetAIRsltROINumRecord(IN g_ImageAreaStruct *img_area_pt)
{
	s32 ret_val = MACR_NONE;
	vector<DetectionBBoxInfo> *result_global_pt = (vector<DetectionBBoxInfo> *)(img_area_pt->ai_dete_rslt_global_pt);
	vector<DetectionBBoxInfo> *result_roi_pt = (vector<DetectionBBoxInfo> *)(img_area_pt->ai_dete_rslt_roi_pt);
	ai_dete_result_roi_num_record *record_buff_pt = (ai_dete_result_roi_num_record *)s_ai_dete_rslt_roi_num_record_buff;
	u32 next_offset = s_ai_dete_rslt_roi_num_record_next_offset;
	const f32 rlst_score_threshold = MACR_AI_DETE_RSLT_VALID_ROI_SCORE_THRESHEOLD;
	f32 score_tmp = 0;
	s32 valid_roi_num = 0;

	// 检查 AI 检测结果是否有效
	if (result_global_pt == NULL)
	{
		return MACR_ERROR;
	}

	// 遍历所有 AI 结果，统计有效 ROI 数量
	auto global_rslt_it = result_global_pt->begin();
	auto global_rslt_end = result_global_pt->end();
	for (;global_rslt_it != global_rslt_end; global_rslt_it++)
	{
		// 当置信度大于等于阈值时，认为 ROI 是有效的
		if (global_rslt_it->score >= rlst_score_threshold)
		{
			valid_roi_num++;
		}
	}

	// 全图结果没有找到到有效 ROI，则进行挖图结果查找
	if (valid_roi_num == 0)
	{
		if (result_roi_pt != NULL)
		{
			// 遍历所有 AI 结果，统计有效 ROI 数量
			auto roi_rslt_it = result_roi_pt->begin();
			auto roi_rslt_end = result_roi_pt->end();
			for (;roi_rslt_it != roi_rslt_end; roi_rslt_it++)
			{
				// 当置信度大于等于阈值时，认为 ROI 是有效的
				if (roi_rslt_it->score >= rlst_score_threshold)
				{
					valid_roi_num++;
				}
			}
		}
	}

	// 将有效 roi 数量写入缓冲区，即使 roi 数量为 0，也是有效数量
	record_buff_pt[next_offset].img_index = img_area_pt->index;
	record_buff_pt[next_offset].valid_roi_num = valid_roi_num;

	// 更新下一次偏移
	next_offset++;

	// 如果 next_offset >= MACR_AI_DETE_RSLT_ROI_NUM_RECORD_BUFF_SIZE_MAX，表示缓冲区已放满，
	// 则下一次需要存放的位置为初始位置
	if (next_offset >= MACR_AI_DETE_RSLT_ROI_NUM_RECORD_BUFF_SIZE_MAX)
	{
		next_offset = 0;
	}

	// 保存系统记录的次数，当前变量大于 MACR_AI_DETE_RSLT_ROI_NUM_RECORD_BUFF_SIZE_MAX 时，则发生覆盖。
	// 覆盖原则：最新的覆盖最旧的
	s_ai_dete_rslt_roi_num_record_save_num++;

	// 保存 next_offset
	s_ai_dete_rslt_roi_num_record_next_offset = next_offset;

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_GetEmptyAIRsltNumInIndexSegment()
Description   : 获取 ROI 数量记录中处于索引段范围内且有效 ROI 数量为0的记录数量
Input         : index_min : 有效索引段的最小值
                index_max : 有效索引段的最大值
Output        : rslt_num_pt : 有效 ROI 数量为0的记录数量
Return        : MACR_NONE : 成功
                MACR_ERROR : 失败
Author        : TanJW
Create Date   : 2023/08/16
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" s32 AIDete_GetEmptyAIRsltNumInIndexSegment(IN u32 index_min, IN u32 index_max, OUT s32 *rslt_num_pt)
{
	u32 i = 0;
	s32 ret_val = MACR_NONE;
	ai_dete_result_roi_num_record *record_buff_pt = (ai_dete_result_roi_num_record *)s_ai_dete_rslt_roi_num_record_buff;
	u32 result_num = 0;

	// 判断指针有效性
	if (rslt_num_pt == NULL)
	{
		return MACR_ERROR;
	}

	// 当检索段发生溢出时，进行特殊处理
	if (index_max < index_min)
	{
		// 遍历所有记录
		for (i = 0; i < MACR_AI_DETE_RSLT_ROI_NUM_RECORD_BUFF_SIZE_MAX; i++)
		{
			// 判断记录是否符合索引范围并判断ROI检测结果是否为空
			if (((record_buff_pt[i].img_index > index_min) || 	\
				(record_buff_pt[i].img_index < index_max)) &&	\
				(record_buff_pt[i].valid_roi_num == 0))
			{
				result_num++;
			}
		}
	}
	else
	{
		// 遍历所有记录
		for (i = 0; i < MACR_AI_DETE_RSLT_ROI_NUM_RECORD_BUFF_SIZE_MAX; i++)
		{
			// 判断记录是否符合索引范围并判断ROI检测结果是否为空
			if ((record_buff_pt[i].img_index > index_min) && 	\
				(record_buff_pt[i].img_index < index_max) &&	\
				(record_buff_pt[i].valid_roi_num == 0))
			{
				result_num++;
			}
		}
	}


	*rslt_num_pt = result_num;

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_OutputAIRsltROINum()
Description   : 输出 ROI 数量记录
Input         : method : 打印方法
Output        : None
Return        : MACR_NONE : 成功
                MACR_ERROR : 失败
Author        : TanJW
Create Date   : 2023/08/16
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" s32 AIDete_OutputAIRsltROINum(printf_method method)
{
	u32 i = 0;
	u32 next_offset = s_ai_dete_rslt_roi_num_record_next_offset;
	u32 record_save_num = s_ai_dete_rslt_roi_num_record_save_num;


	method((const s8*)"AI Result ROI Num Record:\r\n");
	method((const s8*)"  next_offset = %u\r\n", next_offset);
	method((const s8*)"  total num = %u\r\n", record_save_num);
	method((const s8*)"  |-------------------------------|\r\n  |   index   |   valid_roi_num   |\r\n  |-------------------------------|\r\n");

	// 记录的总数量已超出缓冲区
	if (record_save_num > MACR_AI_DETE_RSLT_ROI_NUM_RECORD_BUFF_SIZE_MAX)
	{
		for (i = 0; i < MACR_AI_DETE_RSLT_ROI_NUM_RECORD_BUFF_SIZE_MAX; i++)
		{
			// 从最旧的开始输出
			method((const s8*)"  |%-10u |%-10u         |\r\n  |-------------------------------|\r\n", s_ai_dete_rslt_roi_num_record_buff[next_offset].img_index, s_ai_dete_rslt_roi_num_record_buff[next_offset].valid_roi_num);
		
			next_offset++;

			if (next_offset >= MACR_AI_DETE_RSLT_ROI_NUM_RECORD_BUFF_SIZE_MAX)
			{
				next_offset = 0;
			}
		}
	}
	// 记录的数量未超出缓冲区
	else
	{
		for (i = 0; i < MACR_AI_DETE_RSLT_ROI_NUM_RECORD_BUFF_SIZE_MAX; i++)
		{
			method((const s8*)"  |%-10u |%-10u         |\r\n  |-------------------------------|\r\n", s_ai_dete_rslt_roi_num_record_buff[i].img_index, s_ai_dete_rslt_roi_num_record_buff[i].valid_roi_num);
		}
	}

	return MACR_NONE;
}

/*******************************************************************************
Function      : AIDete_PrintfAIRsltROINum2Buff()
Description   : 打印 ROI 数量记录到缓冲区
Input         : info_len_max : 输出缓冲区总大小
Output        : info_pt : 输出信息缓冲区
                info_len_pt : 输出信息长度
Output        : None
Return        : MACR_NONE : 成功
                MACR_ERROR : 失败
Author        : TanJW
Create Date   : 2023/08/16
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" s32 AIDete_PrintfAIRsltROINum2Buff(u8 *info_pt, u32 *info_len_pt, u32 info_len_max)
{
	u32 i = 0;
	u32 next_offset = s_ai_dete_rslt_roi_num_record_next_offset;
	u32 record_save_num = s_ai_dete_rslt_roi_num_record_save_num;
	u8 *info_buff_pt = NULL;
	u32 len = 0;
	u32 total_len = 0;
	u32 len_tmp = 0;

	// 判断指针有效性
	if ((info_pt == NULL) || (info_len_pt == NULL))
	{
		if (info_len_pt != NULL)
		{
			*info_len_pt = 0;
		}

		return MACR_ERROR;
	}

	info_buff_pt = info_pt;

	SysDebug_PrintfInfo2Buff(&info_buff_pt[0], &len, info_len_max, (const s8*)"AI Result ROI Num Record:\r\n");
	// 更新缓冲区总长度
	total_len += len;

	// 计算目标缓冲区剩余空间
	len_tmp = info_len_max - total_len;
	SysDebug_PrintfInfo2Buff(&info_buff_pt[total_len], &len, len_tmp, (const s8*)"  next_offset = %u\r\n", next_offset);
	// 更新缓冲区总长度
	total_len += len;

	// 计算目标缓冲区剩余空间
	len_tmp = info_len_max - total_len;
	SysDebug_PrintfInfo2Buff(&info_buff_pt[total_len], &len, len_tmp, (const s8*)"  total num = %u\r\n", record_save_num);
	// 更新缓冲区总长度
	total_len += len;

	// 计算目标缓冲区剩余空间
	len_tmp = info_len_max - total_len;
	SysDebug_PrintfInfo2Buff(&info_buff_pt[total_len], &len, len_tmp, (const s8*)"  |-------------------------------|\r\n  |   index   |   valid_roi_num   |\r\n  |-------------------------------|\r\n");
	// 更新缓冲区总长度
	total_len += len;

	// 记录的总数量已超出缓冲区
	if (record_save_num > MACR_AI_DETE_RSLT_ROI_NUM_RECORD_BUFF_SIZE_MAX)
	{
		for (i = 0; i < MACR_AI_DETE_RSLT_ROI_NUM_RECORD_BUFF_SIZE_MAX; i++)
		{
			// 计算目标缓冲区剩余空间
			len_tmp = info_len_max - total_len;
			// 从最旧的开始输出
			SysDebug_PrintfInfo2Buff(&info_buff_pt[total_len], &len, len_tmp, (const s8*)"  |%-10u |%-10u         |\r\n  |-------------------------------|\r\n", s_ai_dete_rslt_roi_num_record_buff[next_offset].img_index, s_ai_dete_rslt_roi_num_record_buff[next_offset].valid_roi_num);
			// 更新缓冲区总长度
			total_len += len;

			next_offset++;

			if (next_offset >= MACR_AI_DETE_RSLT_ROI_NUM_RECORD_BUFF_SIZE_MAX)
			{
				next_offset = 0;
			}
		}
	}
	// 记录的数量未超出缓冲区
	else
	{
		for (i = 0; i < MACR_AI_DETE_RSLT_ROI_NUM_RECORD_BUFF_SIZE_MAX; i++)
		{
			// 计算目标缓冲区剩余空间
			len_tmp = info_len_max - total_len;
			SysDebug_PrintfInfo2Buff(&info_buff_pt[total_len], &len, len_tmp, (const s8*)"  |%-10u |%-10u         |\r\n  |-------------------------------|\r\n", s_ai_dete_rslt_roi_num_record_buff[i].img_index, s_ai_dete_rslt_roi_num_record_buff[i].valid_roi_num);
			// 更新缓冲区总长度
			total_len += len;
		}
	}

	// 将长度提供给外部
	*info_len_pt = total_len;

	return MACR_NONE;
}

/*******************************************************************************
Function      : AIDete_SetSoftwareROICenterDeteState()
Description   : 设置软件 ROI 中心挖图检测需求状态
Input         : state : 软件 ROI Center Detect 状态
                  MACR_TRUE : 无论如何都需要进行 ROI Center Detect
                  MACR_FALSE : 按需进行 ROI Center Detect
Output        : None
Output        : None
Return        : None
Author        : TanJW
Create Date   : 2024/09/18
Last Update   : None
Note          : None
*******************************************************************************/
extern s32 AIDete_SetSoftwareROICenterDeteState(s32 state)
{
	s_ai_dete_software_roi_center_dete_ctrl = state;

	return s_ai_dete_software_roi_center_dete_ctrl;
}

/*******************************************************************************
Function      : AIDete_SetAIDeteValidROIScoreThreMax()
Description   : 设置有效 ROI 上阈值
Input         : score_thre : 有效 ROI 上阈值
Output        : None
Return        : MACR_ERROR : 设置失败
                MACR_NONE : 设置成功
Author        : TanJW
Create Date   : 2024/01/12
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" s32 AIDete_SetAIDeteValidROIScoreThreMax(IN f32 score_thre)
{
	s32 ret_val = MACR_NONE;

	s_ai_dete_valid_roi_score_thre_max = score_thre;

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_GetAIDeteValidROIScoreThre()
Description   : 获取有效 ROI 阈值
Input         : None
Output        : None
Return        : 有效 ROI 阈值
Author        : TanJW
Create Date   : 2024/01/12
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" f32 AIDete_GetAIDeteValidROIScoreThreMax(void)
{
	return s_ai_dete_valid_roi_score_thre_max;
}

/*******************************************************************************
Function      : AIDete_GetAIDeteValidROIDefScoreThre()
Description   : 获取有效 ROI 上阈值默认值
Input         : None
Output        : None
Return        : 有效 ROI 上阈值默认值
Author        : TanJW
Create Date   : 2024/01/12
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" f32 AIDete_GetAIDeteValidROIDefScoreThreMax(void)
{
	return MACR_AI_DETE_RSLT_VALID_ROI_SCORE_THRE_MAX;
}

/*******************************************************************************
Function      : AIDete_SetAIDeteValidROIScoreThreMin()
Description   : 设置有效 ROI 下阈值
Input         : score_thre : 有效 ROI 下阈值
Output        : None
Return        : MACR_ERROR : 设置失败
                MACR_NONE : 设置成功
Author        : TanJW
Create Date   : 2024/01/15
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" s32 AIDete_SetAIDeteValidROIScoreThreMin(IN f32 score_thre)
{
	s32 ret_val = MACR_NONE;

	s_ai_dete_valid_roi_score_thre_min = score_thre;

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_GetAIDeteValidROIScoreThreMin()
Description   : 获取有效 ROI 下阈值
Input         : None
Output        : None
Return        : 有效 ROI 下阈值
Author        : TanJW
Create Date   : 2024/01/15
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" f32 AIDete_GetAIDeteValidROIScoreThreMin(void)
{
	return s_ai_dete_valid_roi_score_thre_min;
}

/*******************************************************************************
Function      : AIDete_GetAIDeteValidROIDefScoreThreMin()
Description   : 获取有效 ROI 下阈值默认值
Input         : None
Output        : None
Return        : 有效 ROI 下阈值默认值
Author        : TanJW
Create Date   : 2024/01/15
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" f32 AIDete_GetAIDeteValidROIDefScoreThreMin(void)
{
	return MACR_AI_DETE_RSLT_VALID_ROI_SCORE_THRE_MIN;
}

/*******************************************************************************
Function      : AIDete_OutputModelInfo()
Description   : 输出AI模块状态信息
Input         : method : 输出方法
Output        : None
Return        : MACR_NONE : 输出成功
Author        : PengHZ
Create Date   : 2025/07/03
Last Update   : None
Note          : None
*******************************************************************************/
s32 AIDete_OutputModelInfo(printf_method method)
{
	for (auto &model_info : s_ai_dete_model_info_buff)
	{
		method((s8*)"AI_Model_Tag: %s \r\n", model_info.model_tag);
		method((s8*)"\tTag_id: %d State: ", model_info.model_tag_id);
		switch (model_info.state)
		{
			case MACR_AI_DETE_MODEL_STATE_NONE:		method((s8*)"None");		break;
			case MACR_AI_DETE_MODEL_STATE_INITED:	method((s8*)"Inited");		break;
			case MACR_AI_DETE_MODEL_STATE_RUNING:	method((s8*)"Running");		break;
			default:								method((s8*)"Unknown");		break;
		}
		method((s8*)"\r\n");
		// 输出记录信息
		method((s8*)"\tAI Running Record:\r\n");
		model_info.dumpRecord(method, "\t\t");
		method((s8*)"\r\n");
	}

	return MACR_NONE;
}

/*******************************************************************************
Function      : AIDete_OutputStateInfo()
Description   : 获取AI模块状态信息
Input         : method : 输出方法
                option : 输出选项
Output        : None
Return        : MACR_NONE : 输出成功
                MACR_ERROR : 输出失败
Author        : PengHZ
Create Date   : 2025/07/03
Last Update   : None
Note          : None
*******************************************************************************/
extern "C" s32 AIDete_OutputStateInfo(printf_method method, s32 option)
{
	s32 ret_val = MACR_NONE;

	method((s8*)"\r\n^^^^^^^^^^^^^^^^^^^^Begin AI Dete Info^^^^^^^^^^^^^^^^^^^^^\r\n\r\n");
	if (option == MACR_TRACE_INFO_STATE_INFO_OPT_ALL)
	{
		AIDete_OutputModelInfo(method);
	}
	method((s8*)"\r\n^^^^^^^^^^^^^^^^^^^^End AI Dete Info^^^^^^^^^^^^^^^^^^^^^^^\r\n\r\n");

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_isPermitConfigHandler()
Description   : 检查参数是否允许设置
Input         : setting_info_pt : 参数设置信息
                arg1 : 设置参数
                arg2_pt : 设置参数
Output        : None
Return        : MACR_TRUE : 允许设置
                MACR_FALSE : 不允许设置
Author        : TanJW
Create Date   : 2025/07/09
Last Update   : None
Note          : None
*******************************************************************************/
static s32 AIDete_isPermitConfigHandler(INOUT syscfg_para_setting_str *setting_info_pt, IN u32 arg1, INOUT void *arg2_pt)
{
	s32 ret_val = MACR_TRUE;
	const u16 PARA_VAL_OFFSET = 1;
	ai_dete_model_info *model_info_pt = NULL;

	if (setting_info_pt == NULL)
	{
		return MACR_TRUE;
	}

	// 如果是 1011 参数，则需要通过 AI 初始化结果来决定是否允许设置
	if (setting_info_pt->para_id == MACR_PARACODE_1011)
	{
		if (setting_info_pt->para_val_data_pt[PARA_VAL_OFFSET] == MDFLAG_AI_ROI_Model_320x320)
		{
			// 尝试初始化 AI ROI Detect 320x320 模型，如果初始化成功则允许设置，否则不允许设置
			// 注意：此处仅仅只是尝试，因为这个参数最终不一定会被设置，仅仅只是确认一下当前环境是否允许设置。

			// 查找 ROI 检测模型对象
			model_info_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_ROI_DETECT, MACR_FALSE);
			// 如果找不到 ROI 检测模型对象，则不允许设置
			if (model_info_pt == NULL)
			{
				ret_val = MACR_FALSE;
				goto AIDETET_IS_PERMIT_CONFIG_HANDLER_EXIT;
			}

			if (model_info_pt->state == MACR_AI_DETE_MODEL_STATE_NONE)
			{
				// 如果 ROI 检测模型对象未初始化，则尝试初始化
				model_info_pt->init_ret = ai_engine_model_init(model_info_pt->model_tag, MACR_AI_DETE_NUM_SESSIONS);

				if (model_info_pt->init_ret == MACR_NONE)
				{
					// 初始化成功，但是未正式启用
					model_info_pt->state = MACR_AI_DETE_MODEL_STATE_INITED;
					ret_val = MACR_TRUE;
					goto AIDETET_IS_PERMIT_CONFIG_HANDLER_EXIT;
				}
				else
				{
					// 初始化失败，不允许设置
					ret_val = MACR_FALSE;
					goto AIDETET_IS_PERMIT_CONFIG_HANDLER_EXIT;
				}

				// SSC377 暂时不支持销毁模型，所以这里不作还原处理。
			}
			else
			{
				// 如果 ROI 检测模型对象已经初始化过了，就没有必要再检查了
				ret_val = MACR_TRUE;
				goto AIDETET_IS_PERMIT_CONFIG_HANDLER_EXIT;
			}
		}
		else if (setting_info_pt->para_val_data_pt[PARA_VAL_OFFSET] == MDFLAG_AI_ROI_Model_512x640)
		{
			// 尝试初始化 AI ROI Detect 512x640 模型，如果初始化成功则允许设置，否则不允许设置
			// 注意：此处仅仅只是尝试，因为这个参数最终不一定会被设置，仅仅只是确认一下当前环境是否允许设置。
			// 查找 ROI 检测模型对象
			model_info_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_ROI_DETECT_ALT, MACR_FALSE);
			// 如果找不到 ROI 检测模型对象，则不允许设置
			if (model_info_pt == NULL)
			{
				ret_val = MACR_FALSE;
				goto AIDETET_IS_PERMIT_CONFIG_HANDLER_EXIT;
			}

			if (model_info_pt->state == MACR_AI_DETE_MODEL_STATE_NONE)
			{
				// 如果 ROI 检测模型对象未初始化，则尝试初始化
				model_info_pt->init_ret = ai_engine_model_init(model_info_pt->model_tag, MACR_AI_DETE_NUM_SESSIONS);

				if (model_info_pt->init_ret == MACR_NONE)
				{
					// 初始化成功，但是未正式启用
					model_info_pt->state = MACR_AI_DETE_MODEL_STATE_INITED;
					ret_val = MACR_TRUE;
					goto AIDETET_IS_PERMIT_CONFIG_HANDLER_EXIT;
				}
				else
				{
					// 初始化失败，不允许设置
					ret_val = MACR_FALSE;
					goto AIDETET_IS_PERMIT_CONFIG_HANDLER_EXIT;
				}

				// SSC377 暂时不支持销毁模型，所以这里不作还原处理。
			}
			else
			{
				// 如果 ROI 检测模型对象已经初始化过了，就没有必要再检查了
				ret_val = MACR_TRUE;
				goto AIDETET_IS_PERMIT_CONFIG_HANDLER_EXIT;
			}
		}
		else
		{
			// 非法参数
			ret_val = MACR_FALSE;
		}
	}

AIDETET_IS_PERMIT_CONFIG_HANDLER_EXIT:
	return ret_val;
}

/*******************************************************************************
Function      : AIDete_HandleMessage()
Description   : AI 模块消息处理函数
Input         : msg : 消息
Output        : None
Return        : None
Author        : TanJW
Create Date   : 2025/07/09
Last Update   : None
Note          : None
*******************************************************************************/
static void AIDete_HandleMessage(IN SMsg msg)
{
	u8* para_info = (u8 *)msg.info;
	u16 para_code = 0;
	u8 para_val = 0;
	ai_dete_model_info *old_info_pt = NULL;	// 旧模型 (此为假设值, 不表示旧模型一定存在或一定使用)
	ai_dete_model_info *new_info_pt = NULL;	// 新模型

	// 参数设置内容的消息
	if (msg.id == Msg_Exe_Param_Cfg_Content)
	{
		// 从信息包中获取参数号
		para_code = para_info[MACR_SYS_CFG_PCMI_PACK_OFFSET_PARA_CODE];
		para_code += ((u16)para_info[MACR_SYS_CFG_PCMI_PACK_OFFSET_PARA_CODE + 1]) << 8;
		// 从信息包中获取第一个参数值，对于此模块而言，不考虑多参数值的问题。
		para_val = para_info[MACR_SYS_CFG_PCMI_PACK_OFFSET_PARA_VAL];

		if (para_code == MACR_PARACODE_1011)
		{
			if (para_val == MDFLAG_AI_ROI_Model_320x320)
			{
				// 尝试初始化 AI ROI Detect 320x320 模型，如果初始化成功则允许设置，否则不允许设置
				// 查找 ROI 检测模型对象
				new_info_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_ROI_DETECT, MACR_FALSE);
				old_info_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_ROI_DETECT_ALT, MACR_FALSE);
				if (new_info_pt == NULL)
				{
					// 此处应添加错误处理
					return;
				}

				// 如果新模型未初始化, 则尝试初始化
				if (new_info_pt->state == MACR_AI_DETE_MODEL_STATE_NONE)
				{
					// 如果 ROI 检测模型对象未初始化，则尝试初始化
					new_info_pt->init_ret = ai_engine_model_init(new_info_pt->model_tag, MACR_AI_DETE_NUM_SESSIONS);
					if (new_info_pt->init_ret == MACR_NONE)
					{
						// 初始化成功，但是未正式启用
						new_info_pt->state = MACR_AI_DETE_MODEL_STATE_INITED;
					}
				}

				// 如果新模型已初始化, 则进行启用
				if ((new_info_pt->state == MACR_AI_DETE_MODEL_STATE_INITED) ||
					(new_info_pt->state == MACR_AI_DETE_MODEL_STATE_RUNING))
				{
					// 启用前, 需要把旧模型给关闭掉
					if (old_info_pt != NULL)
					{
						if (old_info_pt->state == MACR_AI_DETE_MODEL_STATE_RUNING)
						{
							old_info_pt->state = MACR_AI_DETE_MODEL_STATE_INITED;
							old_info_pt->need_init = MACR_FALSE;
						}

						// 由于SSC377 暂时不支持销毁模型, 所以这里不作处理。
						// old_info_pt 销毁
					}

					// 启用新模型
					new_info_pt->need_init = MACR_TRUE;
					new_info_pt->state = MACR_AI_DETE_MODEL_STATE_RUNING;
				}
			}
			else if (para_val == MDFLAG_AI_ROI_Model_512x640)
			{
				// 尝试初始化 AI ROI Detect 512x640 模型，如果初始化成功则允许设置，否则不允许设置
				// 查找 ROI 检测模型对象
				new_info_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_ROI_DETECT_ALT, MACR_FALSE);
				old_info_pt = AIDete_GetModelInfo(AI_MODEL_TAGID_ROI_DETECT, MACR_FALSE);
				// 找不到所需要新模型, 处理失败
				if (new_info_pt == NULL)
				{
					// 此处应添加错误处理
					return;
				}

				// 如果新模型未初始化, 则尝试初始化
				if (new_info_pt->state == MACR_AI_DETE_MODEL_STATE_NONE)
				{
					// 如果 ROI 检测模型对象未初始化，则尝试初始化
					new_info_pt->init_ret = ai_engine_model_init(new_info_pt->model_tag, MACR_AI_DETE_NUM_SESSIONS);
					if (new_info_pt->init_ret == MACR_NONE)
					{
						// 初始化成功，但是未正式启用
						new_info_pt->state = MACR_AI_DETE_MODEL_STATE_INITED;
					}
				}

				// 如果新模型已初始化, 则进行启用
				if ((new_info_pt->state == MACR_AI_DETE_MODEL_STATE_INITED) ||
					(new_info_pt->state == MACR_AI_DETE_MODEL_STATE_RUNING))
				{
					// 启用前, 需要把旧模型给关闭掉
					if (old_info_pt != NULL)
					{
						if (old_info_pt->state == MACR_AI_DETE_MODEL_STATE_RUNING)
						{
							old_info_pt->state = MACR_AI_DETE_MODEL_STATE_INITED;
							old_info_pt->need_init = MACR_FALSE;
						}

						// 由于SSC377 暂时不支持销毁模型, 所以这里不作处理。
						// old_info_pt 销毁
					}

					// 启用新模型
					new_info_pt->need_init = MACR_TRUE;
					new_info_pt->state = MACR_AI_DETE_MODEL_STATE_RUNING;
				}
			}
		}
	}
}

/*******************************************************************************
Function      : AIDete_Classification_GenerateFilename()
Description   : 根据模型名称生成映射表文件名
Input         : model_name : 模型名称
                filename : 输出文件名缓冲区
                filename_size : 文件名缓冲区大小
Output        : filename : 生成的文件名
Return        : None
Author        : TangHL
Create Date   : 2025/07/29
Last Update   : None
Note          : 文件名格式: classification_mapping_<model_name>.txt
*******************************************************************************/
static void AIDete_Classification_GenerateFilename(const char* model_name, char* filename, size_t filename_size)
{
	if (model_name == NULL || filename == NULL || filename_size == 0)
	{
		return;
	}

	// 生成文件名: classification_mapping_<model_name>.txt
	snprintf(filename, filename_size, "classification_mapping_%s.txt", model_name);

	// 确保字符串以null结尾
	filename[filename_size - 1] = '\0';
}

/*******************************************************************************
Function      : AIDete_Classification_LoadMapping()
Description   : 从文件加载分类映射表
Input         : model_name : 模型名称
Output        : None
Return        : MACR_NONE : 加载成功
                MACR_ERROR : 加载失败
Author        : TangHL
Create Date   : 2025/07/28
Last Update   : 2025/07/29
Note          : 包含完整的安全检查和错误处理，支持动态模型切换
*******************************************************************************/
static s32 AIDete_Classification_LoadMapping(const char* model_name)
{
	SFile *mapping_fp = NULL;
	u8 data_info[MACR_CLS_MAPPING_DATA_INFO_SIZE_MAX] = { 0 };
	u32 tmp_offset = 0;
	u32 crc_offset = 0;
	u16 crc_val_in_file = 0;
	u16 crc_val_calculate = 0;
	u32 crc_calculate_len = 0;
	s32 ret_val = MACR_NONE;
	u16 packet_len = 0;
	u16 expected_packet_len = 0;  // 将变量声明移到函数开头
	char mapping_filename[128] = { 0 };

	// 参数检查
	if (model_name == NULL)
	{
		ret_val = MACR_ERROR;
		goto _AIDETE_CLS_LOADMAPPING_EXIT;
	}

	// 生成映射表文件名
	AIDete_Classification_GenerateFilename(model_name, mapping_filename, sizeof(mapping_filename));

	// 1. 打开映射表文件
	mapping_fp = FileOpen(mapping_filename, (char *)"r");
	if (mapping_fp == NULL)
	{
		// 文件不存在或无法打开
		ret_val = MACR_ERROR;
		goto _AIDETE_CLS_LOADMAPPING_EXIT;
	}

	// 1.1 读取文件内容
	if (FileRead(mapping_fp, data_info, MACR_CLS_MAPPING_DATA_INFO_SIZE_MAX) <= 0)
	{
		// 文件读取失败
		ret_val = MACR_ERROR;
		goto _AIDETE_CLS_LOADMAPPING_EXIT;
	}

	// 2. 校验CRC是否正确
	packet_len = (u16)(((u16)data_info[MACR_CLS_MAPPING_DATA_INFO_OFFSET_PacketLen] << 8) |
						data_info[MACR_CLS_MAPPING_DATA_INFO_OFFSET_PacketLen + 1]);

	// 2.0 验证数据包长度的合理性，防止缓冲区越界
	if (packet_len < MACR_CLS_MAPPING_DATA_INFO_OFFSET_ClassNames ||
		packet_len > (MACR_CLS_MAPPING_DATA_INFO_SIZE_MAX - 2))  // 减去CRC的2字节
	{
		// 数据包长度不合理
		ret_val = MACR_ERROR;
		goto _AIDETE_CLS_LOADMAPPING_EXIT;
	}

	crc_offset = packet_len;
	// 确保CRC偏移不会越界
	if (crc_offset + 1 >= MACR_CLS_MAPPING_DATA_INFO_SIZE_MAX)
	{
		ret_val = MACR_ERROR;
		goto _AIDETE_CLS_LOADMAPPING_EXIT;
	}

	crc_val_in_file = (u16)(((u16)data_info[crc_offset] << 8) + data_info[crc_offset + 1]);

	// 2.1 计算本次读取到的数据的CRC
	crc_calculate_len = packet_len;
	crc_val_calculate = calc_CCITT_crc(data_info, crc_calculate_len, MACR_CLS_MAPPING_CRC_VAL_INIT);

	// 2.2 计算值与读取值对比
	if (crc_val_calculate != crc_val_in_file)
	{
		// CRC校验失败
		ret_val = MACR_ERROR;
		goto _AIDETE_CLS_LOADMAPPING_EXIT;
	}

	// 3. 数据解析
	// 3.1 获取模型名称
	memcpy(g_classification_mapping_info.model_name,
		   &data_info[MACR_CLS_MAPPING_DATA_INFO_OFFSET_ModelName],
		   MAX_MODEL_NAME_LEN);

	// 确保模型名称以null结尾，防止字符串溢出
	g_classification_mapping_info.model_name[MAX_MODEL_NAME_LEN - 1] = '\0';

	// 3.2 获取类别数量
	g_classification_mapping_info.class_count = data_info[MACR_CLS_MAPPING_DATA_INFO_OFFSET_ClassCount];

	// 3.2.1 验证类别数量的合理性
	if (g_classification_mapping_info.class_count > MAX_CLASS_COUNT)
	{
		// 类别数量超出最大限制
		ret_val = MACR_ERROR;
		goto _AIDETE_CLS_LOADMAPPING_EXIT;
	}

	// 3.2.2 验证数据包长度是否与类别数量匹配
	expected_packet_len = MACR_CLS_MAPPING_DATA_INFO_OFFSET_ClassNames +
						  (g_classification_mapping_info.class_count * MAX_CLASS_NAME_LEN);
	if (packet_len != expected_packet_len)
	{
		// 数据包长度与类别数量不匹配
		ret_val = MACR_ERROR;
		goto _AIDETE_CLS_LOADMAPPING_EXIT;
	}

	// 3.3 获取类别名称数组
	tmp_offset = MACR_CLS_MAPPING_DATA_INFO_OFFSET_ClassNames;
	for (u8 i = 0; i < g_classification_mapping_info.class_count && i < MAX_CLASS_COUNT; i++)
	{
		// 确保不会越界访问
		if (tmp_offset + MAX_CLASS_NAME_LEN > MACR_CLS_MAPPING_DATA_INFO_SIZE_MAX)
		{
			ret_val = MACR_ERROR;
			goto _AIDETE_CLS_LOADMAPPING_EXIT;
		}

		memcpy(g_classification_mapping_info.class_names[i],
			   &data_info[tmp_offset],
			   MAX_CLASS_NAME_LEN);

		// 确保字符串以null结尾，防止字符串溢出
		g_classification_mapping_info.class_names[i][MAX_CLASS_NAME_LEN - 1] = '\0';

		tmp_offset += MAX_CLASS_NAME_LEN;
	}

_AIDETE_CLS_LOADMAPPING_EXIT:
	// 关闭文件
	if (mapping_fp != NULL)
	{
		FileClose(mapping_fp);
		mapping_fp = NULL;
	}

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_Classification_SaveMapping()
Description   : 保存分类映射表到文件
Input         : model_name : 模型名称
Output        : None
Return        : MACR_NONE : 保存成功
                MACR_ERROR : 保存失败
Author        : TangHL
Create Date   : 2025/07/28
Last Update   : 2025/07/29
Note          : 包含完整的安全检查和错误处理，支持动态模型切换
*******************************************************************************/
static s32 AIDete_Classification_SaveMapping(const char* model_name)
{
	SFile *mapping_fp = NULL;
	u8 data_info[MACR_CLS_MAPPING_DATA_INFO_SIZE_MAX] = { 0 };
	u32 tmp_offset = 0;
	u32 crc_offset = 0;
	u16 crc_val_calculate = 0;
	u32 crc_calculate_len = 0;
	s32 ret_val = MACR_NONE;
	u16 packet_len = 0;
	char mapping_filename[128] = { 0 };
	u32 class_data_size = 0;  // 移到函数开头声明
	u32 total_packet_len = 0;  // 移到函数开头声明

	// 参数检查
	if (model_name == NULL)
	{
		ret_val = MACR_ERROR;
		goto _AIDETE_CLS_SAVEMAPPING_EXIT;
	}

	// 生成映射表文件名
	AIDete_Classification_GenerateFilename(model_name, mapping_filename, sizeof(mapping_filename));

	// 1. 构建数据包
	// 1.0 验证类别数量的合理性
	if (g_classification_mapping_info.class_count > MAX_CLASS_COUNT)
	{
		return MACR_ERROR;
	}

	// 1.1 计算数据包长度，防止溢出
	class_data_size = (u32)g_classification_mapping_info.class_count * MAX_CLASS_NAME_LEN;
	total_packet_len = MACR_CLS_MAPPING_DATA_INFO_OFFSET_ClassNames + class_data_size;

	// 检查是否会超出缓冲区大小（减去CRC的2字节）
	if (total_packet_len > (MACR_CLS_MAPPING_DATA_INFO_SIZE_MAX - 2))
	{
		return MACR_ERROR;
	}

	packet_len = (u16)total_packet_len;

	// 1.2 填充数据包长度
	data_info[MACR_CLS_MAPPING_DATA_INFO_OFFSET_PacketLen] = (u8)(packet_len >> 8);
	data_info[MACR_CLS_MAPPING_DATA_INFO_OFFSET_PacketLen + 1] = (u8)(packet_len & 0xFF);

	// 1.3 填充版本信息
	memcpy(&data_info[MACR_CLS_MAPPING_DATA_INFO_OFFSET_Version],
		   g_classification_mapping_version,
		   MACR_CLS_MAPPING_VERSION_SIZE_MAX);

	// 1.4 填充模型名称
	memcpy(&data_info[MACR_CLS_MAPPING_DATA_INFO_OFFSET_ModelName],
		   g_classification_mapping_info.model_name,
		   MAX_MODEL_NAME_LEN);

	// 1.5 填充类别数量
	data_info[MACR_CLS_MAPPING_DATA_INFO_OFFSET_ClassCount] = g_classification_mapping_info.class_count;

	// 1.6 填充类别名称数组
	tmp_offset = MACR_CLS_MAPPING_DATA_INFO_OFFSET_ClassNames;
	for (u8 i = 0; i < g_classification_mapping_info.class_count && i < MAX_CLASS_COUNT; i++)
	{
		// 确保不会越界写入
		if (tmp_offset + MAX_CLASS_NAME_LEN > MACR_CLS_MAPPING_DATA_INFO_SIZE_MAX)
		{
			ret_val = MACR_ERROR;
			goto _AIDETE_CLS_SAVEMAPPING_EXIT;
		}

		memcpy(&data_info[tmp_offset],
			   g_classification_mapping_info.class_names[i],
			   MAX_CLASS_NAME_LEN);
		tmp_offset += MAX_CLASS_NAME_LEN;
	}

	// 2. 计算并填充CRC
	crc_calculate_len = packet_len;
	crc_val_calculate = calc_CCITT_crc(data_info, crc_calculate_len, MACR_CLS_MAPPING_CRC_VAL_INIT);

	// 2.1 填充CRC值
	crc_offset = packet_len;
	// 确保CRC写入不会越界
	if (crc_offset + 1 >= MACR_CLS_MAPPING_DATA_INFO_SIZE_MAX)
	{
		ret_val = MACR_ERROR;
		goto _AIDETE_CLS_SAVEMAPPING_EXIT;
	}

	data_info[crc_offset] = (u8)(crc_val_calculate >> 8);
	data_info[crc_offset + 1] = (u8)(crc_val_calculate & 0xFF);

	// 3. 写入文件
	mapping_fp = FileOpen(mapping_filename, (char *)"w+");
	if (mapping_fp == NULL)
	{
		ret_val = MACR_ERROR;
		goto _AIDETE_CLS_SAVEMAPPING_EXIT;
	}

	if (FileWrite(mapping_fp, data_info, MACR_CLS_MAPPING_DATA_INFO_SIZE_MAX) <= 0)
	{
		ret_val = MACR_ERROR;
		goto _AIDETE_CLS_SAVEMAPPING_EXIT;
	}

_AIDETE_CLS_SAVEMAPPING_EXIT:
	// 关闭文件
	if (mapping_fp != NULL)
	{
		FileClose(mapping_fp);
		mapping_fp = NULL;
	}

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_Classification_SetDefaultMapping()
Description   : 设置默认分类映射表
Input         : None
Output        : None
Return        : MACR_NONE : 设置成功
                MACR_ERROR : 设置失败
Author        : TangHL
Create Date   : 2025/07/28
Last Update   : None
Note          : 创建默认的映射表配置
*******************************************************************************/
static s32 AIDete_Classification_SetDefaultMapping(void)
{
	// 清空映射表信息
	memset(&g_classification_mapping_info, 0, sizeof(g_classification_mapping_info));

	// 设置模型名称
	strncpy(g_classification_mapping_info.model_name, AI_MODEL_CLASSIFICATION, MAX_MODEL_NAME_LEN - 1);
	g_classification_mapping_info.model_name[MAX_MODEL_NAME_LEN - 1] = '\0';

	// 设置类别数量
	g_classification_mapping_info.class_count = 10;

	// 设置默认类别名称
	for (u8 i = 0; i < g_classification_mapping_info.class_count && i < MAX_CLASS_COUNT; i++)
	{
		snprintf(g_classification_mapping_info.class_names[i], MAX_CLASS_NAME_LEN, "class_%d", i);
		g_classification_mapping_info.class_names[i][MAX_CLASS_NAME_LEN - 1] = '\0';
	}

	return MACR_NONE;
}

/*******************************************************************************
Function      : AIDete_Classification_EnsureMappingLoaded()
Description   : 确保指定模型的映射表已加载
Input         : model_name : 模型名称
Output        : None
Return        : MACR_NONE : 映射表已加载或加载成功
                MACR_ERROR : 加载失败
Author        : TangHL
Create Date   : 2025/07/29
Last Update   : None
Note          : 如果当前加载的映射表不匹配，则动态切换到指定模型的映射表
*******************************************************************************/
static s32 AIDete_Classification_EnsureMappingLoaded(const char* model_name)
{
	s32 ret_val = MACR_NONE;

	// 参数检查
	if (model_name == NULL)
	{
		return MACR_ERROR;
	}

	// 检查当前是否已加载了正确的映射表
	if (g_classification_mapping_loaded == MACR_TRUE &&
		strncmp(g_current_loaded_model, model_name, MAX_MODEL_NAME_LEN) == 0)
	{
		// 已加载正确的映射表，直接返回
		return MACR_NONE;
	}

	// 需要加载新的映射表
	ret_val = AIDete_Classification_LoadMapping(model_name);

	// 更新加载状态
	if (ret_val == MACR_NONE)
	{
		g_classification_mapping_loaded = MACR_TRUE;
		strncpy(g_current_loaded_model, model_name, MAX_MODEL_NAME_LEN - 1);
		g_current_loaded_model[MAX_MODEL_NAME_LEN - 1] = '\0';
	}
	else
	{
		g_classification_mapping_loaded = MACR_FALSE;
		memset(g_current_loaded_model, 0, sizeof(g_current_loaded_model));
	}

	return ret_val;
}

/*******************************************************************************
Function      : AIDete_Classification_InitMapping()
Description   : 初始化分类映射表
Input         : None
Output        : None
Return        : MACR_NONE : 初始化成功
                MACR_ERROR : 初始化失败
Author        : TangHL
Create Date   : 2025/07/28
Last Update   : 2025/07/29
Note          : 修改逻辑：初始化时不加载任何映射表，采用按需加载策略
*******************************************************************************/
static s32 AIDete_Classification_InitMapping(void)
{
	// 初始化全局变量
	g_classification_mapping_loaded = MACR_FALSE;
	memset(g_current_loaded_model, 0, sizeof(g_current_loaded_model));
	memset(g_classification_mapping_filename, 0, sizeof(g_classification_mapping_filename));

	// 采用按需加载策略，不在初始化时加载任何映射表
	// 映射表将在首次调用GetClassName时根据模型名称动态加载

	return MACR_NONE;
}

/*******************************************************************************
Function      : AIDete_Classification_GetClassName()
Description   : 根据模型名称和类别ID获取类别名称
Input         : model_name : 模型名称
                class_id : 类别ID
Output        : None
Return        : 类别名称指针，失败返回格式化的索引字符串
Author        : TangHL
Create Date   : 2025/07/28
Last Update   : 2025/07/29
Note          : 支持多模型动态切换，按需加载映射表
*******************************************************************************/
static const char* AIDete_Classification_GetClassName(const char* model_name, int class_id)
{
	s32 load_result = MACR_NONE;

	// 检查参数有效性
	if (model_name == NULL || class_id < 0 || class_id >= MAX_CLASS_COUNT)
	{
		// 参数无效时，返回格式化的错误信息
		snprintf(g_classification_fallback_name, sizeof(g_classification_fallback_name), "invalid_param");
		return g_classification_fallback_name;
	}

	// 确保指定模型的映射表已加载
	load_result = AIDete_Classification_EnsureMappingLoaded(model_name);
	if (load_result != MACR_NONE)
	{
		// 映射表加载失败时，返回格式化的类别索引
		snprintf(g_classification_fallback_name, sizeof(g_classification_fallback_name), "class_%d", class_id);
		return g_classification_fallback_name;
	}

	// 检查类别ID是否在有效范围内
	if (class_id >= g_classification_mapping_info.class_count)
	{
		// 类别ID超出范围时，返回格式化的类别索引
		snprintf(g_classification_fallback_name, sizeof(g_classification_fallback_name), "class_%d", class_id);
		return g_classification_fallback_name;
	}

	// 返回类别名称
	return g_classification_mapping_info.class_names[class_id];
}

#else
extern "C" s32 AIDete_Init(void)
{
	return MACR_ERROR;
}

extern "C" s32 AIDete_GetInitResult(void)
{
	// 没有AI模块时，应返回正常值
	return MACR_NONE;
}

extern "C" s32 AIDete_CalcResult(IN g_ImageAreaStruct *img_area_pt, u32 dete_mode, IN CoordinateVOC *roi_coord_pt, IN CoordinateYOLO *center_coord_pt)
{
	return MACR_ERROR;
}

extern "C" s32 AIDete_ClrResult(INOUT g_ImageAreaStruct *img_area_pt)
{
	return MACR_ERROR;
}

extern "C" s32 AIDete_WriteResult2Buffer(void *ai_dete_rslt_pt, u8 *buffer, u32 max_len)
{
	return MACR_ERROR;
}

extern "C" s32 AIDete_ContourDet_WriteResult2Buffer(void *ai_dete_rslt_pt, u8 *buffer, u32 max_len)
{
	return MACR_ERROR;
}

extern "C" s32 AIDete_OCRDet_WriteResult2Buffer(void *ai_dete_rslt_pt, u8 *buffer, u32 max_len)
{
	return MACR_ERROR;
}

extern "C" void *AIDete_ROI_Dete(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN CoordinateVOC *roi_coord)
{
	return NULL;
}

extern "C" void *AIDete_Finder_Dete(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN CoordinateVOC *roi_coord)
{
	return NULL;
}

extern "C" void *AIDete_Dot_Dete(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN CoordinateVOC *roi_coord)
{
	return NULL;
}

extern "C" void *AIDete_Restorer(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN CoordinateVOC *roi_coord)
{
	return NULL;
}

extern "C" void *AIDete_CAP_Dete(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN CoordinateVOC *roi_coord)
{
	return NULL;
}

extern "C" void *AIDete_OCRDet(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN CoordinateVOC *roi_coord)
{
	return NULL;
}

extern "C" s32 AIDete_isDeteSucc(void *ai_dete_rslt_pt, f32 score_thre)
{
	return MACR_ERROR;
}

extern "C" s32 AIDete_GetValidROINum(void *ai_dete_rslt_pt, f32 score_thre)
{
	return MACR_ERROR;
}

extern "C" s32 AIDete_GetROIWithHighestScore(void *ai_dete_rslt_pt, f32 *score_pt, f32 *x0_pt, f32 *y0_pt, f32 *x1_pt, f32 *y1_pt, s32 *class_id_pt)
{
	return MACR_ERROR;
}

extern "C" s32 AIDete_SetAIRsltROINumRecord(IN g_ImageAreaStruct *img_area_pt)
{
	return MACR_ERROR;
}

extern "C" s32 AIDete_GetEmptyAIRsltNumInIndexSegment(IN u32 index_min, IN u32 index_max, OUT s32 *rslt_num_pt)
{
	return MACR_ERROR;
}

extern "C" s32 AIDete_OutputAIRsltROINum(printf_method method)
{
	return MACR_ERROR;
}

extern "C" s32 AIDete_PrintfAIRsltROINum2Buff(u8 *info_pt, u32 *info_len_pt, u32 info_len_max)
{
	return MACR_ERROR;
}

extern "C" void *AIDete_GetOCRString(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN QuadPoints *roi_coord)
{
	return NULL;
}

extern "C" s32 AIDete_LPR_Dete(IN g_ImageAreaStruct *img_area_pt, OUT u8* out, OUT s32 *rect)
{
	return MACR_ERROR;
}

extern "C" s32 AIDete_OCRGetString_WriteResult2Buffer(IN void *ai_dete_rslt_pt, OUT u8 *buffer, IN u32 max_len)
{
	return MACR_ERROR;
}

extern "C" s32 AIDete_OCRGetStringPosition(IN void *ai_dete_rslt_pt, OUT u8 *buffer, IN u32 max_len)
{
	return MACR_ERROR;
}

extern "C" void *AIDete_OCRDet_General(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN CoordinateVOC *roi_coord)
{
	return MACR_NULL;
}

extern "C" void *AIDete_OCRRec_General(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN QuadPoints *roi_coord)
{
	return MACR_NULL;
}

extern "C" s32 AIDete_OCRGeneral_DeteString(IN g_ImageAreaStruct *img_area_pt, IN u8* buff, IN u32* buff_len, IN const u32 buff_size)
{
	return MACR_ERROR;
}

extern "C" void *AIDete_Classification_Dete(IN g_ImageAreaStruct *img_area_pt)
{
	return NULL;
}

extern "C" void *AIDete_Classification_Dete_WithClassName(IN g_ImageAreaStruct *img_area_pt)
{
	return NULL;
}

extern "C" void *AIDete_Classification_Dete_WithClassName_ByModel(IN g_ImageAreaStruct *img_area_pt, IN const char* model_name)
{
	return NULL;
}

extern "C" s32 AIDete_Classification_WriteResult2Buffer(void *ai_dete_rslt_pt, u8 *buffer, u32 max_len)
{
	return MACR_ERROR;
}

extern s32 AIDete_SetSoftwareROICenterDeteState(s32 state)
{
	return MACR_ERROR;
}

extern "C" s32 AIDete_OutputStateInfo(printf_method method, s32 option)
{
	s32 ret_val = MACR_NONE;

	method((s8*)"\r\n^^^^^^^^^^^^^^^^^^^^Begin AI Dete Info^^^^^^^^^^^^^^^^^^^^^\r\n\r\n");
	method((s8*)"\tAI Detect Function is not supported in this version.\r\n");
	method((s8*)"\r\n^^^^^^^^^^^^^^^^^^^^End AI Dete Info^^^^^^^^^^^^^^^^^^^^^^^\r\n\r\n");

	return ret_val;
}

#endif /* #if (MACR_FUNCTION_AI_DETECT == 1) */

/******************************** END OF FILE *********************************/

