//==============================================================================
// Copyright (C), 2004-2023, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: ai_detect.cpp
// Author		: TanJW
// Created On	: 2023 April 10, Mon
// Description	: AI Detect Result
//
// History
// 0. V1.0, Created by TanJW, 2023 April 10, Mon
//
//==============================================================================
#ifndef AI_DETECT_H_
#define AI_DETECT_H_

#ifdef __cplusplus
extern "C" {
#endif

/*
 * Headers
 */
#include "common.h"
#include "image.h"
#include "./system/ai_engine/Common/include/AIEngineCommon.h"

#define MACR_AI_DETE_RESULT_BUFF_INDEX_INVALID					(-1)

#define MACR_AI_DETE_MODE_NONE									0				// 无效值
#define MACR_AI_DETE_MODE_COORD									(0x01<<0)		// 检测坐标区域
#define MACR_AI_DETE_MODE_CENTER								(0x01<<1)		// 检测中心点区域
#define MACR_AI_DETE_MODE_CLASSIFICATION						(0x01<<2)		// 分类检测模式

// 调试信息(%DBGIF)AIDETE 允许输出选项
#define MACR_AIDETE_STATE_INFO_OPT_ALL				0	// 全部信息

#define MACR_AIDETE_STATE_INFO_OPT_MIN				MACR_AIDETE_STATE_INFO_OPT_ALL				// 选项的最大值
#define MACR_AIDETE_STATE_INFO_OPT_MAX				MACR_AIDETE_STATE_INFO_OPT_ALL				// 选项的最小值
#define MACR_AIDETE_STATE_INFO_OPT_DEF				MACR_AIDETE_STATE_INFO_OPT_ALL				// 选项的默认值


/*******************************************************************************
Function      : AIDete_Init()
Description   : 初始化
Input         : None
Output        : None
Return        : MACR_NONE : 初始化成功
				MACR_ERROR : 初始化失败
Author        : TanJW
Create Date   : 2023/04/10
Last Update   : None
Note          : None
*******************************************************************************/
s32 AIDete_Init(void);

/*******************************************************************************
Function      : AIDete_GetInitResult()
Description   : 获取AI初始化结果
Input         : None
Output        : None
Return        : MACR_NONE : 初始化成功
				MACR_ERROR : 初始化失败
Author        : PengHZ
Create Date   : 2025/07/02
Last Update   : None
Note          : None
*******************************************************************************/
s32 AIDete_GetInitResult(void);

/*******************************************************************************
Function      : AIDete_CalcResult()
Description   : AI 检测计算结果
Input         : img_area_pt : 图像空间指针
Output        : None
Return        : MACR_NONE : 计算成功
				MACR_ERROR : 计算失败
Author        : TanJW
Create Date   : 2023/04/10
Last Update   : None
Note          : None
*******************************************************************************/
s32 AIDete_CalcResult(IN g_ImageAreaStruct *img_area_pt, u32 dete_mode, IN CoordinateVOC *roi_coord_pt, IN CoordinateYOLO *center_coord_pt);

/*******************************************************************************
Function      : AIDete_ClrResult()
Description   : 清除 AI 检测计算结果
Input         : img_area_pt : 清除结果前的图像指针
Output        : img_area_pt : 清除结果后的图像指针
Return        : MACR_NONE : 清除成功
				MACR_ERROR : 清除失败
Author        : TanJW
Create Date   : 2023/04/10
Last Update   : None
Note          : None
*******************************************************************************/
s32 AIDete_ClrResult(INOUT g_ImageAreaStruct *img_area_pt);

/*******************************************************************************
Function      : AIDete_WriteResult2Buffer()
Description   : 获取 AI 检测计算结果
Input         : ai_dete_rslt_pt : AI 检测结果
			  : max_len : buffer 空间最大长度
Output        : buffer : 检测结果输出空间
Return        : MACR_ERROR : 获取失败
				Others : 获取成功（返回值为buffer中数据有效长度）
Author        : DongDL
Create Date   : 2023/05/10
Last Update   : None
Note          : None
*******************************************************************************/
s32 AIDete_WriteResult2Buffer(void *ai_dete_rslt_pt, u8 *buffer, u32 max_len);

/*******************************************************************************
Function      : AIDete_ContourDet_WriteResult2Buffer()
Description   : 获取 AI Contour_Det检测计算结果
Input         : img_area_pt : 图像存储空间
			  : max_len : buffer 空间最大长度
Output        : buffer : 检测结果输出空间
Return        : MACR_ERROR : 获取失败
				Others : 获取成功（返回值为buffer中数据有效长度）
Author        : PengHZ
Create Date   : 2024/11/01
Last Update   : None
Note          : None
*******************************************************************************/
s32 AIDete_ContourDet_WriteResult2Buffer(void *ai_dete_rslt_pt, u8 *buffer, u32 max_len);

/*******************************************************************************
Function      : AIDete_OCRDet_WriteResult2Buffer()
Description   : 获取 AI OCR_Det检测计算结果
Input         : img_area_pt : 图像存储空间
			  : max_len : buffer 空间最大长度
Output        : buffer : 检测结果输出空间
Return        : MACR_ERROR : 获取失败
				Others : 获取成功（返回值为buffer中数据有效长度）
Author        : PengHZ
Create Date   : 2024/05/21
Last Update   : None
Note          : None
*******************************************************************************/
s32 AIDete_OCRDet_WriteResult2Buffer(void *ai_dete_rslt_pt, u8 *buffer, u32 max_len);

/*******************************************************************************
Function      : AIDete_ROI_Dete()
Description   : AI ROI 检测函数
Input         : img_area_pt : 图像存储空间
			  : roi_ena : 是否开启ROI检测
			  : roi_coord : ROI检测区域
Output        : None
Return        : NULL : 检测失败
				Others : AI 检测结果
Author        : DongDL
Create Date   : 2023/07/07
Last Update   : None
Note          : None
*******************************************************************************/
void *AIDete_ROI_Dete(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN CoordinateVOC *roi_coord);

/*******************************************************************************
Function      : AIDete_Contour_Det()
Description   : AI Contour_Det 检测函数
Input         : img_area_pt : 图像存储空间
			  : roi_ena : 是否开启ROI检测 (暂未使用)
			  : roi_coord : ROI检测区域 (暂未使用)
Output        : None
Return        : NULL : 检测失败
				Others : AI 检测结果
Author        : PengHZ
Create Date   : 2024/11/01
Last Update   : None
Note          : None
*******************************************************************************/
void *AIDete_Contour_Det(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN CoordinateVOC *roi_coord);

/*******************************************************************************
Function      : AIDete_Finder_Dete()
Description   : AI 标识符 检测函数
Input         : img_area_pt : 图像存储空间
			  : roi_ena : 是否开启ROI检测
			  : roi_coord : ROI检测区域
Output        : None
Return        : NULL : 检测失败
				Others : AI 检测结果
Author        : DongDL
Create Date   : 2023/07/07
Last Update   : None
Note          : None
*******************************************************************************/
void *AIDete_Finder_Dete(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN CoordinateVOC *roi_coord);

/*******************************************************************************
Function      : AIDete_Dot_Dete()
Description   : AI Dot 检测函数
Input         : img_area_pt : 图像存储空间
			  : roi_ena : 是否开启ROI检测
			  : roi_coord : ROI检测区域
Output        : None
Return        : NULL : 检测失败
				Others : AI 检测结果
Author        : DongDL
Create Date   : 2023/12/19
Last Update   : None
Note          : None
*******************************************************************************/
void *AIDete_Dot_Dete(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN CoordinateVOC *roi_coord);

/*******************************************************************************
Function      : AIDete_LPR_Dete()
Description   : AI LPR 检测函数
Input         : 
				img_area_pt : 图像存储空间
			  : roi_ena : 是否开启ROI检测
			  : roi_coord : ROI检测区域
Output        : None
Return        : NULL : 检测失败
				Others : AI 检测结果
Author        : PengHZ
Create Date   : 2025/03/25
Last Update   : None
Note          : None
*******************************************************************************/
s32 AIDete_LPR_Dete(IN g_ImageAreaStruct *img_area_pt, OUT u8* out, s32 *rect);


/*******************************************************************************
Function      : AIDete_Restorer()
Description   : AI 复原检测函数
Input         : img_area_pt : 图像存储空间
              : roi_ena : 是否开启ROI检测
              : roi_coord : ROI检测区域
Output        : None
Return        : NULL : 检测失败
                Others : AI 检测结果
Author        : PengHZ
Create Date   : 2025/04/11
Last Update   : 2025/04/11
Note          : None
*******************************************************************************/
void *AIDete_Restorer(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN CoordinateVOC *roi_coord);

/*******************************************************************************
Function      : AIDete_Restorer_WriteResult2StaticBuffer()
Description   : 将AI 复原检测结果写入到静态缓冲区
Input         : result_pt : AI 检测结果
Output        : None
Return        : NULL : 检测失败
                Others : AI 检测结果
Author        : PengHZ
Create Date   : 2025/04/11
Last Update   : None
Note          : None
*******************************************************************************/
void *AIDete_Restorer_WriteResult2StaticBuffer(IN void *result_pt);

/*******************************************************************************
Function      : AIDete_OCRDet()
Description   : AI OCR_Det 检测函数
Input         : img_area_pt : 图像存储空间
			  : roi_ena : 是否开启ROI检测 (暂未使用)
			  : roi_coord : ROI检测区域 (暂未使用)
Output        : None
Return        : NULL : 检测失败
				Others : AI 检测结果
Author        : PengHZ
Create Date   : 2024/05/21
Last Update   : None
Note          : None
*******************************************************************************/
void *AIDete_OCRDet(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN CoordinateVOC *roi_coord);

/*******************************************************************************
Function      : AIDete_CAP_Dete()
Description   : AI 试管检测模型 检测函数
Input         : img_area_pt : 图像存储空间
			  : roi_ena : 是否开启ROI检测
			  : roi_coord : ROI检测区域
Output        : None
Return        : NULL : 检测失败
				Others : AI 检测结果
Author        : PengHZ
Create Date   : 2024/07/01
Last Update   : None
Note          : None
*******************************************************************************/
void *AIDete_CAP_Dete(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN CoordinateVOC *roi_coord);

/*******************************************************************************
Function      : AIDete_Classification_Dete()
Description   : AI 分类检测函数
Input         : img_area_pt : 图像存储空间
Output        : None
Return        : NULL : 检测失败
                Others : AI 分类检测结果
Author        : TangHL
Create Date   : 2025/07/24
Last Update   : None
Note          : None
*******************************************************************************/
void *AIDete_Classification_Dete(IN g_ImageAreaStruct *img_area_pt);

/*******************************************************************************
Function      : AIDete_Classification_Dete_WithClassName()
Description   : AI 分类检测函数（带类别名称）
Input         : img_area_pt : 图像存储空间
Output        : None
Return        : NULL : 检测失败
                Others : AI 分类检测结果（包含类别名称）
Author        : TangHL
Create Date   : 2025/07/28
Last Update   : 2025/07/29
Note          : 支持多模型动态切换，自动识别当前使用的分类模型
*******************************************************************************/
void *AIDete_Classification_Dete_WithClassName(IN g_ImageAreaStruct *img_area_pt);

/*******************************************************************************
Function      : AIDete_Classification_Dete_WithClassName_ByModel()
Description   : AI 分类检测函数（带类别名称，指定模型）
Input         : img_area_pt : 图像存储空间
                model_name : 指定的模型名称
Output        : None
Return        : NULL : 检测失败
                Others : AI 分类检测结果（包含类别名称）
Author        : TangHL
Create Date   : 2025/07/29
Last Update   : None
Note          : 支持指定模型名称进行分类检测，用于多模型场景
*******************************************************************************/
void *AIDete_Classification_Dete_WithClassName_ByModel(IN g_ImageAreaStruct *img_area_pt, IN const char* model_name);

/*******************************************************************************
Function      : AIDete_Classification_WriteResult2Buffer()
Description   : 获取 AI 分类检测计算结果
Input         : ai_dete_rslt_pt : AI 检测结果
              : max_len : buffer 空间最大长度
Output        : buffer : 检测结果输出空间
Return        : MACR_ERROR : 获取失败
                Others : 获取成功（返回值为buffer中数据有效长度）
Author        : TangHL
Create Date   : 2025/07/24
Last Update   : 2025/07/30
Note          : 输出格式：前4字节(结果数量) + 每个结果(4字节classID + 4字节score + 1字节className长度 + N字节className)
*******************************************************************************/
s32 AIDete_Classification_WriteResult2Buffer(void *ai_dete_rslt_pt, u8 *buffer, u32 max_len);

/*******************************************************************************
Function      : AIDete_isDeteSucc()
Description   : 判断图像中是否存在置信度超过阈值的条码
Input         : ai_dete_rslt_pt : AI 检测结果
				score_max : 置信度阈值
Output        : None
Return        : MACR_TRUE : 条码存在
				MACR_FALSE : 条码不存在
				MACR_ERROE : 查询失败
Author        : DongDL
Create Date   : 2023/07/25
Last Update   : None
Note          : None
*******************************************************************************/
s32 AIDete_isDeteSucc(void *ai_dete_rslt_pt, f32 score_thre);

/*******************************************************************************
Function      : AIDete_GetValidROINum()
Description   : 获取AI检测结果中有效ROI的数量
Input         : ai_dete_rslt_pt : AI 检测结果
				score_thre : 置信度阈值
Output        : None
Return        : MACR_ERROR : 获取失败
				Others : AI 检测结果中有效 ROI 的数量
Author        : DongDL
Create Date   : 2023/08/17
Last Update   : None
Note          : None
*******************************************************************************/
s32 AIDete_GetValidROINum(void *ai_dete_rslt_pt, f32 score_thre);

/*******************************************************************************
Function      : AIDete_GetROIWithHighestScore()
Description   : 获取AI检测结果中置信度最高的ROI
Input         : ai_dete_rslt_pt : AI 检测结果
Output        : score_pt：置信度
				x0_pt：ROI左上角坐标
				y0_pt：ROI左上角坐标
				x1_pt：ROI右下角坐标
				y1_pt：ROI右下角坐标
				class_id_pt：AI 分类类型
Return        : MACR_ERROR : 获取失败（AI检测结果中无ROI）
				MACR_TRUE  : 获取成功
Author        : DongDL
Create Date   : 2023/08/24
Last Update   : None
Note          : None
*******************************************************************************/
s32 AIDete_GetROIWithHighestScore(void *ai_dete_rslt_pt, f32 *score_pt, f32 *x0_pt, f32 *y0_pt, f32 *x1_pt, f32 *y1_pt, s32 *class_id_pt);

/*******************************************************************************
Function      : AIDete_SetAIRsltROINumRecord()
Description   : 设置 ROI 数量记录，优先全局结果，如果全局结果 ROI 数量不符要求再保存局部结果 ROI 数量
Input         : img_area_pt : 图像结构体指针，里面包含所有 AI 检测结果
Output        : None
Return        : MACR_NONE : 成功
				MACR_ERROR : 失败
Author        : TanJW
Create Date   : 2023/08/16
Last Update   : None
Note          : None
*******************************************************************************/
s32 AIDete_SetAIRsltROINumRecord(IN g_ImageAreaStruct *img_area_pt);

/*******************************************************************************
Function      : AIDete_GetEmptyAIRsltNumInIndexSegment()
Description   : 获取 ROI 数量记录中处于索引段范围内且有效 ROI 数量为0的记录数量
Input         : index_min : 有效索引段的最小值
				index_max : 有效索引段的最大值
Output        : rslt_num_pt : 有效 ROI 数量为0的记录数量
Return        : MACR_NONE : 成功
				MACR_ERROR : 失败
Author        : DongDL
Create Date   : 2023/08/16
Last Update   : None
Note          : None
*******************************************************************************/
s32 AIDete_GetEmptyAIRsltNumInIndexSegment(IN u32 index_min, IN u32 index_max, OUT s32 *rslt_num_pt);

/*******************************************************************************
Function      : AIDete_OutputAIRsltROINum()
Description   : 输出 ROI 数量记录
Input         : method : 打印方法
Output        : None
Return        : MACR_NONE : 成功
				MACR_ERROR : 失败
Author        : TanJW
Create Date   : 2023/08/16
Last Update   : None
Note          : None
*******************************************************************************/
s32 AIDete_OutputAIRsltROINum(printf_method method);

/*******************************************************************************
Function      : AIDete_PrintfAIRsltROINum2Buff()
Description   : 打印 ROI 数量记录到缓冲区
Input         : info_len_max : 输出缓冲区总大小
Output        : info_pt : 输出信息缓冲区
				info_len_pt : 输出信息长度
Output        : None
Return        : MACR_NONE : 成功
				MACR_ERROR : 失败
Author        : TanJW
Create Date   : 2023/08/16
Last Update   : None
Note          : None
*******************************************************************************/
s32 AIDete_PrintfAIRsltROINum2Buff(u8 *info_pt, u32 *info_len_pt, u32 info_len_max);

/*******************************************************************************
Function      : AIDete_GetOCRString()
Description   : 获取 OCR 转换字符串
Input         : img_area_pt : 图像存储空间
			  : roi_ena : 是否开启ROI检测 (暂未使用)
			  : roi_coord : ROI检测区域 (暂未使用)
Output        : None
Return        : NULL : 获取失败
				Others : 保存有字符串的 vector 指针
Author        : TanJW
Create Date   : 2024/05/23
Last Update   : None
Note          : None
*******************************************************************************/
void *AIDete_GetOCRString(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN QuadPoints *roi_coord);

/*******************************************************************************
Function      : AIDete_OCRGetString_WriteResult2Buffer()
Description   : 获取 AI OCR_GetString 检测计算结果
Input         : img_area_pt : 图像存储空间
			  : max_len : buffer 空间最大长度
Output        : buffer : 检测结果输出空间
Return        : MACR_ERROR : 获取失败
				Others : 获取成功（返回值为buffer中数据有效长度）
Author        : TanJW
Create Date   : 2024/05/24
Last Update   : None
Note          : None
*******************************************************************************/
s32 AIDete_OCRGetString_WriteResult2Buffer(IN void *ai_dete_rslt_pt, OUT u8 *buffer, IN u32 max_len);

/*******************************************************************************
Function      : AIDete_Restorer_WriteResult2Buffer()
Description   : 将 AI 检测结果以数组形式写入 buffer
Input         : ai_dete_rslt_pt : AI 检测结果
              : buffer : 数组形式的 AI 检测结果
              : max_len : buffer 可写入区域的长度
Output        : buffer : 数组形式的 AI 检测结果
Return        : MACR_ERROR : 写入失败
                Others : 写入成功（返回值为buffer中数据有效长度）
Author        : PengHZ
Create Date   : 2025/04/16
Last Update   : 2025/04/16
Note          : None
*******************************************************************************/
s32 AIDete_Restorer_WriteResult2Buffer(void *ai_dete_rslt_pt, u8 *buffer, u32 max_len);

/*******************************************************************************
Function      : AIDete_OCRGetStringPosition()
Description   : 将 OCR 字符坐标以数组形式写入 buffer
Input         : img_area_pt : 图像存储空间
			  : max_len : buffer 空间最大长度
Output        : buffer : 检测结果输出空间
Return        : MACR_ERROR : 获取失败
				Others : 获取成功（返回值为buffer中数据有效长度）
Author        : TanJW
Create Date   : 2024/05/24
Last Update   : None
Note          : None
*******************************************************************************/
s32 AIDete_OCRGetStringPosition(IN void *ai_dete_rslt_pt, OUT u8 *buffer, IN u32 max_len);

/*******************************************************************************
Function      : AIDete_OCRDet_General()
Description   : AI 通用 OCR 字符位置标定模型 检测函数
Input         : img_area_pt : 图像存储空间
              : roi_ena : 是否开启ROI检测 (暂未使用)
              : roi_coord : ROI检测区域 (暂未使用)
Output        : None
Return        : NULL : 检测失败
                Others : AI 检测结果
Author        : PengHZ
Create Date   : 2025/07/22
Last Update   : None
Note          : None
*******************************************************************************/
void *AIDete_OCRDet_General(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN CoordinateVOC *roi_coord);

/*******************************************************************************
Function      : AIDete_OCRRec_General()
Description   : 获取通用 OCR 转换字符串
Input         : img_area_pt : 图像存储空间
              : roi_ena : 是否开启ROI检测 (暂未使用)
              : roi_coord : ROI检测区域 (暂未使用)
Output        : None
Return        : NULL : 获取失败
                Others : 保存有字符串的 vector 指针
Author        : PengHZ
Create Date   : 2025/07/22
Last Update   : None
Note          : None
*******************************************************************************/
void *AIDete_OCRRec_General(IN g_ImageAreaStruct *img_area_pt, IN s32 roi_ena, IN QuadPoints *roi_coord);

/*******************************************************************************
Function      : AIDete_OCRGeneral_DeteString()
Description   : AI 通用 OCR通用字符模型 检测并获取字符串函数
Input         : img_area_pt : 图像存储空间
              : buff : 输出缓冲区
              : buff_len : 输出缓冲区长度
              : buff_size : 输出缓冲区大小
Output        : None
Return        : MACR_NONE : 检测成功
                MACR_ERROR : 检测失败
Author        : PengHZ
Create Date   : 2025/07/24
Last Update   : None
Note          : None
*******************************************************************************/
s32 AIDete_OCRGeneral_DeteString(IN g_ImageAreaStruct *img_area_pt, IN u8* buff, IN u32* buff_len, IN const u32 buff_size);

/*******************************************************************************
Function      : AIDete_SetSoftwareROICenterDeteState()
Description   : 设置软件 ROI 中心挖图检测需求状态
Input         : state : 软件 ROI Center Detect 状态
				  MACR_TRUE : 无论如何都需要进行 ROI Center Detect
				  MACR_FALSE : 按需进行 ROI Center Detect
Output        : None
Output        : None
Return        : None
Author        : TanJW
Create Date   : 2024/09/18
Last Update   : None
Note          : None
*******************************************************************************/
s32 AIDete_SetSoftwareROICenterDeteState(s32 state);

/*******************************************************************************
Function      : AIDete_WriteCapDetResult2Buffer()
Description   : 获取 AI 管帽检测计算结果
Input         : img_area_pt : 图像存储空间
			  : max_len : buffer 空间最大长度
Output        : buffer : 检测结果输出空间
Return        : MACR_ERROR : 获取失败
				Others : 获取成功（返回值为buffer中数据有效长度）
Author        : TanJW
Create Date   : 2024/08/23
Last Update   : None
Note          : None
*******************************************************************************/
s32 AIDete_WriteCapDetResult2Buffer(void *ai_dete_rslt_pt, u8 *buffer, u32 max_len);

/*******************************************************************************
Function      : AIDete_SetAIDeteValidROIScoreThre()
Description   : 设置有效 ROI 上阈值
Input         : score_thre : 有效 ROI 上阈值
Output        : None
Return        : MACR_ERROR : 设置失败
				MACR_NONE : 设置成功
Author        : TanJW
Create Date   : 2024/01/12
Last Update   : None
Note          : None
*******************************************************************************/
s32 AIDete_SetAIDeteValidROIScoreThreMax(IN f32 score_thre);

/*******************************************************************************
Function      : AIDete_GetAIDeteValidROIScoreThreMax()
Description   : 获取有效 ROI 上阈值
Input         : None
Output        : None
Return        : 有效 ROI 上阈值
Author        : TanJW
Create Date   : 2024/01/12
Last Update   : None
Note          : None
*******************************************************************************/
f32 AIDete_GetAIDeteValidROIScoreThreMax(void);

/*******************************************************************************
Function      : AIDete_GetAIDeteValidROIDefScoreThre()
Description   : 获取有效 ROI 上阈值默认值
Input         : None
Output        : None
Return        : 有效 ROI 上阈值默认值
Author        : TanJW
Create Date   : 2024/01/12
Last Update   : None
Note          : None
*******************************************************************************/
f32 AIDete_GetAIDeteValidROIDefScoreThreMax(void);

/*******************************************************************************
Function      : AIDete_GetAIDeteValidROIDefScoreThreMin()
Description   : 获取有效 ROI 下阈值默认值
Input         : None
Output        : None
Return        : 有效 ROI 下阈值默认值
Author        : TanJW
Create Date   : 2024/01/12
Last Update   : None
Note          : None
*******************************************************************************/
f32 AIDete_GetAIDeteValidROIDefScoreThreMin(void);

/*******************************************************************************
Function      : AIDete_SetAIDeteValidROIScoreThreMin()
Description   : 设置有效 ROI 下阈值
Input         : score_thre : 有效 ROI 下阈值
Output        : None
Return        : MACR_ERROR : 设置失败
				MACR_NONE : 设置成功
Author        : TanJW
Create Date   : 2024/01/15
Last Update   : None
Note          : None
*******************************************************************************/
s32 AIDete_SetAIDeteValidROIScoreThreMin(IN f32 score_thre);

/*******************************************************************************
Function      : AIDete_GetAIDeteValidROIScoreThreMin()
Description   : 获取有效 ROI 下阈值
Input         : None
Output        : None
Return        : 有效 ROI 下阈值
Author        : TanJW
Create Date   : 2024/01/15
Last Update   : None
Note          : None
*******************************************************************************/
f32 AIDete_GetAIDeteValidROIScoreThreMin(void);

/*******************************************************************************
Function      : AIDete_GetAIDeteValidROIDefScoreThreMin()
Description   : 获取有效 ROI 下阈值默认值
Input         : None
Output        : None
Return        : 有效 ROI 下阈值默认值
Author        : TanJW
Create Date   : 2024/01/15
Last Update   : None
Note          : None
*******************************************************************************/
f32 AIDete_GetAIDeteValidROIDefScoreThreMin(void);

/*******************************************************************************
Function      : AIDete_OutputStateInfo()
Description   : 获取AI模块状态信息
Input         : method : 输出方法
                option : 输出选项
Output        : None
Return        : MACR_NONE : 输出成功
                MACR_ERROR : 输出失败
Author        : PengHZ
Create Date   : 2025/07/03
Last Update   : None
Note          : None
*******************************************************************************/
s32 AIDete_OutputStateInfo(printf_method method, s32 option);

#ifdef __cplusplus
}
#endif

#endif /* AI_DETECT_H_ */

/******************************** END OF FILE *********************************/

