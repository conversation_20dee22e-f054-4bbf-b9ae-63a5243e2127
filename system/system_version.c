//=============================================================================
// Copyright (C), 2004-2018, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: system_version.c
// Author		: TanR
// Created On	: 2018 Nov 29, Thu
// Description	: Project System 系统版本号信息
//
// History
// 1. V1.0, Created by TanR, 2018 Nov 29, Thu
//
//=============================================================================
#include "system_version.h"

#include "product_info.h"

/*
 * MD_Sys_Version_Info: Project System 系统版本号；
 * 修改系统时，改动MD_Sys_Version_Info；实体存放于：system/system_version.c中。
 *			如果硬件发生了变动，系统要尽最大的可能保持老版本的兼容。不同的地方用宏定义区别。
 * 			同时，在版本说明中标注支持新的硬件。
 * 			系统版本号不用再表示硬件信息改动，由产品APP固件发布版本号表示。
 */

/* MD_Sys_Version_Info: Project System 系统版本号 */
#if ((MACR_MCU_SAMA5D26 == 1) || (MACR_MCU_SAMA5D24 == 1) ||		\
	 (MACR_MCU_MR100 == 1) || (MACR_MCU_D9 == 1) || (MACR_PLATFORM_ANDROID == 1) ||		\
	 (MACR_MCU_IMX6SOLO == 1) || (MACR_MCU_SSC377 == 1))
	const u8 MD_Sys_Version_Info[]={"2.0.442.1.1611.13"};
#elif (MACR_MCU_X1500 == 1)
	const u8 MD_Sys_Version_Info[]={"1.2.347.0"};
#endif

/******************************** END OF FILE ********************************/
