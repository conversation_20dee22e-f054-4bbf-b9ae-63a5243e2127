#ifndef TOOLBOX_H_
#define	TOOLBOX_H_

// Includes --------------------------------------------------------
#include "common.h"
// End of Includes -------------------------------------------------

// Defines ---------------------------------------------------------

#define MACR_SHIFT_RIGHT_1	((s32)1)
#define MACR_SHIFT_RIGHT_2	((s32)2)
#define MACR_SHIFT_RIGHT_3	((s32)3)
#define MACR_SHIFT_LEFT_1	((s32)-1)
#define MACR_SHIFT_LEFT_2	((s32)-2)
#define MACR_SHIFT_LEFT_3	((s32)-3)


// End of Defines --------------------------------------------------
 
// Structures ------------------------------------------------------
// End of Structures ----------------------------------------------

// Variables for External Use -------------------------------------
// End of V for E Use ---------------------------------------------

// Function Declearations -----------------------------------------
f32 Histogram_CompareFloat(u32* hist1, u32* hist2, u32 bin_num);
s32 Histogram_CompareInt(u32* hist1, u32* hist2, u32 bin_num);
u8 Array_ShiftU32(u32* _array_addr, u32 array_size, s32 step);
u8 Array_ShiftVoid(u32 _array_addr, u32 array_size, u32 element_size, s32 step);
u32 Array_GetAverage(u32* addr, u32 num);
u32 Array_GetVariance(u32* addr, u32 num);
s32 Number_GetGreatestCommonDivisor(s32 num_a, s32 num_b);
s32 Array_GetBinarySearchIndexValue_U8(u8* addr_pt, s32 addr_pt_element_number, s32 left, s32 right, u8 search_value);
s32 Array_GetBinarySearchIndexValue_U16(u16* addr_pt, s32 addr_pt_element_number, s32 left, s32 right, u16 search_value);
// End of Declearations -------------------------------------------

#endif
/* ---------------------------- End of File ----------------------------- */
