//==============================================================================
// Copyright (C), 2004-2020, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: app_project_video.c
// Author		: CaoJL
// Created On	: 2020 June 03, Wed
// Description	: Receive Image.
//
// History
// 0. V1.0, Created by CaoJL, 2020 June 03, Wed
//
//
//==============================================================================

#ifndef APP_PROJECT_VIDEO_H_
#define APP_PROJECT_VIDEO_H_

#include "common.h"

void App_ProjectVideo_StartImaging(void);
s32 App_ProjectVideo_HostCmdProcess(u8* data_pt, u32 data_len);
s32 App_ProjectVideo_Shell(u32 op_type, u32 reserve_data);

#endif /* APP_PROJECT_VIDEO_H_ */

