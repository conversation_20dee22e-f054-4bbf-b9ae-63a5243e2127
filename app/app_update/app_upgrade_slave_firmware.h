//==============================================================================
// Copyright (C), 2004-2019, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: app_upgrade_slave_firmware.h
// Author		: DongDL
// Created On	: 2023/10/30
// Description	: Upgrade slave app firmware.
//
// History
// 0. V1.0, Created by DongDL, 2023/10/30
//
//
//==============================================================================

#ifndef APP_UPGRADE_SLAVE_FIRMWARE_H_
#define APP_UPGRADE_SLAVE_FIRMWARE_H_

#include "./common/common.h"

s32 App_UpgradeSlave_Start(u8 *cmd_str);
s32 App_UpgradeSlave_Stop(void);

s32 App_UpgradeSlave_GetSlaveVer(u8 *ver_pt, u32 max_len);
s32 App_UpgradeSlave_isSlaveInUpgradeMode(void);
s32 App_UpgradeSlave_GetSlaveProductInfo(u8 *info_pt, u32 max_len);
s32 App_UpgradeSlave_GetSlaveLoaderVer(u8 *ver_pt, u32 max_len);
s32 App_UpgradeSlave_GetSlaveBIOSVer(u8 *ver_pt, u32 max_len);
s32 App_UpgradeSlave_Recover(void);
s32 App_UpgradeSlave_OutputStateInfo(u8 *buff_pt, u32 max_len);
s32 App_UpgradeSlave_PCCmdHandle(u8 *buff);

#endif
