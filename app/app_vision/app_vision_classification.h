//==============================================================================
// Copyright (C), 2004-2025, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: app_vision_classification.h
// Author		: TanJW
// Created On	: 2025 July 29, Tue
// Description	: App Vision Classification.
//
// History
// 0. V1.0, Created by TanJW, 2025 July 29, Tue
//
//
//==============================================================================

#ifndef APP_VISION_CLASSIFICATION_H_
#define APP_VISION_CLASSIFICATION_H_

/*
 * Headers
 */
#include "common.h"

/*
 * Macros
 */

/*
 * Data Structure
 */

/*******************************************************************************
Function      : AppVisionClassification_Detect()
Description   : 进行分类检测
Input         : rslt_buff_max : 结果信息缓冲区最大长度
Output        : rslt_info_pt : 结果信息指针
Return        : MACR_NONE : 成功
                MACR_ERROR : 失败
Author        : TanJW
Create Date   : 2025/07/29
Last Update   : None
Note          : None
*******************************************************************************/
s32 AppVisionClassification_Detect(OUT u8 *rslt_info_pt, IN u32 rslt_buff_max);

#endif /* APP_VISION_CLASSIFICATION_H_ */

/******************************** END OF FILE *********************************/
