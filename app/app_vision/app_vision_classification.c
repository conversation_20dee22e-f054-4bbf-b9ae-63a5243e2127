//==============================================================================
// Copyright (C), 2004-2025, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: app_vision_classification.c
// Author		: TanJW
// Created On	: 2025 July 29, Tue
// Description	: App Vision Classification.
//
// History
// 0. V1.0, Created by TanJW, 2025 July 29, Tue
//
//==============================================================================

/*
 * Headers
 */
#include "app_vision_classification.h"
#include "./system/system_imaging/image.h"
#include "./system/ai_detect.h"
#include "./system/system_config/system_cfg.h"
#include "./common/para_code_macro.h"


// 模块调试宏开关
#define MACR_APP_VISION_CLASSIFICATION_DEBUG					MACR_OFF		// MACR_ON  开启  / MACR_OFF 关闭

#if (MACR_APP_VISION_CLASSIFICATION_DEBUG == MACR_ON)
	#define VC_LOG												LOG
#else
	#define VC_LOG(...)
#endif

/*
 * Macros
 */

/*
 * Data Structure
 */


/*
 * Variables
 */


/*
 * Functions
 */
static g_ImageAreaStruct* AppVisionClassi_GetImage(void);

/*************************** Function Declaration Ends ************************/

/*******************************************************************************
Function      : AppVisionClassification_Detect()
Description   : 进行分类检测
Input         : rslt_buff_max : 结果信息缓冲区最大长度
Output        : rslt_info_pt : 结果信息指针
Return        : MACR_NONE : 成功
                MACR_ERROR : 失败
Author        : TangHL
Create Date   : 2025/07/29
Last Update   : 2025/07/30
Note          : 使用带类别名称的分类检测接口，通过WriteResult2Buffer获取标准格式结果
*******************************************************************************/
s32 AppVisionClassification_Detect(OUT u8 *rslt_info_pt, IN u32 rslt_buff_max)
{
	s32 ret_val = MACR_NONE;
	g_ImageAreaStruct* img_area_pt = NULL;
	void *ai_dete_rslt_pt = NULL;
	u8 *ai_rslt_pt = NULL;
	const u32 RSLT_BUFF_MIN = 4;		// 结果信息最小长度
	u32 ai_rslt_len = 0;

	// 判断指针有效性
	if ((rslt_info_pt == NULL) || (rslt_buff_max < RSLT_BUFF_MIN))
	{
		VC_LOG("Invalid parameters: rslt_info_pt=%p, rslt_buff_max=%d\r\n", rslt_info_pt, rslt_buff_max);
		return MACR_ERROR;
	}

	// 获取图像
	img_area_pt = AppVisionClassi_GetImage();
	if (img_area_pt == NULL)
	{
		VC_LOG("Failed to get image\r\n");
		return MACR_ERROR;
	}

	ai_rslt_pt = rslt_info_pt + RSLT_BUFF_MIN;

	// 启动AI进行分类检测（带类别名称）
	ai_dete_rslt_pt = AIDete_Classification_Dete_WithClassName(img_area_pt);

	if (ai_dete_rslt_pt != NULL)
	{
		// 将AI检测结果写入缓冲区
		ai_rslt_len = AIDete_Classification_WriteResult2Buffer(
			ai_dete_rslt_pt,
			ai_rslt_pt,
			(rslt_buff_max - RSLT_BUFF_MIN)
		);

		if (ai_rslt_len > 0)
		{
			// 设置结果头信息
			rslt_info_pt[0] = 1;                            // 检测成功标志
			rslt_info_pt[1] = MACR_CODE_TYPE_VISION;        // 检测结果类型
			rslt_info_pt[2] = (u8)(ai_rslt_len & 0xFF);     // 数据长度低8位
			rslt_info_pt[3] = (u8)(ai_rslt_len >> 8);       // 数据长度高8位

			VC_LOG("Classification detection success, result_len = %d\r\n", ai_rslt_len);
		}
		else
		{
			VC_LOG("Failed to write classification results to buffer\r\n");
			ret_val = MACR_ERROR;
		}
	}
	else
	{
		VC_LOG("Classification detection failed\r\n");
		ret_val = MACR_ERROR;
	}

	// 释放图像
	ImgPool_PutImgIdle(IMG_POOL_1, img_area_pt);

	return ret_val;
}

/*******************************************************************************
Function      : AppVisionClassi_GetImage()
Description   : 获取一帧图像
Input         : None
Output        : None
Return        : NULL : 获取失败
                Others : 图像区域指针
Author        : TanJW
Create Date   : 2025/07/24
Last Update   : None
Note          : None
*******************************************************************************/
static g_ImageAreaStruct* AppVisionClassi_GetImage(void)
{
	g_ImageAreaStruct* img_area_pt = NULL;
	u32 img_num = 0;

	// 获取 Ready 图像队列中的 Nor 图像数量
	img_num = ImgPool_GetImgReadyNum(IMG_POOL_1, MACR_IMAGE_TYPE_NORMAL);

	//如果存在Ready的Nor图像
	if (img_num > 0)
	{
		// 获取 Ready 图像队列里最 New 的 Nor 图
		img_area_pt = ImgPool_GetImgReadyFirst(IMG_POOL_1, MACR_IMAGE_OCCUPIER_AUTO_IMGING, MACR_IMAGE_TYPE_NORMAL);
	}
	//不存在 Ready 的 Nor 图像
	else
	{
		img_area_pt = NULL;
	}

	return img_area_pt;
}


/******************************** END OF FILE *********************************/

