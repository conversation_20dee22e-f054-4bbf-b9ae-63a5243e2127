//==============================================================================
// Copyright (C), 2004-2025, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: app_vision.c
// Author		: TanJW
// Created On	: 2025 July 22, Tue
// Description	: App Vision.
//
// History
// 0. V1.0, Created by TanJW, 2025 July 22, Tue
//
//==============================================================================

/*
 * Headers
 */
#include "./app/app_vision/app_vision.h"
#include "./app/app_vision/app_vision_ger_ocr.h"
#include "./app/app_vision/app_vision_classification.h"
#include "./app/app_vision/app_vision_dete_feature.h"
#include "./common/object.h"
#include "./common/para_code_macro.h"
#include "./common/para_code_options_macro.h"
#include "./system/system_msg_manager.h"
#include "./system/system_config/system_cfg.h"
#include "./hal/kernel/hal_events.h"
#include "./hal/kernel/hal_mutex.h"
#include "./system/system_hub.h"

// 模块调试宏开关
#define MACR_APP_VISION_DEBUG									MACR_OFF		// MACR_ON  开启  / MACR_OFF 关闭

#if (MACR_APP_VISION_DEBUG == MACR_ON)
	#define VISION_LOG											LOG
#else
	#define VISION_LOG(...)
#endif

/*
 * Macros
 */
#define MD_APP_VISION_NAME										"vision"		// 服务名称
#define MD_APP_VISION_STACK_SIZE								10240			// 栈大小，单位: Byte
#define MD_APP_VISION_SERVICE_PRIO								7				// 优先级，数字越小优先级越高
#define MD_APP_VISION_SERVICE_TICK								1				// 同优先级线程时间片
#define MD_APP_VISION_MQUEUE_SIZE								40				// 消息队列容量

// 定义管理线程事件集所需事件
#define MACR_APP_VISION_MANA_EVENT_START						(1 << 0)		// Manager 启动事件
#define MACR_APP_VISION_MANA_EVENT_STOP							(1 << 1)		// Manager 停止事件
#define MACR_APP_VISION_MANA_EVENT_WAIT_STOP_REQ				(1 << 2)		// Manager 等待停止请求事件
#define MACR_APP_VISION_MANA_EVENT_WAIT_DETE_END				(1 << 3)		// Manager 等待检测结束事件

// 定义视觉对象事件集所需事件
#define MACR_APP_VISION_OBJ_EVENT_START							(1 << 0)		// Object 启动事件
#define MACR_APP_VISION_OBJ_EVENT_STOP							(1 << 1)		// Object 停止事件
#define MACR_APP_VISION_OBJ_EVENT_WAIT_STOP_REQ					(1 << 2)		// Object 等待停止请求事件

/*
 * Data Structure
 */
Object(AppVisionManager)
{
	Inherit(Thread);
	Events events;
	Mutex mutex;
	s32 is_running;
	s32 is_inited;
};

Object(AppVisionObj)
{
	Inherit(Thread);
	Events events;
};

typedef struct _app_vision_ctrl_str
{
	volatile u8 vision_type;
	volatile SMsgHandler vision_msg_handler;
	volatile u8 *rslt_info_pt;
	volatile u32 rslt_info_len;
}app_vision_ctrl_str;


/*
 * Variables
 */
static app_vision_ctrl_str s_app_vision_ctrl = { 0 };

/*
 * Functions
 */
static s32 AppVision_ManagerOperate(INOUT Thread *thiz, IN u32 what, IN void *param1, IN u32 param2);
static s32 AppVision_ObjOperate(INOUT Thread *thiz, IN u32 what, IN void *param1, IN u32 param2);
static void AppVision_HandleMessage(IN SMsg msg);
static s32 AppVision_UpdateParaConfig(IN u16 para_code, IN u8 para_val);
static s32 AppVision_SetVisionType(IN u8 vision_type);
static Thread *AppVision_GetManagerThread(void);
static Thread *AppVision_GetObjThread(void);

/*************************** Function Declaration Ends ************************/

/*******************************************************************************
Function      : AppVision_ManagerOperate()
Description   : 视觉应用操作
Input         : thiz : 视觉应用指针
                what : 操作类型
                param1 : 参数1
                param2 : 参数2
Output        : None
Return        : MACR_NONE : 成功
                MACR_ERROR : 失败
Author        : TanJW
Create Date   : 2025/07/22
Last Update   : None
Note          : None
*******************************************************************************/
static s32 AppVision_ManagerOperate(INOUT Thread *thiz, IN u32 what, IN void *param1, IN u32 param2)
{
	s32 result = MACR_NONE;
	u32 start_event_sets = 0;
	u32 event_sets = 0;
	app_vision_ctrl_str *vision_ctrl_pt = (app_vision_ctrl_str *)&s_app_vision_ctrl;

	switch (what)
	{
		case ThreadOpt_OnAttr: break;
		case ThreadOpt_OnInit:
		{
			// 创建事件集
			HAL_Events_Setup(&AppVisionManager.events, 0);
			// 创建互斥锁
			HAL_Mutex_Construct(&AppVisionManager.mutex, "vs_mutex");
			// 创建并启动视觉应用对象线程
			HAL_Thread_Boot(AppVision_GetObjThread());
			break;
		}
		case ThreadOpt_OnBoot:
		{
			break;
		}
		case ThreadOpt_OnJoin:
		{
			break;
		}
		case Dormable_AskLowestLv:
		{
			break;
		}
		case ThreadOpt_OnReqw:
		{
			break;
		}
		case ThreadOpt_OnLoop:
		{
			VISION_LOG("func = %s, line = %d\r\n", __func__, __LINE__);
			start_event_sets = HAL_Events_WaitOr(&AppVisionManager.events, MACR_APP_VISION_MANA_EVENT_START, MACR_WAIT_FOREVER);

			VISION_LOG("func = %s, line = %d\r\n", __func__, __LINE__);

			if (start_event_sets & MACR_APP_VISION_MANA_EVENT_START)
			{
				VISION_LOG("AppVision_ManagerOperate: MACR_APP_VISION_MANA_EVENT_START\r\n");
				// 上锁
				HAL_Mutex_Lock(&AppVisionManager.mutex);
				// 设置管理线程启动标志
				AppVisionManager.is_running = MACR_TRUE;
				// 清空管理停止事件
				HAL_Events_ClrEvent(&AppVisionManager.events, MACR_APP_VISION_MANA_EVENT_STOP);
				// 解锁
				HAL_Mutex_Unlock(&AppVisionManager.mutex);

				// 清空管理线程启动事件
				HAL_Events_ClrEvent(&AppVisionManager.events, MACR_APP_VISION_MANA_EVENT_START);

				// 设置视觉应用对象启动事件
				HAL_Events_SetEvent(&AppVisionObj.events, MACR_APP_VISION_OBJ_EVENT_START);

				VISION_LOG("func = %s, line = %d\r\n", __func__, __LINE__);

				// 等待视觉应用对象检测完成或者等待停止需求
				event_sets = HAL_Events_WaitOr(&AppVisionManager.events, (MACR_APP_VISION_MANA_EVENT_WAIT_DETE_END | MACR_APP_VISION_MANA_EVENT_WAIT_STOP_REQ), MACR_WAIT_FOREVER);

				VISION_LOG("func = %s, line = %d\r\n", __func__, __LINE__);

				// 检测结束事件与等待结束事件，两者只处理一个，优先处理检测结束事件
				// 如果检测结束事件触发，则处理检测结束流程
				if (event_sets & MACR_APP_VISION_MANA_EVENT_WAIT_DETE_END)
				{
					VISION_LOG("func = %s, line = %d\r\n", __func__, __LINE__);
					// 处理检测结束流程
					VISION_LOG("func = %s, line = %d\r\n", __func__, __LINE__);
					// 处理停止流程
					// 1. 清空管理线程等待检测结束事件
					HAL_Events_ClrEvent(&AppVisionManager.events, MACR_APP_VISION_MANA_EVENT_WAIT_DETE_END);
					// 2. 设置管理线程停止事件
					HAL_Events_SetEvent(&AppVisionManager.events, MACR_APP_VISION_MANA_EVENT_STOP);
					// 3. 设置管理线程停止标志
					HAL_Mutex_Lock(&AppVisionManager.mutex);
					AppVisionManager.is_running = MACR_FALSE;
					VISION_LOG("func = %s, line = %d\r\n", __func__, __LINE__);
					// 4. 设置系统环境事件，即唤醒系统线程
					SysHub_SetSysEnvRunEvent(MACR_SYS_HUB_EVENT_JUMP_DECO_TO_SYS);
					// 解锁
					HAL_Mutex_Unlock(&AppVisionManager.mutex);
					VISION_LOG("func = %s, line = %d\r\n", __func__, __LINE__);
				}
				else if (event_sets & MACR_APP_VISION_MANA_EVENT_WAIT_STOP_REQ)
				{
					VISION_LOG("func = %s, line = %d\r\n", __func__, __LINE__);
					// 处理停止流程
					// 1. 请求视觉应用对象停止
					HAL_Events_SetEvent(&AppVisionObj.events, MACR_APP_VISION_OBJ_EVENT_WAIT_STOP_REQ);
					// 2. 等待视觉应用对象停止事件
					HAL_Events_WaitOr(&AppVisionObj.events, MACR_APP_VISION_OBJ_EVENT_STOP, MACR_WAIT_FOREVER);
					VISION_LOG("func = %s, line = %d\r\n", __func__, __LINE__);
					// 3. 清空视觉应用对象停止事件
					HAL_Events_ClrEvent(&AppVisionObj.events, MACR_APP_VISION_OBJ_EVENT_STOP);
					// 4. 设置管理线程停止事件
					HAL_Events_SetEvent(&AppVisionManager.events, MACR_APP_VISION_MANA_EVENT_STOP);
					// 5. 清空管理线程等待停止请求事件
					HAL_Events_ClrEvent(&AppVisionManager.events, MACR_APP_VISION_MANA_EVENT_WAIT_STOP_REQ);
					// 6. 设置管理线程停止标志
					HAL_Mutex_Lock(&AppVisionManager.mutex);
					AppVisionManager.is_running = MACR_FALSE;
					VISION_LOG("func = %s, line = %d\r\n", __func__, __LINE__);
					// 解锁
					HAL_Mutex_Unlock(&AppVisionManager.mutex);
					VISION_LOG("func = %s, line = %d\r\n", __func__, __LINE__);
				}
			}

			break;
		}
		default: break;
	}

	return result;
}

/*******************************************************************************
Function      : AppVision_ObjOperate()
Description   : 视觉应用操作
Input         : thiz : 视觉应用指针
                what : 操作类型
                param1 : 参数1
                param2 : 参数2
Output        : None
Return        : MACR_NONE : 成功
                MACR_ERROR : 失败
Author        : TanJW
Create Date   : 2025/07/22
Last Update   : None
Note          : None
*******************************************************************************/
static s32 AppVision_ObjOperate(INOUT Thread *thiz, IN u32 what, IN void *param1, IN u32 param2)
{
	s32 result = MACR_NONE;
	u8 vision_type = MDFLAG_RESV_VisionAppCtrl_None;
	s32 ai_dete_ret = MACR_ERROR;
	app_vision_ctrl_str *vision_ctrl_pt = (app_vision_ctrl_str *)&s_app_vision_ctrl;
	s32 is_need_set_manager_wait_dete_end = MACR_FALSE;

	switch (what)
	{
		case ThreadOpt_OnAttr: break;
		case ThreadOpt_OnInit:
		{
			// 创建事件集
			HAL_Events_Setup(&AppVisionObj.events, 0);
			break;
		}
		case ThreadOpt_OnBoot:
		{
			break;
		}
		case ThreadOpt_OnJoin:
		{
			break;
		}
		case Dormable_AskLowestLv:
		{
			break;
		}
		case ThreadOpt_OnReqw:
		{
			break;
		}
		case ThreadOpt_OnLoop:
		{
			// VISION_LOG("func = %s, line = %d\r\n", __func__, __LINE__);
			u32 sets = HAL_Events_WaitOr(&AppVisionObj.events, (MACR_APP_VISION_OBJ_EVENT_START | MACR_APP_VISION_OBJ_EVENT_WAIT_STOP_REQ), MACR_WAIT_FOREVER);

			// VISION_LOG("func = %s, line = %d\r\n", __func__, __LINE__);

			if (sets & MACR_APP_VISION_OBJ_EVENT_START)
			{
				// VISION_LOG("AppVision_ObjOperate: MACR_APP_VISION_OBJ_EVENT_START\r\n");

				// 获取视觉应用类型
				vision_type = AppVision_GetVisionType();

				// 执行视觉应用检测
				switch (vision_type)
				{
					case MDFLAG_RESV_VisionAppCtrl_OCR:
					{
						ai_dete_ret = AppVisionGerOCR_Detect((u8 *)(vision_ctrl_pt->rslt_info_pt), vision_ctrl_pt->rslt_info_len);
						break;
					}
					case MDFLAG_RESV_VisionAppCtrl_Classification:
					{
						ai_dete_ret = AppVisionClassification_Detect((u8 *)(vision_ctrl_pt->rslt_info_pt), vision_ctrl_pt->rslt_info_len);
						break;
					}
					case MDFLAG_RESV_VisionAppCtrl_DeteFeature:
					{
						ai_dete_ret = AppVisionDeteFeature_Detect((u8 *)(vision_ctrl_pt->rslt_info_pt), vision_ctrl_pt->rslt_info_len);
						break;
					}
					case MDFLAG_RESV_VisionAppCtrl_None:
					default: break;
				}

				// 如果视觉应用检测成功，设置管理线程等待结果事件
				if (ai_dete_ret == MACR_NONE)
				{
					VISION_LOG("AppVision_ObjOperate: ai_dete_succ, type = %d\r\n", vision_type);
					// 清空视觉应用启动与停止事件
					HAL_Events_ClrEvent(&AppVisionObj.events, (MACR_APP_VISION_OBJ_EVENT_START | MACR_APP_VISION_OBJ_EVENT_WAIT_STOP_REQ));
				
					// 借助解码器解码成功接口设置 AI 检测成功结果
					SysHub_SetDecoderDecoResult(MACR_SUCCESS);

					// 设置视觉应用对象停止事件
					HAL_Events_SetEvent(&AppVisionObj.events, MACR_APP_VISION_OBJ_EVENT_STOP);

					// 标记需要设置管理线程请求停止事件
					is_need_set_manager_wait_dete_end = MACR_TRUE;
				}
			}

			if (sets & MACR_APP_VISION_OBJ_EVENT_WAIT_STOP_REQ)
			{
				VISION_LOG("AppVision_ObjOperate: MACR_APP_VISION_OBJ_EVENT_WAIT_STOP_REQ\r\n");
				// 清空视觉应用启动与停止事件
				HAL_Events_ClrEvent(&AppVisionObj.events, (MACR_APP_VISION_OBJ_EVENT_START | MACR_APP_VISION_OBJ_EVENT_WAIT_STOP_REQ));
				
				// 设置视觉应用对象停止事件
				HAL_Events_SetEvent(&AppVisionObj.events, MACR_APP_VISION_OBJ_EVENT_STOP);
			}

			// 备注，唤醒其他线程的动作，一定要放在最后，否则有概率出现此接口未执行完就失去了 CPU，导致后续流程无法执行完成
			// 如果需要设置管理线程请求停止事件，则设置
			if (is_need_set_manager_wait_dete_end == MACR_TRUE)
			{
				// 复位标记
				is_need_set_manager_wait_dete_end = MACR_FALSE;

				// 设置管理线程请求停止事件
				HAL_Events_SetEvent(&AppVisionManager.events, MACR_APP_VISION_MANA_EVENT_WAIT_DETE_END);
			}

			break;
		}
		default: break;
	}

	return result;
}

/*******************************************************************************
Function      : AppVision_GetManagerThread()
Description   : 视觉应用创建线程
Input         : None
Output        : None
Return        : 视觉应用线程指针
Author        : TanJW
Create Date   : 2025/07/23
Last Update   : None
Note          : None
*******************************************************************************/
static Thread *AppVision_GetManagerThread(void)
{
	static s32 constructed = MACR_FALSE;
	if (constructed == MACR_FALSE)
	{
		// 构造标记
		constructed = MACR_TRUE;
		// 构造父类
		Using(Thread, thiz, &AppVisionManager);
		ThrCfgMeta meta;
		thiz->name = MD_APP_VISION_NAME;
		thiz->stack_size = MD_APP_VISION_STACK_SIZE;
		thiz->priority = MD_APP_VISION_SERVICE_PRIO;
		thiz->tick = MD_APP_VISION_SERVICE_TICK;
		
		meta.stack_size = MD_APP_VISION_STACK_SIZE;
		meta.priority = MD_APP_VISION_SERVICE_PRIO;
		meta.tick = MD_APP_VISION_SERVICE_TICK;
		meta.cpus = 0xff;
		// 覆盖接口
		Overlay(AppVision_ManagerOperate);
		HAL_Thread_Init((Thread *)thiz, "visionMana");
		HAL_Thread_Load((Thread *)thiz, &meta);
	}
	return (Thread *)&AppVisionManager;
}

/*******************************************************************************
Function      : AppVision_GetObjThread()
Description   : 视觉应用创建线程
Input         : None
Output        : None
Return        : 视觉应用线程指针
Author        : TanJW
Create Date   : 2025/07/23
Last Update   : None
Note          : None
*******************************************************************************/
static Thread *AppVision_GetObjThread(void)
{
	static s32 constructed = MACR_FALSE;
	if (constructed == MACR_FALSE)
	{
		// 构造标记
		constructed = MACR_TRUE;
		// 构造父类
		Using(Thread, thiz, &AppVisionObj);
		ThrCfgMeta meta;
		thiz->name = MD_APP_VISION_NAME;
		thiz->stack_size = MD_APP_VISION_STACK_SIZE;
		thiz->priority = MD_APP_VISION_SERVICE_PRIO;
		thiz->tick = MD_APP_VISION_SERVICE_TICK;
		
		meta.stack_size = MD_APP_VISION_STACK_SIZE;
		meta.priority = MD_APP_VISION_SERVICE_PRIO;
		meta.tick = MD_APP_VISION_SERVICE_TICK;
		meta.cpus = 0xff;
		// 覆盖接口
		Overlay(AppVision_ObjOperate);
		HAL_Thread_Init((Thread *)thiz, "visionObj");
		HAL_Thread_Load((Thread *)thiz, &meta);
	}
	return (Thread *)&AppVisionObj;
}

/*******************************************************************************
Function      : AppVision_Init()
Description   : 初始化视觉应用
Input         : None
Output        : None
Return        : MACR_NONE : 成功
                MACR_ERROR : 失败
Author        : TanJW
Create Date   : 2025/07/22
Last Update   : None
Note          : None
*******************************************************************************/
s32 AppVision_Init(void)
{
	s32 ret_val = MACR_NONE;
	u8 vision_app_ctrl = MDFLAG_RESV_VisionAppCtrl_None;

	// 初始化消息处理器
	s_app_vision_ctrl.vision_msg_handler.HandleMessage = AppVision_HandleMessage;
	// 注册消息映射
	MsgManager->AttachMessageMap((SMsgHandler*)&s_app_vision_ctrl.vision_msg_handler, Msg_Exe_Param_Cfg_Content);

	SysCfg_GetParaVal_1B_WithCode(MACR_PARACODE_9896, MACR_CURRENT_PARA, &vision_app_ctrl);

	// 更新视觉应用控制参数
	AppVision_UpdateParaConfig(MACR_PARACODE_9896, vision_app_ctrl);

	return ret_val;
}

/*******************************************************************************
Function      : AppVision_HandleMessage()
Description   : 视觉应用消息处理
Input         : msg : 消息
Output        : None
Return        : None
Author        : TanJW
Create Date   : 2025/07/23
Last Update   : None
Note          : None
*******************************************************************************/
static void AppVision_HandleMessage(IN SMsg msg)
{
	u8* para_info = (u8 *)msg.info;
	u16 para_code = 0;
	u8 para_val = 0;

	// 参数设置内容的消息
	if (msg.id == Msg_Exe_Param_Cfg_Content)
	{
		// 从信息包中获取参数号
		para_code = para_info[MACR_SYS_CFG_PCMI_PACK_OFFSET_PARA_CODE];
		para_code += ((u16)para_info[MACR_SYS_CFG_PCMI_PACK_OFFSET_PARA_CODE + 1]) << 8;
		// 从信息包中获取第一个参数值，对于此模块而言，不考虑多参数值的问题。
		para_val = para_info[MACR_SYS_CFG_PCMI_PACK_OFFSET_PARA_VAL];

		AppVision_UpdateParaConfig(para_code, para_val);
	}
}

/*******************************************************************************
Function      : AppVision_UpdateParaConfig()
Description   : 视觉应用更新参数信息
Input         : para_code : 参数号
                para_val : 参数值
Output        : None
Return        : None
Author        : TanJW
Create Date   : 2025/07/23
Last Update   : None
Note          : None
*******************************************************************************/
static s32 AppVision_UpdateParaConfig(IN u16 para_code, IN u8 para_val)
{
	u8 vision_app_ctrl = MDFLAG_RESV_VisionAppCtrl_None;

	// 设置参数项 9896
	if (para_code == MACR_PARACODE_9896)
	{
		// 设置视觉应用类型
		AppVision_SetVisionType(para_val);
	}
	// 匹配到恢复默认参数%%%DEF或出厂参数%%MDEF命令
	else if ((para_code == MACR_PARACODE_9500) || (para_code == MACR_PARACODE_9501) || (para_code == MACR_PARACODE_9502))
	{
		// 恢复默认参数后，再次获取当前的参数值
		SysCfg_GetParaVal_1B_WithCode(MACR_PARACODE_9896, MACR_CURRENT_PARA, &vision_app_ctrl);

		// 设置视觉应用类型
		AppVision_SetVisionType(vision_app_ctrl);
	}

	return MACR_NONE;
}

/*******************************************************************************
Function      : AppVision_SetVisionType()
Description   : 设置视觉应用类型
Input         : vision_type : 视觉应用类型
Output        : None
Return        : None
Author        : TanJW
Create Date   : 2025/07/23
Last Update   : None
Note          : None
*******************************************************************************/
static s32 AppVision_SetVisionType(IN u8 vision_type)
{
	s32 result = MACR_NONE;

	// 设置视觉应用类型
	s_app_vision_ctrl.vision_type = vision_type;

	return result;
}

/*******************************************************************************
Function      : AppVision_GetVisionType()
Description   : 获取视觉应用类型
Input         : None
Output        : None
Return        : 视觉应用类型
Author        : TanJW
Create Date   : 2025/07/23
Last Update   : None
Note          : None
*******************************************************************************/
s32 AppVision_GetVisionType(void)
{
	return (s32)(s_app_vision_ctrl.vision_type);
}

/*******************************************************************************
Function      : AppVision_Shell()
Description   : 视觉应用入口
Input         : shell_str_pt : 视觉应用入口参数
Return        : MACR_NONE : 成功
                MACR_ERROR : 失败
Author        : TanJW
Create Date   : 2025/07/23
Last Update   : None
Note          : None
*******************************************************************************/
s32 AppVision_Shell(INOUT app_vision_shell_str *shell_str_pt)
{

	if((shell_str_pt == NULL) || (shell_str_pt->rslt_info_pt == NULL) || (shell_str_pt->rslt_buff_max <= 4))
	{
		return MACR_ERROR;
	}

	VISION_LOG("func = %s, line = %d\r\n", __func__, __LINE__);

	// 记录部分控制信息
	s_app_vision_ctrl.rslt_info_pt = shell_str_pt->rslt_info_pt;
	s_app_vision_ctrl.rslt_info_len = shell_str_pt->rslt_buff_max;

	// 首次进入，线程未创建
	if (AppVisionManager.is_inited == MACR_FALSE)
	{
		// 清零首次进入标记
		AppVisionManager.is_inited = MACR_TRUE;

		// 创建与启动线程
		HAL_Thread_Boot(AppVision_GetManagerThread());

		VISION_LOG("func = %s, line = %d\r\n", __func__, __LINE__);
	}
	// 线程已创建
	else
	{
		VISION_LOG("func = %s, line = %d\r\n", __func__, __LINE__);
		HAL_Mutex_Lock(&AppVisionManager.mutex);
		if(AppVisionManager.is_running == MACR_TRUE)				// 线程已启动
		{
			//与lock配套使用
			HAL_Mutex_Unlock(&AppVisionManager.mutex);

			//需要启动解码（伪解码成功时的要求）
			if(shell_str_pt->restart_req == MACR_TRUE)
			{
				VISION_LOG("func = %s, line = %d\r\n", __func__, __LINE__);
				// 挂起等待线程结束。
				HAL_Events_WaitOr((Events *)(&AppVisionManager.events), MACR_APP_VISION_MANA_EVENT_STOP, MACR_WAIT_FOREVER);
				VISION_LOG("func = %s, line = %d\r\n", __func__, __LINE__);
			}
			//不需要 restart
			else
			{
				// 线程已启动未结束，直接退出
				return MACR_ERROR;
			}
		}
		else
		{
			// 与 lock 配套使用
			HAL_Mutex_Unlock(&AppVisionManager.mutex);
		}
		// 若线程未启动，则直接往下走启动线程
	}// end else 线程已创建

	VISION_LOG("func = %s, line = %d\r\n", __func__, __LINE__);

	// 线程结束，清除标志。
	HAL_Events_ClrEvent((Events *)(&AppVisionManager.events), MACR_APP_VISION_MANA_EVENT_STOP);

	VISION_LOG("func = %s, line = %d\r\n", __func__, __LINE__);

	//启动解码器管理线程
	HAL_Events_SetEvent((Events *)(&AppVisionManager.events), MACR_APP_VISION_MANA_EVENT_START);

	VISION_LOG("func = %s, line = %d\r\n", __func__, __LINE__);
	return MACR_NONE;
}

/*******************************************************************************
Function      : AppVision_Destroy_Callback()
Description   : 视觉应用销毁回调
Input         : None
Output        : None
Return        : MACR_NONE : 成功
                MACR_ERROR : 失败
Author        : TanJW
Create Date   : 2025/07/23
Last Update   : None
Note          : 此接口不能多线程调用
*******************************************************************************/
s32 AppVision_Destroy_Callback(void)
{
	s32 manager_start_event_ret = 0;

	if (AppVisionManager.is_inited == MACR_FALSE)
	{
		return MACR_NONE;
	}

	HAL_Mutex_Lock(&AppVisionManager.mutex);

	// 获取事件状态
	manager_start_event_ret = HAL_Events_WaitAnd((Events *)(&AppVisionManager.events), MACR_APP_VISION_MANA_EVENT_START, MACR_WAIT_NO);

	// 若管理线程未启动，则直接返回
	if((AppVisionManager.is_running == MACR_FALSE) && 
		((manager_start_event_ret & MACR_APP_VISION_MANA_EVENT_START) != MACR_APP_VISION_MANA_EVENT_START))
	{
		HAL_Mutex_Unlock(&AppVisionManager.mutex);

		// 清除管理线程结束事件
		HAL_Events_ClrEvent((Events *)(&AppVisionManager.events), MACR_APP_VISION_MANA_EVENT_STOP);
		return MACR_NONE;
	}
	else
	{	// 与 lock 配套
		HAL_Mutex_Unlock(&AppVisionManager.mutex);
	}

	// S1: 请求管理线程结束
	HAL_Events_SetEvent((Events *)(&AppVisionManager.events), MACR_APP_VISION_MANA_EVENT_WAIT_STOP_REQ);

	VISION_LOG("func = %s, line = %d\r\n", __func__, __LINE__);

	// S2: 挂起等待管理结束。
	HAL_Events_WaitAnd((Events *)(&AppVisionManager.events), MACR_APP_VISION_MANA_EVENT_STOP, MACR_WAIT_FOREVER);

	VISION_LOG("func = %s, line = %d\r\n", __func__, __LINE__);
	
	// 清除管理线程结束事件
	HAL_Events_ClrEvent((Events *)(&AppVisionManager.events), MACR_APP_VISION_MANA_EVENT_STOP);

	return MACR_NONE;
}

/*******************************************************************************
Function      : AppVision_isDone()
Description   : 视觉应用是否完成
Input         : None
Output        : None
Return        : MACR_TRUE : 完成
                MACR_FALSE : 未完成
Author        : TanJW
Create Date   : 2025/07/25
Last Update   : None
Note          : None
*******************************************************************************/
s32 AppVision_isDone(void)
{
	s32 ret_val = MACR_FALSE;

	ret_val = AppVisionManager.is_running;

	return ret_val;
}

/******************************** END OF FILE *********************************/

