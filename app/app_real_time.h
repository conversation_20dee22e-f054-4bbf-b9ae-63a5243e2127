//==============================================================================
// Copyright (C), 2004-2019, MINDEO, Shenzhen MinDe Electronics Technology Ltd.
//
// File Name	: app_real_time.h
// Author		: TanJW
// Created On	: 2019 August 26, Mon
// Description	: Real Time(RT).
//
// History
// 0. V1.0, Created by TanJW, 2019 August 26, Mon
//
//
//==============================================================================

#ifndef __REAL_TIME_H_
#define __REAL_TIME_H_

#include "product_info.h"
#include "common.h"

#define MACR_RT_RET_SUCCESS					(1)
#define MACR_RT_RET_NONE					(0)
#define MACR_RT_RET_FAIL					(-1)
#define MACR_RT_RET_ERROR					(-2)

void App_RealTime_Init(void);
void App_RealTime_Start(void);
s32 App_RealTime_SetTime(u8 *data, s32 data_len);
void App_RealTime_SendTime(void);

#endif

