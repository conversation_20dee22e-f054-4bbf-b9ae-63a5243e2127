#ifndef APP_OUTPUT_H_
#define APP_OUTPUT_H_

#include "customer_info.h"
#include "code_info.h"
// defined for VC++ Testing
//#define VC_TEST_ON	TRUE

// define for ARM CPU
#define ARM_CPU_ON	TRUE

// For VC++ Test
#ifdef VC_TEST_ON
typedef unsigned char  u8;
typedef unsigned short u16;
typedef unsigned long  u32; 
#endif

// For ARM
#ifdef ARM_CPU_ON
#include"common.h"
#endif

#define APP_OUTPUT_TRUE		1
#define APP_OUTPUT_FALSE	0

#define MACR_APP_OUTPUT_ERR			APP_OUTPUT_FALSE
#define MACR_APP_OUTPUT_NO_ERR		APP_OUTPUT_TRUE

#define MACR_APP_OUTPUT_STR_BUF_SIZE	4096


u8 App_Output_CodeResult2OutputString(u32 index, u8 *pt_CodeResult, u16 *pt_CodeResultLen, u8 m_CodeType, u8 *pt_OutputString, u16 *pt_OutputStringLen);
u8 App_SameCode_Check(u8 *pt_cur_buffer,u8 *pt_pre_buffer, u16 *pt_pre_len);
u8 App_Cfg_Param(s32 code_result_index);
u8 App_CodeResult_Handle(void);
void App_Output_OPOS_Handle(u32 code_index, u8 m_CodeType, u8 *pt_OutputString, u16 *pt_OutputStringLen);
u16 App_Output_OPOS_CodeTypeMap(u16 m_code_type);
#if (MACR_CUSTOMER_WeChatPay == 1)
s32 SpecialKeys_CfgHandle(u8 cfg_state);
#endif
void App_Output_Module_Init(void);

//Backup Original Code Data Interface
u16 App_Output_Backup_OCode_Buff_Get_Len(void);
u8* App_Output_Backup_OCode_Get_Buff_Ptr(void);
u16 App_Output_Backup_OCode_Get_Bar_Type(void);
void App_Output_Backup_OCode_Buff_Init(void);
void App_Output_Backup_OCode_Rslt(u8* src_addr, u16 rslt_len, u16 barcode_type);
u8 App_Output_Backup_OCode_Send(void);
void App_Output_Module_Init(void);
u8 App_Output_Send_SerialNum(u32 data_trans_channel);

s32 App_Output_ParseCutCodeHeaderCmd(u8 *cmd_pt, u32 cmd_len);
u8 App_Read_Validity_Check(u8 code_type, u16 code_len);
s32 App_Output_GoodReadDelay_Check(u8 cfg_match_flag);

#endif /*APP_OUTPUT_NEW_H_*/
